#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版NER API服务
用于演示和测试
"""

from flask import Flask, request, jsonify, send_file
from flask_cors import CORS
import json
import random
import tempfile
import os
from datetime import datetime

app = Flask(__name__)
CORS(app)

# 简化的NER数据生成器
class SimpleNERGenerator:
    def __init__(self):
        self.person_names = ["张三", "李四", "王五", "赵六", "陈七", "刘八", "杨九", "黄十"]
        self.locations = ["北京", "上海", "广州", "深圳", "杭州", "南京", "武汉", "成都"]
        self.organizations = ["阿里巴巴", "腾讯", "百度", "字节跳动", "美团", "滴滴", "京东", "网易"]
        
        self.templates = [
            "{person}在{location}的{organization}工作。",
            "{person}于2023年从{location}来到{organization}。",
            "{organization}位于{location}，由{person}创立。",
            "2024年，{person}在{location}成立了{organization}。"
        ]
    
    def generate_sample(self):
        template = random.choice(self.templates)
        person = random.choice(self.person_names)
        location = random.choice(self.locations)
        organization = random.choice(self.organizations)
        
        text = template.format(person=person, location=location, organization=organization)
        tokens = list(text)
        labels = ['O'] * len(tokens)
        
        # 简单的实体标注
        if person in text:
            start = text.find(person)
            labels[start] = 'B-PERSON'
            for i in range(start + 1, start + len(person)):
                if i < len(labels):
                    labels[i] = 'I-PERSON'
        
        if location in text:
            start = text.find(location)
            labels[start] = 'B-LOCATION'
            for i in range(start + 1, start + len(location)):
                if i < len(labels):
                    labels[i] = 'I-LOCATION'
        
        if organization in text:
            start = text.find(organization)
            labels[start] = 'B-ORGANIZATION'
            for i in range(start + 1, start + len(organization)):
                if i < len(labels):
                    labels[i] = 'I-ORGANIZATION'
        
        return {
            'text': text,
            'tokens': tokens,
            'labels': labels
        }
    
    def generate_dataset(self, count):
        dataset = []
        for i in range(count):
            sample = self.generate_sample()
            sample['id'] = i + 1
            dataset.append(sample)
        return dataset
    
    def get_stats(self, dataset):
        stats = {
            'total_samples': len(dataset),
            'entity_counts': {'PERSON': 0, 'LOCATION': 0, 'ORGANIZATION': 0},
            'avg_sentence_length': 0
        }
        
        total_length = 0
        for sample in dataset:
            total_length += len(sample['text'])
            for label in sample['labels']:
                if label.startswith('B-'):
                    entity_type = label[2:]
                    if entity_type in stats['entity_counts']:
                        stats['entity_counts'][entity_type] += 1
        
        if dataset:
            stats['avg_sentence_length'] = total_length / len(dataset)
        
        return stats

# 全局变量
generator = SimpleNERGenerator()
current_dataset = []
current_stats = {}

@app.route('/api/health', methods=['GET'])
def health_check():
    return jsonify({
        'status': 'healthy',
        'service': 'Simple NER API',
        'version': '1.0.0'
    })

@app.route('/api/ner/generate', methods=['POST'])
def generate_data():
    global current_dataset, current_stats
    
    try:
        data = request.get_json()
        sample_count = data.get('sample_count', 100)
        
        if sample_count < 1 or sample_count > 10000:
            return jsonify({'error': '样本数量必须在1-10000之间'}), 400
        
        current_dataset = generator.generate_dataset(sample_count)
        current_stats = generator.get_stats(current_dataset)
        
        return jsonify({
            'success': True,
            'message': f'成功生成 {sample_count} 条数据',
            'stats': current_stats,
            'sample_preview': current_dataset[:5]
        })
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/ner/stats', methods=['GET'])
def get_stats():
    if not current_stats:
        return jsonify({'error': '暂无数据'}), 404
    return jsonify({'success': True, 'stats': current_stats})

@app.route('/api/ner/preview', methods=['GET'])
def get_preview():
    if not current_dataset:
        return jsonify({'error': '暂无数据'}), 404
    
    page = request.args.get('page', 1, type=int)
    per_page = request.args.get('per_page', 10, type=int)
    
    start = (page - 1) * per_page
    end = start + per_page
    
    return jsonify({
        'success': True,
        'data': current_dataset[start:end],
        'total': len(current_dataset)
    })

@app.route('/api/ner/download', methods=['GET'])
def download_data():
    if not current_dataset:
        return jsonify({'error': '暂无数据'}), 404
    
    try:
        temp_file = tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False, encoding='utf-8')
        json.dump(current_dataset, temp_file, ensure_ascii=False, indent=2)
        temp_file.close()
        
        return send_file(temp_file.name, as_attachment=True, download_name='ner_data.json')
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/ner/clear', methods=['DELETE'])
def clear_data():
    global current_dataset, current_stats
    current_dataset = []
    current_stats = {}
    return jsonify({'success': True, 'message': '数据已清空'})

@app.route('/api/ner/sample', methods=['GET'])
def get_sample():
    if not current_dataset:
        return jsonify({'error': '暂无数据'}), 404
    
    sample_id = request.args.get('id', 0, type=int)
    if sample_id < 0 or sample_id >= len(current_dataset):
        return jsonify({'error': '样本ID无效'}), 400
    
    sample = current_dataset[sample_id]
    entities = []
    
    # 提取实体信息
    current_entity = None
    for i, (token, label) in enumerate(zip(sample['tokens'], sample['labels'])):
        if label.startswith('B-'):
            if current_entity:
                entities.append(current_entity)
            current_entity = {
                'text': token,
                'label': label[2:],
                'start': i,
                'end': i
            }
        elif label.startswith('I-') and current_entity:
            current_entity['text'] += token
            current_entity['end'] = i
        else:
            if current_entity:
                entities.append(current_entity)
                current_entity = None
    
    if current_entity:
        entities.append(current_entity)
    
    return jsonify({
        'success': True,
        'sample': sample,
        'entities': entities
    })

if __name__ == '__main__':
    print("🚀 启动简化版NER API服务...")
    print("📍 API地址: http://localhost:5000")
    app.run(host='0.0.0.0', port=5000, debug=True)
