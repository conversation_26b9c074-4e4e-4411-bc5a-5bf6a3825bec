// 基于角色的访问控制系统
class RoleBasedAccessControl {
    constructor() {
        this.currentUser = null;
        this.permissions = {};
        this.rolePermissions = this.initializeRolePermissions();
        
        this.loadCurrentUser();
        this.applyRoleBasedAccess();
    }
    
    // 初始化角色权限配置
    initializeRolePermissions() {
        return {
            '系统管理员': {
                pages: ['dashboard', 'users', 'orders', 'analytics', 'ner', 'files', 'notifications', 'settings'],
                features: {
                    users: ['view', 'create', 'edit', 'delete', 'export', 'bulk_operations'],
                    orders: ['view', 'create', 'edit', 'delete', 'export', 'bulk_operations'],
                    analytics: ['view', 'export', 'advanced_analytics'],
                    ner: ['view', 'create', 'edit', 'delete', 'export'],
                    files: ['view', 'upload', 'download', 'delete', 'bulk_operations'],
                    notifications: ['view', 'create', 'delete', 'bulk_operations'],
                    settings: ['view', 'edit', 'system_settings', 'user_management']
                },
                ui: {
                    sidebar: ['all_sections'],
                    header: ['all_features'],
                    dashboard: ['all_stats', 'all_charts']
                }
            },
            
            '部门经理': {
                pages: ['dashboard', 'users', 'orders', 'analytics', 'files', 'notifications'],
                features: {
                    users: ['view', 'edit', 'export'],
                    orders: ['view', 'edit', 'export', 'bulk_operations'],
                    analytics: ['view', 'export'],
                    files: ['view', 'upload', 'download'],
                    notifications: ['view', 'create'],
                    settings: ['view', 'profile_only']
                },
                ui: {
                    sidebar: ['main_sections'],
                    header: ['basic_features'],
                    dashboard: ['basic_stats', 'basic_charts']
                }
            },
            
            '普通用户': {
                pages: ['dashboard', 'orders', 'files', 'notifications'],
                features: {
                    orders: ['view', 'create'],
                    files: ['view', 'upload', 'download'],
                    notifications: ['view'],
                    settings: ['view', 'profile_only']
                },
                ui: {
                    sidebar: ['basic_sections'],
                    header: ['basic_features'],
                    dashboard: ['user_stats']
                }
            },
            
            '测试用户': {
                pages: ['dashboard', 'ner', 'files'],
                features: {
                    ner: ['view', 'create'],
                    files: ['view', 'upload'],
                    settings: ['view', 'profile_only']
                },
                ui: {
                    sidebar: ['test_sections'],
                    header: ['basic_features'],
                    dashboard: ['test_stats']
                }
            },
            
            '演示用户': {
                pages: ['dashboard'],
                features: {
                    settings: ['view', 'profile_only']
                },
                ui: {
                    sidebar: ['demo_sections'],
                    header: ['basic_features'],
                    dashboard: ['demo_stats']
                }
            }
        };
    }
    
    loadCurrentUser() {
        const userData = localStorage.getItem('currentUser');
        if (userData) {
            try {
                this.currentUser = JSON.parse(userData);
                this.permissions = this.rolePermissions[this.currentUser.role] || this.rolePermissions['普通用户'];
            } catch (error) {
                console.error('加载用户数据失败:', error);
            }
        }
    }
    
    // 检查用户是否有访问页面的权限
    hasPageAccess(page) {
        return this.permissions.pages && this.permissions.pages.includes(page);
    }
    
    // 检查用户是否有特定功能的权限
    hasFeatureAccess(module, action) {
        return this.permissions.features && 
               this.permissions.features[module] && 
               this.permissions.features[module].includes(action);
    }
    
    // 检查用户是否有UI元素的权限
    hasUIAccess(section, element) {
        return this.permissions.ui && 
               this.permissions.ui[section] && 
               (this.permissions.ui[section].includes(element) || 
                this.permissions.ui[section].includes('all_' + section.replace('_', '')));
    }
    
    // 应用基于角色的访问控制
    applyRoleBasedAccess() {
        this.hideUnauthorizedPages();
        this.hideUnauthorizedFeatures();
        this.customizeDashboard();
        this.updateWelcomeMessage();
    }
    
    // 隐藏未授权的页面
    hideUnauthorizedPages() {
        const allPages = ['dashboard', 'users', 'orders', 'analytics', 'ner', 'files', 'notifications', 'settings'];
        
        allPages.forEach(page => {
            if (!this.hasPageAccess(page)) {
                // 隐藏导航项
                const navItem = document.querySelector(`[data-page="${page}"]`);
                if (navItem) {
                    navItem.style.display = 'none';
                }
                
                // 隐藏页面内容
                const pageElement = document.getElementById(page);
                if (pageElement) {
                    pageElement.style.display = 'none';
                }
            }
        });
    }
    
    // 隐藏未授权的功能
    hideUnauthorizedFeatures() {
        // 用户管理功能
        if (!this.hasFeatureAccess('users', 'create')) {
            this.hideElements('.add-user-btn, [onclick*="addUser"]');
        }
        if (!this.hasFeatureAccess('users', 'delete')) {
            this.hideElements('.delete-user-btn, [onclick*="deleteUser"]');
        }
        if (!this.hasFeatureAccess('users', 'bulk_operations')) {
            this.hideElements('.bulk-actions-toolbar');
        }
        
        // 订单管理功能
        if (!this.hasFeatureAccess('orders', 'create')) {
            this.hideElements('.add-order-btn, [onclick*="addOrder"]');
        }
        if (!this.hasFeatureAccess('orders', 'bulk_operations')) {
            this.hideElements('#orderBulkActionsToolbar');
        }
        
        // 文件管理功能
        if (!this.hasFeatureAccess('files', 'upload')) {
            this.hideElements('[onclick*="showUploadModal"], .upload-btn');
        }
        if (!this.hasFeatureAccess('files', 'delete')) {
            this.hideElements('[onclick*="deleteFile"]');
        }
        
        // 系统设置功能
        if (!this.hasFeatureAccess('settings', 'system_settings')) {
            this.hideElements('.system-settings-section');
        }
    }
    
    // 自定义仪表盘
    customizeDashboard() {
        const role = this.currentUser?.role;
        
        switch (role) {
            case '系统管理员':
                this.showAdminDashboard();
                break;
            case '部门经理':
                this.showManagerDashboard();
                break;
            case '普通用户':
                this.showUserDashboard();
                break;
            case '测试用户':
                this.showTestDashboard();
                break;
            case '演示用户':
                this.showDemoDashboard();
                break;
            default:
                this.showUserDashboard();
        }
    }
    
    showAdminDashboard() {
        // 管理员看到所有统计信息
        this.showElements('.stats-grid .stat-card');
        this.updateDashboardTitle('系统管理概览', '管理整个系统的运营状况');
    }
    
    showManagerDashboard() {
        // 经理看到部门相关统计
        this.hideElements('.stat-card:nth-child(n+5)'); // 隐藏部分高级统计
        this.updateDashboardTitle('部门管理概览', '管理您的部门和团队');
        this.addManagerSpecificStats();
    }
    
    showUserDashboard() {
        // 普通用户只看到基本信息
        this.hideElements('.stat-card:nth-child(n+3)'); // 只显示前两个统计卡片
        this.updateDashboardTitle('个人工作台', '查看您的工作进展和任务');
        this.addUserSpecificStats();
    }
    
    showTestDashboard() {
        // 测试用户看到测试相关功能
        this.hideElements('.stats-grid .stat-card');
        this.updateDashboardTitle('测试环境', '进行系统功能测试');
        this.addTestSpecificStats();
    }
    
    showDemoDashboard() {
        // 演示用户看到演示内容
        this.hideElements('.stats-grid .stat-card');
        this.updateDashboardTitle('系统演示', '体验系统主要功能');
        this.addDemoSpecificStats();
    }
    
    // 更新仪表盘标题
    updateDashboardTitle(title, subtitle) {
        const titleElement = document.querySelector('.welcome-title');
        const subtitleElement = document.querySelector('.welcome-subtitle');
        
        if (titleElement) {
            // 保留问候语，只更新后面的部分
            const greeting = titleElement.querySelector('.greeting');
            const userName = titleElement.querySelector('.user-name-display');
            const wave = titleElement.querySelector('.wave-emoji');
            
            if (greeting && userName) {
                titleElement.innerHTML = `
                    ${greeting.outerHTML}，${userName.outerHTML}${wave ? wave.outerHTML : ''}
                    <div style="font-size: 1.2rem; margin-top: 8px; opacity: 0.8;">${title}</div>
                `;
            }
        }
        
        if (subtitleElement) {
            subtitleElement.textContent = subtitle;
        }
    }
    
    // 添加角色特定的统计信息
    addManagerSpecificStats() {
        const statsGrid = document.querySelector('.stats-grid');
        if (statsGrid) {
            const managerStats = `
                <div class="stat-card warning">
                    <div class="stat-content">
                        <div class="stat-icon">
                            <i class="fas fa-users"></i>
                        </div>
                        <div class="stat-info">
                            <h3 class="stat-number">12</h3>
                            <p class="stat-label">团队成员</p>
                        </div>
                    </div>
                </div>
                <div class="stat-card info">
                    <div class="stat-content">
                        <div class="stat-icon">
                            <i class="fas fa-tasks"></i>
                        </div>
                        <div class="stat-info">
                            <h3 class="stat-number">8</h3>
                            <p class="stat-label">待审批</p>
                        </div>
                    </div>
                </div>
            `;
            statsGrid.insertAdjacentHTML('beforeend', managerStats);
        }
    }
    
    addUserSpecificStats() {
        const statsGrid = document.querySelector('.stats-grid');
        if (statsGrid) {
            const userStats = `
                <div class="stat-card success">
                    <div class="stat-content">
                        <div class="stat-icon">
                            <i class="fas fa-check-circle"></i>
                        </div>
                        <div class="stat-info">
                            <h3 class="stat-number">5</h3>
                            <p class="stat-label">已完成任务</p>
                        </div>
                    </div>
                </div>
                <div class="stat-card warning">
                    <div class="stat-content">
                        <div class="stat-icon">
                            <i class="fas fa-clock"></i>
                        </div>
                        <div class="stat-info">
                            <h3 class="stat-number">3</h3>
                            <p class="stat-label">待处理任务</p>
                        </div>
                    </div>
                </div>
            `;
            statsGrid.insertAdjacentHTML('beforeend', userStats);
        }
    }
    
    addTestSpecificStats() {
        const statsGrid = document.querySelector('.stats-grid');
        if (statsGrid) {
            const testStats = `
                <div class="stat-card primary">
                    <div class="stat-content">
                        <div class="stat-icon">
                            <i class="fas fa-vial"></i>
                        </div>
                        <div class="stat-info">
                            <h3 class="stat-number">15</h3>
                            <p class="stat-label">测试用例</p>
                        </div>
                    </div>
                </div>
                <div class="stat-card success">
                    <div class="stat-content">
                        <div class="stat-icon">
                            <i class="fas fa-check"></i>
                        </div>
                        <div class="stat-info">
                            <h3 class="stat-number">12</h3>
                            <p class="stat-label">通过测试</p>
                        </div>
                    </div>
                </div>
            `;
            statsGrid.insertAdjacentHTML('beforeend', testStats);
        }
    }
    
    addDemoSpecificStats() {
        const statsGrid = document.querySelector('.stats-grid');
        if (statsGrid) {
            const demoStats = `
                <div class="stat-card info">
                    <div class="stat-content">
                        <div class="stat-icon">
                            <i class="fas fa-play"></i>
                        </div>
                        <div class="stat-info">
                            <h3 class="stat-number">演示</h3>
                            <p class="stat-label">模式</p>
                        </div>
                    </div>
                </div>
                <div class="stat-card primary">
                    <div class="stat-content">
                        <div class="stat-icon">
                            <i class="fas fa-eye"></i>
                        </div>
                        <div class="stat-info">
                            <h3 class="stat-number">只读</h3>
                            <p class="stat-label">权限</p>
                        </div>
                    </div>
                </div>
            `;
            statsGrid.insertAdjacentHTML('beforeend', demoStats);
        }
    }
    
    // 更新欢迎消息
    updateWelcomeMessage() {
        const role = this.currentUser?.role;
        const welcomeStats = document.querySelector('.welcome-stats');
        
        if (welcomeStats && role !== '系统管理员') {
            // 非管理员用户显示不同的快速统计
            const roleSpecificStats = this.getRoleSpecificWelcomeStats(role);
            welcomeStats.innerHTML = roleSpecificStats;
        }
    }
    
    getRoleSpecificWelcomeStats(role) {
        switch (role) {
            case '部门经理':
                return `
                    <div class="welcome-stat-item">
                        <span class="stat-value">12</span>
                        <span class="stat-label">团队成员</span>
                    </div>
                    <div class="welcome-stat-item">
                        <span class="stat-value">8</span>
                        <span class="stat-label">待审批</span>
                    </div>
                    <div class="welcome-stat-item">
                        <span class="stat-value">正常</span>
                        <span class="stat-label">部门状态</span>
                    </div>
                `;
            case '普通用户':
                return `
                    <div class="welcome-stat-item">
                        <span class="stat-value">5</span>
                        <span class="stat-label">我的任务</span>
                    </div>
                    <div class="welcome-stat-item">
                        <span class="stat-value">3</span>
                        <span class="stat-label">待处理</span>
                    </div>
                    <div class="welcome-stat-item">
                        <span class="stat-value">在线</span>
                        <span class="stat-label">状态</span>
                    </div>
                `;
            case '测试用户':
                return `
                    <div class="welcome-stat-item">
                        <span class="stat-value">15</span>
                        <span class="stat-label">测试用例</span>
                    </div>
                    <div class="welcome-stat-item">
                        <span class="stat-value">12</span>
                        <span class="stat-label">通过</span>
                    </div>
                    <div class="welcome-stat-item">
                        <span class="stat-value">测试中</span>
                        <span class="stat-label">状态</span>
                    </div>
                `;
            default:
                return `
                    <div class="welcome-stat-item">
                        <span class="stat-value">演示</span>
                        <span class="stat-label">模式</span>
                    </div>
                    <div class="welcome-stat-item">
                        <span class="stat-value">只读</span>
                        <span class="stat-label">权限</span>
                    </div>
                    <div class="welcome-stat-item">
                        <span class="stat-value">体验</span>
                        <span class="stat-label">状态</span>
                    </div>
                `;
        }
    }
    
    // 工具方法
    hideElements(selector) {
        const elements = document.querySelectorAll(selector);
        elements.forEach(element => {
            element.style.display = 'none';
        });
    }
    
    showElements(selector) {
        const elements = document.querySelectorAll(selector);
        elements.forEach(element => {
            element.style.display = '';
        });
    }
    
    // 获取当前用户角色
    getCurrentUserRole() {
        return this.currentUser?.role || '普通用户';
    }
    
    // 获取当前用户权限
    getCurrentUserPermissions() {
        return this.permissions;
    }
}

// 全局权限控制实例
let rbacManager = null;

// 权限检查函数
function hasPermission(module, action) {
    return rbacManager ? rbacManager.hasFeatureAccess(module, action) : false;
}

function hasPageAccess(page) {
    return rbacManager ? rbacManager.hasPageAccess(page) : false;
}

function getCurrentUserRole() {
    return rbacManager ? rbacManager.getCurrentUserRole() : '普通用户';
}

// 初始化权限控制
function initializeRBAC() {
    rbacManager = new RoleBasedAccessControl();
    console.log('✅ 基于角色的访问控制已初始化');
    console.log('当前用户角色:', rbacManager.getCurrentUserRole());
    console.log('用户权限:', rbacManager.getCurrentUserPermissions());
}

// 页面加载时初始化
document.addEventListener('DOMContentLoaded', function() {
    // 延迟初始化，确保其他组件已加载
    setTimeout(initializeRBAC, 200);
});
