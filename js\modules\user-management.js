// 用户管理功能

// 增强的通知函数 - 现在使用统一的通知管理器
function showNotification(message, type = 'info', options = {}) {
    // 确保通知管理器已加载
    if (window.notificationManager) {
        return window.notificationManager.show(message, type, options);
    }

    // 如果通知管理器未加载，等待加载后再显示
    if (window.moduleLoader) {
        window.moduleLoader.waitFor('notificationManager').then(manager => {
            manager.show(message, type, options);
        });
        return;
    }

    // 降级到简单的alert（仅作为最后的备选方案）
    const typeMap = {
        'error': '错误',
        'success': '成功',
        'warning': '警告',
        'info': '提示'
    };
    alert(`${typeMap[type] || '通知'}: ${message}`);
}

// 加载注册用户数据 - 使用安全的localStorage操作
function loadRegisteredUsers() {
    if (window.errorHandler) {
        const users = window.errorHandler.safeLocalStorageGet('registeredUsers', []);
        console.log('从localStorage加载的注册用户:', users);
        return users;
    }

    // 降级处理
    try {
        const registeredUsers = JSON.parse(localStorage.getItem('registeredUsers') || '[]');
        console.log('从localStorage加载的注册用户:', registeredUsers);
        return registeredUsers;
    } catch (error) {
        console.error('加载注册用户数据时出错:', error);
        showNotification('用户数据加载失败', 'error');
        return [];
    }
}

// 模拟用户数据
let users = [
    {
        id: 1,
        name: '张管理员',
        username: 'admin',
        email: '<EMAIL>',
        phone: '13800138001',
        role: '系统管理员',
        status: 'active',
        department: '技术部',
        lastLogin: '2024-01-15 10:30:00',
        createdAt: '2023-01-01 09:00:00',
        avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=40&h=40&fit=crop&crop=face'
    },
    {
        id: 2,
        name: '李经理',
        username: 'manager',
        email: '<EMAIL>',
        phone: '13800138002',
        role: '部门经理',
        status: 'active',
        department: '市场部',
        lastLogin: '2024-01-15 09:15:00',
        createdAt: '2023-02-15 14:30:00',
        avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=40&h=40&fit=crop&crop=face'
    },
    {
        id: 3,
        name: '王用户',
        username: 'user',
        email: '<EMAIL>',
        phone: '13800138003',
        role: '普通用户',
        status: 'active',
        department: '销售部',
        lastLogin: '2024-01-14 16:45:00',
        createdAt: '2023-03-10 11:20:00',
        avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=40&h=40&fit=crop&crop=face'
    },
    {
        id: 4,
        name: '测试员',
        username: 'test',
        email: '<EMAIL>',
        phone: '13800138004',
        role: '测试用户',
        status: 'inactive',
        department: '测试部',
        lastLogin: '2024-01-10 14:20:00',
        createdAt: '2023-06-01 10:00:00',
        avatar: 'https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=40&h=40&fit=crop&crop=face'
    },
    {
        id: 5,
        name: '演示员',
        username: 'demo',
        email: '<EMAIL>',
        phone: '13800138005',
        role: '演示用户',
        status: 'active',
        department: '演示部',
        lastLogin: '2024-01-13 11:30:00',
        createdAt: '2023-08-15 15:45:00',
        avatar: 'https://images.unsplash.com/photo-1535713875002-d1d0cf377fde?w=40&h=40&fit=crop&crop=face'
    }
];

// 合并注册用户到用户列表
function mergeRegisteredUsers() {
    const registeredUsers = loadRegisteredUsers();

    registeredUsers.forEach(regUser => {
        // 检查用户是否已存在（避免重复）
        const existingUser = users.find(user =>
            user.username === regUser.username || user.email === regUser.email
        );

        if (!existingUser) {
            // 确保ID不冲突
            const maxId = Math.max(...users.map(u => u.id), 0);
            regUser.id = Math.max(regUser.id, maxId + 1);

            users.push(regUser);
            console.log('添加注册用户到用户列表:', regUser.name);
        }
    });
}

// 初始化时合并注册用户
mergeRegisteredUsers();

// 分页配置
let currentPage = 1;
const itemsPerPage = 10;
let filteredUsers = [...users];
let editingUserId = null;

// 刷新用户列表（重新加载注册用户）
function refreshUserList() {
    console.log('刷新用户列表...');
    mergeRegisteredUsers();
    filteredUsers = [...users];
    updateUserStats();
    renderUsersTable();
    renderPagination();
}

// 初始化用户管理
function initializeUserManagement() {
    updateUserStats();
    renderUsersTable();
    renderPagination();

    // 设置定期刷新，以便检测新注册的用户
    setInterval(refreshUserList, 5000); // 每5秒检查一次
}

// 更新用户统计
function updateUserStats() {
    const totalUsers = users.length;
    const activeUsers = users.filter(user => user.status === 'active').length;
    const adminUsers = users.filter(user => user.role === '系统管理员').length;
    
    // 计算本月新增用户（模拟数据）
    const currentMonth = new Date().getMonth();
    const newUsers = users.filter(user => {
        const createdMonth = new Date(user.createdAt).getMonth();
        return createdMonth === currentMonth;
    }).length;

    document.getElementById('totalUsers').textContent = totalUsers;
    document.getElementById('activeUsers').textContent = activeUsers;
    document.getElementById('adminUsers').textContent = adminUsers;
    document.getElementById('newUsers').textContent = newUsers;
}

// 渲染用户表格
function renderUsersTable() {
    const tbody = document.getElementById('usersTableBody');
    const startIndex = (currentPage - 1) * itemsPerPage;
    const endIndex = startIndex + itemsPerPage;
    const pageUsers = filteredUsers.slice(startIndex, endIndex);

    tbody.innerHTML = pageUsers.map(user => `
        <tr>
            <td>
                <input type="checkbox" class="user-checkbox" value="${user.id}">
            </td>
            <td>
                <div class="user-info-cell">
                    <div class="user-avatar-small">
                        <img src="${user.avatar}" alt="${user.name}">
                    </div>
                    <div class="user-details">
                        <div class="user-name">${user.name}</div>
                        <div class="user-email">${user.email}</div>
                    </div>
                </div>
            </td>
            <td>
                <span class="role-badge ${getRoleClass(user.role)}">
                    <i class="fas ${getRoleIcon(user.role)}"></i>
                    ${user.role}
                </span>
            </td>
            <td>
                <span class="status-badge ${user.status}">
                    <i class="fas ${getStatusIcon(user.status)}"></i>
                    ${getStatusText(user.status)}
                </span>
            </td>
            <td>${formatDateTime(user.lastLogin)}</td>
            <td>${formatDateTime(user.createdAt)}</td>
            <td>
                <div class="action-buttons">
                    <button class="btn-icon" title="查看详情" onclick="viewUser(${user.id})">
                        <i class="fas fa-eye"></i>
                    </button>
                    <button class="btn-icon" title="编辑" onclick="editUser(${user.id})">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="btn-icon danger" title="删除" onclick="deleteUser(${user.id})">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </td>
        </tr>
    `).join('');
}

// 获取角色样式类
function getRoleClass(role) {
    const roleMap = {
        '系统管理员': 'admin',
        '部门经理': 'manager',
        '普通用户': 'user',
        '测试用户': 'test',
        '演示用户': 'demo'
    };
    return roleMap[role] || 'user';
}

// 获取角色图标
function getRoleIcon(role) {
    const iconMap = {
        '系统管理员': 'fa-user-shield',
        '部门经理': 'fa-user-tie',
        '普通用户': 'fa-user',
        '测试用户': 'fa-user-cog',
        '演示用户': 'fa-user-graduate'
    };
    return iconMap[role] || 'fa-user';
}

// 获取状态图标
function getStatusIcon(status) {
    const iconMap = {
        'active': 'fa-check-circle',
        'inactive': 'fa-pause-circle',
        'suspended': 'fa-ban'
    };
    return iconMap[status] || 'fa-question-circle';
}

// 获取状态文本
function getStatusText(status) {
    const textMap = {
        'active': '活跃',
        'inactive': '非活跃',
        'suspended': '已暂停'
    };
    return textMap[status] || '未知';
}

// 格式化日期时间
function formatDateTime(dateString) {
    const date = new Date(dateString);
    return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
    });
}

// 渲染分页
function renderPagination() {
    const pagination = document.getElementById('usersPagination');
    const totalPages = Math.ceil(filteredUsers.length / itemsPerPage);
    
    if (totalPages <= 1) {
        pagination.innerHTML = '';
        return;
    }

    let paginationHTML = `
        <button class="pagination-btn" ${currentPage === 1 ? 'disabled' : ''} onclick="changePage(${currentPage - 1})">
            <i class="fas fa-chevron-left"></i>
        </button>
    `;

    // 显示页码
    for (let i = 1; i <= totalPages; i++) {
        if (i === 1 || i === totalPages || (i >= currentPage - 2 && i <= currentPage + 2)) {
            paginationHTML += `
                <button class="pagination-btn ${i === currentPage ? 'active' : ''}" onclick="changePage(${i})">
                    ${i}
                </button>
            `;
        } else if (i === currentPage - 3 || i === currentPage + 3) {
            paginationHTML += '<span class="pagination-ellipsis">...</span>';
        }
    }

    paginationHTML += `
        <button class="pagination-btn" ${currentPage === totalPages ? 'disabled' : ''} onclick="changePage(${currentPage + 1})">
            <i class="fas fa-chevron-right"></i>
        </button>
        <div class="pagination-info">
            显示 ${(currentPage - 1) * itemsPerPage + 1}-${Math.min(currentPage * itemsPerPage, filteredUsers.length)} 
            共 ${filteredUsers.length} 条
        </div>
    `;

    pagination.innerHTML = paginationHTML;
}

// 切换页面
function changePage(page) {
    const totalPages = Math.ceil(filteredUsers.length / itemsPerPage);
    if (page >= 1 && page <= totalPages) {
        currentPage = page;
        renderUsersTable();
        renderPagination();
    }
}

// 筛选用户
function filterUsers() {
    const searchTerm = document.getElementById('userSearch').value.toLowerCase();
    const roleFilter = document.getElementById('roleFilter').value;
    const statusFilter = document.getElementById('statusFilter').value;

    filteredUsers = users.filter(user => {
        const matchesSearch = !searchTerm || 
            user.name.toLowerCase().includes(searchTerm) ||
            user.username.toLowerCase().includes(searchTerm) ||
            user.email.toLowerCase().includes(searchTerm);
        
        const matchesRole = !roleFilter || user.role === roleFilter;
        const matchesStatus = !statusFilter || user.status === statusFilter;

        return matchesSearch && matchesRole && matchesStatus;
    });

    currentPage = 1;
    renderUsersTable();
    renderPagination();
}

// 全选/取消全选
function toggleSelectAll() {
    const selectAll = document.getElementById('selectAll');
    const checkboxes = document.querySelectorAll('.user-checkbox');
    
    checkboxes.forEach(checkbox => {
        checkbox.checked = selectAll.checked;
    });
}

// 显示添加用户模态框
function showAddUserModal() {
    editingUserId = null;
    document.getElementById('userModalTitle').textContent = '添加用户';
    document.getElementById('passwordGroup').style.display = 'block';
    document.getElementById('userForm').reset();
    document.getElementById('userModalOverlay').classList.add('active');
}

// 编辑用户
function editUser(userId) {
    const user = users.find(u => u.id === userId);
    if (!user) return;

    editingUserId = userId;
    document.getElementById('userModalTitle').textContent = '编辑用户';
    document.getElementById('passwordGroup').style.display = 'none';
    
    // 填充表单数据
    document.getElementById('userName').value = user.name;
    document.getElementById('userUsername').value = user.username;
    document.getElementById('userEmail').value = user.email;
    document.getElementById('userPhone').value = user.phone || '';
    document.getElementById('userRole').value = user.role;
    document.getElementById('userStatus').value = user.status;
    document.getElementById('userDepartment').value = user.department || '';
    
    document.getElementById('userModalOverlay').classList.add('active');
}

// 关闭用户模态框
function closeUserModal() {
    document.getElementById('userModalOverlay').classList.remove('active');
}

// 保存用户
function saveUser() {
    const form = document.getElementById('userForm');
    const formData = new FormData(form);
    
    // 基本验证
    if (!formData.get('name') || !formData.get('username') || !formData.get('email') || !formData.get('role')) {
        showNotification('请填写所有必填字段', 'error');
        return;
    }

    const userData = {
        name: formData.get('name'),
        username: formData.get('username'),
        email: formData.get('email'),
        phone: formData.get('phone'),
        role: formData.get('role'),
        status: formData.get('status'),
        department: formData.get('department')
    };

    if (editingUserId) {
        // 编辑现有用户
        const userIndex = users.findIndex(u => u.id === editingUserId);
        if (userIndex !== -1) {
            users[userIndex] = { ...users[userIndex], ...userData };
            showNotification('用户信息更新成功', 'success');
        }
    } else {
        // 添加新用户
        const newUser = {
            id: Math.max(...users.map(u => u.id)) + 1,
            ...userData,
            lastLogin: '从未登录',
            createdAt: new Date().toISOString(),
            avatar: 'https://images.unsplash.com/photo-1535713875002-d1d0cf377fde?w=40&h=40&fit=crop&crop=face'
        };
        users.push(newUser);
        showNotification('用户添加成功', 'success');
    }

    closeUserModal();
    filterUsers(); // 重新渲染表格
    updateUserStats();
}

// 删除用户
function deleteUser(userId) {
    const user = users.find(u => u.id === userId);
    if (!user) return;

    document.getElementById('deleteUserName').textContent = user.name;
    document.getElementById('deleteModalOverlay').classList.add('active');
    window.deleteUserId = userId;
}

// 关闭删除模态框
function closeDeleteModal() {
    document.getElementById('deleteModalOverlay').classList.remove('active');
}

// 确认删除用户
function confirmDeleteUser() {
    const userId = window.deleteUserId;
    const userIndex = users.findIndex(u => u.id === userId);
    
    if (userIndex !== -1) {
        users.splice(userIndex, 1);
        showNotification('用户删除成功', 'success');
        closeDeleteModal();
        filterUsers();
        updateUserStats();
    }
}

// 查看用户详情
function viewUser(userId) {
    const user = users.find(u => u.id === userId);
    if (!user) return;

    showNotification(`查看用户: ${user.name} 的详细信息`, 'info');
    // 这里可以实现用户详情页面
}

// 导出用户数据
function exportUsers() {
    const csvContent = "data:text/csv;charset=utf-8," 
        + "姓名,用户名,邮箱,电话,角色,状态,部门,最后登录,注册时间\n"
        + users.map(user => 
            `${user.name},${user.username},${user.email},${user.phone || ''},${user.role},${getStatusText(user.status)},${user.department || ''},${user.lastLogin},${formatDateTime(user.createdAt)}`
        ).join("\n");

    const encodedUri = encodeURI(csvContent);
    const link = document.createElement("a");
    link.setAttribute("href", encodedUri);
    link.setAttribute("download", "users.csv");
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    
    showNotification('用户数据导出成功', 'success');
}

// 绑定用户管理事件
function bindUserManagementEvents() {
    // 搜索框事件
    const searchInput = document.getElementById('userSearch');
    if (searchInput) {
        searchInput.addEventListener('input', debounce(handleUserSearch, 300));
    }

    // 角色筛选事件
    const roleFilter = document.getElementById('roleFilter');
    if (roleFilter) {
        roleFilter.addEventListener('change', handleRoleFilter);
    }

    // 状态筛选事件
    const statusFilter = document.getElementById('statusFilter');
    if (statusFilter) {
        statusFilter.addEventListener('change', handleStatusFilter);
    }

    // 批量操作事件
    const batchActions = document.getElementById('batchActions');
    if (batchActions) {
        batchActions.addEventListener('change', handleBatchAction);
    }

    // 添加用户按钮
    const addUserBtn = document.getElementById('addUserBtn');
    if (addUserBtn) {
        addUserBtn.addEventListener('click', showAddUserModal);
    }

    // 导出用户按钮
    const exportBtn = document.getElementById('exportUsersBtn');
    if (exportBtn) {
        exportBtn.addEventListener('click', exportUsers);
    }
}

// 初始化用户筛选功能
function initializeUserFilters() {
    // 初始化角色筛选选项
    const roleFilter = document.getElementById('roleFilter');
    if (roleFilter) {
        const roles = [...new Set(users.map(user => user.role))];
        roleFilter.innerHTML = '<option value="">所有角色</option>';
        roles.forEach(role => {
            const option = document.createElement('option');
            option.value = role;
            option.textContent = role;
            roleFilter.appendChild(option);
        });
    }
}

// 防抖函数
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// 处理用户搜索
function handleUserSearch(event) {
    const searchTerm = event.target.value.toLowerCase();
    const filteredUsers = users.filter(user =>
        user.name.toLowerCase().includes(searchTerm) ||
        user.username.toLowerCase().includes(searchTerm) ||
        user.email.toLowerCase().includes(searchTerm) ||
        user.department.toLowerCase().includes(searchTerm)
    );
    renderUsersTable(filteredUsers);
}

// 处理角色筛选
function handleRoleFilter(event) {
    const selectedRole = event.target.value;
    const filteredUsers = selectedRole ?
        users.filter(user => user.role === selectedRole) :
        users;
    renderUsersTable(filteredUsers);
}

// 处理状态筛选
function handleStatusFilter(event) {
    const selectedStatus = event.target.value;
    const filteredUsers = selectedStatus ?
        users.filter(user => user.status === selectedStatus) :
        users;
    renderUsersTable(filteredUsers);
}

// 处理批量操作
function handleBatchAction(event) {
    const action = event.target.value;
    const selectedUsers = getSelectedUsers();

    if (selectedUsers.length === 0) {
        showNotification('请先选择要操作的用户', 'warning');
        return;
    }

    switch (action) {
        case 'activate':
            batchActivateUsers(selectedUsers);
            break;
        case 'deactivate':
            batchDeactivateUsers(selectedUsers);
            break;
        case 'delete':
            batchDeleteUsers(selectedUsers);
            break;
        case 'export':
            exportSelectedUsers(selectedUsers);
            break;
    }

    // 重置选择
    event.target.value = '';
}

// 获取选中的用户
function getSelectedUsers() {
    const checkboxes = document.querySelectorAll('.user-checkbox:checked');
    return Array.from(checkboxes).map(cb => parseInt(cb.value));
}

// 批量激活用户
function batchActivateUsers(userIds) {
    if (confirm(`确定要激活选中的 ${userIds.length} 个用户吗？`)) {
        userIds.forEach(id => {
            const user = users.find(u => u.id === id);
            if (user) {
                user.status = 'active';
            }
        });
        renderUsersTable();
        updateUserStats();
        showNotification(`成功激活 ${userIds.length} 个用户`, 'success');
    }
}

// 批量停用用户
function batchDeactivateUsers(userIds) {
    if (confirm(`确定要停用选中的 ${userIds.length} 个用户吗？`)) {
        userIds.forEach(id => {
            const user = users.find(u => u.id === id);
            if (user) {
                user.status = 'inactive';
            }
        });
        renderUsersTable();
        updateUserStats();
        showNotification(`成功停用 ${userIds.length} 个用户`, 'success');
    }
}

// 导出用户数据
function exportUsers() {
    const csvContent = generateUserCSV(users);
    downloadCSV(csvContent, 'users.csv');
    showNotification('用户数据导出成功', 'success');
}

// 导出选中用户
function exportSelectedUsers(userIds) {
    const selectedUsers = users.filter(user => userIds.includes(user.id));
    const csvContent = generateUserCSV(selectedUsers);
    downloadCSV(csvContent, 'selected_users.csv');
    showNotification(`成功导出 ${selectedUsers.length} 个用户数据`, 'success');
}

// 生成CSV内容
function generateUserCSV(userData) {
    const headers = ['ID', '姓名', '用户名', '邮箱', '电话', '角色', '状态', '部门', '最后登录', '创建时间'];
    const csvRows = [headers.join(',')];

    userData.forEach(user => {
        const row = [
            user.id,
            user.name,
            user.username,
            user.email,
            user.phone,
            user.role,
            user.status === 'active' ? '激活' : '停用',
            user.department,
            user.lastLogin,
            user.createdAt
        ];
        csvRows.push(row.join(','));
    });

    return csvRows.join('\n');
}

// 下载CSV文件
function downloadCSV(content, filename) {
    const blob = new Blob([content], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', filename);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
}

// 页面加载时初始化
document.addEventListener('DOMContentLoaded', function() {
    // 检查是否在用户管理页面
    if (document.getElementById('usersTable')) {
        initializeUserManagement();
    }
});
