// 报表管理系统

// 报表数据模拟
const reports = [
    {
        id: 1,
        name: '月度销售报表',
        type: 'sales',
        description: '2024年1月销售数据统计',
        createdAt: '2024-01-15',
        createdBy: '张管理员',
        lastGenerated: '2024-01-15 14:30:00',
        downloads: 23,
        format: 'excel',
        status: 'completed',
        size: '2.3MB'
    },
    {
        id: 2,
        name: '库存状态报表',
        type: 'inventory',
        description: '当前库存状态和预警信息',
        createdAt: '2024-01-14',
        createdBy: '李经理',
        lastGenerated: '2024-01-14 16:45:00',
        downloads: 15,
        format: 'pdf',
        status: 'completed',
        size: '1.8MB'
    },
    {
        id: 3,
        name: '用户活跃度分析',
        type: 'users',
        description: '用户登录和使用情况分析',
        createdAt: '2024-01-13',
        createdBy: '王分析师',
        lastGenerated: '2024-01-13 10:20:00',
        downloads: 8,
        format: 'pdf',
        status: 'completed',
        size: '3.1MB'
    },
    {
        id: 4,
        name: '财务收支报表',
        type: 'financial',
        description: '本月收入支出详细统计',
        createdAt: '2024-01-12',
        createdBy: '赵会计',
        lastGenerated: '2024-01-12 09:15:00',
        downloads: 31,
        format: 'excel',
        status: 'completed',
        size: '4.2MB'
    },
    {
        id: 5,
        name: '自定义数据报表',
        type: 'custom',
        description: '订单趋势和用户分布分析',
        createdAt: '2024-01-11',
        createdBy: '张管理员',
        lastGenerated: '2024-01-11 15:30:00',
        downloads: 12,
        format: 'pdf',
        status: 'generating',
        size: '0MB'
    }
];

// 报表模板
const reportTemplates = {
    sales: {
        name: '销售报表',
        icon: 'fas fa-chart-line',
        color: 'primary',
        fields: ['订单数量', '销售金额', '客户数量', '平均订单价值'],
        chartTypes: ['line', 'bar', 'pie']
    },
    inventory: {
        name: '库存报表',
        icon: 'fas fa-boxes',
        color: 'warning',
        fields: ['商品数量', '库存价值', '周转率', '缺货商品'],
        chartTypes: ['bar', 'table', 'pie']
    },
    users: {
        name: '用户报表',
        icon: 'fas fa-users',
        color: 'success',
        fields: ['活跃用户', '新增用户', '留存率', '使用时长'],
        chartTypes: ['line', 'bar', 'table']
    },
    financial: {
        name: '财务报表',
        icon: 'fas fa-dollar-sign',
        color: 'info',
        fields: ['总收入', '总支出', '净利润', '成本分析'],
        chartTypes: ['line', 'bar', 'pie', 'table']
    }
};

let filteredReports = [...reports];

// 初始化报表管理
function initializeReportsManagement() {
    renderReportsList();
    updateReportsStats();
}

// 渲染报表列表
function renderReportsList() {
    const container = document.getElementById('reportsList');
    if (!container) return;

    if (filteredReports.length === 0) {
        container.innerHTML = `
            <div class="empty-state">
                <i class="fas fa-chart-bar"></i>
                <h3>暂无报表</h3>
                <p>还没有生成任何报表</p>
            </div>
        `;
        return;
    }

    container.innerHTML = filteredReports.map(report => `
        <div class="report-item">
            <div class="report-icon ${getReportTypeColor(report.type)}">
                <i class="${getReportTypeIcon(report.type)}"></i>
            </div>
            <div class="report-content">
                <div class="report-header">
                    <h4 class="report-name">${report.name}</h4>
                    <div class="report-meta">
                        <span class="report-type">${getReportTypeLabel(report.type)}</span>
                        <span class="report-status status-${report.status}">${getStatusLabel(report.status)}</span>
                    </div>
                </div>
                <p class="report-description">${report.description}</p>
                <div class="report-details">
                    <span class="report-detail">
                        <i class="fas fa-calendar"></i>
                        ${report.lastGenerated}
                    </span>
                    <span class="report-detail">
                        <i class="fas fa-download"></i>
                        ${report.downloads} 次下载
                    </span>
                    <span class="report-detail">
                        <i class="fas fa-file"></i>
                        ${report.size}
                    </span>
                </div>
            </div>
            <div class="report-actions">
                ${report.status === 'completed' ? `
                    <button class="btn-icon" onclick="previewReport(${report.id})" title="预览">
                        <i class="fas fa-eye"></i>
                    </button>
                    <button class="btn-icon" onclick="downloadReport(${report.id})" title="下载">
                        <i class="fas fa-download"></i>
                    </button>
                ` : ''}
                <button class="btn-icon" onclick="regenerateReport(${report.id})" title="重新生成">
                    <i class="fas fa-redo"></i>
                </button>
                <button class="btn-icon danger" onclick="deleteReport(${report.id})" title="删除">
                    <i class="fas fa-trash"></i>
                </button>
            </div>
        </div>
    `).join('');
}

// 获取报表类型图标
function getReportTypeIcon(type) {
    return reportTemplates[type]?.icon || 'fas fa-file-alt';
}

// 获取报表类型颜色
function getReportTypeColor(type) {
    return reportTemplates[type]?.color || 'secondary';
}

// 获取报表类型标签
function getReportTypeLabel(type) {
    return reportTemplates[type]?.name || type;
}

// 获取状态标签
function getStatusLabel(status) {
    const labels = {
        'completed': '已完成',
        'generating': '生成中',
        'failed': '失败',
        'scheduled': '已计划'
    };
    return labels[status] || status;
}

// 更新报表统计
function updateReportsStats() {
    const totalReports = reports.length;
    const monthlyDownloads = reports.reduce((sum, report) => sum + report.downloads, 0);
    const scheduledReports = reports.filter(r => r.status === 'scheduled').length;
    const reportUsers = new Set(reports.map(r => r.createdBy)).size;

    document.getElementById('totalReports').textContent = totalReports;
    document.getElementById('monthlyDownloads').textContent = monthlyDownloads;
    document.getElementById('scheduledReports').textContent = scheduledReports;
    document.getElementById('reportUsers').textContent = reportUsers;
}

// 筛选报表
function filterReports() {
    const typeFilter = document.getElementById('reportTypeFilter').value;
    
    filteredReports = reports.filter(report => {
        return !typeFilter || report.type === typeFilter;
    });

    renderReportsList();
}

// 生成快速报表
function generateQuickReport(type) {
    showNotification(`正在生成${getReportTypeLabel(type)}...`, 'info');
    
    // 模拟报表生成过程
    setTimeout(() => {
        const newReport = {
            id: Math.max(...reports.map(r => r.id), 0) + 1,
            name: `${getReportTypeLabel(type)}_${new Date().toISOString().split('T')[0]}`,
            type: type,
            description: `自动生成的${getReportTypeLabel(type)}`,
            createdAt: new Date().toISOString().split('T')[0],
            createdBy: '当前用户',
            lastGenerated: new Date().toLocaleString('zh-CN'),
            downloads: 0,
            format: 'pdf',
            status: 'completed',
            size: `${(Math.random() * 3 + 1).toFixed(1)}MB`
        };

        reports.unshift(newReport);
        filteredReports = [...reports];
        
        renderReportsList();
        updateReportsStats();
        showNotification(`${getReportTypeLabel(type)}生成完成`, 'success');
    }, 2000);
}

// 构建自定义报表
function buildCustomReport() {
    const dataSource = document.getElementById('reportDataSource').value;
    const chartType = document.getElementById('reportChartType').value;
    const dateRange = document.getElementById('reportDateRange').value;

    if (!dataSource || !chartType || !dateRange) {
        showNotification('请填写完整的报表配置', 'error');
        return;
    }

    showNotification('正在生成自定义报表...', 'info');
    
    // 显示报表预览区域
    const previewCard = document.getElementById('reportPreviewCard');
    previewCard.style.display = 'block';
    
    // 模拟报表生成
    setTimeout(() => {
        generateReportPreview(dataSource, chartType, dateRange);
        showNotification('自定义报表生成完成', 'success');
    }, 3000);
}

// 生成报表预览
function generateReportPreview(dataSource, chartType, dateRange) {
    const preview = document.getElementById('reportPreview');
    
    // 根据图表类型生成不同的预览内容
    switch (chartType) {
        case 'table':
            preview.innerHTML = generateTablePreview(dataSource);
            break;
        case 'line':
        case 'bar':
        case 'pie':
            preview.innerHTML = generateChartPreview(chartType, dataSource);
            break;
        default:
            preview.innerHTML = '<p>不支持的图表类型</p>';
    }
}

// 生成表格预览
function generateTablePreview(dataSource) {
    const sampleData = getSampleData(dataSource);
    
    return `
        <div class="report-table">
            <table class="modern-table">
                <thead>
                    <tr>
                        ${sampleData.headers.map(header => `<th>${header}</th>`).join('')}
                    </tr>
                </thead>
                <tbody>
                    ${sampleData.rows.map(row => `
                        <tr>
                            ${row.map(cell => `<td>${cell}</td>`).join('')}
                        </tr>
                    `).join('')}
                </tbody>
            </table>
        </div>
    `;
}

// 生成图表预览
function generateChartPreview(chartType, dataSource) {
    return `
        <div class="report-chart">
            <div class="chart-placeholder">
                <i class="fas fa-chart-${chartType === 'line' ? 'line' : chartType === 'pie' ? 'pie-chart' : 'bar'}"></i>
                <h3>${chartType.toUpperCase()} 图表</h3>
                <p>数据源: ${dataSource}</p>
                <p>这里将显示实际的图表内容</p>
            </div>
        </div>
    `;
}

// 获取示例数据
function getSampleData(dataSource) {
    const dataSources = {
        orders: {
            headers: ['订单号', '客户', '金额', '状态', '日期'],
            rows: [
                ['#001', '张三', '¥1,299', '已完成', '2024-01-15'],
                ['#002', '李四', '¥899', '处理中', '2024-01-14'],
                ['#003', '王五', '¥2,599', '已完成', '2024-01-13']
            ]
        },
        users: {
            headers: ['用户名', '邮箱', '注册日期', '最后登录', '状态'],
            rows: [
                ['张三', '<EMAIL>', '2024-01-10', '2024-01-15', '活跃'],
                ['李四', '<EMAIL>', '2024-01-08', '2024-01-14', '活跃'],
                ['王五', '<EMAIL>', '2024-01-05', '2024-01-12', '不活跃']
            ]
        },
        products: {
            headers: ['商品名称', '分类', '价格', '库存', '状态'],
            rows: [
                ['iPhone 15', '电子产品', '¥7,999', '25', '正常'],
                ['运动鞋', '服装', '¥899', '45', '正常'],
                ['编程书籍', '图书', '¥129', '3', '库存不足']
            ]
        },
        inventory: {
            headers: ['商品', '当前库存', '最低库存', '库存价值', '状态'],
            rows: [
                ['iPhone 15', '25', '10', '¥199,975', '正常'],
                ['运动鞋', '45', '20', '¥40,455', '正常'],
                ['编程书籍', '3', '10', '¥387', '库存不足']
            ]
        }
    };
    
    return dataSources[dataSource] || dataSources.orders;
}

// 预览报表
function previewReport(reportId) {
    const report = reports.find(r => r.id === reportId);
    if (!report) return;

    showNotification(`正在预览 ${report.name}...`, 'info');

    // 显示预览区域
    const previewCard = document.getElementById('reportPreviewCard');
    previewCard.style.display = 'block';

    // 模拟预览加载
    setTimeout(() => {
        generateReportPreview('orders', 'table', '30d');
        showNotification('报表预览加载完成', 'success');
    }, 1000);
}

// 下载报表
function downloadReport(reportId) {
    const report = reports.find(r => r.id === reportId);
    if (!report) return;

    showNotification(`正在下载 ${report.name}...`, 'info');

    // 更新下载次数
    report.downloads++;

    // 模拟下载过程
    setTimeout(() => {
        // 创建模拟下载链接
        const link = document.createElement('a');
        link.href = '#';
        link.download = `${report.name}.${report.format}`;
        link.click();

        renderReportsList();
        updateReportsStats();
        showNotification('报表下载完成', 'success');
    }, 2000);
}

// 重新生成报表
function regenerateReport(reportId) {
    const report = reports.find(r => r.id === reportId);
    if (!report) return;

    if (confirm(`确定要重新生成报表"${report.name}"吗？`)) {
        report.status = 'generating';
        report.lastGenerated = new Date().toLocaleString('zh-CN');

        renderReportsList();
        showNotification(`正在重新生成 ${report.name}...`, 'info');

        // 模拟生成过程
        setTimeout(() => {
            report.status = 'completed';
            report.size = `${(Math.random() * 3 + 1).toFixed(1)}MB`;
            renderReportsList();
            showNotification('报表重新生成完成', 'success');
        }, 3000);
    }
}

// 删除报表
function deleteReport(reportId) {
    const report = reports.find(r => r.id === reportId);
    if (!report) return;

    if (confirm(`确定要删除报表"${report.name}"吗？此操作不可撤销！`)) {
        const index = reports.findIndex(r => r.id === reportId);
        if (index !== -1) {
            reports.splice(index, 1);
            filteredReports = [...reports];
            renderReportsList();
            updateReportsStats();
            showNotification('报表删除成功', 'success');
        }
    }
}

// 导出报表
function exportReport(format) {
    showNotification(`正在导出${format.toUpperCase()}格式...`, 'info');

    setTimeout(() => {
        const link = document.createElement('a');
        link.href = '#';
        link.download = `自定义报表_${new Date().toISOString().split('T')[0]}.${format}`;
        link.click();

        showNotification(`${format.toUpperCase()}导出完成`, 'success');
    }, 2000);
}

// 保存报表
function saveReport() {
    const reportName = prompt('请输入报表名称:', '自定义报表');
    if (!reportName) return;

    const newReport = {
        id: Math.max(...reports.map(r => r.id), 0) + 1,
        name: reportName,
        type: 'custom',
        description: '用户自定义生成的报表',
        createdAt: new Date().toISOString().split('T')[0],
        createdBy: '当前用户',
        lastGenerated: new Date().toLocaleString('zh-CN'),
        downloads: 0,
        format: 'pdf',
        status: 'completed',
        size: `${(Math.random() * 3 + 1).toFixed(1)}MB`
    };

    reports.unshift(newReport);
    filteredReports = [...reports];

    renderReportsList();
    updateReportsStats();
    showNotification('报表保存成功', 'success');
}

// 创建自定义报表
function createCustomReport() {
    showNotification('打开自定义报表创建器...', 'info');
    // 这里可以打开一个更复杂的报表创建模态框
}

// 定时报表
function scheduleReport() {
    showNotification('定时报表功能开发中...', 'info');
    // 这里可以实现定时报表的设置功能
}

// 批量操作报表
function batchReportOperation(operation) {
    const selectedReports = Array.from(document.querySelectorAll('.report-checkbox:checked'))
        .map(cb => parseInt(cb.value));

    if (selectedReports.length === 0) {
        showNotification('请先选择要操作的报表', 'warning');
        return;
    }

    switch (operation) {
        case 'delete':
            if (confirm(`确定要删除选中的 ${selectedReports.length} 个报表吗？`)) {
                selectedReports.forEach(reportId => {
                    const index = reports.findIndex(r => r.id === reportId);
                    if (index !== -1) {
                        reports.splice(index, 1);
                    }
                });
                filteredReports = [...reports];
                renderReportsList();
                updateReportsStats();
                showNotification(`成功删除 ${selectedReports.length} 个报表`, 'success');
            }
            break;
        case 'download':
            showNotification(`正在批量下载 ${selectedReports.length} 个报表...`, 'info');
            // 模拟批量下载
            setTimeout(() => {
                showNotification('批量下载完成', 'success');
            }, 3000);
            break;
        default:
            showNotification('未知操作', 'error');
    }
}

// 搜索报表
function searchReports(query) {
    if (!query.trim()) {
        filteredReports = [...reports];
    } else {
        filteredReports = reports.filter(report =>
            report.name.toLowerCase().includes(query.toLowerCase()) ||
            report.description.toLowerCase().includes(query.toLowerCase()) ||
            report.createdBy.toLowerCase().includes(query.toLowerCase())
        );
    }

    renderReportsList();
}

// 全局函数导出
window.initializeReportsManagement = initializeReportsManagement;
window.filterReports = filterReports;
window.generateQuickReport = generateQuickReport;
window.buildCustomReport = buildCustomReport;
window.previewReport = previewReport;
window.downloadReport = downloadReport;
window.regenerateReport = regenerateReport;
window.deleteReport = deleteReport;
window.exportReport = exportReport;
window.saveReport = saveReport;
window.createCustomReport = createCustomReport;
window.scheduleReport = scheduleReport;
window.batchReportOperation = batchReportOperation;
window.searchReports = searchReports;
