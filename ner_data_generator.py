#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
NER测试数据生成器
生成10,000条命名实体识别训练数据
"""

import json
import random
from datetime import datetime, timedelta
from typing import List, Dict, Tuple
import re

class NERDataGenerator:
    def __init__(self):
        # 实体词库
        self.person_names = [
            "张三", "李四", "王五", "赵六", "陈七", "刘八", "杨九", "黄十",
            "周明", "吴亮", "郑强", "王芳", "李娜", "张伟", "刘洋", "陈静",
            "马云", "马化腾", "李彦宏", "雷军", "任正非", "董明珠", "柳传志",
            "王健林", "许家印", "李嘉诚", "马明哲", "张瑞敏", "曹德旺"
        ]
        
        self.locations = [
            "北京", "上海", "广州", "深圳", "杭州", "南京", "武汉", "成都",
            "西安", "重庆", "天津", "青岛", "大连", "厦门", "苏州", "无锡",
            "中国", "美国", "日本", "韩国", "德国", "法国", "英国", "意大利",
            "朝阳区", "海淀区", "西城区", "东城区", "丰台区", "石景山区",
            "浦东新区", "黄浦区", "徐汇区", "长宁区", "静安区", "普陀区"
        ]
        
        self.organizations = [
            "阿里巴巴", "腾讯", "百度", "字节跳动", "美团", "滴滴", "京东", "网易",
            "华为", "小米", "OPPO", "vivo", "联想", "海尔", "格力", "美的",
            "中国银行", "工商银行", "建设银行", "农业银行", "招商银行",
            "清华大学", "北京大学", "复旦大学", "上海交通大学", "浙江大学",
            "国务院", "教育部", "工信部", "商务部", "发改委", "央行"
        ]
        
        # 句子模板
        self.sentence_templates = [
            "{person}在{location}的{organization}工作了{time}。",
            "{person}于{time}从{location}来到{organization}。",
            "{organization}位于{location}，由{person}创立于{time}。",
            "{time}，{person}在{location}成立了{organization}。",
            "{person}计划在{time}访问{location}的{organization}。",
            "{organization}的{person}表示，{time}将在{location}举办活动。",
            "据{organization}消息，{person}将于{time}在{location}发表演讲。",
            "{location}的{organization}宣布，{person}获得了{number}万元奖金。",
            "{person}从{organization}离职后，于{time}在{location}创业。",
            "{time}，{location}的{organization}任命{person}为新CEO。"
        ]
        
    def generate_time_entity(self) -> str:
        """生成时间实体"""
        time_formats = [
            # 具体日期
            lambda: f"{random.randint(2020, 2024)}年{random.randint(1, 12)}月{random.randint(1, 28)}日",
            lambda: f"{random.randint(2020, 2024)}年{random.randint(1, 12)}月",
            lambda: f"{random.randint(2020, 2024)}年",
            # 相对时间
            lambda: random.choice(["昨天", "今天", "明天", "上周", "下周", "上个月", "下个月"]),
            lambda: random.choice(["去年", "今年", "明年", "前年", "后年"]),
            # 时间段
            lambda: random.choice(["上午", "下午", "晚上", "凌晨", "中午"]),
            lambda: f"{random.randint(1, 12)}点{random.randint(0, 59)}分",
        ]
        return random.choice(time_formats)()
    
    def generate_number_entity(self) -> str:
        """生成数字实体"""
        number_formats = [
            lambda: str(random.randint(1, 1000)),
            lambda: f"{random.randint(1, 999)}.{random.randint(1, 99)}",
            lambda: f"{random.randint(1, 999)}万",
            lambda: f"{random.randint(1, 999)}亿",
            lambda: f"{random.randint(1, 100)}%",
        ]
        return random.choice(number_formats)()
    
    def create_bio_labels(self, text: str, entities: Dict[str, List[Tuple[int, int, str]]]) -> List[str]:
        """创建BIO标签"""
        labels = ['O'] * len(text)
        
        for entity_type, entity_list in entities.items():
            for start, end, entity_text in entity_list:
                if start < len(labels) and end <= len(labels):
                    labels[start] = f'B-{entity_type}'
                    for i in range(start + 1, end):
                        if i < len(labels):
                            labels[i] = f'I-{entity_type}'
        
        return labels
    
    def generate_sentence(self) -> Tuple[str, List[str]]:
        """生成一个句子和对应的BIO标签"""
        template = random.choice(self.sentence_templates)
        entities = {}
        
        # 随机选择实体
        person = random.choice(self.person_names) if "{person}" in template else None
        location = random.choice(self.locations) if "{location}" in template else None
        organization = random.choice(self.organizations) if "{organization}" in template else None
        time_entity = self.generate_time_entity() if "{time}" in template else None
        number = self.generate_number_entity() if "{number}" in template else None
        
        # 构建句子
        sentence = template
        if person:
            sentence = sentence.replace("{person}", person)
        if location:
            sentence = sentence.replace("{location}", location)
        if organization:
            sentence = sentence.replace("{organization}", organization)
        if time_entity:
            sentence = sentence.replace("{time}", time_entity)
        if number:
            sentence = sentence.replace("{number}", number)
        
        # 找到实体位置并创建标签
        entity_positions = {}
        
        if person:
            start = sentence.find(person)
            if start != -1:
                if 'PERSON' not in entity_positions:
                    entity_positions['PERSON'] = []
                entity_positions['PERSON'].append((start, start + len(person), person))
        
        if location:
            start = sentence.find(location)
            if start != -1:
                if 'LOCATION' not in entity_positions:
                    entity_positions['LOCATION'] = []
                entity_positions['LOCATION'].append((start, start + len(location), location))
        
        if organization:
            start = sentence.find(organization)
            if start != -1:
                if 'ORGANIZATION' not in entity_positions:
                    entity_positions['ORGANIZATION'] = []
                entity_positions['ORGANIZATION'].append((start, start + len(organization), organization))
        
        if time_entity:
            start = sentence.find(time_entity)
            if start != -1:
                if 'TIME' not in entity_positions:
                    entity_positions['TIME'] = []
                entity_positions['TIME'].append((start, start + len(time_entity), time_entity))
        
        if number:
            start = sentence.find(number)
            if start != -1:
                if 'NUMBER' not in entity_positions:
                    entity_positions['NUMBER'] = []
                entity_positions['NUMBER'].append((start, start + len(number), number))
        
        # 创建字符级别的BIO标签
        char_labels = ['O'] * len(sentence)
        for entity_type, positions in entity_positions.items():
            for start, end, _ in positions:
                char_labels[start] = f'B-{entity_type}'
                for i in range(start + 1, end):
                    char_labels[i] = f'I-{entity_type}'
        
        return sentence, char_labels
    
    def generate_dataset(self, num_samples: int = 10000) -> List[Dict]:
        """生成完整的数据集"""
        dataset = []
        
        print(f"开始生成 {num_samples} 条NER训练数据...")
        
        for i in range(num_samples):
            if (i + 1) % 1000 == 0:
                print(f"已生成 {i + 1} 条数据...")
            
            sentence, labels = self.generate_sentence()
            
            # 将字符和标签组合
            tokens = list(sentence)
            
            dataset.append({
                "id": i + 1,
                "text": sentence,
                "tokens": tokens,
                "labels": labels
            })
        
        return dataset
    
    def save_dataset(self, dataset: List[Dict], filename: str = "ner_training_data.json"):
        """保存数据集到文件"""
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(dataset, f, ensure_ascii=False, indent=2)
        
        print(f"数据集已保存到 {filename}")
    
    def generate_statistics(self, dataset: List[Dict]) -> Dict:
        """生成数据统计信息"""
        stats = {
            "total_samples": len(dataset),
            "entity_counts": {
                "PERSON": 0,
                "LOCATION": 0,
                "ORGANIZATION": 0,
                "TIME": 0,
                "NUMBER": 0
            },
            "sentence_lengths": [],
            "entities_per_sentence": []
        }
        
        for sample in dataset:
            labels = sample["labels"]
            sentence_length = len(sample["text"])
            stats["sentence_lengths"].append(sentence_length)
            
            # 统计实体
            entity_count = 0
            for label in labels:
                if label.startswith('B-'):
                    entity_type = label[2:]
                    if entity_type in stats["entity_counts"]:
                        stats["entity_counts"][entity_type] += 1
                        entity_count += 1
            
            stats["entities_per_sentence"].append(entity_count)
        
        # 计算平均值
        stats["avg_sentence_length"] = sum(stats["sentence_lengths"]) / len(stats["sentence_lengths"])
        stats["avg_entities_per_sentence"] = sum(stats["entities_per_sentence"]) / len(stats["entities_per_sentence"])
        
        return stats
    
    def print_statistics(self, stats: Dict):
        """打印统计信息"""
        print("\n=== 数据集统计信息 ===")
        print(f"总样本数: {stats['total_samples']}")
        print(f"平均句子长度: {stats['avg_sentence_length']:.2f} 字符")
        print(f"平均每句实体数: {stats['avg_entities_per_sentence']:.2f}")
        print("\n实体类型分布:")
        for entity_type, count in stats["entity_counts"].items():
            print(f"  {entity_type}: {count}")
        print()

def main():
    """主函数"""
    generator = NERDataGenerator()
    
    # 生成数据集
    dataset = generator.generate_dataset(10000)
    
    # 保存数据集
    generator.save_dataset(dataset)
    
    # 生成并打印统计信息
    stats = generator.generate_statistics(dataset)
    generator.print_statistics(stats)
    
    # 保存统计信息
    with open("ner_data_statistics.json", 'w', encoding='utf-8') as f:
        json.dump(stats, f, ensure_ascii=False, indent=2)
    
    print("统计信息已保存到 ner_data_statistics.json")
    
    # 显示几个样本
    print("\n=== 样本展示 ===")
    for i in range(3):
        sample = dataset[i]
        print(f"\n样本 {i+1}:")
        print(f"文本: {sample['text']}")
        print("标签:", end=" ")
        for j, (char, label) in enumerate(zip(sample['tokens'], sample['labels'])):
            if label != 'O':
                print(f"{char}({label})", end=" ")
        print()

if __name__ == "__main__":
    main()