// 数据可视化增强功能

class ChartManager {
    constructor() {
        this.charts = {};
        this.defaultOptions = {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom',
                    labels: {
                        usePointStyle: true,
                        padding: 20,
                        font: {
                            family: 'Inter',
                            size: 12
                        }
                    }
                },
                tooltip: {
                    backgroundColor: 'rgba(0, 0, 0, 0.8)',
                    titleColor: '#fff',
                    bodyColor: '#fff',
                    borderColor: '#6366f1',
                    borderWidth: 1,
                    cornerRadius: 8,
                    displayColors: false,
                    titleFont: {
                        family: 'Inter',
                        size: 14,
                        weight: '600'
                    },
                    bodyFont: {
                        family: 'Inter',
                        size: 13
                    }
                }
            },
            animation: {
                duration: 1000,
                easing: 'easeInOutQuart'
            }
        };
    }

    // 创建折线图
    createLineChart(canvasId, data, options = {}) {
        const canvas = document.getElementById(canvasId);
        if (!canvas) return null;

        const ctx = canvas.getContext('2d');
        
        // 销毁现有图表
        if (this.charts[canvasId]) {
            this.charts[canvasId].destroy();
        }

        const config = {
            type: 'line',
            data: {
                labels: data.labels,
                datasets: data.datasets.map(dataset => ({
                    ...dataset,
                    borderWidth: 3,
                    pointRadius: 4,
                    pointHoverRadius: 6,
                    pointBackgroundColor: '#fff',
                    pointBorderWidth: 2,
                    tension: 0.4,
                    fill: dataset.fill !== false
                }))
            },
            options: {
                ...this.defaultOptions,
                ...options,
                scales: {
                    x: {
                        grid: {
                            color: 'rgba(0, 0, 0, 0.05)',
                            drawBorder: false
                        },
                        ticks: {
                            font: {
                                family: 'Inter',
                                size: 11
                            },
                            color: '#6b7280'
                        }
                    },
                    y: {
                        grid: {
                            color: 'rgba(0, 0, 0, 0.05)',
                            drawBorder: false
                        },
                        ticks: {
                            font: {
                                family: 'Inter',
                                size: 11
                            },
                            color: '#6b7280',
                            callback: function(value) {
                                if (options.formatY === 'currency') {
                                    return '¥' + value.toLocaleString();
                                }
                                return value.toLocaleString();
                            }
                        }
                    }
                }
            }
        };

        this.charts[canvasId] = new Chart(ctx, config);
        return this.charts[canvasId];
    }

    // 创建饼图
    createPieChart(canvasId, data, options = {}) {
        const canvas = document.getElementById(canvasId);
        if (!canvas) return null;

        const ctx = canvas.getContext('2d');
        
        if (this.charts[canvasId]) {
            this.charts[canvasId].destroy();
        }

        const config = {
            type: 'doughnut',
            data: {
                labels: data.labels,
                datasets: [{
                    data: data.data,
                    backgroundColor: data.colors || [
                        '#6366f1', '#10b981', '#f59e0b', '#ef4444', '#8b5cf6', '#06b6d4'
                    ],
                    borderWidth: 0,
                    hoverBorderWidth: 2,
                    hoverBorderColor: '#fff'
                }]
            },
            options: {
                ...this.defaultOptions,
                ...options,
                cutout: '60%',
                plugins: {
                    ...this.defaultOptions.plugins,
                    legend: {
                        ...this.defaultOptions.plugins.legend,
                        position: 'right'
                    },
                    tooltip: {
                        ...this.defaultOptions.plugins.tooltip,
                        callbacks: {
                            label: function(context) {
                                const total = context.dataset.data.reduce((a, b) => a + b, 0);
                                const percentage = ((context.parsed / total) * 100).toFixed(1);
                                return `${context.label}: ${context.parsed} (${percentage}%)`;
                            }
                        }
                    }
                }
            }
        };

        this.charts[canvasId] = new Chart(ctx, config);
        return this.charts[canvasId];
    }

    // 创建柱状图
    createBarChart(canvasId, data, options = {}) {
        const canvas = document.getElementById(canvasId);
        if (!canvas) return null;

        const ctx = canvas.getContext('2d');
        
        if (this.charts[canvasId]) {
            this.charts[canvasId].destroy();
        }

        const config = {
            type: 'bar',
            data: {
                labels: data.labels,
                datasets: data.datasets.map(dataset => ({
                    ...dataset,
                    borderRadius: 6,
                    borderSkipped: false,
                    borderWidth: 0
                }))
            },
            options: {
                ...this.defaultOptions,
                ...options,
                scales: {
                    x: {
                        grid: {
                            display: false
                        },
                        ticks: {
                            font: {
                                family: 'Inter',
                                size: 11
                            },
                            color: '#6b7280'
                        }
                    },
                    y: {
                        grid: {
                            color: 'rgba(0, 0, 0, 0.05)',
                            drawBorder: false
                        },
                        ticks: {
                            font: {
                                family: 'Inter',
                                size: 11
                            },
                            color: '#6b7280',
                            callback: function(value) {
                                if (options.formatY === 'currency') {
                                    return '¥' + value.toLocaleString();
                                }
                                if (options.formatY === 'percentage') {
                                    return value + '%';
                                }
                                return value.toLocaleString();
                            }
                        }
                    }
                }
            }
        };

        this.charts[canvasId] = new Chart(ctx, config);
        return this.charts[canvasId];
    }

    // 创建面积图
    createAreaChart(canvasId, data, options = {}) {
        const canvas = document.getElementById(canvasId);
        if (!canvas) return null;

        const ctx = canvas.getContext('2d');
        
        if (this.charts[canvasId]) {
            this.charts[canvasId].destroy();
        }

        const config = {
            type: 'line',
            data: {
                labels: data.labels,
                datasets: data.datasets.map(dataset => ({
                    ...dataset,
                    fill: true,
                    backgroundColor: dataset.backgroundColor || 'rgba(99, 102, 241, 0.1)',
                    borderColor: dataset.borderColor || '#6366f1',
                    borderWidth: 2,
                    pointRadius: 0,
                    pointHoverRadius: 4,
                    tension: 0.4
                }))
            },
            options: {
                ...this.defaultOptions,
                ...options,
                interaction: {
                    intersect: false,
                    mode: 'index'
                },
                scales: {
                    x: {
                        grid: {
                            display: false
                        },
                        ticks: {
                            font: {
                                family: 'Inter',
                                size: 11
                            },
                            color: '#6b7280'
                        }
                    },
                    y: {
                        grid: {
                            color: 'rgba(0, 0, 0, 0.05)',
                            drawBorder: false
                        },
                        ticks: {
                            font: {
                                family: 'Inter',
                                size: 11
                            },
                            color: '#6b7280'
                        }
                    }
                }
            }
        };

        this.charts[canvasId] = new Chart(ctx, config);
        return this.charts[canvasId];
    }

    // 更新图表数据
    updateChart(canvasId, newData) {
        const chart = this.charts[canvasId];
        if (!chart) return;

        chart.data = newData;
        chart.update('active');
    }

    // 销毁图表
    destroyChart(canvasId) {
        if (this.charts[canvasId]) {
            this.charts[canvasId].destroy();
            delete this.charts[canvasId];
        }
    }

    // 销毁所有图表
    destroyAllCharts() {
        Object.keys(this.charts).forEach(canvasId => {
            this.destroyChart(canvasId);
        });
    }

    // 获取图表实例
    getChart(canvasId) {
        return this.charts[canvasId];
    }
}

// 实时数据管理
class RealTimeDataManager {
    constructor() {
        this.intervals = {};
        this.subscribers = {};
    }

    // 订阅实时数据
    subscribe(dataType, callback, interval = 5000) {
        if (!this.subscribers[dataType]) {
            this.subscribers[dataType] = [];
        }
        
        this.subscribers[dataType].push(callback);

        // 如果是第一个订阅者，开始数据更新
        if (this.subscribers[dataType].length === 1) {
            this.startDataUpdate(dataType, interval);
        }
    }

    // 取消订阅
    unsubscribe(dataType, callback) {
        if (!this.subscribers[dataType]) return;

        const index = this.subscribers[dataType].indexOf(callback);
        if (index > -1) {
            this.subscribers[dataType].splice(index, 1);
        }

        // 如果没有订阅者了，停止数据更新
        if (this.subscribers[dataType].length === 0) {
            this.stopDataUpdate(dataType);
        }
    }

    // 开始数据更新
    startDataUpdate(dataType, interval) {
        this.intervals[dataType] = setInterval(() => {
            const newData = this.generateMockData(dataType);
            this.notifySubscribers(dataType, newData);
        }, interval);
    }

    // 停止数据更新
    stopDataUpdate(dataType) {
        if (this.intervals[dataType]) {
            clearInterval(this.intervals[dataType]);
            delete this.intervals[dataType];
        }
    }

    // 通知订阅者
    notifySubscribers(dataType, data) {
        if (this.subscribers[dataType]) {
            this.subscribers[dataType].forEach(callback => {
                callback(data);
            });
        }
    }

    // 生成模拟数据
    generateMockData(dataType) {
        switch (dataType) {
            case 'revenue':
                return {
                    value: Math.floor(Math.random() * 10000) + 50000,
                    timestamp: new Date()
                };
            case 'orders':
                return {
                    value: Math.floor(Math.random() * 50) + 100,
                    timestamp: new Date()
                };
            case 'users':
                return {
                    value: Math.floor(Math.random() * 20) + 200,
                    timestamp: new Date()
                };
            default:
                return {
                    value: Math.random() * 100,
                    timestamp: new Date()
                };
        }
    }
}

// 数据导出管理
class DataExportManager {
    // 导出图表为图片
    exportChartAsImage(canvasId, filename = 'chart.png') {
        const chart = chartManager.getChart(canvasId);
        if (!chart) return;

        const url = chart.toBase64Image();
        const link = document.createElement('a');
        link.download = filename;
        link.href = url;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    }

    // 导出数据为CSV
    exportDataAsCSV(data, filename = 'data.csv') {
        let csvContent = "data:text/csv;charset=utf-8,";
        
        // 添加表头
        if (data.length > 0) {
            csvContent += Object.keys(data[0]).join(",") + "\n";
        }
        
        // 添加数据行
        data.forEach(row => {
            csvContent += Object.values(row).join(",") + "\n";
        });

        const encodedUri = encodeURI(csvContent);
        const link = document.createElement("a");
        link.setAttribute("href", encodedUri);
        link.setAttribute("download", filename);
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    }

    // 导出数据为JSON
    exportDataAsJSON(data, filename = 'data.json') {
        const dataStr = JSON.stringify(data, null, 2);
        const dataBlob = new Blob([dataStr], { type: 'application/json' });
        
        const link = document.createElement('a');
        link.href = URL.createObjectURL(dataBlob);
        link.download = filename;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    }
}

// 初始化管理器
const chartManager = new ChartManager();
const realTimeDataManager = new RealTimeDataManager();
const dataExportManager = new DataExportManager();

// 全局函数
window.createLineChart = (id, data, options) => chartManager.createLineChart(id, data, options);
window.createPieChart = (id, data, options) => chartManager.createPieChart(id, data, options);
window.createBarChart = (id, data, options) => chartManager.createBarChart(id, data, options);
window.createAreaChart = (id, data, options) => chartManager.createAreaChart(id, data, options);
window.updateChart = (id, data) => chartManager.updateChart(id, data);
window.destroyChart = (id) => chartManager.destroyChart(id);

window.subscribeToRealTimeData = (type, callback, interval) => realTimeDataManager.subscribe(type, callback, interval);
window.unsubscribeFromRealTimeData = (type, callback) => realTimeDataManager.unsubscribe(type, callback);

window.exportChartAsImage = (id, filename) => dataExportManager.exportChartAsImage(id, filename);
window.exportDataAsCSV = (data, filename) => dataExportManager.exportDataAsCSV(data, filename);
window.exportDataAsJSON = (data, filename) => dataExportManager.exportDataAsJSON(data, filename);

// 页面卸载时清理
window.addEventListener('beforeunload', () => {
    chartManager.destroyAllCharts();
    Object.keys(realTimeDataManager.intervals).forEach(dataType => {
        realTimeDataManager.stopDataUpdate(dataType);
    });
});
