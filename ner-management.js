// NER数据管理JavaScript模块

// API基础URL
const NER_API_BASE = 'http://localhost:5000/api';

// 全局变量
let nerChart = null;
let currentNERData = [];
let currentNERStats = {};

// NER数据生成函数
async function generateNERData() {
    try {
        // 显示加载状态
        showNERLoading(true);
        
        // 获取配置参数
        const sampleCount = parseInt(document.getElementById('nerSampleCount').value);
        const entityTypes = {
            person: document.getElementById('enablePerson').checked,
            location: document.getElementById('enableLocation').checked,
            organization: document.getElementById('enableOrganization').checked,
            time: document.getElementById('enableTime').checked,
            number: document.getElementById('enableNumber').checked
        };
        
        // 验证参数
        if (sampleCount < 100 || sampleCount > 50000) {
            showNotification('样本数量必须在100-50000之间', 'error');
            return;
        }
        
        // 发送生成请求
        const response = await fetch(`${NER_API_BASE}/ner/generate`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                sample_count: sampleCount,
                entity_types: entityTypes
            })
        });
        
        const result = await response.json();
        
        if (result.success) {
            currentNERStats = result.stats;
            
            // 更新统计显示
            updateNERStats(result.stats);
            
            // 更新数据预览
            updateNERPreview(result.sample_preview);
            
            // 更新图表
            updateNERChart();
            
            showNotification(result.message, 'success');
        } else {
            showNotification(result.error || '生成数据失败', 'error');
        }
        
    } catch (error) {
        console.error('生成NER数据时发生错误:', error);
        showNotification('网络错误，请检查API服务是否启动', 'error');
    } finally {
        showNERLoading(false);
    }
}

// 下载NER数据
async function downloadNERData() {
    try {
        const format = document.getElementById('nerOutputFormat').value;
        
        // 创建下载链接
        const downloadUrl = `${NER_API_BASE}/ner/download?format=${format}`;
        
        // 创建临时链接并触发下载
        const link = document.createElement('a');
        link.href = downloadUrl;
        link.download = `ner_data_${new Date().toISOString().slice(0, 10)}.${format === 'json' ? 'json' : format === 'conll' ? 'conll' : 'txt'}`;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        
        showNotification('数据下载已开始', 'success');
        
    } catch (error) {
        console.error('下载数据时发生错误:', error);
        showNotification('下载失败，请重试', 'error');
    }
}

// 清空NER数据
async function clearNERData() {
    if (!confirm('确定要清空所有NER数据吗？此操作不可恢复。')) {
        return;
    }
    
    try {
        const response = await fetch(`${NER_API_BASE}/ner/clear`, {
            method: 'DELETE'
        });
        
        const result = await response.json();
        
        if (result.success) {
            // 重置统计数据
            currentNERStats = {};
            currentNERData = [];
            
            // 更新界面
            updateNERStats({});
            updateNERPreview([]);
            updateNERChart();
            
            showNotification(result.message, 'success');
        } else {
            showNotification(result.error || '清空数据失败', 'error');
        }
        
    } catch (error) {
        console.error('清空数据时发生错误:', error);
        showNotification('网络错误，请重试', 'error');
    }
}

// 刷新NER数据预览
async function refreshNERPreview() {
    try {
        const response = await fetch(`${NER_API_BASE}/ner/preview?page=1&per_page=10`);
        const result = await response.json();
        
        if (result.success) {
            currentNERData = result.data;
            updateNERPreview(result.data);
            showNotification('预览数据已刷新', 'success');
        } else {
            showNotification(result.error || '刷新失败', 'error');
        }
        
    } catch (error) {
        console.error('刷新预览时发生错误:', error);
        showNotification('网络错误，请重试', 'error');
    }
}

// 更新NER统计显示
function updateNERStats(stats) {
    if (!stats || Object.keys(stats).length === 0) {
        // 重置所有统计数字
        document.getElementById('totalNERSamples').textContent = '0';
        document.getElementById('totalPersonEntities').textContent = '0';
        document.getElementById('totalLocationEntities').textContent = '0';
        document.getElementById('totalOrgEntities').textContent = '0';
        return;
    }
    
    // 更新总样本数
    document.getElementById('totalNERSamples').textContent = stats.total_samples || 0;
    
    // 更新实体统计
    const entityCounts = stats.entity_counts || {};
    document.getElementById('totalPersonEntities').textContent = entityCounts.PERSON || 0;
    document.getElementById('totalLocationEntities').textContent = entityCounts.LOCATION || 0;
    document.getElementById('totalOrgEntities').textContent = entityCounts.ORGANIZATION || 0;
    
    // 更新趋势（这里可以根据历史数据计算）
    const samplesTrend = document.getElementById('samplesTrend');
    if (samplesTrend) {
        samplesTrend.textContent = '+100%'; // 新生成的数据
    }
}

// 更新NER数据预览
function updateNERPreview(previewData) {
    const previewContainer = document.getElementById('nerDataPreview');
    
    if (!previewData || previewData.length === 0) {
        previewContainer.innerHTML = `
            <div class="preview-placeholder">
                <i class="fas fa-database"></i>
                <p>暂无数据，请先生成NER训练数据</p>
            </div>
        `;
        return;
    }
    
    let previewHTML = '<div class="ner-samples">';
    
    previewData.forEach((sample, index) => {
        previewHTML += `
            <div class="ner-sample" onclick="showNERSampleDetail(${sample.id - 1})">
                <div class="sample-header">
                    <span class="sample-id">样本 #${sample.id}</span>
                    <span class="sample-length">${sample.text.length} 字符</span>
                </div>
                <div class="sample-text">${highlightEntities(sample.text, sample.tokens, sample.labels)}</div>
                <div class="sample-stats">
                    <span class="entity-count">实体数: ${countEntities(sample.labels)}</span>
                </div>
            </div>
        `;
    });
    
    previewHTML += '</div>';
    previewContainer.innerHTML = previewHTML;
}

// 高亮显示实体
function highlightEntities(text, tokens, labels) {
    let result = '';
    let currentEntity = null;
    
    for (let i = 0; i < tokens.length; i++) {
        const token = tokens[i];
        const label = labels[i];
        
        if (label.startsWith('B-')) {
            if (currentEntity) {
                result += `</span>`;
            }
            const entityType = label.substring(2).toLowerCase();
            result += `<span class="entity entity-${entityType}" title="${label}">`;
            currentEntity = entityType;
        } else if (label === 'O' && currentEntity) {
            result += `</span>`;
            currentEntity = null;
        }
        
        result += token;
    }
    
    if (currentEntity) {
        result += `</span>`;
    }
    
    return result;
}

// 统计实体数量
function countEntities(labels) {
    return labels.filter(label => label.startsWith('B-')).length;
}

// 显示NER样本详情
async function showNERSampleDetail(sampleId) {
    try {
        const response = await fetch(`${NER_API_BASE}/ner/sample?id=${sampleId}`);
        const result = await response.json();
        
        if (result.success) {
            // 这里可以显示一个模态框来展示详细信息
            console.log('样本详情:', result.sample, result.entities);
            showNotification(`样本 #${sampleId + 1} 详情已在控制台显示`, 'info');
        } else {
            showNotification(result.error || '获取样本详情失败', 'error');
        }
        
    } catch (error) {
        console.error('获取样本详情时发生错误:', error);
        showNotification('网络错误，请重试', 'error');
    }
}

// 更新NER图表
function updateNERChart() {
    const ctx = document.getElementById('nerEntityChart');
    if (!ctx) return;
    
    // 销毁现有图表
    if (nerChart) {
        nerChart.destroy();
    }
    
    if (!currentNERStats.entity_counts) {
        return;
    }
    
    const chartType = document.getElementById('nerChartType').value;
    const entityCounts = currentNERStats.entity_counts;
    
    const data = {
        labels: Object.keys(entityCounts),
        datasets: [{
            data: Object.values(entityCounts),
            backgroundColor: [
                '#FF6384',
                '#36A2EB',
                '#FFCE56',
                '#4BC0C0',
                '#9966FF'
            ],
            borderWidth: 2,
            borderColor: '#fff'
        }]
    };
    
    nerChart = new Chart(ctx, {
        type: chartType,
        data: data,
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom'
                },
                title: {
                    display: true,
                    text: '实体类型分布'
                }
            }
        }
    });
}

// 重置NER配置
function resetNERConfig() {
    document.getElementById('nerSampleCount').value = 1000;
    document.getElementById('enablePerson').checked = true;
    document.getElementById('enableLocation').checked = true;
    document.getElementById('enableOrganization').checked = true;
    document.getElementById('enableTime').checked = true;
    document.getElementById('enableNumber').checked = true;
    document.getElementById('nerOutputFormat').value = 'json';
    
    showNotification('配置已重置', 'info');
}

// 导出NER样本
function exportNERSample() {
    if (currentNERData.length === 0) {
        showNotification('暂无数据可导出', 'warning');
        return;
    }
    
    const sampleData = currentNERData.slice(0, 5); // 导出前5个样本
    const dataStr = JSON.stringify(sampleData, null, 2);
    const dataBlob = new Blob([dataStr], {type: 'application/json'});
    
    const link = document.createElement('a');
    link.href = URL.createObjectURL(dataBlob);
    link.download = `ner_sample_${new Date().toISOString().slice(0, 10)}.json`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    
    showNotification('样本数据已导出', 'success');
}

// 显示/隐藏加载状态
function showNERLoading(show) {
    const generateBtn = document.querySelector('button[onclick="generateNERData()"]');
    if (generateBtn) {
        if (show) {
            generateBtn.disabled = true;
            generateBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 生成中...';
        } else {
            generateBtn.disabled = false;
            generateBtn.innerHTML = '<i class="fas fa-magic"></i> 生成数据';
        }
    }
}

// 初始化NER管理页面
function initializeNERManagement() {
    // 检查API服务状态
    checkNERAPIStatus();
    
    // 初始化图表
    updateNERChart();
}

// 检查API服务状态
async function checkNERAPIStatus() {
    try {
        const response = await fetch(`${NER_API_BASE}/health`);
        const result = await response.json();
        
        if (result.status === 'healthy') {
            console.log('NER API服务正常');
        }
    } catch (error) {
        console.warn('NER API服务未启动，请运行 python ner_api.py');
        showNotification('NER API服务未启动，部分功能可能无法使用', 'warning');
    }
}
