// 文件管理系统

// 文件数据模拟
const files = [
    {
        id: 1,
        name: '项目文档.pdf',
        type: 'document',
        size: 2048576, // 2MB
        path: '/',
        createdAt: '2024-01-15 14:30:00',
        modifiedAt: '2024-01-15 16:45:00',
        owner: '张管理员',
        shared: false,
        thumbnail: 'https://images.unsplash.com/photo-1568667256549-094345857637?w=100&h=100&fit=crop',
        downloads: 23
    },
    {
        id: 2,
        name: '产品图片',
        type: 'folder',
        size: 0,
        path: '/',
        createdAt: '2024-01-14 10:20:00',
        modifiedAt: '2024-01-15 12:30:00',
        owner: '李设计师',
        shared: true,
        children: [
            {
                id: 21,
                name: 'product1.jpg',
                type: 'image',
                size: 1024000, // 1MB
                path: '/产品图片/',
                createdAt: '2024-01-14 10:25:00',
                modifiedAt: '2024-01-14 10:25:00',
                owner: '李设计师',
                shared: false,
                thumbnail: 'https://images.unsplash.com/photo-1523275335684-37898b6baf30?w=100&h=100&fit=crop'
            },
            {
                id: 22,
                name: 'product2.jpg',
                type: 'image',
                size: 1536000, // 1.5MB
                path: '/产品图片/',
                createdAt: '2024-01-14 10:30:00',
                modifiedAt: '2024-01-14 10:30:00',
                owner: '李设计师',
                shared: false,
                thumbnail: 'https://images.unsplash.com/photo-1542291026-7eec264c27ff?w=100&h=100&fit=crop'
            }
        ]
    },
    {
        id: 3,
        name: '销售数据.xlsx',
        type: 'document',
        size: 512000, // 512KB
        path: '/',
        createdAt: '2024-01-13 09:15:00',
        modifiedAt: '2024-01-14 11:20:00',
        owner: '王分析师',
        shared: true,
        thumbnail: 'https://images.unsplash.com/photo-1551288049-bebda4e38f71?w=100&h=100&fit=crop',
        downloads: 15
    },
    {
        id: 4,
        name: '宣传视频.mp4',
        type: 'video',
        size: 52428800, // 50MB
        path: '/',
        createdAt: '2024-01-12 15:45:00',
        modifiedAt: '2024-01-12 15:45:00',
        owner: '赵编辑',
        shared: false,
        thumbnail: 'https://images.unsplash.com/photo-1574717024653-61fd2cf4d44d?w=100&h=100&fit=crop',
        downloads: 8
    },
    {
        id: 5,
        name: '会议录音.mp3',
        type: 'audio',
        size: 10485760, // 10MB
        path: '/',
        createdAt: '2024-01-11 14:00:00',
        modifiedAt: '2024-01-11 14:00:00',
        owner: '钱秘书',
        shared: false,
        thumbnail: 'https://images.unsplash.com/photo-1493225457124-a3eb161ffa5f?w=100&h=100&fit=crop',
        downloads: 3
    }
];

// 最近访问的文件
const recentFiles = [];

// 当前路径和视图设置
let currentPath = '/';
let currentView = 'grid';
let filteredFiles = [];
let currentFilePage = 1;
const filesPerPage = 12;

// 上传队列
let uploadQueue = [];
let currentPreviewFile = null;

// 初始化文件管理
function initializeFileManagement() {
    updateFileStats();
    loadCurrentDirectory();
    renderRecentFiles();
    setupDragAndDrop();
}

// 更新文件统计
function updateFileStats() {
    const allFiles = getAllFiles();
    const totalFiles = allFiles.filter(f => f.type !== 'folder').length;
    const totalFolders = allFiles.filter(f => f.type === 'folder').length;
    const totalSize = allFiles.reduce((sum, file) => sum + file.size, 0);
    const sharedFiles = allFiles.filter(f => f.shared).length;

    document.getElementById('totalFiles').textContent = totalFiles;
    document.getElementById('totalFolders').textContent = totalFolders;
    document.getElementById('totalSize').textContent = formatFileSize(totalSize);
    document.getElementById('sharedFiles').textContent = sharedFiles;
}

// 获取所有文件（包括子文件夹中的文件）
function getAllFiles() {
    const allFiles = [];
    
    function collectFiles(fileList) {
        fileList.forEach(file => {
            allFiles.push(file);
            if (file.children) {
                collectFiles(file.children);
            }
        });
    }
    
    collectFiles(files);
    return allFiles;
}

// 格式化文件大小
function formatFileSize(bytes) {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// 加载当前目录
function loadCurrentDirectory() {
    const currentFiles = getCurrentDirectoryFiles();
    filteredFiles = [...currentFiles];
    renderFileContainer();
    updateBreadcrumb();
    renderFilesPagination();
}

// 获取当前目录的文件
function getCurrentDirectoryFiles() {
    if (currentPath === '/') {
        return files;
    }
    
    // 查找当前路径对应的文件夹
    const pathParts = currentPath.split('/').filter(part => part);
    let currentFolder = files;
    
    for (const part of pathParts) {
        const folder = currentFolder.find(f => f.name === part && f.type === 'folder');
        if (folder && folder.children) {
            currentFolder = folder.children;
        } else {
            return [];
        }
    }
    
    return currentFolder || [];
}

// 渲染文件容器
function renderFileContainer() {
    const container = document.getElementById('fileContainer');
    if (!container) return;

    const startIndex = (currentFilePage - 1) * filesPerPage;
    const endIndex = startIndex + filesPerPage;
    const pageFiles = filteredFiles.slice(startIndex, endIndex);

    if (pageFiles.length === 0) {
        container.innerHTML = `
            <div class="empty-state">
                <i class="fas fa-folder-open"></i>
                <h3>文件夹为空</h3>
                <p>这个文件夹还没有任何文件</p>
            </div>
        `;
        return;
    }

    container.className = `file-container ${currentView}-view`;
    
    if (currentView === 'grid') {
        container.innerHTML = pageFiles.map(file => `
            <div class="file-item" data-id="${file.id}" onclick="handleFileClick(${file.id})" oncontextmenu="showFileMenu(${file.id}, event)">
                <div class="file-thumbnail">
                    ${getFileIcon(file)}
                    ${file.shared ? '<div class="shared-indicator"><i class="fas fa-share-alt"></i></div>' : ''}
                </div>
                <div class="file-info">
                    <div class="file-name" title="${file.name}">${file.name}</div>
                    <div class="file-meta">
                        <span class="file-size">${formatFileSize(file.size)}</span>
                        <span class="file-date">${formatDate(file.modifiedAt)}</span>
                    </div>
                </div>
                <div class="file-actions">
                    <button class="btn-icon" onclick="event.stopPropagation(); downloadFile(${file.id})" title="下载">
                        <i class="fas fa-download"></i>
                    </button>
                    <button class="btn-icon" onclick="event.stopPropagation(); showFileMenu(${file.id}, event)" title="更多">
                        <i class="fas fa-ellipsis-v"></i>
                    </button>
                </div>
            </div>
        `).join('');
    } else {
        container.innerHTML = `
            <div class="file-list-header">
                <div class="file-col-name">名称</div>
                <div class="file-col-size">大小</div>
                <div class="file-col-date">修改时间</div>
                <div class="file-col-owner">所有者</div>
                <div class="file-col-actions">操作</div>
            </div>
            ${pageFiles.map(file => `
                <div class="file-list-item" data-id="${file.id}" onclick="handleFileClick(${file.id})">
                    <div class="file-col-name">
                        <div class="file-icon-small">${getFileIcon(file, true)}</div>
                        <span class="file-name">${file.name}</span>
                        ${file.shared ? '<i class="fas fa-share-alt shared-icon"></i>' : ''}
                    </div>
                    <div class="file-col-size">${formatFileSize(file.size)}</div>
                    <div class="file-col-date">${formatDate(file.modifiedAt)}</div>
                    <div class="file-col-owner">${file.owner}</div>
                    <div class="file-col-actions">
                        <button class="btn-icon" onclick="event.stopPropagation(); downloadFile(${file.id})" title="下载">
                            <i class="fas fa-download"></i>
                        </button>
                        <button class="btn-icon" onclick="event.stopPropagation(); showFileMenu(${file.id}, event)" title="更多">
                            <i class="fas fa-ellipsis-v"></i>
                        </button>
                    </div>
                </div>
            `).join('')}
        `;
    }
}

// 获取文件图标
function getFileIcon(file, small = false) {
    const iconClass = small ? 'file-icon-small' : 'file-icon';
    
    if (file.type === 'folder') {
        return `<i class="fas fa-folder ${iconClass}"></i>`;
    }
    
    if (file.thumbnail) {
        return `<img src="${file.thumbnail}" alt="${file.name}" class="${iconClass}">`;
    }
    
    const typeIcons = {
        'document': 'fas fa-file-alt',
        'image': 'fas fa-file-image',
        'video': 'fas fa-file-video',
        'audio': 'fas fa-file-audio',
        'archive': 'fas fa-file-archive'
    };
    
    const iconName = typeIcons[file.type] || 'fas fa-file';
    return `<i class="${iconName} ${iconClass}"></i>`;
}

// 格式化日期
function formatDate(dateString) {
    const date = new Date(dateString);
    const now = new Date();
    const diff = now - date;
    const days = Math.floor(diff / (1000 * 60 * 60 * 24));
    
    if (days === 0) return '今天';
    if (days === 1) return '昨天';
    if (days < 7) return `${days}天前`;
    
    return date.toLocaleDateString('zh-CN');
}

// 更新面包屑导航
function updateBreadcrumb() {
    const pathElement = document.getElementById('currentPath');
    if (!pathElement) return;
    
    if (currentPath === '/') {
        pathElement.innerHTML = '';
        return;
    }
    
    const pathParts = currentPath.split('/').filter(part => part);
    let breadcrumbHTML = '';
    let buildPath = '';
    
    pathParts.forEach((part, index) => {
        buildPath += '/' + part;
        breadcrumbHTML += `
            <i class="fas fa-chevron-right"></i>
            <span class="breadcrumb-item" onclick="navigateToFolder('${buildPath}')">
                ${part}
            </span>
        `;
    });
    
    pathElement.innerHTML = breadcrumbHTML;
}

// 处理文件点击
function handleFileClick(fileId) {
    const file = findFileById(fileId);
    if (!file) return;

    if (file.type === 'folder') {
        navigateToFolder(currentPath === '/' ? `/${file.name}` : `${currentPath}/${file.name}`);
    } else {
        previewFile(fileId);
        addToRecentFiles(file);
    }
}

// 查找文件
function findFileById(fileId) {
    const allFiles = getAllFiles();
    return allFiles.find(f => f.id === fileId);
}

// 导航到文件夹
function navigateToFolder(path) {
    currentPath = path || '/';
    currentFilePage = 1;
    loadCurrentDirectory();
}

// 切换文件视图
function changeFileView(view) {
    currentView = view;

    // 更新按钮状态
    document.querySelectorAll('.view-btn').forEach(btn => {
        btn.classList.remove('active');
    });
    document.querySelector(`[data-view="${view}"]`).classList.add('active');

    renderFileContainer();
}

// 搜索文件
function searchFiles() {
    const query = document.getElementById('fileSearch').value.toLowerCase();
    const currentFiles = getCurrentDirectoryFiles();

    if (!query) {
        filteredFiles = [...currentFiles];
    } else {
        filteredFiles = currentFiles.filter(file =>
            file.name.toLowerCase().includes(query)
        );
    }

    currentFilePage = 1;
    renderFileContainer();
    renderFilesPagination();
}

// 筛选文件
function filterFiles() {
    const typeFilter = document.getElementById('fileTypeFilter').value;
    const currentFiles = getCurrentDirectoryFiles();

    if (!typeFilter) {
        filteredFiles = [...currentFiles];
    } else {
        filteredFiles = currentFiles.filter(file => file.type === typeFilter);
    }

    currentFilePage = 1;
    renderFileContainer();
    renderFilesPagination();
}

// 排序文件
function sortFiles() {
    const sortBy = document.getElementById('fileSortBy').value;

    filteredFiles.sort((a, b) => {
        switch (sortBy) {
            case 'name':
                return a.name.localeCompare(b.name);
            case 'date':
                return new Date(b.modifiedAt) - new Date(a.modifiedAt);
            case 'size':
                return b.size - a.size;
            case 'type':
                return a.type.localeCompare(b.type);
            default:
                return 0;
        }
    });

    renderFileContainer();
}

// 渲染分页
function renderFilesPagination() {
    const container = document.getElementById('filesPagination');
    if (!container) return;

    const totalPages = Math.ceil(filteredFiles.length / filesPerPage);

    if (totalPages <= 1) {
        container.innerHTML = '';
        return;
    }

    let paginationHTML = '<div class="pagination-controls">';

    // 上一页
    paginationHTML += `
        <button class="pagination-btn ${currentFilePage === 1 ? 'disabled' : ''}"
                onclick="changeFilePage(${currentFilePage - 1})"
                ${currentFilePage === 1 ? 'disabled' : ''}>
            <i class="fas fa-chevron-left"></i>
        </button>
    `;

    // 页码
    for (let i = 1; i <= totalPages; i++) {
        if (i === 1 || i === totalPages || (i >= currentFilePage - 1 && i <= currentFilePage + 1)) {
            paginationHTML += `
                <button class="pagination-btn ${i === currentFilePage ? 'active' : ''}"
                        onclick="changeFilePage(${i})">
                    ${i}
                </button>
            `;
        } else if (i === currentFilePage - 2 || i === currentFilePage + 2) {
            paginationHTML += '<span class="pagination-ellipsis">...</span>';
        }
    }

    // 下一页
    paginationHTML += `
        <button class="pagination-btn ${currentFilePage === totalPages ? 'disabled' : ''}"
                onclick="changeFilePage(${currentFilePage + 1})"
                ${currentFilePage === totalPages ? 'disabled' : ''}>
            <i class="fas fa-chevron-right"></i>
        </button>
    `;

    paginationHTML += '</div>';
    container.innerHTML = paginationHTML;
}

// 切换页面
function changeFilePage(page) {
    const totalPages = Math.ceil(filteredFiles.length / filesPerPage);
    if (page < 1 || page > totalPages) return;

    currentFilePage = page;
    renderFileContainer();
    renderFilesPagination();
}

// 添加到最近文件
function addToRecentFiles(file) {
    // 移除已存在的记录
    const existingIndex = recentFiles.findIndex(f => f.id === file.id);
    if (existingIndex !== -1) {
        recentFiles.splice(existingIndex, 1);
    }

    // 添加到开头
    recentFiles.unshift({
        ...file,
        accessedAt: new Date().toISOString()
    });

    // 限制最近文件数量
    if (recentFiles.length > 10) {
        recentFiles.splice(10);
    }

    renderRecentFiles();
}

// 渲染最近文件
function renderRecentFiles() {
    const container = document.getElementById('recentFilesList');
    if (!container) return;

    if (recentFiles.length === 0) {
        container.innerHTML = `
            <div class="no-recent-files">
                <i class="fas fa-clock"></i>
                <p>暂无最近访问的文件</p>
            </div>
        `;
        return;
    }

    container.innerHTML = recentFiles.slice(0, 5).map(file => `
        <div class="recent-file-item" onclick="handleFileClick(${file.id})">
            <div class="recent-file-icon">${getFileIcon(file, true)}</div>
            <div class="recent-file-info">
                <div class="recent-file-name">${file.name}</div>
                <div class="recent-file-time">${formatDate(file.accessedAt)}</div>
            </div>
        </div>
    `).join('');
}

// 清空最近文件
function clearRecentFiles() {
    if (confirm('确定要清空最近访问的文件记录吗？')) {
        recentFiles.length = 0;
        renderRecentFiles();
        showNotification('最近文件记录已清空', 'success');
    }
}

// 创建文件夹
function createFolder() {
    const folderName = prompt('请输入文件夹名称:', '新建文件夹');
    if (!folderName) return;

    // 检查名称是否重复
    const currentFiles = getCurrentDirectoryFiles();
    if (currentFiles.some(f => f.name === folderName)) {
        showNotification('文件夹名称已存在', 'error');
        return;
    }

    const newFolder = {
        id: Math.max(...getAllFiles().map(f => f.id), 0) + 1,
        name: folderName,
        type: 'folder',
        size: 0,
        path: currentPath,
        createdAt: new Date().toISOString(),
        modifiedAt: new Date().toISOString(),
        owner: '当前用户',
        shared: false,
        children: []
    };

    // 添加到当前目录
    if (currentPath === '/') {
        files.push(newFolder);
    } else {
        // 找到当前文件夹并添加
        const pathParts = currentPath.split('/').filter(part => part);
        let currentFolder = files;

        for (const part of pathParts) {
            const folder = currentFolder.find(f => f.name === part && f.type === 'folder');
            if (folder && folder.children) {
                currentFolder = folder.children;
            }
        }

        currentFolder.push(newFolder);
    }

    loadCurrentDirectory();
    updateFileStats();
    showNotification('文件夹创建成功', 'success');
}

// 显示上传模态框
function showUploadModal() {
    document.getElementById('uploadModalOverlay').classList.add('active');
    resetUploadArea();
}

// 关闭上传模态框
function closeUploadModal() {
    document.getElementById('uploadModalOverlay').classList.remove('active');
    uploadQueue = [];
    resetUploadArea();
}

// 重置上传区域
function resetUploadArea() {
    const uploadArea = document.getElementById('uploadArea');
    const uploadList = document.getElementById('uploadList');
    const startUploadBtn = document.getElementById('startUploadBtn');

    uploadArea.style.display = 'block';
    uploadList.style.display = 'none';
    startUploadBtn.style.display = 'none';
    uploadList.innerHTML = '';
}

// 设置拖拽上传
function setupDragAndDrop() {
    const uploadZone = document.querySelector('.upload-zone');
    if (!uploadZone) return;

    uploadZone.addEventListener('dragover', handleDragOver);
    uploadZone.addEventListener('dragleave', handleDragLeave);
    uploadZone.addEventListener('drop', handleFileDrop);
}

// 处理拖拽悬停
function handleDragOver(event) {
    event.preventDefault();
    event.currentTarget.classList.add('drag-over');
}

// 处理拖拽离开
function handleDragLeave(event) {
    event.preventDefault();
    event.currentTarget.classList.remove('drag-over');
}

// 处理文件拖拽
function handleFileDrop(event) {
    event.preventDefault();
    event.currentTarget.classList.remove('drag-over');

    const files = Array.from(event.dataTransfer.files);
    processSelectedFiles(files);
}

// 处理文件选择
function handleFileSelect(event) {
    const files = Array.from(event.target.files);
    processSelectedFiles(files);
}

// 处理选择的文件
function processSelectedFiles(files) {
    if (files.length === 0) return;

    // 验证文件
    const validFiles = files.filter(file => {
        if (file.size > 100 * 1024 * 1024) { // 100MB限制
            showNotification(`文件 ${file.name} 超过100MB限制`, 'error');
            return false;
        }
        return true;
    });

    if (validFiles.length === 0) return;

    // 添加到上传队列
    uploadQueue = validFiles.map((file, index) => ({
        id: Date.now() + index,
        file: file,
        name: file.name,
        size: file.size,
        type: getFileTypeFromName(file.name),
        progress: 0,
        status: 'pending' // pending, uploading, completed, error
    }));

    renderUploadList();

    // 显示上传列表和开始按钮
    document.getElementById('uploadArea').style.display = 'none';
    document.getElementById('uploadList').style.display = 'block';
    document.getElementById('startUploadBtn').style.display = 'inline-flex';
}

// 从文件名获取文件类型
function getFileTypeFromName(fileName) {
    const extension = fileName.split('.').pop().toLowerCase();

    const typeMap = {
        // 图片
        'jpg': 'image', 'jpeg': 'image', 'png': 'image', 'gif': 'image', 'bmp': 'image', 'svg': 'image',
        // 文档
        'pdf': 'document', 'doc': 'document', 'docx': 'document', 'xls': 'document', 'xlsx': 'document', 'ppt': 'document', 'pptx': 'document', 'txt': 'document',
        // 视频
        'mp4': 'video', 'avi': 'video', 'mov': 'video', 'wmv': 'video', 'flv': 'video', 'mkv': 'video',
        // 音频
        'mp3': 'audio', 'wav': 'audio', 'flac': 'audio', 'aac': 'audio', 'ogg': 'audio',
        // 压缩包
        'zip': 'archive', 'rar': 'archive', '7z': 'archive', 'tar': 'archive', 'gz': 'archive'
    };

    return typeMap[extension] || 'document';
}

// 渲染上传列表
function renderUploadList() {
    const container = document.getElementById('uploadList');
    if (!container) return;

    container.innerHTML = `
        <h4>待上传文件 (${uploadQueue.length})</h4>
        <div class="upload-items">
            ${uploadQueue.map(item => `
                <div class="upload-item" data-id="${item.id}">
                    <div class="upload-file-info">
                        <div class="upload-file-icon">${getFileIconByType(item.type)}</div>
                        <div class="upload-file-details">
                            <div class="upload-file-name">${item.name}</div>
                            <div class="upload-file-size">${formatFileSize(item.size)}</div>
                        </div>
                    </div>
                    <div class="upload-progress">
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: ${item.progress}%"></div>
                        </div>
                        <div class="upload-status">${getUploadStatusText(item.status)}</div>
                    </div>
                    <button class="btn-icon danger" onclick="removeFromUploadQueue(${item.id})" title="移除">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            `).join('')}
        </div>
    `;
}

// 根据类型获取文件图标
function getFileIconByType(type) {
    const icons = {
        'image': 'fas fa-file-image',
        'document': 'fas fa-file-alt',
        'video': 'fas fa-file-video',
        'audio': 'fas fa-file-audio',
        'archive': 'fas fa-file-archive'
    };

    return `<i class="${icons[type] || 'fas fa-file'}"></i>`;
}

// 获取上传状态文本
function getUploadStatusText(status) {
    const statusMap = {
        'pending': '等待上传',
        'uploading': '上传中...',
        'completed': '上传完成',
        'error': '上传失败'
    };

    return statusMap[status] || status;
}

// 从上传队列中移除文件
function removeFromUploadQueue(itemId) {
    uploadQueue = uploadQueue.filter(item => item.id !== itemId);

    if (uploadQueue.length === 0) {
        resetUploadArea();
    } else {
        renderUploadList();
    }
}

// 开始上传
function startUpload() {
    if (uploadQueue.length === 0) return;

    const startBtn = document.getElementById('startUploadBtn');
    startBtn.disabled = true;
    startBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 上传中...';

    // 模拟上传过程
    uploadQueue.forEach((item, index) => {
        setTimeout(() => {
            simulateFileUpload(item);
        }, index * 500);
    });
}

// 模拟文件上传
function simulateFileUpload(item) {
    item.status = 'uploading';
    renderUploadList();

    // 模拟上传进度
    let progress = 0;
    const progressInterval = setInterval(() => {
        progress += Math.random() * 20;
        if (progress >= 100) {
            progress = 100;
            clearInterval(progressInterval);

            // 上传完成
            item.progress = 100;
            item.status = 'completed';

            // 添加到文件列表
            addUploadedFile(item);

            renderUploadList();

            // 检查是否所有文件都上传完成
            if (uploadQueue.every(i => i.status === 'completed')) {
                setTimeout(() => {
                    closeUploadModal();
                    showNotification(`成功上传 ${uploadQueue.length} 个文件`, 'success');
                    loadCurrentDirectory();
                    updateFileStats();
                }, 1000);
            }
        } else {
            item.progress = Math.round(progress);
            renderUploadList();
        }
    }, 200);
}

// 添加上传的文件到文件列表
function addUploadedFile(uploadItem) {
    const newFile = {
        id: Math.max(...getAllFiles().map(f => f.id), 0) + 1,
        name: uploadItem.name,
        type: uploadItem.type,
        size: uploadItem.size,
        path: currentPath,
        createdAt: new Date().toISOString(),
        modifiedAt: new Date().toISOString(),
        owner: '当前用户',
        shared: false,
        downloads: 0
    };

    // 添加到当前目录
    if (currentPath === '/') {
        files.push(newFile);
    } else {
        // 找到当前文件夹并添加
        const pathParts = currentPath.split('/').filter(part => part);
        let currentFolder = files;

        for (const part of pathParts) {
            const folder = currentFolder.find(f => f.name === part && f.type === 'folder');
            if (folder && folder.children) {
                currentFolder = folder.children;
            }
        }

        currentFolder.push(newFile);
    }
}

// 预览文件
function previewFile(fileId) {
    const file = findFileById(fileId);
    if (!file || file.type === 'folder') return;

    currentPreviewFile = file;
    document.getElementById('previewFileName').textContent = file.name;

    const previewContainer = document.getElementById('filePreview');

    switch (file.type) {
        case 'image':
            previewContainer.innerHTML = `
                <div class="image-preview">
                    <img src="${file.thumbnail}" alt="${file.name}" style="max-width: 100%; max-height: 500px;">
                </div>
            `;
            break;
        case 'video':
            previewContainer.innerHTML = `
                <div class="video-preview">
                    <video controls style="max-width: 100%; max-height: 500px;">
                        <source src="${file.thumbnail}" type="video/mp4">
                        您的浏览器不支持视频播放
                    </video>
                </div>
            `;
            break;
        case 'audio':
            previewContainer.innerHTML = `
                <div class="audio-preview">
                    <audio controls style="width: 100%;">
                        <source src="${file.thumbnail}" type="audio/mpeg">
                        您的浏览器不支持音频播放
                    </audio>
                    <div class="audio-info">
                        <h4>${file.name}</h4>
                        <p>大小: ${formatFileSize(file.size)}</p>
                        <p>创建时间: ${formatDate(file.createdAt)}</p>
                    </div>
                </div>
            `;
            break;
        case 'document':
            previewContainer.innerHTML = `
                <div class="document-preview">
                    <div class="document-icon">
                        <i class="fas fa-file-alt"></i>
                    </div>
                    <div class="document-info">
                        <h4>${file.name}</h4>
                        <p>文档类型: ${file.name.split('.').pop().toUpperCase()}</p>
                        <p>大小: ${formatFileSize(file.size)}</p>
                        <p>创建时间: ${formatDate(file.createdAt)}</p>
                        <p>所有者: ${file.owner}</p>
                        <button class="btn-primary" onclick="downloadFile(${file.id})">
                            <i class="fas fa-download"></i>
                            下载文件
                        </button>
                    </div>
                </div>
            `;
            break;
        default:
            previewContainer.innerHTML = `
                <div class="file-preview-default">
                    <div class="file-icon-large">
                        ${getFileIcon(file)}
                    </div>
                    <div class="file-details">
                        <h4>${file.name}</h4>
                        <p>文件类型: ${file.type}</p>
                        <p>大小: ${formatFileSize(file.size)}</p>
                        <p>创建时间: ${formatDate(file.createdAt)}</p>
                        <p>所有者: ${file.owner}</p>
                    </div>
                </div>
            `;
    }

    document.getElementById('previewModalOverlay').classList.add('active');
}

// 关闭预览模态框
function closePreviewModal() {
    document.getElementById('previewModalOverlay').classList.remove('active');
    currentPreviewFile = null;
}

// 下载预览文件
function downloadPreviewFile() {
    if (currentPreviewFile) {
        downloadFile(currentPreviewFile.id);
    }
}

// 分享预览文件
function sharePreviewFile() {
    if (currentPreviewFile) {
        showShareModal(currentPreviewFile.id);
    }
}

// 下载文件
function downloadFile(fileId) {
    const file = findFileById(fileId);
    if (!file || file.type === 'folder') return;

    showNotification(`正在下载 ${file.name}...`, 'info');

    // 更新下载次数
    file.downloads = (file.downloads || 0) + 1;

    // 模拟下载
    setTimeout(() => {
        const link = document.createElement('a');
        link.href = file.thumbnail || '#';
        link.download = file.name;
        link.click();

        showNotification('文件下载完成', 'success');
        addToRecentFiles(file);
    }, 1000);
}

// 显示分享模态框
function showShareModal(fileId) {
    const file = findFileById(fileId);
    if (!file) return;

    currentPreviewFile = file;
    document.getElementById('shareModalOverlay').classList.add('active');
}

// 关闭分享模态框
function closeShareModal() {
    document.getElementById('shareModalOverlay').classList.remove('active');
}

// 处理分享
function processShare() {
    const users = Array.from(document.getElementById('shareUsers').selectedOptions).map(option => option.text);
    const permission = document.getElementById('sharePermission').value;
    const expiry = document.getElementById('shareExpiry').value;
    const message = document.getElementById('shareMessage').value;

    if (users.length === 0) {
        showNotification('请选择要分享的用户', 'error');
        return;
    }

    // 标记文件为已分享
    if (currentPreviewFile) {
        currentPreviewFile.shared = true;
    }

    showNotification(`文件已分享给 ${users.join(', ')}`, 'success');
    closeShareModal();
    loadCurrentDirectory();
    updateFileStats();
}

// ===== 文件操作增强功能 =====

// 文件操作管理器
class FileOperationManager {
    constructor() {
        this.moveModal = null;
        this.renameModal = null;
        this.contextMenu = null;
        this.selectedFiles = [];
        this.currentFile = null;
        this.directories = [];
        this.selectedDirectory = null;
        
        this.init();
    }
    
    init() {
        this.moveModal = document.getElementById('fileMoveModalOverlay');
        this.renameModal = document.getElementById('fileRenameModalOverlay');
        this.contextMenu = document.getElementById('fileContextMenu');
        
        this.loadDirectories();
        this.bindEvents();
    }
    
    bindEvents() {
        // 点击外部关闭右键菜单
        document.addEventListener('click', (e) => {
            if (this.contextMenu && !this.contextMenu.contains(e.target)) {
                this.hideContextMenu();
            }
        });
        
        // 阻止默认右键菜单
        document.addEventListener('contextmenu', (e) => {
            if (e.target.closest('.file-item')) {
                e.preventDefault();
            }
        });
    }
    
    // 加载目录结构
    loadDirectories() {
        // 模拟目录结构
        this.directories = [
            { id: 'root', name: '根目录', path: '/', parent: null },
            { id: 'documents', name: '文档', path: '/documents', parent: 'root' },
            { id: 'images', name: '图片', path: '/images', parent: 'root' },
            { id: 'videos', name: '视频', path: '/videos', parent: 'root' },
            { id: 'downloads', name: '下载', path: '/downloads', parent: 'root' },
            { id: 'work', name: '工作文件', path: '/documents/work', parent: 'documents' },
            { id: 'personal', name: '个人文件', path: '/documents/personal', parent: 'documents' },
            { id: 'photos', name: '照片', path: '/images/photos', parent: 'images' },
            { id: 'screenshots', name: '截图', path: '/images/screenshots', parent: 'images' }
        ];
    }
    
    // 显示文件移动模态框
    showMoveModal(fileIds = []) {
        if (fileIds.length === 0 && this.selectedFiles.length === 0) {
            showNotification('请先选择要移动的文件', 'warning');
            return;
        }
        
        this.selectedFiles = fileIds.length > 0 ? fileIds : this.selectedFiles;
        this.populateSelectedFiles();
        this.renderDirectoryTree();
        this.moveModal.classList.add('active');
        document.body.style.overflow = 'hidden';
    }
    
    // 关闭文件移动模态框
    closeMoveModal() {
        this.moveModal.classList.remove('active');
        document.body.style.overflow = '';
        this.selectedFiles = [];
        this.selectedDirectory = null;
    }
    
    // 显示文件重命名模态框
    showRenameModal(fileId) {
        const file = findFileById(fileId);
        if (!file) {
            showNotification('文件不存在', 'error');
            return;
        }
        
        this.currentFile = file;
        this.populateRenameForm(file);
        this.renameModal.classList.add('active');
        document.body.style.overflow = 'hidden';
        
        // 聚焦到输入框并选中文件名（不包括扩展名）
        setTimeout(() => {
            const input = document.getElementById('newFileName');
            if (input) {
                input.focus();
                const lastDotIndex = file.name.lastIndexOf('.');
                if (lastDotIndex > 0) {
                    input.setSelectionRange(0, lastDotIndex);
                }
            }
        }, 100);
    }
    
    // 关闭文件重命名模态框
    closeRenameModal() {
        this.renameModal.classList.remove('active');
        document.body.style.overflow = '';
        this.currentFile = null;
        this.clearRenameForm();
    }
    
    // 显示右键菜单
    showContextMenu(fileId, event) {
        event.preventDefault();
        event.stopPropagation();
        
        this.currentFile = findFileById(fileId);
        if (!this.currentFile) return;
        
        const x = event.clientX;
        const y = event.clientY;
        
        // 调整菜单位置，确保不超出屏幕
        const menuWidth = 160;
        const menuHeight = 200;
        const adjustedX = x + menuWidth > window.innerWidth ? x - menuWidth : x;
        const adjustedY = y + menuHeight > window.innerHeight ? y - menuHeight : y;
        
        this.contextMenu.style.left = adjustedX + 'px';
        this.contextMenu.style.top = adjustedY + 'px';
        this.contextMenu.classList.add('show');
    }
    
    // 隐藏右键菜单
    hideContextMenu() {
        if (this.contextMenu) {
            this.contextMenu.classList.remove('show');
        }
    }
    
    // 填充选中文件列表
    populateSelectedFiles() {
        const container = document.getElementById('selectedFilesList');
        if (!container) return;
        
        const filesHtml = this.selectedFiles.map(fileId => {
            const file = findFileById(fileId);
            if (!file) return '';
            
            return `
                <div class="selected-file-item">
                    <div class="file-icon">
                        <i class="${this.getFileIcon(file)}"></i>
                    </div>
                    <div class="file-name">${file.name}</div>
                    <div class="file-size">${this.formatFileSize(file.size)}</div>
                </div>
            `;
        }).join('');
        
        container.innerHTML = filesHtml;
    }
    
    // 渲染目录树
    renderDirectoryTree() {
        const container = document.getElementById('directoryTree');
        if (!container) return;
        
        const treeHtml = this.directories.map(dir => `
            <div class="directory-item" data-dir-id="${dir.id}" onclick="selectDirectory('${dir.id}')">
                <div class="folder-icon">
                    <i class="fas fa-folder"></i>
                </div>
                <div class="folder-name">${dir.name}</div>
                <div class="folder-path">${dir.path}</div>
            </div>
        `).join('');
        
        container.innerHTML = treeHtml;
    }
    
    // 选择目录
    selectDirectory(dirId) {
        // 移除之前的选中状态
        document.querySelectorAll('.directory-item').forEach(item => {
            item.classList.remove('selected');
        });
        
        // 添加新的选中状态
        const selectedItem = document.querySelector(`[data-dir-id="${dirId}"]`);
        if (selectedItem) {
            selectedItem.classList.add('selected');
            this.selectedDirectory = this.directories.find(dir => dir.id === dirId);
        }
    }
    
    // 填充重命名表单
    populateRenameForm(file) {
        document.getElementById('currentFileName').textContent = file.name;
        document.getElementById('currentFileType').textContent = this.getFileTypeDescription(file);
        document.getElementById('newFileName').value = file.name;
        
        // 更新文件图标
        const iconElement = document.getElementById('renameFileIcon');
        if (iconElement) {
            iconElement.innerHTML = `<i class="${this.getFileIcon(file)}"></i>`;
        }
    }
    
    // 清空重命名表单
    clearRenameForm() {
        document.getElementById('newFileName').value = '';
        this.hideFieldError('fileNameError');
    }
    
    // 获取文件图标
    getFileIcon(file) {
        const extension = file.name.split('.').pop().toLowerCase();
        const iconMap = {
            'pdf': 'fas fa-file-pdf',
            'doc': 'fas fa-file-word',
            'docx': 'fas fa-file-word',
            'xls': 'fas fa-file-excel',
            'xlsx': 'fas fa-file-excel',
            'ppt': 'fas fa-file-powerpoint',
            'pptx': 'fas fa-file-powerpoint',
            'jpg': 'fas fa-file-image',
            'jpeg': 'fas fa-file-image',
            'png': 'fas fa-file-image',
            'gif': 'fas fa-file-image',
            'mp4': 'fas fa-file-video',
            'avi': 'fas fa-file-video',
            'mov': 'fas fa-file-video',
            'mp3': 'fas fa-file-audio',
            'wav': 'fas fa-file-audio',
            'zip': 'fas fa-file-archive',
            'rar': 'fas fa-file-archive',
            'txt': 'fas fa-file-alt',
            'js': 'fas fa-file-code',
            'html': 'fas fa-file-code',
            'css': 'fas fa-file-code',
            'json': 'fas fa-file-code'
        };
        
        return iconMap[extension] || 'fas fa-file';
    }
    
    // 获取文件类型描述
    getFileTypeDescription(file) {
        const extension = file.name.split('.').pop().toLowerCase();
        const typeMap = {
            'pdf': 'PDF文档',
            'doc': 'Word文档',
            'docx': 'Word文档',
            'xls': 'Excel表格',
            'xlsx': 'Excel表格',
            'ppt': 'PowerPoint演示',
            'pptx': 'PowerPoint演示',
            'jpg': '图片文件',
            'jpeg': '图片文件',
            'png': '图片文件',
            'gif': '动图文件',
            'mp4': '视频文件',
            'avi': '视频文件',
            'mov': '视频文件',
            'mp3': '音频文件',
            'wav': '音频文件',
            'zip': '压缩文件',
            'rar': '压缩文件',
            'txt': '文本文件',
            'js': 'JavaScript文件',
            'html': 'HTML文件',
            'css': 'CSS文件',
            'json': 'JSON文件'
        };
        
        return typeMap[extension] || '未知类型';
    }
    
    // 格式化文件大小
    formatFileSize(bytes) {
        if (bytes === 0) return '0 B';
        const k = 1024;
        const sizes = ['B', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }
    
    // 验证文件名
    validateFileName(fileName) {
        const errors = [];
        
        if (!fileName.trim()) {
            errors.push('文件名不能为空');
        }
        
        if (fileName.length > 255) {
            errors.push('文件名长度不能超过255个字符');
        }
        
        const invalidChars = /[\\/:*?"<>|]/;
        if (invalidChars.test(fileName)) {
            errors.push('文件名不能包含特殊字符：\\ / : * ? " < > |');
        }
        
        // 检查是否与现有文件重名（简化检查）
        const existingFiles = getAllFiles();
        const isDuplicate = existingFiles.some(file => 
            file.name === fileName && file.id !== this.currentFile?.id
        );
        
        if (isDuplicate) {
            errors.push('文件名已存在，请选择其他名称');
        }
        
        return errors;
    }
    
    // 显示字段错误
    showFieldError(errorId, message) {
        const errorElement = document.getElementById(errorId);
        if (errorElement) {
            errorElement.textContent = message;
            errorElement.classList.add('show');
        }
    }
    
    // 隐藏字段错误
    hideFieldError(errorId) {
        const errorElement = document.getElementById(errorId);
        if (errorElement) {
            errorElement.classList.remove('show');
        }
    }
    
    // 执行文件操作
    executeFileOperation() {
        if (!this.selectedDirectory) {
            showNotification('请选择目标目录', 'warning');
            return;
        }
        
        const operation = document.querySelector('input[name="operation"]:checked').value;
        const operationText = operation === 'move' ? '移动' : '复制';
        
        try {
            this.selectedFiles.forEach(fileId => {
                const file = findFileById(fileId);
                if (file) {
                    if (operation === 'move') {
                        // 移动文件逻辑
                        this.moveFileToDirectory(file, this.selectedDirectory);
                    } else {
                        // 复制文件逻辑
                        this.copyFileToDirectory(file, this.selectedDirectory);
                    }
                }
            });
            
            showNotification(`成功${operationText}${this.selectedFiles.length}个文件`, 'success');
            this.closeMoveModal();
            loadCurrentDirectory();
            updateFileStats();
            
        } catch (error) {
            console.error('文件操作失败:', error);
            showNotification(`${operationText}文件失败，请重试`, 'error');
        }
    }
    
    // 移动文件到目录
    moveFileToDirectory(file, targetDir) {
        // 模拟移动操作
        console.log(`移动文件 ${file.name} 到 ${targetDir.path}`);
        // 在实际应用中，这里会调用API来移动文件
    }
    
    // 复制文件到目录
    copyFileToDirectory(file, targetDir) {
        // 模拟复制操作
        console.log(`复制文件 ${file.name} 到 ${targetDir.path}`);
        // 在实际应用中，这里会调用API来复制文件
    }
    
    // 执行文件重命名
    executeFileRename() {
        const newFileName = document.getElementById('newFileName').value.trim();
        const errors = this.validateFileName(newFileName);
        
        if (errors.length > 0) {
            this.showFieldError('fileNameError', errors[0]);
            return;
        }
        
        this.hideFieldError('fileNameError');
        
        try {
            // 执行重命名操作
            this.renameFile(this.currentFile, newFileName);
            showNotification(`文件已重命名为 "${newFileName}"`, 'success');
            this.closeRenameModal();
            loadCurrentDirectory();
            updateFileStats();
            
        } catch (error) {
            console.error('重命名失败:', error);
            showNotification('重命名失败，请重试', 'error');
        }
    }
    
    // 重命名文件
    renameFile(file, newName) {
        // 模拟重命名操作
        console.log(`重命名文件 ${file.name} 为 ${newName}`);
        // 在实际应用中，这里会调用API来重命名文件
        file.name = newName;
    }
    
    // 显示新建文件夹输入框
    showNewFolderInput() {
        const input = document.getElementById('newFolderInput');
        if (input) {
            input.style.display = 'flex';
            document.getElementById('newFolderName').focus();
        }
    }
    
    // 隐藏新建文件夹输入框
    hideNewFolderInput() {
        const input = document.getElementById('newFolderInput');
        if (input) {
            input.style.display = 'none';
            document.getElementById('newFolderName').value = '';
        }
    }
    
    // 创建新文件夹
    createNewFolder() {
        const folderName = document.getElementById('newFolderName').value.trim();
        if (!folderName) {
            showNotification('请输入文件夹名称', 'warning');
            return;
        }
        
        // 验证文件夹名称
        const errors = this.validateFileName(folderName);
        if (errors.length > 0) {
            showNotification(errors[0], 'error');
            return;
        }
        
        // 创建新文件夹
        const newFolder = {
            id: Date.now().toString(),
            name: folderName,
            path: `/new/${folderName}`,
            parent: 'root'
        };
        
        this.directories.push(newFolder);
        this.renderDirectoryTree();
        this.hideNewFolderInput();
        showNotification(`文件夹 "${folderName}" 创建成功`, 'success');
    }
}

// 创建全局文件操作管理器实例
let fileOperationManager;

// 显示移动模态框
function showMoveModal(fileIds = []) {
    if (!fileOperationManager) {
        fileOperationManager = new FileOperationManager();
    }
    fileOperationManager.showMoveModal(fileIds);
}

// 显示文件菜单
function showFileMenu(fileId, event) {
    if (!fileOperationManager) {
        fileOperationManager = new FileOperationManager();
    }
    fileOperationManager.showContextMenu(fileId, event);
}

// 删除文件
function deleteFile(fileId) {
    const file = findFileById(fileId);
    if (!file) return;

    if (confirm(`确定要删除 "${file.name}" 吗？此操作不可撤销！`)) {
        // 从文件列表中移除
        removeFileFromList(fileId);

        loadCurrentDirectory();
        updateFileStats();
        showNotification('文件删除成功', 'success');
    }
}

// 从文件列表中移除文件
function removeFileFromList(fileId) {
    function removeFromArray(arr) {
        const index = arr.findIndex(f => f.id === fileId);
        if (index !== -1) {
            arr.splice(index, 1);
            return true;
        }

        // 递归查找子文件夹
        for (const item of arr) {
            if (item.children && removeFromArray(item.children)) {
                return true;
            }
        }

        return false;
    }

    removeFromArray(files);
}

// 全局函数导出
window.initializeFileManagement = initializeFileManagement;
window.navigateToFolder = navigateToFolder;
window.changeFileView = changeFileView;
window.searchFiles = searchFiles;
window.filterFiles = filterFiles;
window.sortFiles = sortFiles;
window.changeFilePage = changeFilePage;
window.handleFileClick = handleFileClick;
window.createFolder = createFolder;
window.showUploadModal = showUploadModal;
window.closeUploadModal = closeUploadModal;
window.handleFileDrop = handleFileDrop;
window.handleDragOver = handleDragOver;
window.handleDragLeave = handleDragLeave;
window.handleFileSelect = handleFileSelect;
window.removeFromUploadQueue = removeFromUploadQueue;
window.startUpload = startUpload;
window.previewFile = previewFile;
window.closePreviewModal = closePreviewModal;
window.downloadPreviewFile = downloadPreviewFile;
window.sharePreviewFile = sharePreviewFile;
window.downloadFile = downloadFile;
window.showShareModal = showShareModal;
window.closeShareModal = closeShareModal;
window.processShare = processShare;
window.showMoveModal = showMoveModal;
window.showFileMenu = showFileMenu;
window.deleteFile = deleteFile;
window.clearRecentFiles = clearRecentFiles;
// ===== 文件操作全局函数 =====

function closeFileMoveModal() {
    if (fileOperationManager) {
        fileOperationManager.closeMoveModal();
    }
}

function showFileMoveModal() {
    showMoveModal();
}

function executeFileOperation() {
    if (fileOperationManager) {
        fileOperationManager.executeFileOperation();
    }
}

function selectDirectory(dirId) {
    if (fileOperationManager) {
        fileOperationManager.selectDirectory(dirId);
    }
}

function showNewFolderInput() {
    if (fileOperationManager) {
        fileOperationManager.showNewFolderInput();
    }
}

function hideNewFolderInput() {
    if (fileOperationManager) {
        fileOperationManager.hideNewFolderInput();
    }
}

function createNewFolder() {
    if (fileOperationManager) {
        fileOperationManager.createNewFolder();
    }
}

function showFileRenameModal(fileId = null) {
    if (!fileOperationManager) {
        fileOperationManager = new FileOperationManager();
    }
    
    const targetFileId = fileId || (fileOperationManager.currentFile ? fileOperationManager.currentFile.id : null);
    if (targetFileId) {
        fileOperationManager.showRenameModal(targetFileId);
    } else {
        showNotification('请先选择要重命名的文件', 'warning');
    }
}

function closeFileRenameModal() {
    if (fileOperationManager) {
        fileOperationManager.closeRenameModal();
    }
}

function executeFileRename() {
    if (fileOperationManager) {
        fileOperationManager.executeFileRename();
    }
}

// 右键菜单操作函数
function openFile() {
    if (fileOperationManager && fileOperationManager.currentFile) {
        showNotification(`打开文件: ${fileOperationManager.currentFile.name}`, 'info');
        fileOperationManager.hideContextMenu();
    }
}

function downloadFile() {
    if (fileOperationManager && fileOperationManager.currentFile) {
        showNotification(`下载文件: ${fileOperationManager.currentFile.name}`, 'success');
        fileOperationManager.hideContextMenu();
    }
}

function copyFile() {
    if (fileOperationManager && fileOperationManager.currentFile) {
        // 模拟复制到剪贴板
        showNotification(`文件 "${fileOperationManager.currentFile.name}" 已复制`, 'success');
        fileOperationManager.hideContextMenu();
    }
}

function deleteSelectedFile() {
    if (fileOperationManager && fileOperationManager.currentFile) {
        const fileName = fileOperationManager.currentFile.name;
        if (confirm(`确定要删除文件 "${fileName}" 吗？此操作不可撤销！`)) {
            deleteFile(fileOperationManager.currentFile.id);
        }
        fileOperationManager.hideContextMenu();
    }
}

// 更新全局导出
window.closeFileMoveModal = closeFileMoveModal;
window.showFileMoveModal = showFileMoveModal;
window.executeFileOperation = executeFileOperation;
window.selectDirectory = selectDirectory;
window.showNewFolderInput = showNewFolderInput;
window.hideNewFolderInput = hideNewFolderInput;
window.createNewFolder = createNewFolder;
window.showFileRenameModal = showFileRenameModal;
window.closeFileRenameModal = closeFileRenameModal;
window.executeFileRename = executeFileRename;
window.openFile = openFile;
window.downloadFile = downloadFile;
window.copyFile = copyFile;
window.deleteSelectedFile = deleteSelectedFile;