// 实时通知系统
class NotificationSystem {
    constructor() {
        this.notifications = [];
        this.filteredNotifications = [];
        this.currentPage = 1;
        this.pageSize = 10;
        this.filters = {
            type: '',
            status: '',
            time: ''
        };
        this.unreadCount = 0;
        this.isEnabled = true;
        this.soundEnabled = false;
        
        this.initializeNotifications();
        this.startRealTimeUpdates();
        this.requestNotificationPermission();
    }
    
    initializeNotifications() {
        // 生成示例通知数据
        this.notifications = this.generateSampleNotifications();
        this.updateStats();
        this.applyFilters();
        this.updateNotificationBadge();
    }
    
    generateSampleNotifications() {
        const notifications = [];
        const types = ['system', 'order', 'user', 'security', 'reminder'];
        const titles = {
            system: ['系统更新完成', '数据备份成功', '服务器维护通知', '系统性能优化'],
            order: ['新订单提醒', '订单状态更新', '支付成功通知', '发货提醒'],
            user: ['新用户注册', '用户权限变更', '用户反馈', '账户验证'],
            security: ['登录异常检测', '密码修改提醒', '安全策略更新', '权限变更'],
            reminder: ['会议提醒', '任务截止提醒', '生日提醒', '重要事项']
        };
        
        const messages = {
            system: ['系统已成功更新到最新版本', '数据备份已完成，所有数据安全', '系统将于今晚进行维护', '系统性能已优化，响应速度提升'],
            order: ['您有一个新的订单需要处理', '订单 #12345 状态已更新', '订单支付已成功到账', '订单已发货，请注意查收'],
            user: ['新用户张三已成功注册', '用户李四的权限已更新', '收到用户反馈建议', '用户邮箱验证已完成'],
            security: ['检测到异常登录行为', '您的密码已成功修改', '安全策略已更新', '用户权限发生变更'],
            reminder: ['会议将在30分钟后开始', '任务即将到期，请及时处理', '今天是张三的生日', '重要事项需要您的关注']
        };
        
        for (let i = 1; i <= 50; i++) {
            const type = types[Math.floor(Math.random() * types.length)];
            const createTime = new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000);
            const isRead = Math.random() > 0.4; // 60% 已读
            
            notifications.push({
                id: i,
                type: type,
                title: titles[type][Math.floor(Math.random() * titles[type].length)],
                message: messages[type][Math.floor(Math.random() * messages[type].length)],
                createTime: createTime,
                readTime: isRead ? new Date(createTime.getTime() + Math.random() * 24 * 60 * 60 * 1000) : null,
                isRead: isRead,
                priority: Math.random() > 0.8 ? 'high' : Math.random() > 0.6 ? 'medium' : 'low',
                actionUrl: type === 'order' ? '#orders' : type === 'user' ? '#users' : null
            });
        }
        
        return notifications.sort((a, b) => b.createTime - a.createTime);
    }
    
    updateStats() {
        const total = this.notifications.length;
        const unread = this.notifications.filter(n => !n.isRead).length;
        const read = total - unread;
        const today = new Date();
        today.setHours(0, 0, 0, 0);
        const todayCount = this.notifications.filter(n => n.createTime >= today).length;
        
        this.unreadCount = unread;
        
        document.getElementById('totalNotifications').textContent = total;
        document.getElementById('unreadNotifications').textContent = unread;
        document.getElementById('readNotifications').textContent = read;
        document.getElementById('todayNotifications').textContent = todayCount;
    }
    
    updateNotificationBadge() {
        const badge = document.getElementById('notificationBadge');
        if (badge) {
            badge.textContent = this.unreadCount;
            badge.style.display = this.unreadCount > 0 ? 'inline' : 'none';
        }
    }
    
    applyFilters() {
        this.filteredNotifications = this.notifications.filter(notification => {
            // 类型筛选
            if (this.filters.type && notification.type !== this.filters.type) {
                return false;
            }
            
            // 状态筛选
            if (this.filters.status) {
                if (this.filters.status === 'read' && !notification.isRead) return false;
                if (this.filters.status === 'unread' && notification.isRead) return false;
            }
            
            // 时间筛选
            if (this.filters.time) {
                const now = new Date();
                let startDate;
                
                switch (this.filters.time) {
                    case 'today':
                        startDate = new Date(now.getFullYear(), now.getMonth(), now.getDate());
                        break;
                    case 'week':
                        startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
                        break;
                    case 'month':
                        startDate = new Date(now.getFullYear(), now.getMonth(), 1);
                        break;
                }
                
                if (startDate && notification.createTime < startDate) {
                    return false;
                }
            }
            
            return true;
        });
        
        this.currentPage = 1;
        this.renderNotifications();
        this.renderPagination();
    }
    
    renderNotifications() {
        const container = document.getElementById('notificationsContainer');
        const startIndex = (this.currentPage - 1) * this.pageSize;
        const endIndex = startIndex + this.pageSize;
        const pageNotifications = this.filteredNotifications.slice(startIndex, endIndex);
        
        if (pageNotifications.length === 0) {
            container.innerHTML = `
                <div class="empty-state">
                    <i class="fas fa-bell-slash"></i>
                    <h3>暂无通知</h3>
                    <p>当前没有符合条件的通知</p>
                </div>
            `;
            return;
        }
        
        container.innerHTML = pageNotifications.map(notification => this.renderNotificationItem(notification)).join('');
    }
    
    renderNotificationItem(notification) {
        const timeAgo = this.getTimeAgo(notification.createTime);
        const priorityClass = notification.priority === 'high' ? 'high-priority' : 
                             notification.priority === 'medium' ? 'medium-priority' : '';
        
        return `
            <div class="notification-item ${notification.isRead ? 'read' : 'unread'} ${priorityClass}" 
                 data-notification-id="${notification.id}">
                <div class="notification-icon">
                    <i class="fas fa-${this.getNotificationIcon(notification.type)}"></i>
                </div>
                <div class="notification-content" onclick="notificationSystem.markAsRead(${notification.id})">
                    <div class="notification-header">
                        <h4 class="notification-title">${notification.title}</h4>
                        <span class="notification-time">${timeAgo}</span>
                    </div>
                    <p class="notification-message">${notification.message}</p>
                    <div class="notification-meta">
                        <span class="notification-type">${this.getTypeText(notification.type)}</span>
                        ${notification.priority === 'high' ? '<span class="priority-badge">重要</span>' : ''}
                        ${!notification.isRead ? '<span class="unread-indicator">未读</span>' : ''}
                    </div>
                </div>
                <div class="notification-actions">
                    ${notification.actionUrl ? `
                        <button class="notification-action-btn" onclick="notificationSystem.handleAction('${notification.actionUrl}')" title="查看详情">
                            <i class="fas fa-external-link-alt"></i>
                        </button>
                    ` : ''}
                    <button class="notification-action-btn" onclick="notificationSystem.toggleRead(${notification.id})" 
                            title="${notification.isRead ? '标记未读' : '标记已读'}">
                        <i class="fas fa-${notification.isRead ? 'envelope' : 'envelope-open'}"></i>
                    </button>
                    <button class="notification-action-btn danger" onclick="notificationSystem.deleteNotification(${notification.id})" title="删除">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </div>
        `;
    }
    
    renderPagination() {
        const pagination = document.getElementById('notificationsPagination');
        const totalPages = Math.ceil(this.filteredNotifications.length / this.pageSize);
        
        if (totalPages <= 1) {
            pagination.innerHTML = '';
            return;
        }
        
        let paginationHTML = `
            <div class="pagination-info">
                显示 ${(this.currentPage - 1) * this.pageSize + 1}-${Math.min(this.currentPage * this.pageSize, this.filteredNotifications.length)} 
                共 ${this.filteredNotifications.length} 条通知
            </div>
            <div class="pagination-controls">
        `;
        
        // 上一页
        paginationHTML += `
            <button class="pagination-btn ${this.currentPage === 1 ? 'disabled' : ''}" 
                    onclick="notificationSystem.goToPage(${this.currentPage - 1})" 
                    ${this.currentPage === 1 ? 'disabled' : ''}>
                <i class="fas fa-chevron-left"></i>
            </button>
        `;
        
        // 页码
        for (let i = 1; i <= totalPages; i++) {
            if (i === 1 || i === totalPages || (i >= this.currentPage - 2 && i <= this.currentPage + 2)) {
                paginationHTML += `
                    <button class="pagination-btn ${i === this.currentPage ? 'active' : ''}" 
                            onclick="notificationSystem.goToPage(${i})">
                        ${i}
                    </button>
                `;
            } else if (i === this.currentPage - 3 || i === this.currentPage + 3) {
                paginationHTML += '<span class="pagination-ellipsis">...</span>';
            }
        }
        
        // 下一页
        paginationHTML += `
            <button class="pagination-btn ${this.currentPage === totalPages ? 'disabled' : ''}" 
                    onclick="notificationSystem.goToPage(${this.currentPage + 1})" 
                    ${this.currentPage === totalPages ? 'disabled' : ''}>
                <i class="fas fa-chevron-right"></i>
            </button>
        `;
        
        paginationHTML += '</div>';
        pagination.innerHTML = paginationHTML;
    }
    
    getNotificationIcon(type) {
        const icons = {
            system: 'cog',
            order: 'shopping-cart',
            user: 'user',
            security: 'shield-alt',
            reminder: 'clock'
        };
        return icons[type] || 'bell';
    }
    
    getTypeText(type) {
        const typeTexts = {
            system: '系统',
            order: '订单',
            user: '用户',
            security: '安全',
            reminder: '提醒'
        };
        return typeTexts[type] || type;
    }
    
    getTimeAgo(date) {
        const now = new Date();
        const diffMs = now - date;
        const diffMinutes = Math.floor(diffMs / (1000 * 60));
        const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
        const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));
        
        if (diffMinutes < 1) return '刚刚';
        if (diffMinutes < 60) return `${diffMinutes}分钟前`;
        if (diffHours < 24) return `${diffHours}小时前`;
        if (diffDays < 7) return `${diffDays}天前`;
        
        return date.toLocaleDateString('zh-CN');
    }
    
    // 通知操作方法
    markAsRead(notificationId) {
        const notification = this.notifications.find(n => n.id === notificationId);
        if (notification && !notification.isRead) {
            notification.isRead = true;
            notification.readTime = new Date();
            this.updateStats();
            this.updateNotificationBadge();
            this.renderNotifications();
        }
    }
    
    toggleRead(notificationId) {
        const notification = this.notifications.find(n => n.id === notificationId);
        if (notification) {
            notification.isRead = !notification.isRead;
            notification.readTime = notification.isRead ? new Date() : null;
            this.updateStats();
            this.updateNotificationBadge();
            this.renderNotifications();
            
            showNotification(`通知已标记为${notification.isRead ? '已读' : '未读'}`, 'info');
        }
    }
    
    deleteNotification(notificationId) {
        if (confirm('确定要删除这条通知吗？')) {
            this.notifications = this.notifications.filter(n => n.id !== notificationId);
            this.updateStats();
            this.updateNotificationBadge();
            this.applyFilters();
            
            showNotification('通知已删除', 'warning');
        }
    }
    
    handleAction(actionUrl) {
        if (actionUrl) {
            // 跳转到相关页面
            const page = actionUrl.replace('#', '');
            if (window.showPage) {
                showPage(page);
            }
        }
    }
    
    markAllAsRead() {
        const unreadNotifications = this.notifications.filter(n => !n.isRead);
        if (unreadNotifications.length === 0) {
            showNotification('没有未读通知', 'info');
            return;
        }
        
        if (confirm(`确定要将所有 ${unreadNotifications.length} 条未读通知标记为已读吗？`)) {
            unreadNotifications.forEach(notification => {
                notification.isRead = true;
                notification.readTime = new Date();
            });
            
            this.updateStats();
            this.updateNotificationBadge();
            this.renderNotifications();
            
            showNotification(`已标记 ${unreadNotifications.length} 条通知为已读`, 'success');
        }
    }
    
    clearAllNotifications() {
        if (this.notifications.length === 0) {
            showNotification('没有通知可清空', 'info');
            return;
        }
        
        if (confirm(`确定要清空所有 ${this.notifications.length} 条通知吗？此操作不可撤销。`)) {
            this.notifications = [];
            this.updateStats();
            this.updateNotificationBadge();
            this.applyFilters();
            
            showNotification('所有通知已清空', 'warning');
        }
    }
    
    // 筛选方法
    setFilter(key, value) {
        this.filters[key] = value;
        this.applyFilters();
    }
    
    // 分页方法
    goToPage(page) {
        const totalPages = Math.ceil(this.filteredNotifications.length / this.pageSize);
        if (page >= 1 && page <= totalPages) {
            this.currentPage = page;
            this.renderNotifications();
            this.renderPagination();
        }
    }
    
    // 实时通知功能
    addNotification(notification) {
        const newNotification = {
            id: Date.now() + Math.random(),
            type: notification.type || 'system',
            title: notification.title,
            message: notification.message,
            createTime: new Date(),
            readTime: null,
            isRead: false,
            priority: notification.priority || 'low',
            actionUrl: notification.actionUrl || null
        };
        
        this.notifications.unshift(newNotification);
        this.updateStats();
        this.updateNotificationBadge();
        this.applyFilters();
        
        // 显示桌面通知
        this.showDesktopNotification(newNotification);
        
        // 播放声音
        if (this.soundEnabled) {
            this.playNotificationSound();
        }
        
        return newNotification;
    }
    
    showDesktopNotification(notification) {
        if (!this.isEnabled || Notification.permission !== 'granted') return;
        
        const desktopNotification = new Notification(notification.title, {
            body: notification.message,
            icon: '/favicon.ico',
            tag: `notification-${notification.id}`,
            requireInteraction: notification.priority === 'high'
        });
        
        desktopNotification.onclick = () => {
            window.focus();
            this.markAsRead(notification.id);
            if (notification.actionUrl) {
                this.handleAction(notification.actionUrl);
            }
            desktopNotification.close();
        };
        
        // 自动关闭
        setTimeout(() => {
            desktopNotification.close();
        }, 5000);
    }
    
    playNotificationSound() {
        // 创建音频上下文播放提示音
        try {
            const audioContext = new (window.AudioContext || window.webkitAudioContext)();
            const oscillator = audioContext.createOscillator();
            const gainNode = audioContext.createGain();
            
            oscillator.connect(gainNode);
            gainNode.connect(audioContext.destination);
            
            oscillator.frequency.setValueAtTime(800, audioContext.currentTime);
            gainNode.gain.setValueAtTime(0.1, audioContext.currentTime);
            gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.5);
            
            oscillator.start(audioContext.currentTime);
            oscillator.stop(audioContext.currentTime + 0.5);
        } catch (error) {
            console.log('无法播放通知声音:', error);
        }
    }
    
    requestNotificationPermission() {
        if ('Notification' in window && Notification.permission === 'default') {
            Notification.requestPermission().then(permission => {
                if (permission === 'granted') {
                    console.log('通知权限已授予');
                }
            });
        }
    }
    
    startRealTimeUpdates() {
        // 模拟实时通知
        setInterval(() => {
            if (Math.random() > 0.95) { // 5% 概率生成新通知
                const types = ['system', 'order', 'user', 'security', 'reminder'];
                const type = types[Math.floor(Math.random() * types.length)];
                
                const notifications = {
                    system: { title: '系统提醒', message: '系统运行正常' },
                    order: { title: '新订单', message: '收到新的订单' },
                    user: { title: '用户活动', message: '有新用户注册' },
                    security: { title: '安全提醒', message: '检测到登录活动' },
                    reminder: { title: '任务提醒', message: '有任务需要处理' }
                };
                
                this.addNotification({
                    type: type,
                    title: notifications[type].title,
                    message: notifications[type].message,
                    priority: Math.random() > 0.8 ? 'high' : 'low'
                });
            }
        }, 30000); // 每30秒检查一次
    }
}

// 全局通知系统实例
let notificationSystem = null;

// 通知相关的全局函数
function filterNotifications() {
    if (!notificationSystem) return;
    
    const type = document.getElementById('notificationTypeFilter').value;
    const status = document.getElementById('notificationStatusFilter').value;
    const time = document.getElementById('notificationTimeFilter').value;
    
    notificationSystem.setFilter('type', type);
    notificationSystem.setFilter('status', status);
    notificationSystem.setFilter('time', time);
}

function markAllAsRead() {
    if (notificationSystem) {
        notificationSystem.markAllAsRead();
    }
}

function clearAllNotifications() {
    if (notificationSystem) {
        notificationSystem.clearAllNotifications();
    }
}

// 初始化通知系统
function initializeNotificationSystem() {
    notificationSystem = new NotificationSystem();
    console.log('✅ 实时通知系统已初始化');
}

// 页面加载时初始化
document.addEventListener('DOMContentLoaded', function() {
    setTimeout(initializeNotificationSystem, 100);
});
