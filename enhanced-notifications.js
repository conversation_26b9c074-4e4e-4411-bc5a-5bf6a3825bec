/**
 * 增强的通知管理系统
 * 解决原有通知系统的问题，提供统一的通知管理
 */

class NotificationManager {
    constructor() {
        this.notifications = [];
        this.container = null;
        this.maxNotifications = 5;
        this.defaultDuration = 5000;
        this.init();
    }

    init() {
        this.createContainer();
        this.setupStyles();
    }

    createContainer() {
        // 检查是否已存在容器
        if (document.getElementById('notification-container')) {
            this.container = document.getElementById('notification-container');
            return;
        }

        this.container = document.createElement('div');
        this.container.id = 'notification-container';
        this.container.className = 'notification-container';
        document.body.appendChild(this.container);
    }

    setupStyles() {
        // 检查是否已添加样式
        if (document.getElementById('notification-styles')) {
            return;
        }

        const style = document.createElement('style');
        style.id = 'notification-styles';
        style.textContent = `
            .notification-container {
                position: fixed;
                top: 20px;
                right: 20px;
                z-index: 10000;
                pointer-events: none;
            }

            .notification {
                background: white;
                border-radius: 8px;
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
                margin-bottom: 12px;
                padding: 16px;
                min-width: 300px;
                max-width: 400px;
                pointer-events: auto;
                transform: translateX(100%);
                opacity: 0;
                transition: all 0.3s ease;
                border-left: 4px solid #e2e8f0;
                display: flex;
                align-items: flex-start;
                gap: 12px;
            }

            .notification.show {
                transform: translateX(0);
                opacity: 1;
            }

            .notification.hide {
                transform: translateX(100%);
                opacity: 0;
            }

            .notification-icon {
                width: 24px;
                height: 24px;
                border-radius: 50%;
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: 12px;
                color: white;
                flex-shrink: 0;
            }

            .notification-content {
                flex: 1;
                min-width: 0;
            }

            .notification-title {
                font-weight: 600;
                color: #1f2937;
                margin-bottom: 4px;
                font-size: 14px;
            }

            .notification-message {
                color: #6b7280;
                font-size: 13px;
                line-height: 1.4;
            }

            .notification-close {
                background: none;
                border: none;
                color: #9ca3af;
                cursor: pointer;
                padding: 0;
                width: 20px;
                height: 20px;
                display: flex;
                align-items: center;
                justify-content: center;
                border-radius: 4px;
                transition: all 0.2s;
                flex-shrink: 0;
            }

            .notification-close:hover {
                background: #f3f4f6;
                color: #374151;
            }

            .notification-progress {
                position: absolute;
                bottom: 0;
                left: 0;
                height: 2px;
                background: rgba(0, 0, 0, 0.1);
                border-radius: 0 0 8px 8px;
                transition: width linear;
            }

            /* 不同类型的通知样式 */
            .notification.success {
                border-left-color: #10b981;
            }

            .notification.success .notification-icon {
                background: #10b981;
            }

            .notification.error {
                border-left-color: #ef4444;
            }

            .notification.error .notification-icon {
                background: #ef4444;
            }

            .notification.warning {
                border-left-color: #f59e0b;
            }

            .notification.warning .notification-icon {
                background: #f59e0b;
            }

            .notification.info {
                border-left-color: #3b82f6;
            }

            .notification.info .notification-icon {
                background: #3b82f6;
            }

            /* 响应式设计 */
            @media (max-width: 480px) {
                .notification-container {
                    left: 20px;
                    right: 20px;
                    top: 20px;
                }

                .notification {
                    min-width: auto;
                    max-width: none;
                }
            }
        `;
        document.head.appendChild(style);
    }

    show(message, type = 'info', options = {}) {
        const notification = this.createNotification(message, type, options);
        this.addNotification(notification);
        return notification.id;
    }

    createNotification(message, type, options) {
        const id = 'notification-' + Date.now() + '-' + Math.random().toString(36).substr(2, 9);
        const duration = options.duration || this.defaultDuration;
        const title = options.title || this.getDefaultTitle(type);
        const persistent = options.persistent || false;

        const notification = {
            id,
            message,
            type,
            title,
            duration,
            persistent,
            element: null,
            timer: null
        };

        notification.element = this.createNotificationElement(notification);
        return notification;
    }

    createNotificationElement(notification) {
        const element = document.createElement('div');
        element.className = `notification ${notification.type}`;
        element.setAttribute('data-id', notification.id);

        const icon = this.getIcon(notification.type);
        
        element.innerHTML = `
            <div class="notification-icon">
                <i class="${icon}"></i>
            </div>
            <div class="notification-content">
                <div class="notification-title">${notification.title}</div>
                <div class="notification-message">${notification.message}</div>
            </div>
            <button class="notification-close" onclick="window.notificationManager.hide('${notification.id}')">
                <i class="fas fa-times"></i>
            </button>
            ${!notification.persistent ? '<div class="notification-progress"></div>' : ''}
        `;

        return element;
    }

    addNotification(notification) {
        // 限制通知数量
        if (this.notifications.length >= this.maxNotifications) {
            this.hide(this.notifications[0].id);
        }

        this.notifications.push(notification);
        this.container.appendChild(notification.element);

        // 触发显示动画
        setTimeout(() => {
            notification.element.classList.add('show');
        }, 10);

        // 设置自动隐藏
        if (!notification.persistent) {
            this.setAutoHide(notification);
        }
    }

    setAutoHide(notification) {
        const progressBar = notification.element.querySelector('.notification-progress');
        if (progressBar) {
            progressBar.style.width = '100%';
            progressBar.style.transitionDuration = notification.duration + 'ms';
            
            setTimeout(() => {
                progressBar.style.width = '0%';
            }, 10);
        }

        notification.timer = setTimeout(() => {
            this.hide(notification.id);
        }, notification.duration);
    }

    hide(id) {
        const notification = this.notifications.find(n => n.id === id);
        if (!notification) return;

        notification.element.classList.add('hide');
        
        setTimeout(() => {
            if (notification.element.parentNode) {
                notification.element.parentNode.removeChild(notification.element);
            }
            this.notifications = this.notifications.filter(n => n.id !== id);
            
            if (notification.timer) {
                clearTimeout(notification.timer);
            }
        }, 300);
    }

    hideAll() {
        this.notifications.forEach(notification => {
            this.hide(notification.id);
        });
    }

    getDefaultTitle(type) {
        const titles = {
            success: '成功',
            error: '错误',
            warning: '警告',
            info: '提示'
        };
        return titles[type] || '通知';
    }

    getIcon(type) {
        const icons = {
            success: 'fas fa-check',
            error: 'fas fa-times',
            warning: 'fas fa-exclamation',
            info: 'fas fa-info'
        };
        return icons[type] || 'fas fa-bell';
    }

    // 便捷方法
    success(message, options = {}) {
        return this.show(message, 'success', options);
    }

    error(message, options = {}) {
        return this.show(message, 'error', options);
    }

    warning(message, options = {}) {
        return this.show(message, 'warning', options);
    }

    info(message, options = {}) {
        return this.show(message, 'info', options);
    }
}

// 创建全局实例
if (typeof window !== 'undefined') {
    window.notificationManager = new NotificationManager();
    
    // 兼容旧的通知函数
    window.showNotification = function(message, type = 'info', options = {}) {
        return window.notificationManager.show(message, type, options);
    };
}

// 导出模块（如果支持）
if (typeof module !== 'undefined' && module.exports) {
    module.exports = NotificationManager;
}
