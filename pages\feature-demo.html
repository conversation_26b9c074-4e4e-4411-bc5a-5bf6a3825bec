<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>功能演示 - 企业管理系统</title>
    <link rel="stylesheet" href="../assets/css/styles.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        body {
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .demo-container {
            max-width: 1400px;
            margin: 0 auto;
        }
        .demo-title {
            color: white;
            text-align: center;
            margin-bottom: 32px;
            font-size: 36px;
            font-weight: 700;
        }
        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 24px;
            margin-bottom: 32px;
        }
        .feature-card {
            background: rgba(255, 255, 255, 0.98);
            border-radius: 16px;
            padding: 24px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
            transition: transform 0.3s ease;
        }
        .feature-card:hover {
            transform: translateY(-4px);
        }
        .feature-header {
            display: flex;
            align-items: center;
            margin-bottom: 16px;
        }
        .feature-icon {
            width: 48px;
            height: 48px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 16px;
            font-size: 20px;
            color: white;
        }
        .feature-title {
            font-size: 20px;
            font-weight: 600;
            color: #1f2937;
        }
        .feature-description {
            color: #6b7280;
            margin-bottom: 20px;
            line-height: 1.6;
        }
        .demo-buttons {
            display: flex;
            gap: 12px;
            flex-wrap: wrap;
        }
        .demo-btn {
            padding: 8px 16px;
            border: none;
            border-radius: 8px;
            background: #667eea;
            color: white;
            font-size: 14px;
            cursor: pointer;
            transition: background 0.3s;
        }
        .demo-btn:hover {
            background: #5a6fd8;
        }
        .demo-btn.secondary {
            background: #6b7280;
        }
        .demo-btn.secondary:hover {
            background: #4b5563;
        }
        .status-indicator {
            display: inline-block;
            width: 8px;
            height: 8px;
            border-radius: 50%;
            margin-right: 8px;
        }
        .status-working { background: #10b981; }
        .status-partial { background: #f59e0b; }
        .status-planned { background: #6b7280; }
    </style>
</head>
<body>
    <div class="demo-container">
        <h1 class="demo-title">🚀 企业管理系统功能演示</h1>
        
        <div class="feature-grid">
            <!-- 数据可视化 -->
            <div class="feature-card">
                <div class="feature-header">
                    <div class="feature-icon" style="background: var(--gradient-primary);">
                        <i class="fas fa-chart-line"></i>
                    </div>
                    <div>
                        <h3 class="feature-title">数据可视化</h3>
                        <span class="status-indicator status-working"></span>
                        <span style="color: #10b981; font-size: 12px;">已实现</span>
                    </div>
                </div>
                <p class="feature-description">
                    使用Chart.js实现的专业图表系统，包括销售趋势、用户增长、收入分析等多种图表类型。
                </p>
                <div class="demo-buttons">
                    <button class="demo-btn" onclick="testCharts()">测试图表</button>
                    <button class="demo-btn secondary" onclick="openDashboard()">查看仪表盘</button>
                </div>
            </div>

            <!-- 用户管理 -->
            <div class="feature-card">
                <div class="feature-header">
                    <div class="feature-icon" style="background: var(--gradient-success);">
                        <i class="fas fa-users"></i>
                    </div>
                    <div>
                        <h3 class="feature-title">用户管理</h3>
                        <span class="status-indicator status-working"></span>
                        <span style="color: #10b981; font-size: 12px;">已实现</span>
                    </div>
                </div>
                <p class="feature-description">
                    完整的用户管理系统，包括用户增删改查、批量操作、搜索筛选、数据导出等功能。
                </p>
                <div class="demo-buttons">
                    <button class="demo-btn" onclick="testUserManagement()">测试用户管理</button>
                    <button class="demo-btn secondary" onclick="showUserStats()">用户统计</button>
                </div>
            </div>

            <!-- 订单管理 -->
            <div class="feature-card">
                <div class="feature-header">
                    <div class="feature-icon" style="background: var(--gradient-warning);">
                        <i class="fas fa-shopping-cart"></i>
                    </div>
                    <div>
                        <h3 class="feature-title">订单管理</h3>
                        <span class="status-indicator status-working"></span>
                        <span style="color: #10b981; font-size: 12px;">已实现</span>
                    </div>
                </div>
                <p class="feature-description">
                    订单全生命周期管理，支持高级搜索、批量操作、状态跟踪、数据分析等功能。
                </p>
                <div class="demo-buttons">
                    <button class="demo-btn" onclick="testOrderManagement()">测试订单管理</button>
                    <button class="demo-btn secondary" onclick="showOrderStats()">订单统计</button>
                </div>
            </div>

            <!-- 库存管理 -->
            <div class="feature-card">
                <div class="feature-header">
                    <div class="feature-icon" style="background: var(--gradient-danger);">
                        <i class="fas fa-boxes"></i>
                    </div>
                    <div>
                        <h3 class="feature-title">库存管理</h3>
                        <span class="status-indicator status-partial"></span>
                        <span style="color: #f59e0b; font-size: 12px;">部分实现</span>
                    </div>
                </div>
                <p class="feature-description">
                    商品库存管理，包括入库出库、库存预警、盘点管理等功能。
                </p>
                <div class="demo-buttons">
                    <button class="demo-btn" onclick="testInventory()">测试库存</button>
                    <button class="demo-btn secondary" onclick="showInventoryAlerts()">库存预警</button>
                </div>
            </div>

            <!-- 报表中心 -->
            <div class="feature-card">
                <div class="feature-header">
                    <div class="feature-icon" style="background: linear-gradient(135deg, #8b5cf6, #a855f7);">
                        <i class="fas fa-chart-bar"></i>
                    </div>
                    <div>
                        <h3 class="feature-title">报表中心</h3>
                        <span class="status-indicator status-partial"></span>
                        <span style="color: #f59e0b; font-size: 12px;">部分实现</span>
                    </div>
                </div>
                <p class="feature-description">
                    多维度业务报表生成，支持自定义报表、数据导出、定时报表等功能。
                </p>
                <div class="demo-buttons">
                    <button class="demo-btn" onclick="generateReport()">生成报表</button>
                    <button class="demo-btn secondary" onclick="showReportTemplates()">报表模板</button>
                </div>
            </div>

            <!-- 通知系统 -->
            <div class="feature-card">
                <div class="feature-header">
                    <div class="feature-icon" style="background: linear-gradient(135deg, #06b6d4, #0891b2);">
                        <i class="fas fa-bell"></i>
                    </div>
                    <div>
                        <h3 class="feature-title">通知系统</h3>
                        <span class="status-indicator status-working"></span>
                        <span style="color: #10b981; font-size: 12px;">已实现</span>
                    </div>
                </div>
                <p class="feature-description">
                    实时通知系统，支持多种通知类型、消息中心、通知设置等功能。
                </p>
                <div class="demo-buttons">
                    <button class="demo-btn" onclick="testNotifications()">测试通知</button>
                    <button class="demo-btn secondary" onclick="showNotificationCenter()">通知中心</button>
                </div>
            </div>
        </div>

        <!-- 系统状态 -->
        <div class="feature-card" style="margin-bottom: 32px;">
            <div class="feature-header">
                <div class="feature-icon" style="background: linear-gradient(135deg, #10b981, #059669);">
                    <i class="fas fa-cogs"></i>
                </div>
                <div>
                    <h3 class="feature-title">系统状态总览</h3>
                </div>
            </div>
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 16px;">
                <div style="text-align: center; padding: 16px; background: #f8fafc; border-radius: 8px;">
                    <div style="font-size: 24px; font-weight: 700; color: #10b981;">85%</div>
                    <div style="color: #6b7280; font-size: 14px;">功能完成度</div>
                </div>
                <div style="text-align: center; padding: 16px; background: #f8fafc; border-radius: 8px;">
                    <div style="font-size: 24px; font-weight: 700; color: #667eea;">16</div>
                    <div style="color: #6b7280; font-size: 14px;">核心模块</div>
                </div>
                <div style="text-align: center; padding: 16px; background: #f8fafc; border-radius: 8px;">
                    <div style="font-size: 24px; font-weight: 700; color: #f59e0b;">3</div>
                    <div style="color: #6b7280; font-size: 14px;">待完善功能</div>
                </div>
                <div style="text-align: center; padding: 16px; background: #f8fafc; border-radius: 8px;">
                    <div style="font-size: 24px; font-weight: 700; color: #ef4444;">正常</div>
                    <div style="color: #6b7280; font-size: 14px;">系统状态</div>
                </div>
            </div>
        </div>

        <!-- 返回按钮 -->
        <div style="text-align: center;">
            <button class="demo-btn" style="padding: 12px 24px; font-size: 16px;" onclick="goBack()">
                <i class="fas fa-arrow-left"></i> 返回主系统
            </button>
        </div>
    </div>

    <script src="../js/utils/chart-initializer.js"></script>
    <script>
        // 测试图表功能
        function testCharts() {
            if (typeof initializeDashboardCharts === 'function') {
                showNotification('图表系统正常工作！', 'success');
                // 可以在这里创建一个小的演示图表
            } else {
                showNotification('图表系统未加载', 'error');
            }
        }

        // 测试用户管理
        function testUserManagement() {
            showNotification('用户管理功能包括：增删改查、批量操作、搜索筛选、数据导出', 'info');
        }

        // 测试订单管理
        function testOrderManagement() {
            showNotification('订单管理功能包括：订单跟踪、状态管理、高级搜索、批量操作', 'info');
        }

        // 测试通知系统
        function testNotifications() {
            showNotification('这是一个成功通知示例', 'success');
            setTimeout(() => showNotification('这是一个警告通知示例', 'warning'), 1000);
            setTimeout(() => showNotification('这是一个信息通知示例', 'info'), 2000);
        }

        // 返回主系统
        function goBack() {
            window.location.href = 'index.html';
        }

        // 简单的通知函数
        function showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: white;
                padding: 16px 20px;
                border-radius: 8px;
                box-shadow: 0 4px 12px rgba(0,0,0,0.15);
                border-left: 4px solid ${type === 'success' ? '#10b981' : type === 'warning' ? '#f59e0b' : type === 'error' ? '#ef4444' : '#3b82f6'};
                z-index: 10000;
                max-width: 300px;
                animation: slideIn 0.3s ease;
            `;
            notification.innerHTML = `
                <div style="display: flex; align-items: center; gap: 8px;">
                    <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'warning' ? 'exclamation-triangle' : type === 'error' ? 'times-circle' : 'info-circle'}" style="color: ${type === 'success' ? '#10b981' : type === 'warning' ? '#f59e0b' : type === 'error' ? '#ef4444' : '#3b82f6'};"></i>
                    <span style="color: #374151;">${message}</span>
                </div>
            `;
            
            document.body.appendChild(notification);
            
            setTimeout(() => {
                notification.style.animation = 'slideOut 0.3s ease';
                setTimeout(() => notification.remove(), 300);
            }, 3000);
        }

        // 添加动画样式
        const style = document.createElement('style');
        style.textContent = `
            @keyframes slideIn {
                from { transform: translateX(100%); opacity: 0; }
                to { transform: translateX(0); opacity: 1; }
            }
            @keyframes slideOut {
                from { transform: translateX(0); opacity: 1; }
                to { transform: translateX(100%); opacity: 0; }
            }
        `;
        document.head.appendChild(style);

        // 其他演示函数
        function openDashboard() { window.location.href = 'index.html#dashboard'; }
        function showUserStats() { showNotification('用户统计：总用户数 2,847，活跃用户 2,234', 'info'); }
        function showOrderStats() { showNotification('订单统计：总订单 1,234，今日新增 89', 'info'); }
        function testInventory() { showNotification('库存管理功能正在完善中...', 'warning'); }
        function showInventoryAlerts() { showNotification('发现 3 个商品库存不足', 'warning'); }
        function generateReport() { showNotification('报表生成功能正在开发中...', 'info'); }
        function showReportTemplates() { showNotification('报表模板功能正在开发中...', 'info'); }
        function showNotificationCenter() { showNotification('通知中心功能已实现', 'success'); }
    </script>
</body>
</html>
