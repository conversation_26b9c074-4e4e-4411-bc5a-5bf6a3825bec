# 系统整合完成指南

## 🎉 整合成果

成功将**企业管理系统**和**NER数据生成器**整合为一个统一的平台！

### 新增功能
- ✅ NER数据管理页面
- ✅ RESTful API后端服务
- ✅ 实时数据统计和可视化
- ✅ 多格式数据导出
- ✅ 一键启动脚本

## 🚀 快速启动

### 1. 安装依赖
```bash
pip install -r requirements.txt
```

### 2. 启动系统
```bash
python start_system.py
```

### 3. 访问系统
- 前端: http://localhost:8000/login.html
- API: http://localhost:5000/api/health

## 📋 测试系统

运行集成测试：
```bash
python test_integration.py
```

## 🎯 使用流程

### 1. 登录系统
- 访问登录页面
- 使用测试账号：`admin` / `admin123`

### 2. 访问NER功能
- 登录后点击侧边栏"NER数据管理"
- 配置生成参数（样本数量、实体类型等）
- 点击"生成数据"按钮

### 3. 查看结果
- 实时统计图表更新
- 数据预览显示样本
- 可下载多种格式的数据

## 📁 文件结构

```
├── 前端文件
│   ├── index.html          # 主页面（已添加NER页面）
│   ├── login.html          # 登录页面
│   ├── app.js              # 主应用逻辑（已集成NER）
│   ├── ner-management.js   # NER管理功能
│   └── styles.css          # 样式文件（已添加NER样式）
│
├── 后端文件
│   ├── ner_api.py          # NER API服务
│   ├── ner_data_generator.py # NER数据生成器
│   └── requirements.txt    # Python依赖
│
├── 系统文件
│   ├── start_system.py     # 一键启动脚本
│   ├── test_integration.py # 集成测试脚本
│   └── README.md           # 更新的说明文档
```

## 🔧 API接口

### 生成数据
```http
POST /api/ner/generate
Content-Type: application/json

{
  "sample_count": 1000,
  "entity_types": {
    "person": true,
    "location": true,
    "organization": true,
    "time": true,
    "number": true
  }
}
```

### 获取统计
```http
GET /api/ner/stats
```

### 下载数据
```http
GET /api/ner/download?format=json
```

## 🎨 界面特性

### NER管理页面包含：
- 📊 实时统计卡片（总样本数、各类实体数量）
- ⚙️ 配置面板（样本数量、实体类型选择）
- 👀 数据预览（高亮显示实体）
- 📈 可视化图表（饼图、柱状图、环形图）
- 💾 导出功能（JSON、CoNLL、BIO格式）

### 实体高亮颜色：
- 🟡 人名 (PERSON) - 黄色
- 🔵 地名 (LOCATION) - 蓝色  
- 🟢 机构 (ORGANIZATION) - 绿色
- 🟣 时间 (TIME) - 紫色
- 🟠 数字 (NUMBER) - 橙色

## 🔍 故障排除

### 常见问题

1. **API服务无法启动**
   - 检查端口5000是否被占用
   - 确认Flask依赖已安装

2. **前端无法连接API**
   - 确认API服务正在运行
   - 检查浏览器控制台错误信息

3. **数据生成失败**
   - 检查样本数量是否在合理范围(100-50000)
   - 确认至少选择一种实体类型

### 调试模式

启动调试模式：
```bash
# 分别启动服务进行调试
python ner_api.py  # 终端1
python -m http.server 8000  # 终端2
```

## 🎯 下一步扩展

可以考虑的功能扩展：
- 🔄 数据导入功能
- 📝 自定义实体类型
- 🤖 模型训练集成
- 📊 更多统计图表
- 🔐 用户权限管理
- 💾 数据库存储

## 📞 技术支持

如遇问题，请检查：
1. Python版本 >= 3.7
2. 所有依赖已正确安装
3. 端口5000和8000未被占用
4. 浏览器支持现代JavaScript特性

---

🎉 **恭喜！系统整合完成，开始体验全新的NER数据管理功能吧！**
