<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>简单登录测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 400px;
            margin: 50px auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .login-container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 16px;
            box-sizing: border-box;
        }
        button {
            width: 100%;
            padding: 12px;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 5px;
            font-size: 16px;
            cursor: pointer;
        }
        button:hover {
            background: #0056b3;
        }
        .test-accounts {
            margin-top: 20px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 5px;
        }
        .test-accounts h3 {
            margin-top: 0;
            color: #333;
        }
        .account {
            margin: 10px 0;
            padding: 8px;
            background: white;
            border-radius: 3px;
            cursor: pointer;
            border: 1px solid #ddd;
        }
        .account:hover {
            background: #e9ecef;
        }
        .message {
            margin: 10px 0;
            padding: 10px;
            border-radius: 5px;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
    </style>
</head>
<body>
    <div class="login-container">
        <h2>简单登录测试</h2>
        
        <form id="loginForm">
            <div class="form-group">
                <label for="username">用户名:</label>
                <input type="text" id="username" name="username" required>
            </div>
            
            <div class="form-group">
                <label for="password">密码:</label>
                <input type="password" id="password" name="password" required>
            </div>
            
            <button type="submit">登录</button>
        </form>
        
        <div id="message"></div>
        
        <div class="test-accounts">
            <h3>测试账号 (点击自动填入)</h3>
            <div class="account" onclick="fillLogin('admin', 'admin123')">
                <strong>admin</strong> / admin123 (系统管理员)
            </div>
            <div class="account" onclick="fillLogin('manager', 'manager123')">
                <strong>manager</strong> / manager123 (部门经理)
            </div>
            <div class="account" onclick="fillLogin('user', 'user123')">
                <strong>user</strong> / user123 (普通用户)
            </div>
            <div class="account" onclick="fillLogin('test', 'test123')">
                <strong>test</strong> / test123 (测试用户)
            </div>
            <div class="account" onclick="fillLogin('demo', 'demo123')">
                <strong>demo</strong> / demo123 (演示用户)
            </div>
        </div>
        
        <div style="margin-top: 20px;">
            <button type="button" onclick="openDashboard()" style="background: #28a745;">
                打开后台管理
            </button>
        </div>
    </div>

    <script>
        // 测试账号数据
        const TEST_ACCOUNTS = [
            { username: 'admin', password: 'admin123', role: '系统管理员', name: '张管理员' },
            { username: 'manager', password: 'manager123', role: '部门经理', name: '李经理' },
            { username: 'user', password: 'user123', role: '普通用户', name: '王用户' },
            { username: 'test', password: 'test123', role: '测试用户', name: '测试员' },
            { username: 'demo', password: 'demo123', role: '演示用户', name: '演示员' }
        ];

        // 显示消息
        function showMessage(text, type = 'success') {
            const messageDiv = document.getElementById('message');
            messageDiv.innerHTML = `<div class="message ${type}">${text}</div>`;
        }

        // 填入登录信息
        function fillLogin(username, password) {
            document.getElementById('username').value = username;
            document.getElementById('password').value = password;
            showMessage(`已填入账号: ${username}`, 'success');
        }

        // 处理登录
        function handleLogin(username, password) {
            console.log('处理登录:', username, password);
            
            // 查找匹配的账号
            const validAccount = TEST_ACCOUNTS.find(account => 
                account.username === username && account.password === password
            );

            if (validAccount) {
                // 保存登录状态
                localStorage.setItem('isLoggedIn', 'true');
                localStorage.setItem('currentUser', JSON.stringify({
                    username: validAccount.username,
                    name: validAccount.name,
                    role: validAccount.role
                }));
                
                showMessage(`登录成功！欢迎 ${validAccount.name}`, 'success');
                
                // 1秒后自动跳转
                setTimeout(() => {
                    window.location.href = 'index.html';
                }, 1000);
                
                return true;
            } else {
                showMessage('用户名或密码错误！', 'error');
                return false;
            }
        }

        // 打开后台管理
        function openDashboard() {
            window.open('index.html', '_blank');
        }

        // 页面初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('页面加载完成');
            
            // 绑定表单提交事件
            const form = document.getElementById('loginForm');
            form.addEventListener('submit', function(e) {
                e.preventDefault();
                console.log('表单提交');
                
                const username = document.getElementById('username').value.trim();
                const password = document.getElementById('password').value;
                
                if (!username || !password) {
                    showMessage('请填写用户名和密码！', 'error');
                    return;
                }
                
                handleLogin(username, password);
            });
            
            // 检查当前登录状态
            const isLoggedIn = localStorage.getItem('isLoggedIn');
            if (isLoggedIn === 'true') {
                const currentUser = JSON.parse(localStorage.getItem('currentUser') || '{}');
                showMessage(`当前已登录: ${currentUser.name || '未知用户'}`, 'success');
            }
        });
    </script>
</body>
</html>
