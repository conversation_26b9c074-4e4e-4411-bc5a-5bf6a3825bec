// 高级安全中心
class SecurityCenter {
    constructor() {
        this.securityEvents = [];
        this.threats = [];
        this.vulnerabilities = [];
        this.complianceRules = [];
        this.securityPolicies = [];
        this.auditLogs = [];
        this.riskAssessments = [];
        this.securityMetrics = {};
        this.alertRules = [];
        this.incidentResponses = [];
        
        this.initializeSecurityCenter();
        this.setupSecurityMonitoring();
        this.loadSecurityPolicies();
        this.startThreatDetection();
    }

    initializeSecurityCenter() {
        this.createSecurityInterface();
        this.bindSecurityEvents();
        this.setupRealTimeMonitoring();
        this.loadSecurityData();
    }

    createSecurityInterface() {
        const securityPanel = document.createElement('div');
        securityPanel.id = 'securityPanel';
        securityPanel.className = 'security-panel';
        securityPanel.innerHTML = `
            <div class="security-header">
                <h3>
                    <i class="fas fa-shield-alt"></i>
                    安全中心
                </h3>
                <div class="security-controls">
                    <div class="security-status" id="securityStatus">
                        <div class="status-indicator secure">
                            <i class="fas fa-check-circle"></i>
                            <span>系统安全</span>
                        </div>
                    </div>
                    <button class="btn-secondary" onclick="securityCenter.runSecurityScan()">
                        <i class="fas fa-search"></i>
                        安全扫描
                    </button>
                    <button class="btn-secondary" onclick="securityCenter.generateSecurityReport()">
                        <i class="fas fa-file-alt"></i>
                        安全报告
                    </button>
                    <button class="btn-primary" onclick="securityCenter.showIncidentResponse()">
                        <i class="fas fa-exclamation-triangle"></i>
                        应急响应
                    </button>
                    <button class="btn-icon" onclick="securityCenter.closeSecurityPanel()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            </div>
            
            <div class="security-body">
                <div class="security-tabs">
                    <button class="tab-btn active" data-tab="dashboard">
                        <i class="fas fa-tachometer-alt"></i>
                        安全仪表板
                    </button>
                    <button class="tab-btn" data-tab="threats">
                        <i class="fas fa-bug"></i>
                        威胁检测
                    </button>
                    <button class="tab-btn" data-tab="compliance">
                        <i class="fas fa-clipboard-check"></i>
                        合规管理
                    </button>
                    <button class="tab-btn" data-tab="policies">
                        <i class="fas fa-shield-alt"></i>
                        安全策略
                    </button>
                    <button class="tab-btn" data-tab="audit">
                        <i class="fas fa-history"></i>
                        审计日志
                    </button>
                    <button class="tab-btn" data-tab="encryption">
                        <i class="fas fa-lock"></i>
                        数据加密
                    </button>
                </div>
                
                <div class="security-content">
                    <!-- 安全仪表板 -->
                    <div class="tab-content active" id="dashboardTab">
                        <div class="security-overview">
                            <div class="security-metrics">
                                <div class="metric-card threat-level">
                                    <div class="metric-icon">
                                        <i class="fas fa-exclamation-triangle"></i>
                                    </div>
                                    <div class="metric-content">
                                        <h4>威胁等级</h4>
                                        <div class="metric-value" id="threatLevel">低</div>
                                        <div class="metric-trend good">
                                            <i class="fas fa-arrow-down"></i>
                                            <span>-15%</span>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="metric-card security-score">
                                    <div class="metric-icon">
                                        <i class="fas fa-shield-alt"></i>
                                    </div>
                                    <div class="metric-content">
                                        <h4>安全评分</h4>
                                        <div class="metric-value" id="securityScore">92</div>
                                        <div class="metric-trend good">
                                            <i class="fas fa-arrow-up"></i>
                                            <span>+3%</span>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="metric-card active-threats">
                                    <div class="metric-icon">
                                        <i class="fas fa-bug"></i>
                                    </div>
                                    <div class="metric-content">
                                        <h4>活跃威胁</h4>
                                        <div class="metric-value" id="activeThreats">2</div>
                                        <div class="metric-trend warning">
                                            <i class="fas fa-arrow-up"></i>
                                            <span>+1</span>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="metric-card compliance-rate">
                                    <div class="metric-icon">
                                        <i class="fas fa-clipboard-check"></i>
                                    </div>
                                    <div class="metric-content">
                                        <h4>合规率</h4>
                                        <div class="metric-value" id="complianceRate">98%</div>
                                        <div class="metric-trend good">
                                            <i class="fas fa-check"></i>
                                            <span>合规</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="security-charts">
                                <div class="chart-container">
                                    <h4>安全事件趋势</h4>
                                    <canvas id="securityTrendChart" width="400" height="200"></canvas>
                                </div>
                                
                                <div class="chart-container">
                                    <h4>威胁类型分布</h4>
                                    <canvas id="threatDistributionChart" width="400" height="200"></canvas>
                                </div>
                            </div>
                            
                            <div class="recent-events">
                                <h4>最近安全事件</h4>
                                <div class="events-list" id="recentEventsList">
                                    <!-- 最近安全事件列表 -->
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 威胁检测 -->
                    <div class="tab-content" id="threatsTab">
                        <div class="threats-section">
                            <div class="section-header">
                                <h4>威胁检测与分析</h4>
                                <div class="threat-controls">
                                    <button class="btn-secondary" onclick="securityCenter.configureThreatRules()">
                                        <i class="fas fa-cog"></i>
                                        配置规则
                                    </button>
                                    <button class="btn-primary" onclick="securityCenter.startThreatScan()">
                                        <i class="fas fa-search"></i>
                                        开始扫描
                                    </button>
                                </div>
                            </div>
                            
                            <div class="threat-filters">
                                <select id="threatSeverityFilter">
                                    <option value="all">所有严重程度</option>
                                    <option value="critical">严重</option>
                                    <option value="high">高</option>
                                    <option value="medium">中</option>
                                    <option value="low">低</option>
                                </select>
                                <select id="threatTypeFilter">
                                    <option value="all">所有威胁类型</option>
                                    <option value="malware">恶意软件</option>
                                    <option value="phishing">钓鱼攻击</option>
                                    <option value="bruteforce">暴力破解</option>
                                    <option value="injection">注入攻击</option>
                                    <option value="ddos">DDoS攻击</option>
                                </select>
                                <select id="threatStatusFilter">
                                    <option value="all">所有状态</option>
                                    <option value="active">活跃</option>
                                    <option value="mitigated">已缓解</option>
                                    <option value="resolved">已解决</option>
                                </select>
                            </div>
                            
                            <div class="threats-list" id="threatsList">
                                <!-- 威胁列表 -->
                            </div>
                        </div>
                    </div>
                    
                    <!-- 合规管理 -->
                    <div class="tab-content" id="complianceTab">
                        <div class="compliance-section">
                            <div class="section-header">
                                <h4>合规性检查</h4>
                                <div class="compliance-controls">
                                    <button class="btn-secondary" onclick="securityCenter.addComplianceRule()">
                                        <i class="fas fa-plus"></i>
                                        添加规则
                                    </button>
                                    <button class="btn-primary" onclick="securityCenter.runComplianceCheck()">
                                        <i class="fas fa-check"></i>
                                        运行检查
                                    </button>
                                </div>
                            </div>
                            
                            <div class="compliance-frameworks">
                                <h5>合规框架</h5>
                                <div class="frameworks-grid">
                                    <div class="framework-card">
                                        <div class="framework-icon">
                                            <i class="fas fa-shield-alt"></i>
                                        </div>
                                        <div class="framework-info">
                                            <h6>ISO 27001</h6>
                                            <div class="compliance-status compliant">
                                                <i class="fas fa-check-circle"></i>
                                                <span>合规</span>
                                            </div>
                                            <div class="compliance-score">95%</div>
                                        </div>
                                    </div>
                                    
                                    <div class="framework-card">
                                        <div class="framework-icon">
                                            <i class="fas fa-user-shield"></i>
                                        </div>
                                        <div class="framework-info">
                                            <h6>GDPR</h6>
                                            <div class="compliance-status compliant">
                                                <i class="fas fa-check-circle"></i>
                                                <span>合规</span>
                                            </div>
                                            <div class="compliance-score">98%</div>
                                        </div>
                                    </div>
                                    
                                    <div class="framework-card">
                                        <div class="framework-icon">
                                            <i class="fas fa-credit-card"></i>
                                        </div>
                                        <div class="framework-info">
                                            <h6>PCI DSS</h6>
                                            <div class="compliance-status warning">
                                                <i class="fas fa-exclamation-triangle"></i>
                                                <span>需要关注</span>
                                            </div>
                                            <div class="compliance-score">87%</div>
                                        </div>
                                    </div>
                                    
                                    <div class="framework-card">
                                        <div class="framework-icon">
                                            <i class="fas fa-hospital"></i>
                                        </div>
                                        <div class="framework-info">
                                            <h6>HIPAA</h6>
                                            <div class="compliance-status non-compliant">
                                                <i class="fas fa-times-circle"></i>
                                                <span>不合规</span>
                                            </div>
                                            <div class="compliance-score">72%</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="compliance-rules">
                                <h5>合规规则</h5>
                                <div class="rules-list" id="complianceRulesList">
                                    <!-- 合规规则列表 -->
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 安全策略 -->
                    <div class="tab-content" id="policiesTab">
                        <div class="policies-section">
                            <div class="section-header">
                                <h4>安全策略管理</h4>
                                <div class="policy-controls">
                                    <button class="btn-secondary" onclick="securityCenter.importPolicy()">
                                        <i class="fas fa-upload"></i>
                                        导入策略
                                    </button>
                                    <button class="btn-primary" onclick="securityCenter.createPolicy()">
                                        <i class="fas fa-plus"></i>
                                        创建策略
                                    </button>
                                </div>
                            </div>
                            
                            <div class="policy-categories">
                                <div class="category-tabs">
                                    <button class="category-tab active" data-category="access">访问控制</button>
                                    <button class="category-tab" data-category="password">密码策略</button>
                                    <button class="category-tab" data-category="network">网络安全</button>
                                    <button class="category-tab" data-category="data">数据保护</button>
                                    <button class="category-tab" data-category="incident">事件响应</button>
                                </div>
                                
                                <div class="category-content">
                                    <div class="policies-list" id="policiesList">
                                        <!-- 安全策略列表 -->
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 审计日志 -->
                    <div class="tab-content" id="auditTab">
                        <div class="audit-section">
                            <div class="section-header">
                                <h4>审计日志</h4>
                                <div class="audit-controls">
                                    <input type="date" id="auditStartDate" class="date-input">
                                    <input type="date" id="auditEndDate" class="date-input">
                                    <select id="auditEventType">
                                        <option value="all">所有事件</option>
                                        <option value="login">登录事件</option>
                                        <option value="access">访问事件</option>
                                        <option value="change">变更事件</option>
                                        <option value="security">安全事件</option>
                                    </select>
                                    <button class="btn-secondary" onclick="securityCenter.exportAuditLog()">
                                        <i class="fas fa-download"></i>
                                        导出日志
                                    </button>
                                    <button class="btn-primary" onclick="securityCenter.searchAuditLog()">
                                        <i class="fas fa-search"></i>
                                        搜索
                                    </button>
                                </div>
                            </div>
                            
                            <div class="audit-stats">
                                <div class="stat-card">
                                    <div class="stat-icon">
                                        <i class="fas fa-sign-in-alt"></i>
                                    </div>
                                    <div class="stat-content">
                                        <div class="stat-value">1,247</div>
                                        <div class="stat-label">登录事件</div>
                                    </div>
                                </div>
                                
                                <div class="stat-card">
                                    <div class="stat-icon">
                                        <i class="fas fa-key"></i>
                                    </div>
                                    <div class="stat-content">
                                        <div class="stat-value">89</div>
                                        <div class="stat-label">权限变更</div>
                                    </div>
                                </div>
                                
                                <div class="stat-card">
                                    <div class="stat-icon">
                                        <i class="fas fa-exclamation-triangle"></i>
                                    </div>
                                    <div class="stat-content">
                                        <div class="stat-value">12</div>
                                        <div class="stat-label">安全事件</div>
                                    </div>
                                </div>
                                
                                <div class="stat-card">
                                    <div class="stat-icon">
                                        <i class="fas fa-ban"></i>
                                    </div>
                                    <div class="stat-content">
                                        <div class="stat-value">5</div>
                                        <div class="stat-label">访问拒绝</div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="audit-logs" id="auditLogsList">
                                <!-- 审计日志列表 -->
                            </div>
                        </div>
                    </div>
                    
                    <!-- 数据加密 -->
                    <div class="tab-content" id="encryptionTab">
                        <div class="encryption-section">
                            <div class="section-header">
                                <h4>数据加密管理</h4>
                                <div class="encryption-controls">
                                    <button class="btn-secondary" onclick="securityCenter.generateKey()">
                                        <i class="fas fa-key"></i>
                                        生成密钥
                                    </button>
                                    <button class="btn-primary" onclick="securityCenter.encryptData()">
                                        <i class="fas fa-lock"></i>
                                        加密数据
                                    </button>
                                </div>
                            </div>
                            
                            <div class="encryption-overview">
                                <div class="encryption-status">
                                    <h5>加密状态概览</h5>
                                    <div class="status-grid">
                                        <div class="status-item">
                                            <div class="status-icon encrypted">
                                                <i class="fas fa-lock"></i>
                                            </div>
                                            <div class="status-info">
                                                <div class="status-label">数据库加密</div>
                                                <div class="status-value">已启用</div>
                                            </div>
                                        </div>
                                        
                                        <div class="status-item">
                                            <div class="status-icon encrypted">
                                                <i class="fas fa-shield-alt"></i>
                                            </div>
                                            <div class="status-info">
                                                <div class="status-label">传输加密</div>
                                                <div class="status-value">TLS 1.3</div>
                                            </div>
                                        </div>
                                        
                                        <div class="status-item">
                                            <div class="status-icon encrypted">
                                                <i class="fas fa-file-alt"></i>
                                            </div>
                                            <div class="status-info">
                                                <div class="status-label">文件加密</div>
                                                <div class="status-value">AES-256</div>
                                            </div>
                                        </div>
                                        
                                        <div class="status-item">
                                            <div class="status-icon warning">
                                                <i class="fas fa-exclamation-triangle"></i>
                                            </div>
                                            <div class="status-info">
                                                <div class="status-label">备份加密</div>
                                                <div class="status-value">部分加密</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="key-management">
                                    <h5>密钥管理</h5>
                                    <div class="keys-list" id="encryptionKeysList">
                                        <!-- 加密密钥列表 -->
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
        
        document.body.appendChild(securityPanel);
        
        // 创建应急响应界面
        this.createIncidentResponseInterface();
        
        // 创建安全扫描界面
        this.createSecurityScanInterface();
        
        // 创建策略编辑器
        this.createPolicyEditor();
    }

    createIncidentResponseInterface() {
        const incidentResponse = document.createElement('div');
        incidentResponse.id = 'incidentResponseInterface';
        incidentResponse.className = 'incident-response-modal';
        incidentResponse.innerHTML = `
            <div class="modal-content large">
                <div class="modal-header">
                    <h3>安全事件应急响应</h3>
                    <button class="modal-close" onclick="securityCenter.closeIncidentResponse()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="modal-body">
                    <div class="incident-response-content">
                        <div class="response-sidebar">
                            <div class="incident-severity">
                                <h4>事件严重程度</h4>
                                <div class="severity-levels">
                                    <div class="severity-level critical">
                                        <div class="severity-indicator"></div>
                                        <span>严重 (P1)</span>
                                    </div>
                                    <div class="severity-level high">
                                        <div class="severity-indicator"></div>
                                        <span>高 (P2)</span>
                                    </div>
                                    <div class="severity-level medium">
                                        <div class="severity-indicator"></div>
                                        <span>中 (P3)</span>
                                    </div>
                                    <div class="severity-level low">
                                        <div class="severity-indicator"></div>
                                        <span>低 (P4)</span>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="response-templates">
                                <h4>响应模板</h4>
                                <div class="template-list">
                                    <div class="template-item" data-template="malware">
                                        <i class="fas fa-bug"></i>
                                        <span>恶意软件响应</span>
                                    </div>
                                    <div class="template-item" data-template="breach">
                                        <i class="fas fa-shield-alt"></i>
                                        <span>数据泄露响应</span>
                                    </div>
                                    <div class="template-item" data-template="ddos">
                                        <i class="fas fa-server"></i>
                                        <span>DDoS攻击响应</span>
                                    </div>
                                    <div class="template-item" data-template="phishing">
                                        <i class="fas fa-fish"></i>
                                        <span>钓鱼攻击响应</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="response-main">
                            <div class="incident-form">
                                <div class="form-group">
                                    <label>事件标题</label>
                                    <input type="text" id="incidentTitle" placeholder="输入安全事件标题">
                                </div>
                                
                                <div class="form-row">
                                    <div class="form-group">
                                        <label>事件类型</label>
                                        <select id="incidentType">
                                            <option value="malware">恶意软件</option>
                                            <option value="breach">数据泄露</option>
                                            <option value="ddos">DDoS攻击</option>
                                            <option value="phishing">钓鱼攻击</option>
                                            <option value="insider">内部威胁</option>
                                            <option value="other">其他</option>
                                        </select>
                                    </div>
                                    
                                    <div class="form-group">
                                        <label>严重程度</label>
                                        <select id="incidentSeverity">
                                            <option value="critical">严重 (P1)</option>
                                            <option value="high">高 (P2)</option>
                                            <option value="medium">中 (P3)</option>
                                            <option value="low">低 (P4)</option>
                                        </select>
                                    </div>
                                </div>
                                
                                <div class="form-group">
                                    <label>事件描述</label>
                                    <textarea id="incidentDescription" placeholder="详细描述安全事件" rows="4"></textarea>
                                </div>
                                
                                <div class="form-group">
                                    <label>影响范围</label>
                                    <div class="impact-checkboxes">
                                        <label class="checkbox-label">
                                            <input type="checkbox" value="users">
                                            <span class="checkmark"></span>
                                            用户数据
                                        </label>
                                        <label class="checkbox-label">
                                            <input type="checkbox" value="systems">
                                            <span class="checkmark"></span>
                                            系统服务
                                        </label>
                                        <label class="checkbox-label">
                                            <input type="checkbox" value="network">
                                            <span class="checkmark"></span>
                                            网络基础设施
                                        </label>
                                        <label class="checkbox-label">
                                            <input type="checkbox" value="reputation">
                                            <span class="checkmark"></span>
                                            公司声誉
                                        </label>
                                    </div>
                                </div>
                                
                                <div class="form-group">
                                    <label>响应团队</label>
                                    <div class="team-selector">
                                        <select id="responseTeam" multiple>
                                            <option value="security">安全团队</option>
                                            <option value="it">IT团队</option>
                                            <option value="legal">法务团队</option>
                                            <option value="pr">公关团队</option>
                                            <option value="management">管理层</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="response-actions">
                                <h4>自动响应操作</h4>
                                <div class="action-list" id="responseActionsList">
                                    <!-- 响应操作列表 -->
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button class="btn-secondary" onclick="securityCenter.closeIncidentResponse()">取消</button>
                    <button class="btn-secondary" onclick="securityCenter.saveIncidentDraft()">保存草稿</button>
                    <button class="btn-primary" onclick="securityCenter.initiateResponse()">启动响应</button>
                </div>
            </div>
        `;
        
        document.body.appendChild(incidentResponse);
    }

    setupSecurityMonitoring() {
        // 设置实时安全监控
        this.startRealTimeMonitoring();
        this.setupThreatIntelligence();
        this.initializeBehaviorAnalysis();
    }

    startRealTimeMonitoring() {
        // 模拟实时安全监控
        setInterval(() => {
            this.generateSecurityEvent();
            this.updateSecurityMetrics();
            this.checkSecurityThresholds();
        }, 30000); // 每30秒检查一次
        
        // 模拟威胁检测
        setInterval(() => {
            if (Math.random() > 0.95) { // 5%概率检测到威胁
                this.detectThreat();
            }
        }, 60000); // 每分钟检查一次
    }

    generateSecurityEvent() {
        const eventTypes = ['login_attempt', 'access_denied', 'privilege_escalation', 'data_access', 'system_change'];
        const severities = ['low', 'medium', 'high', 'critical'];
        const sources = ['*************', '*********', '***********', 'external'];
        
        const event = {
            id: 'event_' + Date.now(),
            type: eventTypes[Math.floor(Math.random() * eventTypes.length)],
            severity: severities[Math.floor(Math.random() * severities.length)],
            source: sources[Math.floor(Math.random() * sources.length)],
            timestamp: new Date(),
            description: this.generateEventDescription(),
            status: 'new'
        };
        
        this.securityEvents.unshift(event);
        
        // 保留最近1000个事件
        if (this.securityEvents.length > 1000) {
            this.securityEvents = this.securityEvents.slice(0, 1000);
        }
        
        this.updateRecentEventsList();
    }

    generateEventDescription() {
        const descriptions = [
            '检测到异常登录尝试',
            '发现未授权的文件访问',
            '系统配置发生变更',
            '检测到可疑网络流量',
            '发现权限提升尝试',
            '检测到恶意软件特征',
            '发现数据泄露迹象',
            '检测到暴力破解攻击'
        ];
        
        return descriptions[Math.floor(Math.random() * descriptions.length)];
    }

    detectThreat() {
        const threatTypes = ['malware', 'phishing', 'bruteforce', 'injection', 'ddos'];
        const severities = ['medium', 'high', 'critical'];
        
        const threat = {
            id: 'threat_' + Date.now(),
            type: threatTypes[Math.floor(Math.random() * threatTypes.length)],
            severity: severities[Math.floor(Math.random() * severities.length)],
            source: this.generateThreatSource(),
            target: this.generateThreatTarget(),
            timestamp: new Date(),
            status: 'active',
            description: this.generateThreatDescription(),
            indicators: this.generateThreatIndicators()
        };
        
        this.threats.unshift(threat);
        
        // 更新威胁计数
        this.updateThreatMetrics();
        
        // 发送威胁告警
        this.sendThreatAlert(threat);
    }

    generateThreatSource() {
        const sources = [
            '************',
            '*************',
            '***********',
            'unknown',
            'internal'
        ];
        return sources[Math.floor(Math.random() * sources.length)];
    }

    generateThreatTarget() {
        const targets = [
            'web_server',
            'database',
            'user_accounts',
            'file_system',
            'network_infrastructure'
        ];
        return targets[Math.floor(Math.random() * targets.length)];
    }

    generateThreatDescription() {
        const descriptions = [
            '检测到恶意软件感染',
            '发现钓鱼攻击尝试',
            '检测到暴力破解攻击',
            '发现SQL注入攻击',
            '检测到DDoS攻击流量',
            '发现异常数据传输',
            '检测到权限滥用行为'
        ];
        
        return descriptions[Math.floor(Math.random() * descriptions.length)];
    }

    generateThreatIndicators() {
        return [
            'suspicious_file_hash',
            'malicious_ip_address',
            'unusual_network_pattern',
            'abnormal_user_behavior'
        ];
    }

    loadSecurityPolicies() {
        // 加载默认安全策略
        this.securityPolicies = [
            {
                id: 'policy_password',
                name: '密码策略',
                category: 'password',
                description: '用户密码复杂度和有效期要求',
                status: 'active',
                rules: [
                    '密码长度至少8位',
                    '包含大小写字母、数字和特殊字符',
                    '密码有效期90天',
                    '不能重复使用最近5个密码'
                ],
                lastUpdated: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000)
            },
            {
                id: 'policy_access',
                name: '访问控制策略',
                category: 'access',
                description: '系统访问权限控制规则',
                status: 'active',
                rules: [
                    '基于角色的访问控制',
                    '最小权限原则',
                    '定期权限审查',
                    '访问日志记录'
                ],
                lastUpdated: new Date(Date.now() - 14 * 24 * 60 * 60 * 1000)
            },
            {
                id: 'policy_network',
                name: '网络安全策略',
                category: 'network',
                description: '网络访问和防护策略',
                status: 'active',
                rules: [
                    '防火墙规则配置',
                    '入侵检测系统',
                    '网络流量监控',
                    'VPN访问控制'
                ],
                lastUpdated: new Date(Date.now() - 21 * 24 * 60 * 60 * 1000)
            }
        ];
        
        this.renderPoliciesList();
    }

    startThreatDetection() {
        // 启动威胁检测引擎
        console.log('威胁检测引擎已启动');
        
        // 模拟初始威胁数据
        this.generateInitialThreats();
    }

    generateInitialThreats() {
        // 生成一些初始威胁数据用于演示
        const initialThreats = [
            {
                id: 'threat_001',
                type: 'malware',
                severity: 'high',
                source: '************',
                target: 'web_server',
                timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000),
                status: 'active',
                description: '检测到恶意软件感染尝试',
                indicators: ['suspicious_file_hash', 'malicious_ip_address']
            },
            {
                id: 'threat_002',
                type: 'bruteforce',
                severity: 'medium',
                source: '*************',
                target: 'user_accounts',
                timestamp: new Date(Date.now() - 4 * 60 * 60 * 1000),
                status: 'mitigated',
                description: '检测到暴力破解攻击',
                indicators: ['unusual_login_pattern', 'multiple_failed_attempts']
            }
        ];
        
        this.threats = initialThreats;
        this.updateThreatMetrics();
    }

    bindSecurityEvents() {
        // 标签切换
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('tab-btn') && e.target.closest('.security-panel')) {
                this.switchSecurityTab(e.target);
            }
            
            if (e.target.classList.contains('category-tab')) {
                this.switchPolicyCategory(e.target);
            }
            
            if (e.target.classList.contains('template-item')) {
                this.selectResponseTemplate(e.target);
            }
        });
        
        // 威胁筛选
        document.getElementById('threatSeverityFilter')?.addEventListener('change', () => {
            this.filterThreats();
        });
        
        document.getElementById('threatTypeFilter')?.addEventListener('change', () => {
            this.filterThreats();
        });
        
        document.getElementById('threatStatusFilter')?.addEventListener('change', () => {
            this.filterThreats();
        });
    }

    switchSecurityTab(tabBtn) {
        const container = tabBtn.closest('.security-panel');
        const tabName = tabBtn.dataset.tab;
        
        // 更新标签状态
        container.querySelectorAll('.tab-btn').forEach(btn => btn.classList.remove('active'));
        container.querySelectorAll('.tab-content').forEach(content => content.classList.remove('active'));
        
        tabBtn.classList.add('active');
        container.querySelector(`#${tabName}Tab`).classList.add('active');
        
        // 加载对应内容
        this.loadSecurityTabContent(tabName);
    }

    loadSecurityTabContent(tabName) {
        switch (tabName) {
            case 'dashboard':
                this.updateSecurityDashboard();
                break;
            case 'threats':
                this.renderThreatsList();
                break;
            case 'compliance':
                this.renderComplianceRules();
                break;
            case 'policies':
                this.renderPoliciesList();
                break;
            case 'audit':
                this.renderAuditLogs();
                break;
            case 'encryption':
                this.renderEncryptionKeys();
                break;
        }
    }

    updateSecurityDashboard() {
        // 更新安全仪表板
        this.updateSecurityMetrics();
        this.updateRecentEventsList();
        this.renderSecurityCharts();
    }

    updateSecurityMetrics() {
        // 计算安全指标
        const activeThreats = this.threats.filter(t => t.status === 'active').length;
        const securityScore = this.calculateSecurityScore();
        const threatLevel = this.calculateThreatLevel();
        const complianceRate = this.calculateComplianceRate();
        
        // 更新显示
        document.getElementById('activeThreats').textContent = activeThreats;
        document.getElementById('securityScore').textContent = securityScore;
        document.getElementById('threatLevel').textContent = threatLevel;
        document.getElementById('complianceRate').textContent = complianceRate + '%';
        
        // 更新安全状态
        this.updateSecurityStatus(securityScore, activeThreats);
    }

    calculateSecurityScore() {
        // 简化的安全评分计算
        let score = 100;
        
        // 根据活跃威胁数量扣分
        const activeThreats = this.threats.filter(t => t.status === 'active').length;
        score -= activeThreats * 5;
        
        // 根据最近安全事件扣分
        const recentEvents = this.securityEvents.filter(e => 
            Date.now() - e.timestamp.getTime() < 24 * 60 * 60 * 1000
        ).length;
        score -= recentEvents * 0.5;
        
        return Math.max(0, Math.min(100, Math.round(score)));
    }

    calculateThreatLevel() {
        const activeThreats = this.threats.filter(t => t.status === 'active');
        
        if (activeThreats.length === 0) return '低';
        
        const criticalThreats = activeThreats.filter(t => t.severity === 'critical').length;
        const highThreats = activeThreats.filter(t => t.severity === 'high').length;
        
        if (criticalThreats > 0) return '严重';
        if (highThreats > 2) return '高';
        if (activeThreats.length > 5) return '中';
        return '低';
    }

    calculateComplianceRate() {
        // 简化的合规率计算
        return Math.floor(Math.random() * 10) + 90; // 90-99%
    }

    updateSecurityStatus(score, threats) {
        const statusElement = document.getElementById('securityStatus');
        const indicator = statusElement.querySelector('.status-indicator');
        
        if (score >= 90 && threats === 0) {
            indicator.className = 'status-indicator secure';
            indicator.innerHTML = '<i class="fas fa-check-circle"></i><span>系统安全</span>';
        } else if (score >= 70) {
            indicator.className = 'status-indicator warning';
            indicator.innerHTML = '<i class="fas fa-exclamation-triangle"></i><span>需要关注</span>';
        } else {
            indicator.className = 'status-indicator danger';
            indicator.innerHTML = '<i class="fas fa-times-circle"></i><span>存在风险</span>';
        }
    }

    updateRecentEventsList() {
        const eventsList = document.getElementById('recentEventsList');
        if (!eventsList) return;
        
        const recentEvents = this.securityEvents.slice(0, 10);
        
        eventsList.innerHTML = recentEvents.map(event => `
            <div class="event-item ${event.severity}">
                <div class="event-icon">
                    <i class="fas fa-${this.getEventIcon(event.type)}"></i>
                </div>
                <div class="event-content">
                    <div class="event-header">
                        <span class="event-type">${this.getEventTypeName(event.type)}</span>
                        <span class="event-time">${this.formatTime(event.timestamp)}</span>
                    </div>
                    <div class="event-description">${event.description}</div>
                    <div class="event-meta">
                        <span class="event-source">来源: ${event.source}</span>
                        <span class="event-severity ${event.severity}">${this.getSeverityName(event.severity)}</span>
                    </div>
                </div>
                <div class="event-actions">
                    <button class="btn-icon" onclick="securityCenter.viewEventDetails('${event.id}')" title="查看详情">
                        <i class="fas fa-eye"></i>
                    </button>
                    <button class="btn-icon" onclick="securityCenter.resolveEvent('${event.id}')" title="标记已解决">
                        <i class="fas fa-check"></i>
                    </button>
                </div>
            </div>
        `).join('');
    }

    getEventIcon(type) {
        const icons = {
            'login_attempt': 'sign-in-alt',
            'access_denied': 'ban',
            'privilege_escalation': 'user-shield',
            'data_access': 'database',
            'system_change': 'cogs'
        };
        return icons[type] || 'exclamation-triangle';
    }

    getEventTypeName(type) {
        const names = {
            'login_attempt': '登录尝试',
            'access_denied': '访问拒绝',
            'privilege_escalation': '权限提升',
            'data_access': '数据访问',
            'system_change': '系统变更'
        };
        return names[type] || type;
    }

    getSeverityName(severity) {
        const names = {
            'low': '低',
            'medium': '中',
            'high': '高',
            'critical': '严重'
        };
        return names[severity] || severity;
    }

    formatTime(date) {
        const now = new Date();
        const diff = now - date;
        
        if (diff < 60 * 1000) {
            return '刚刚';
        } else if (diff < 60 * 60 * 1000) {
            return Math.floor(diff / (60 * 1000)) + '分钟前';
        } else if (diff < 24 * 60 * 60 * 1000) {
            return Math.floor(diff / (60 * 60 * 1000)) + '小时前';
        } else {
            return date.toLocaleDateString();
        }
    }

    renderSecurityCharts() {
        // 渲染安全趋势图表
        this.renderSecurityTrendChart();
        this.renderThreatDistributionChart();
    }

    renderSecurityTrendChart() {
        const canvas = document.getElementById('securityTrendChart');
        if (!canvas) return;
        
        const ctx = canvas.getContext('2d');
        
        // 简化的图表渲染
        ctx.fillStyle = '#667eea';
        ctx.fillRect(10, 10, canvas.width - 20, canvas.height - 20);
        
        ctx.fillStyle = 'white';
        ctx.font = '14px Arial';
        ctx.textAlign = 'center';
        ctx.fillText('安全事件趋势图', canvas.width / 2, canvas.height / 2);
    }

    renderThreatDistributionChart() {
        const canvas = document.getElementById('threatDistributionChart');
        if (!canvas) return;
        
        const ctx = canvas.getContext('2d');
        
        // 简化的饼图渲染
        ctx.fillStyle = '#764ba2';
        ctx.fillRect(10, 10, canvas.width - 20, canvas.height - 20);
        
        ctx.fillStyle = 'white';
        ctx.font = '14px Arial';
        ctx.textAlign = 'center';
        ctx.fillText('威胁类型分布图', canvas.width / 2, canvas.height / 2);
    }

    // 面板控制方法
    showSecurityPanel() {
        const panel = document.getElementById('securityPanel');
        if (panel) {
            panel.classList.add('show');
            this.updateSecurityDashboard();
        }
    }

    closeSecurityPanel() {
        const panel = document.getElementById('securityPanel');
        if (panel) {
            panel.classList.remove('show');
        }
    }

    // 其他方法的占位符
    runSecurityScan() {
        console.log('运行安全扫描');
    }

    generateSecurityReport() {
        console.log('生成安全报告');
    }

    showIncidentResponse() {
        const modal = document.getElementById('incidentResponseInterface');
        if (modal) {
            modal.style.display = 'flex';
        }
    }

    closeIncidentResponse() {
        const modal = document.getElementById('incidentResponseInterface');
        if (modal) {
            modal.style.display = 'none';
        }
    }

    renderThreatsList() {
        const threatsList = document.getElementById('threatsList');
        if (!threatsList) return;

        const filteredThreats = this.getFilteredThreats();

        if (filteredThreats.length === 0) {
            threatsList.innerHTML = `
                <div class="empty-state">
                    <i class="fas fa-shield-alt"></i>
                    <p>暂无威胁检测结果</p>
                </div>
            `;
            return;
        }

        threatsList.innerHTML = filteredThreats.map(threat => `
            <div class="threat-item ${threat.severity} ${threat.status}">
                <div class="threat-icon">
                    <i class="fas fa-${this.getThreatIcon(threat.type)}"></i>
                </div>
                <div class="threat-content">
                    <div class="threat-header">
                        <span class="threat-type">${this.getThreatTypeName(threat.type)}</span>
                        <span class="threat-severity ${threat.severity}">${this.getSeverityName(threat.severity)}</span>
                        <span class="threat-status ${threat.status}">${this.getThreatStatusName(threat.status)}</span>
                    </div>
                    <div class="threat-description">${threat.description}</div>
                    <div class="threat-meta">
                        <span class="threat-source">来源: ${threat.source}</span>
                        <span class="threat-target">目标: ${this.getTargetName(threat.target)}</span>
                        <span class="threat-time">${this.formatTime(threat.timestamp)}</span>
                    </div>
                    <div class="threat-indicators">
                        ${threat.indicators.map(indicator =>
                            `<span class="indicator-tag">${this.getIndicatorName(indicator)}</span>`
                        ).join('')}
                    </div>
                </div>
                <div class="threat-actions">
                    <button class="btn-icon" onclick="securityCenter.viewThreatDetails('${threat.id}')" title="查看详情">
                        <i class="fas fa-eye"></i>
                    </button>
                    <button class="btn-icon" onclick="securityCenter.mitigateThreat('${threat.id}')" title="缓解威胁">
                        <i class="fas fa-shield-alt"></i>
                    </button>
                    <button class="btn-icon" onclick="securityCenter.blockThreat('${threat.id}')" title="阻止威胁">
                        <i class="fas fa-ban"></i>
                    </button>
                </div>
            </div>
        `).join('');
    }

    getFilteredThreats() {
        const severityFilter = document.getElementById('threatSeverityFilter')?.value || 'all';
        const typeFilter = document.getElementById('threatTypeFilter')?.value || 'all';
        const statusFilter = document.getElementById('threatStatusFilter')?.value || 'all';

        return this.threats.filter(threat => {
            if (severityFilter !== 'all' && threat.severity !== severityFilter) return false;
            if (typeFilter !== 'all' && threat.type !== typeFilter) return false;
            if (statusFilter !== 'all' && threat.status !== statusFilter) return false;
            return true;
        });
    }

    getThreatIcon(type) {
        const icons = {
            'malware': 'bug',
            'phishing': 'fish',
            'bruteforce': 'hammer',
            'injection': 'syringe',
            'ddos': 'server'
        };
        return icons[type] || 'exclamation-triangle';
    }

    getThreatTypeName(type) {
        const names = {
            'malware': '恶意软件',
            'phishing': '钓鱼攻击',
            'bruteforce': '暴力破解',
            'injection': '注入攻击',
            'ddos': 'DDoS攻击'
        };
        return names[type] || type;
    }

    getThreatStatusName(status) {
        const names = {
            'active': '活跃',
            'mitigated': '已缓解',
            'resolved': '已解决',
            'blocked': '已阻止'
        };
        return names[status] || status;
    }

    getTargetName(target) {
        const names = {
            'web_server': 'Web服务器',
            'database': '数据库',
            'user_accounts': '用户账户',
            'file_system': '文件系统',
            'network_infrastructure': '网络基础设施'
        };
        return names[target] || target;
    }

    getIndicatorName(indicator) {
        const names = {
            'suspicious_file_hash': '可疑文件哈希',
            'malicious_ip_address': '恶意IP地址',
            'unusual_network_pattern': '异常网络模式',
            'abnormal_user_behavior': '异常用户行为',
            'unusual_login_pattern': '异常登录模式',
            'multiple_failed_attempts': '多次失败尝试'
        };
        return names[indicator] || indicator;
    }

    renderComplianceRules() {
        const rulesList = document.getElementById('complianceRulesList');
        if (!rulesList) return;

        // 模拟合规规则数据
        const complianceRules = [
            {
                id: 'rule_001',
                name: '数据加密要求',
                framework: 'GDPR',
                status: 'compliant',
                description: '所有个人数据必须进行加密存储',
                lastCheck: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000),
                score: 98
            },
            {
                id: 'rule_002',
                name: '访问日志记录',
                framework: 'ISO 27001',
                status: 'compliant',
                description: '系统访问必须记录详细日志',
                lastCheck: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000),
                score: 95
            },
            {
                id: 'rule_003',
                name: '密码复杂度要求',
                framework: 'PCI DSS',
                status: 'warning',
                description: '用户密码必须满足复杂度要求',
                lastCheck: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000),
                score: 87
            },
            {
                id: 'rule_004',
                name: '医疗数据保护',
                framework: 'HIPAA',
                status: 'non-compliant',
                description: '医疗相关数据需要特殊保护措施',
                lastCheck: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000),
                score: 72
            }
        ];

        rulesList.innerHTML = complianceRules.map(rule => `
            <div class="compliance-rule ${rule.status}">
                <div class="rule-header">
                    <div class="rule-info">
                        <h6>${rule.name}</h6>
                        <span class="rule-framework">${rule.framework}</span>
                    </div>
                    <div class="rule-status">
                        <div class="status-indicator ${rule.status}">
                            <i class="fas fa-${this.getComplianceIcon(rule.status)}"></i>
                        </div>
                        <span class="rule-score">${rule.score}%</span>
                    </div>
                </div>
                <div class="rule-description">${rule.description}</div>
                <div class="rule-meta">
                    <span class="last-check">最后检查: ${this.formatTime(rule.lastCheck)}</span>
                    <div class="rule-actions">
                        <button class="btn-icon" onclick="securityCenter.checkComplianceRule('${rule.id}')" title="立即检查">
                            <i class="fas fa-sync"></i>
                        </button>
                        <button class="btn-icon" onclick="securityCenter.editComplianceRule('${rule.id}')" title="编辑规则">
                            <i class="fas fa-edit"></i>
                        </button>
                    </div>
                </div>
            </div>
        `).join('');
    }

    getComplianceIcon(status) {
        const icons = {
            'compliant': 'check-circle',
            'warning': 'exclamation-triangle',
            'non-compliant': 'times-circle'
        };
        return icons[status] || 'question-circle';
    }

    renderPoliciesList() {
        const policiesList = document.getElementById('policiesList');
        if (!policiesList) return;

        const activeCategory = document.querySelector('.category-tab.active')?.dataset.category || 'access';
        const filteredPolicies = this.securityPolicies.filter(policy => policy.category === activeCategory);

        if (filteredPolicies.length === 0) {
            policiesList.innerHTML = `
                <div class="empty-state">
                    <i class="fas fa-shield-alt"></i>
                    <p>该分类下暂无安全策略</p>
                    <button class="btn-primary" onclick="securityCenter.createPolicy()">
                        创建策略
                    </button>
                </div>
            `;
            return;
        }

        policiesList.innerHTML = filteredPolicies.map(policy => `
            <div class="policy-item ${policy.status}">
                <div class="policy-header">
                    <div class="policy-info">
                        <h6>${policy.name}</h6>
                        <span class="policy-status ${policy.status}">${this.getPolicyStatusName(policy.status)}</span>
                    </div>
                    <div class="policy-actions">
                        <button class="btn-icon" onclick="securityCenter.editPolicy('${policy.id}')" title="编辑策略">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn-icon" onclick="securityCenter.duplicatePolicy('${policy.id}')" title="复制策略">
                            <i class="fas fa-copy"></i>
                        </button>
                        <button class="btn-icon" onclick="securityCenter.deletePolicy('${policy.id}')" title="删除策略">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </div>
                <div class="policy-description">${policy.description}</div>
                <div class="policy-rules">
                    <h6>策略规则:</h6>
                    <ul>
                        ${policy.rules.map(rule => `<li>${rule}</li>`).join('')}
                    </ul>
                </div>
                <div class="policy-meta">
                    <span class="last-updated">最后更新: ${this.formatTime(policy.lastUpdated)}</span>
                </div>
            </div>
        `).join('');
    }

    getPolicyStatusName(status) {
        const names = {
            'active': '已启用',
            'inactive': '已禁用',
            'draft': '草稿'
        };
        return names[status] || status;
    }

    renderAuditLogs() {
        const auditLogsList = document.getElementById('auditLogsList');
        if (!auditLogsList) return;

        // 模拟审计日志数据
        const auditLogs = this.generateAuditLogs();

        auditLogsList.innerHTML = `
            <div class="audit-table">
                <div class="table-header">
                    <div class="header-cell">时间</div>
                    <div class="header-cell">事件类型</div>
                    <div class="header-cell">用户</div>
                    <div class="header-cell">操作</div>
                    <div class="header-cell">结果</div>
                    <div class="header-cell">IP地址</div>
                    <div class="header-cell">操作</div>
                </div>
                <div class="table-body">
                    ${auditLogs.map(log => `
                        <div class="table-row">
                            <div class="table-cell">${log.timestamp.toLocaleString()}</div>
                            <div class="table-cell">
                                <span class="event-type-badge ${log.eventType}">${this.getAuditEventTypeName(log.eventType)}</span>
                            </div>
                            <div class="table-cell">${log.user}</div>
                            <div class="table-cell">${log.action}</div>
                            <div class="table-cell">
                                <span class="result-badge ${log.result}">${this.getAuditResultName(log.result)}</span>
                            </div>
                            <div class="table-cell">${log.ipAddress}</div>
                            <div class="table-cell">
                                <button class="btn-icon" onclick="securityCenter.viewAuditDetails('${log.id}')" title="查看详情">
                                    <i class="fas fa-eye"></i>
                                </button>
                            </div>
                        </div>
                    `).join('')}
                </div>
            </div>
        `;
    }

    generateAuditLogs() {
        const logs = [];
        const eventTypes = ['login', 'access', 'change', 'security'];
        const users = ['admin', 'user1', 'user2', 'system'];
        const actions = ['登录系统', '访问文件', '修改配置', '创建用户', '删除数据'];
        const results = ['success', 'failure', 'warning'];
        const ips = ['*************', '*********', '***********'];

        for (let i = 0; i < 50; i++) {
            logs.push({
                id: 'audit_' + i,
                timestamp: new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000),
                eventType: eventTypes[Math.floor(Math.random() * eventTypes.length)],
                user: users[Math.floor(Math.random() * users.length)],
                action: actions[Math.floor(Math.random() * actions.length)],
                result: results[Math.floor(Math.random() * results.length)],
                ipAddress: ips[Math.floor(Math.random() * ips.length)]
            });
        }

        return logs.sort((a, b) => b.timestamp - a.timestamp);
    }

    getAuditEventTypeName(eventType) {
        const names = {
            'login': '登录',
            'access': '访问',
            'change': '变更',
            'security': '安全'
        };
        return names[eventType] || eventType;
    }

    getAuditResultName(result) {
        const names = {
            'success': '成功',
            'failure': '失败',
            'warning': '警告'
        };
        return names[result] || result;
    }

    renderEncryptionKeys() {
        const keysList = document.getElementById('encryptionKeysList');
        if (!keysList) return;

        // 模拟加密密钥数据
        const encryptionKeys = [
            {
                id: 'key_001',
                name: '数据库主密钥',
                algorithm: 'AES-256',
                status: 'active',
                created: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
                expires: new Date(Date.now() + 335 * 24 * 60 * 60 * 1000),
                usage: 'database_encryption'
            },
            {
                id: 'key_002',
                name: '文件系统密钥',
                algorithm: 'AES-256',
                status: 'active',
                created: new Date(Date.now() - 60 * 24 * 60 * 60 * 1000),
                expires: new Date(Date.now() + 305 * 24 * 60 * 60 * 1000),
                usage: 'file_encryption'
            },
            {
                id: 'key_003',
                name: '通信加密密钥',
                algorithm: 'RSA-2048',
                status: 'expiring',
                created: new Date(Date.now() - 350 * 24 * 60 * 60 * 1000),
                expires: new Date(Date.now() + 15 * 24 * 60 * 60 * 1000),
                usage: 'communication'
            },
            {
                id: 'key_004',
                name: '备份加密密钥',
                algorithm: 'AES-256',
                status: 'inactive',
                created: new Date(Date.now() - 400 * 24 * 60 * 60 * 1000),
                expires: new Date(Date.now() - 35 * 24 * 60 * 60 * 1000),
                usage: 'backup_encryption'
            }
        ];

        keysList.innerHTML = encryptionKeys.map(key => `
            <div class="encryption-key ${key.status}">
                <div class="key-header">
                    <div class="key-info">
                        <h6>${key.name}</h6>
                        <span class="key-algorithm">${key.algorithm}</span>
                    </div>
                    <div class="key-status">
                        <span class="status-badge ${key.status}">${this.getKeyStatusName(key.status)}</span>
                    </div>
                </div>
                <div class="key-details">
                    <div class="key-meta">
                        <span class="key-usage">用途: ${this.getKeyUsageName(key.usage)}</span>
                        <span class="key-created">创建: ${key.created.toLocaleDateString()}</span>
                        <span class="key-expires">过期: ${key.expires.toLocaleDateString()}</span>
                    </div>
                    <div class="key-actions">
                        <button class="btn-icon" onclick="securityCenter.rotateKey('${key.id}')" title="轮换密钥">
                            <i class="fas fa-sync"></i>
                        </button>
                        <button class="btn-icon" onclick="securityCenter.exportKey('${key.id}')" title="导出密钥">
                            <i class="fas fa-download"></i>
                        </button>
                        <button class="btn-icon" onclick="securityCenter.revokeKey('${key.id}')" title="撤销密钥">
                            <i class="fas fa-ban"></i>
                        </button>
                    </div>
                </div>
            </div>
        `).join('');
    }

    getKeyStatusName(status) {
        const names = {
            'active': '活跃',
            'inactive': '非活跃',
            'expiring': '即将过期',
            'expired': '已过期',
            'revoked': '已撤销'
        };
        return names[status] || status;
    }

    getKeyUsageName(usage) {
        const names = {
            'database_encryption': '数据库加密',
            'file_encryption': '文件加密',
            'communication': '通信加密',
            'backup_encryption': '备份加密'
        };
        return names[usage] || usage;
    }

    setupRealTimeMonitoring() {
        console.log('设置实时监控');
    }

    loadSecurityData() {
        console.log('加载安全数据');
    }
}

// 全局安全中心实例
let securityCenter = null;

// 初始化安全中心
function initializeSecurityCenter() {
    securityCenter = new SecurityCenter();
    console.log('✅ 高级安全中心已初始化');
}

// 显示安全面板
function showSecurityPanel() {
    if (securityCenter) {
        securityCenter.showSecurityPanel();
    }
}

// 页面加载时初始化
document.addEventListener('DOMContentLoaded', function() {
    setTimeout(initializeSecurityCenter, 1800);
});
