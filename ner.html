<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>NER数据生成器</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { font-family: 'Arial', sans-serif; background: #f5f7fa; }
        .container { max-width: 1200px; margin: 0 auto; padding: 20px; }
        .header { background: white; padding: 20px; border-radius: 10px; margin-bottom: 20px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .card { background: white; border-radius: 10px; padding: 20px; margin-bottom: 20px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .stats-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; margin-bottom: 20px; }
        .stat-card { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 20px; border-radius: 10px; text-align: center; }
        .stat-number { font-size: 2em; font-weight: bold; margin-bottom: 5px; }
        .stat-label { opacity: 0.9; }
        .form-group { margin-bottom: 15px; }
        .form-group label { display: block; margin-bottom: 5px; font-weight: bold; }
        .form-group input, .form-group select { width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 5px; }
        .btn { padding: 12px 24px; border: none; border-radius: 5px; cursor: pointer; font-weight: bold; margin: 5px; }
        .btn-primary { background: #667eea; color: white; }
        .btn-secondary { background: #6c757d; color: white; }
        .btn:hover { opacity: 0.9; }
        .btn:disabled { opacity: 0.5; cursor: not-allowed; }
        .checkbox-group { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 10px; }
        .checkbox-item { display: flex; align-items: center; padding: 10px; background: #f8f9fa; border-radius: 5px; }
        .checkbox-item input { margin-right: 10px; }
        .preview { max-height: 400px; overflow-y: auto; }
        .sample { border: 1px solid #eee; padding: 15px; margin-bottom: 10px; border-radius: 5px; }
        .sample-header { display: flex; justify-content: between; margin-bottom: 10px; font-weight: bold; color: #667eea; }
        .entity { padding: 2px 6px; border-radius: 3px; margin: 0 2px; font-weight: bold; }
        .entity-person { background: #fff3cd; color: #856404; }
        .entity-location { background: #d1ecf1; color: #0c5460; }
        .entity-organization { background: #d4edda; color: #155724; }
        .entity-time { background: #f8d7da; color: #721c24; }
        .entity-number { background: #e2e3f0; color: #383d41; }
        .chart-container { position: relative; height: 300px; }
        .notification { position: fixed; top: 20px; right: 20px; padding: 15px 20px; border-radius: 5px; color: white; z-index: 1000; }
        .notification.success { background: #28a745; }
        .notification.error { background: #dc3545; }
        .notification.warning { background: #ffc107; color: #212529; }
        .notification.info { background: #17a2b8; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><i class="fas fa-brain"></i> NER数据生成器</h1>
            <p>命名实体识别训练数据生成和管理工具</p>
        </div>
        
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-number" id="totalSamples">0</div>
                <div class="stat-label">总样本数</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="totalPersons">0</div>
                <div class="stat-label">人名实体</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="totalLocations">0</div>
                <div class="stat-label">地名实体</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="totalOrgs">0</div>
                <div class="stat-label">机构实体</div>
            </div>
        </div>
        
        <div class="card">
            <h3><i class="fas fa-cog"></i> 生成配置</h3>
            <div style="display: grid; grid-template-columns: 1fr 2fr; gap: 20px;">
                <div>
                    <div class="form-group">
                        <label for="sampleCount">样本数量</label>
                        <input type="number" id="sampleCount" value="100" min="10" max="1000">
                    </div>
                    <div class="form-group">
                        <label for="outputFormat">输出格式</label>
                        <select id="outputFormat">
                            <option value="json">JSON格式</option>
                            <option value="conll">CoNLL格式</option>
                            <option value="txt">文本格式</option>
                        </select>
                    </div>
                </div>
                <div>
                    <label>实体类型</label>
                    <div class="checkbox-group">
                        <div class="checkbox-item">
                            <input type="checkbox" id="enablePerson" checked>
                            <label for="enablePerson">人名 (PERSON)</label>
                        </div>
                        <div class="checkbox-item">
                            <input type="checkbox" id="enableLocation" checked>
                            <label for="enableLocation">地名 (LOCATION)</label>
                        </div>
                        <div class="checkbox-item">
                            <input type="checkbox" id="enableOrg" checked>
                            <label for="enableOrg">机构 (ORGANIZATION)</label>
                        </div>
                        <div class="checkbox-item">
                            <input type="checkbox" id="enableTime" checked>
                            <label for="enableTime">时间 (TIME)</label>
                        </div>
                        <div class="checkbox-item">
                            <input type="checkbox" id="enableNumber" checked>
                            <label for="enableNumber">数字 (NUMBER)</label>
                        </div>
                    </div>
                </div>
            </div>
            <div style="margin-top: 20px;">
                <button class="btn btn-primary" onclick="generateData()">
                    <i class="fas fa-magic"></i> 生成数据
                </button>
                <button class="btn btn-secondary" onclick="downloadData()">
                    <i class="fas fa-download"></i> 下载数据
                </button>
                <button class="btn btn-secondary" onclick="clearData()">
                    <i class="fas fa-trash"></i> 清空数据
                </button>
            </div>
        </div>
        
        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
            <div class="card">
                <h3><i class="fas fa-eye"></i> 数据预览</h3>
                <div id="dataPreview" class="preview">
                    <p style="text-align: center; color: #666; padding: 40px;">
                        <i class="fas fa-database" style="font-size: 3em; opacity: 0.3;"></i><br>
                        暂无数据，请先生成数据
                    </p>
                </div>
            </div>
            
            <div class="card">
                <h3><i class="fas fa-chart-pie"></i> 实体分布</h3>
                <div class="chart-container">
                    <canvas id="entityChart"></canvas>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        // NER数据生成器
        class NERGenerator {
            constructor() {
                this.persons = ['张三', '李四', '王五', '赵六', '陈七', '刘八', '马云', '马化腾', '李彦宏', '雷军'];
                this.locations = ['北京', '上海', '广州', '深圳', '杭州', '南京', '武汉', '成都', '西安', '重庆'];
                this.orgs = ['阿里巴巴', '腾讯', '百度', '字节跳动', '美团', '滴滴', '京东', '网易', '华为', '小米'];
                this.times = ['2023年', '2024年', '今年', '去年', '明年', '上个月', '下个月', '昨天', '今天', '明天'];
                this.numbers = ['100', '1000', '50%', '3.5万', '10亿', '第一', '第二', '三个', '五年', '十次'];
                
                this.templates = [
                    '{person}在{location}的{organization}工作了{time}。',
                    '{organization}位于{location}，由{person}创立于{time}。',
                    '{person}于{time}从{location}来到{organization}。',
                    '{time}，{person}在{location}成立了{organization}。',
                    '{organization}的{person}获得了{number}的投资。',
                    '{person}计划{time}在{location}举办活动。'
                ];
                
                this.dataset = [];
                this.stats = {};
            }
            
            random(arr) {
                return arr[Math.floor(Math.random() * arr.length)];
            }
            
            generateSample() {
                const template = this.random(this.templates);
                let text = template;
                const entities = [];
                
                if (text.includes('{person}')) {
                    const person = this.random(this.persons);
                    text = text.replace('{person}', person);
                    entities.push({type: 'PERSON', value: person});
                }
                
                if (text.includes('{location}')) {
                    const location = this.random(this.locations);
                    text = text.replace('{location}', location);
                    entities.push({type: 'LOCATION', value: location});
                }
                
                if (text.includes('{organization}')) {
                    const org = this.random(this.orgs);
                    text = text.replace('{organization}', org);
                    entities.push({type: 'ORGANIZATION', value: org});
                }
                
                if (text.includes('{time}')) {
                    const time = this.random(this.times);
                    text = text.replace('{time}', time);
                    entities.push({type: 'TIME', value: time});
                }
                
                if (text.includes('{number}')) {
                    const number = this.random(this.numbers);
                    text = text.replace('{number}', number);
                    entities.push({type: 'NUMBER', value: number});
                }
                
                return {text, entities};
            }
            
            generate(count) {
                this.dataset = [];
                for (let i = 0; i < count; i++) {
                    const sample = this.generateSample();
                    sample.id = i + 1;
                    this.dataset.push(sample);
                }
                
                this.calculateStats();
                return this.dataset;
            }
            
            calculateStats() {
                this.stats = {
                    total: this.dataset.length,
                    entities: {PERSON: 0, LOCATION: 0, ORGANIZATION: 0, TIME: 0, NUMBER: 0}
                };
                
                this.dataset.forEach(sample => {
                    sample.entities.forEach(entity => {
                        if (this.stats.entities[entity.type] !== undefined) {
                            this.stats.entities[entity.type]++;
                        }
                    });
                });
            }
            
            export(format) {
                if (this.dataset.length === 0) return '';
                
                if (format === 'json') {
                    return JSON.stringify(this.dataset, null, 2);
                } else if (format === 'conll') {
                    return this.dataset.map(sample => {
                        const tokens = Array.from(sample.text);
                        const labels = new Array(tokens.length).fill('O');
                        
                        sample.entities.forEach(entity => {
                            const start = sample.text.indexOf(entity.value);
                            if (start !== -1) {
                                labels[start] = `B-${entity.type}`;
                                for (let i = 1; i < entity.value.length; i++) {
                                    if (start + i < labels.length) {
                                        labels[start + i] = `I-${entity.type}`;
                                    }
                                }
                            }
                        });
                        
                        return tokens.map((token, i) => `${token}\t${labels[i]}`).join('\n');
                    }).join('\n\n');
                } else {
                    return this.dataset.map(sample => 
                        `样本${sample.id}: ${sample.text}\n实体: ${sample.entities.map(e => `${e.value}(${e.type})`).join(', ')}\n`
                    ).join('\n');
                }
            }
        }
        
        const generator = new NERGenerator();
        let chart = null;
        
        function showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            notification.className = `notification ${type}`;
            notification.textContent = message;
            document.body.appendChild(notification);
            
            setTimeout(() => {
                notification.remove();
            }, 3000);
        }
        
        function generateData() {
            const count = parseInt(document.getElementById('sampleCount').value);
            if (count < 10 || count > 1000) {
                showNotification('样本数量必须在10-1000之间', 'warning');
                return;
            }
            
            const dataset = generator.generate(count);
            updateStats();
            updatePreview();
            updateChart();
            
            showNotification(`成功生成${count}条数据`, 'success');
        }
        
        function updateStats() {
            document.getElementById('totalSamples').textContent = generator.stats.total;
            document.getElementById('totalPersons').textContent = generator.stats.entities.PERSON;
            document.getElementById('totalLocations').textContent = generator.stats.entities.LOCATION;
            document.getElementById('totalOrgs').textContent = generator.stats.entities.ORGANIZATION;
        }
        
        function updatePreview() {
            const preview = document.getElementById('dataPreview');
            if (generator.dataset.length === 0) {
                preview.innerHTML = '<p style="text-align: center; color: #666; padding: 40px;"><i class="fas fa-database" style="font-size: 3em; opacity: 0.3;"></i><br>暂无数据，请先生成数据</p>';
                return;
            }
            
            const samples = generator.dataset.slice(0, 5);
            preview.innerHTML = samples.map(sample => {
                let highlightedText = sample.text;
                sample.entities.forEach(entity => {
                    const className = `entity-${entity.type.toLowerCase()}`;
                    highlightedText = highlightedText.replace(
                        entity.value, 
                        `<span class="entity ${className}">${entity.value}</span>`
                    );
                });
                
                return `
                    <div class="sample">
                        <div class="sample-header">样本 #${sample.id}</div>
                        <div>${highlightedText}</div>
                        <div style="margin-top: 10px; font-size: 0.9em; color: #666;">
                            实体: ${sample.entities.map(e => `${e.value}(${e.type})`).join(', ')}
                        </div>
                    </div>
                `;
            }).join('');
        }
        
        function updateChart() {
            const ctx = document.getElementById('entityChart');
            if (chart) chart.destroy();
            
            if (generator.stats.total === 0) return;
            
            chart = new Chart(ctx, {
                type: 'doughnut',
                data: {
                    labels: Object.keys(generator.stats.entities),
                    datasets: [{
                        data: Object.values(generator.stats.entities),
                        backgroundColor: ['#FF6384', '#36A2EB', '#FFCE56', '#4BC0C0', '#9966FF']
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {position: 'bottom'}
                    }
                }
            });
        }
        
        function downloadData() {
            if (generator.dataset.length === 0) {
                showNotification('暂无数据可下载', 'warning');
                return;
            }
            
            const format = document.getElementById('outputFormat').value;
            const content = generator.export(format);
            const blob = new Blob([content], {type: 'text/plain'});
            const url = URL.createObjectURL(blob);
            
            const a = document.createElement('a');
            a.href = url;
            a.download = `ner_data.${format}`;
            a.click();
            
            URL.revokeObjectURL(url);
            showNotification('数据下载成功', 'success');
        }
        
        function clearData() {
            if (confirm('确定要清空所有数据吗？')) {
                generator.dataset = [];
                generator.stats = {total: 0, entities: {PERSON: 0, LOCATION: 0, ORGANIZATION: 0, TIME: 0, NUMBER: 0}};
                updateStats();
                updatePreview();
                if (chart) chart.destroy();
                showNotification('数据已清空', 'info');
            }
        }
        
        // 初始化
        updateStats();
        updatePreview();
    </script>
</body>
</html>
