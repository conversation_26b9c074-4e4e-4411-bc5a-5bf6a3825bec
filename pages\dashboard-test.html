<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>仪表盘测试 - 企业管理系统</title>
    <link rel="stylesheet" href="../assets/css/styles.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .test-title {
            color: white;
            text-align: center;
            margin-bottom: 32px;
            font-size: 32px;
            font-weight: 700;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1 class="test-title">🎯 仪表盘样式测试</h1>
        
        <!-- 欢迎横幅 -->
        <div class="welcome-banner">
            <div class="welcome-content">
                <div class="welcome-text">
                    <h1 class="welcome-title">
                        <span class="greeting">早上好</span>，
                        <span class="user-name-display">张管理员</span>
                        <span class="wave-emoji">👋</span>
                    </h1>
                    <p class="welcome-subtitle">今天是美好的一天，让我们一起创造更多价值</p>
                    <div class="welcome-stats">
                        <div class="welcome-stat-item">
                            <span class="stat-value">1,234</span>
                            <span class="stat-label">今日访问</span>
                        </div>
                        <div class="welcome-stat-item">
                            <span class="stat-value">89</span>
                            <span class="stat-label">在线用户</span>
                        </div>
                        <div class="welcome-stat-item">
                            <span class="stat-value">正常</span>
                            <span class="stat-label">系统状态</span>
                        </div>
                    </div>
                </div>
                <div class="welcome-visual">
                    <div class="floating-elements">
                        <div class="floating-element" style="--delay: 0s;">📊</div>
                        <div class="floating-element" style="--delay: 0.5s;">💼</div>
                        <div class="floating-element" style="--delay: 1s;">🚀</div>
                        <div class="floating-element" style="--delay: 1.5s;">⭐</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 统计卡片 -->
        <div class="stats-grid">
            <div class="stat-card primary">
                <div class="stat-content">
                    <div class="stat-icon">
                        <i class="fas fa-users"></i>
                    </div>
                    <div class="stat-info">
                        <h3 class="stat-number">2,847</h3>
                        <p class="stat-label">总用户数</p>
                        <div class="stat-trend positive">
                            <i class="fas fa-arrow-up"></i>
                            <span>+12.5%</span>
                        </div>
                    </div>
                </div>
            </div>

            <div class="stat-card success">
                <div class="stat-content">
                    <div class="stat-icon">
                        <i class="fas fa-dollar-sign"></i>
                    </div>
                    <div class="stat-info">
                        <h3 class="stat-number">¥156,432</h3>
                        <p class="stat-label">月收入</p>
                        <div class="stat-trend positive">
                            <i class="fas fa-arrow-up"></i>
                            <span>+8.2%</span>
                        </div>
                    </div>
                </div>
            </div>

            <div class="stat-card warning">
                <div class="stat-content">
                    <div class="stat-icon">
                        <i class="fas fa-shopping-cart"></i>
                    </div>
                    <div class="stat-info">
                        <h3 class="stat-number">1,234</h3>
                        <p class="stat-label">订单数量</p>
                        <div class="stat-trend negative">
                            <i class="fas fa-arrow-down"></i>
                            <span>-3.1%</span>
                        </div>
                    </div>
                </div>
            </div>

            <div class="stat-card danger">
                <div class="stat-content">
                    <div class="stat-icon">
                        <i class="fas fa-exclamation-triangle"></i>
                    </div>
                    <div class="stat-info">
                        <h3 class="stat-number">8</h3>
                        <p class="stat-label">待处理</p>
                        <div class="stat-trend neutral">
                            <i class="fas fa-minus"></i>
                            <span>0%</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 仪表盘卡片 -->
        <div class="dashboard-grid">
            <div class="dashboard-card">
                <div class="card-header">
                    <h3>销售趋势</h3>
                </div>
                <div class="card-content">
                    <p>这里是图表内容区域</p>
                </div>
            </div>

            <div class="dashboard-card">
                <div class="card-header">
                    <h3>用户活动</h3>
                </div>
                <div class="card-content">
                    <p>这里是用户活动数据</p>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 添加页面加载动画
        document.addEventListener('DOMContentLoaded', function() {
            document.body.classList.add('loaded');
        });
    </script>
</body>
</html>
