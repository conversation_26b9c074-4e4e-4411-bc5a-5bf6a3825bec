// 增强用户管理功能
class EnhancedUserManager {
    constructor() {
        this.users = [];
        this.filteredUsers = [];
        this.selectedUsers = new Set();
        this.currentPage = 1;
        this.pageSize = 10;
        this.sortField = 'name';
        this.sortDirection = 'asc';
        this.filters = {
            search: '',
            role: '',
            status: '',
            registerDateFrom: '',
            registerDateTo: '',
            lastLogin: '',
            permission: ''
        };
        
        this.initializeUsers();
    }
    
    initializeUsers() {
        // 生成示例用户数据
        this.users = [
            {
                id: 1,
                username: 'admin',
                name: '系统管理员',
                email: '<EMAIL>',
                role: '系统管理员',
                status: 'active',
                avatar: 'https://ui-avatars.com/api/?name=Admin&background=6366f1&color=fff',
                lastLogin: new Date('2024-01-15T10:30:00'),
                registerDate: new Date('2023-01-01T00:00:00'),
                permissions: ['full'],
                loginCount: 245
            },
            {
                id: 2,
                username: 'manager',
                name: '部门经理',
                email: '<EMAIL>',
                role: '部门经理',
                status: 'active',
                avatar: 'https://ui-avatars.com/api/?name=Manager&background=10b981&color=fff',
                lastLogin: new Date('2024-01-14T16:45:00'),
                registerDate: new Date('2023-02-15T00:00:00'),
                permissions: ['limited'],
                loginCount: 189
            },
            {
                id: 3,
                username: 'user1',
                name: '张三',
                email: '<EMAIL>',
                role: '普通用户',
                status: 'active',
                avatar: 'https://ui-avatars.com/api/?name=张三&background=f59e0b&color=fff',
                lastLogin: new Date('2024-01-13T09:15:00'),
                registerDate: new Date('2023-03-20T00:00:00'),
                permissions: ['readonly'],
                loginCount: 156
            },
            {
                id: 4,
                username: 'user2',
                name: '李四',
                email: '<EMAIL>',
                role: '普通用户',
                status: 'inactive',
                avatar: 'https://ui-avatars.com/api/?name=李四&background=ef4444&color=fff',
                lastLogin: new Date('2023-12-20T14:20:00'),
                registerDate: new Date('2023-04-10T00:00:00'),
                permissions: ['readonly'],
                loginCount: 89
            },
            {
                id: 5,
                username: 'user3',
                name: '王五',
                email: '<EMAIL>',
                role: '测试用户',
                status: 'suspended',
                avatar: 'https://ui-avatars.com/api/?name=王五&background=8b5cf6&color=fff',
                lastLogin: new Date('2024-01-10T11:30:00'),
                registerDate: new Date('2023-05-05T00:00:00'),
                permissions: ['limited'],
                loginCount: 67
            },
            {
                id: 6,
                username: 'demo',
                name: '演示用户',
                email: '<EMAIL>',
                role: '演示用户',
                status: 'active',
                avatar: 'https://ui-avatars.com/api/?name=Demo&background=06b6d4&color=fff',
                lastLogin: null,
                registerDate: new Date('2024-01-01T00:00:00'),
                permissions: ['readonly'],
                loginCount: 0
            }
        ];
        
        this.updateStats();
        this.applyFilters();
    }
    
    updateStats() {
        const totalUsers = this.users.length;
        const activeUsers = this.users.filter(u => u.status === 'active').length;
        const adminUsers = this.users.filter(u => u.role === '系统管理员' || u.role === '部门经理').length;
        const thisMonth = new Date();
        thisMonth.setMonth(thisMonth.getMonth() - 1);
        const newUsers = this.users.filter(u => u.registerDate > thisMonth).length;
        
        document.getElementById('totalUsers').textContent = totalUsers;
        document.getElementById('activeUsers').textContent = activeUsers;
        document.getElementById('adminUsers').textContent = adminUsers;
        document.getElementById('newUsers').textContent = newUsers;
    }
    
    applyFilters() {
        this.filteredUsers = this.users.filter(user => {
            // 搜索筛选
            if (this.filters.search) {
                const searchTerm = this.filters.search.toLowerCase();
                if (!user.name.toLowerCase().includes(searchTerm) &&
                    !user.email.toLowerCase().includes(searchTerm) &&
                    !user.username.toLowerCase().includes(searchTerm)) {
                    return false;
                }
            }
            
            // 角色筛选
            if (this.filters.role && user.role !== this.filters.role) {
                return false;
            }
            
            // 状态筛选
            if (this.filters.status && user.status !== this.filters.status) {
                return false;
            }
            
            // 注册日期筛选
            if (this.filters.registerDateFrom) {
                const fromDate = new Date(this.filters.registerDateFrom);
                if (user.registerDate < fromDate) {
                    return false;
                }
            }
            
            if (this.filters.registerDateTo) {
                const toDate = new Date(this.filters.registerDateTo);
                toDate.setHours(23, 59, 59, 999);
                if (user.registerDate > toDate) {
                    return false;
                }
            }
            
            // 最后登录筛选
            if (this.filters.lastLogin) {
                const now = new Date();
                switch (this.filters.lastLogin) {
                    case 'today':
                        if (!user.lastLogin || user.lastLogin.toDateString() !== now.toDateString()) {
                            return false;
                        }
                        break;
                    case 'week':
                        const weekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
                        if (!user.lastLogin || user.lastLogin < weekAgo) {
                            return false;
                        }
                        break;
                    case 'month':
                        const monthAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
                        if (!user.lastLogin || user.lastLogin < monthAgo) {
                            return false;
                        }
                        break;
                    case 'never':
                        if (user.lastLogin) {
                            return false;
                        }
                        break;
                }
            }
            
            // 权限筛选
            if (this.filters.permission) {
                if (!user.permissions.includes(this.filters.permission)) {
                    return false;
                }
            }
            
            return true;
        });
        
        // 排序
        this.filteredUsers.sort((a, b) => {
            let aValue = a[this.sortField];
            let bValue = b[this.sortField];
            
            if (aValue instanceof Date) {
                aValue = aValue.getTime();
                bValue = bValue ? bValue.getTime() : 0;
            }
            
            if (typeof aValue === 'string') {
                aValue = aValue.toLowerCase();
                bValue = bValue.toLowerCase();
            }
            
            if (this.sortDirection === 'asc') {
                return aValue > bValue ? 1 : -1;
            } else {
                return aValue < bValue ? 1 : -1;
            }
        });
        
        this.currentPage = 1;
        this.renderTable();
        this.renderPagination();
    }
    
    renderTable() {
        const tbody = document.getElementById('usersTableBody');
        const startIndex = (this.currentPage - 1) * this.pageSize;
        const endIndex = startIndex + this.pageSize;
        const pageUsers = this.filteredUsers.slice(startIndex, endIndex);
        
        tbody.innerHTML = pageUsers.map(user => `
            <tr>
                <td>
                    <input type="checkbox" value="${user.id}" onchange="userManager.toggleUserSelection(${user.id})">
                </td>
                <td>
                    <div class="user-info-cell">
                        <div class="user-avatar-small">
                            <img src="${user.avatar}" alt="${user.name}">
                        </div>
                        <div class="user-details">
                            <div class="user-name">${user.name}</div>
                            <div class="user-email">${user.email}</div>
                            <div class="user-username">@${user.username}</div>
                        </div>
                    </div>
                </td>
                <td>
                    <span class="role-badge role-${user.role.replace(/\s+/g, '-').toLowerCase()}">${user.role}</span>
                </td>
                <td>
                    <span class="status-badge status-${user.status}">${this.getStatusText(user.status)}</span>
                </td>
                <td>
                    ${user.lastLogin ? this.formatDate(user.lastLogin) : '<span class="text-muted">从未登录</span>'}
                </td>
                <td>
                    ${this.formatDate(user.registerDate)}
                </td>
                <td>
                    <div class="action-buttons">
                        <button class="btn-icon" onclick="userManager.editUser(${user.id})" title="编辑">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn-icon" onclick="userManager.viewUserDetails(${user.id})" title="详情">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button class="btn-icon" onclick="userManager.toggleUserStatus(${user.id})" title="${user.status === 'active' ? '停用' : '激活'}">
                            <i class="fas fa-${user.status === 'active' ? 'pause' : 'play'}"></i>
                        </button>
                        <button class="btn-icon danger" onclick="userManager.deleteUser(${user.id})" title="删除">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </td>
            </tr>
        `).join('');
        
        this.updateBulkActionsToolbar();
    }
    
    renderPagination() {
        const pagination = document.getElementById('usersPagination');
        const totalPages = Math.ceil(this.filteredUsers.length / this.pageSize);
        
        if (totalPages <= 1) {
            pagination.innerHTML = '';
            return;
        }
        
        let paginationHTML = `
            <div class="pagination-info">
                显示 ${(this.currentPage - 1) * this.pageSize + 1}-${Math.min(this.currentPage * this.pageSize, this.filteredUsers.length)} 
                共 ${this.filteredUsers.length} 条记录
            </div>
            <div class="pagination-controls">
        `;
        
        // 上一页
        paginationHTML += `
            <button class="pagination-btn ${this.currentPage === 1 ? 'disabled' : ''}" 
                    onclick="userManager.goToPage(${this.currentPage - 1})" 
                    ${this.currentPage === 1 ? 'disabled' : ''}>
                <i class="fas fa-chevron-left"></i>
            </button>
        `;
        
        // 页码
        for (let i = 1; i <= totalPages; i++) {
            if (i === 1 || i === totalPages || (i >= this.currentPage - 2 && i <= this.currentPage + 2)) {
                paginationHTML += `
                    <button class="pagination-btn ${i === this.currentPage ? 'active' : ''}" 
                            onclick="userManager.goToPage(${i})">
                        ${i}
                    </button>
                `;
            } else if (i === this.currentPage - 3 || i === this.currentPage + 3) {
                paginationHTML += '<span class="pagination-ellipsis">...</span>';
            }
        }
        
        // 下一页
        paginationHTML += `
            <button class="pagination-btn ${this.currentPage === totalPages ? 'disabled' : ''}" 
                    onclick="userManager.goToPage(${this.currentPage + 1})" 
                    ${this.currentPage === totalPages ? 'disabled' : ''}>
                <i class="fas fa-chevron-right"></i>
            </button>
        `;
        
        paginationHTML += '</div>';
        pagination.innerHTML = paginationHTML;
    }
    
    getStatusText(status) {
        const statusMap = {
            'active': '活跃',
            'inactive': '非活跃',
            'suspended': '已暂停'
        };
        return statusMap[status] || status;
    }
    
    formatDate(date) {
        if (!date) return '';
        return new Intl.DateTimeFormat('zh-CN', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit'
        }).format(date);
    }
    
    // 用户选择相关方法
    toggleUserSelection(userId) {
        if (this.selectedUsers.has(userId)) {
            this.selectedUsers.delete(userId);
        } else {
            this.selectedUsers.add(userId);
        }
        this.updateBulkActionsToolbar();
        this.updateSelectAllCheckbox();
    }
    
    toggleSelectAll() {
        const selectAllCheckbox = document.getElementById('selectAll');
        const pageUsers = this.getPageUsers();
        
        if (selectAllCheckbox.checked) {
            pageUsers.forEach(user => this.selectedUsers.add(user.id));
        } else {
            pageUsers.forEach(user => this.selectedUsers.delete(user.id));
        }
        
        this.renderTable();
        this.updateBulkActionsToolbar();
    }
    
    updateSelectAllCheckbox() {
        const selectAllCheckbox = document.getElementById('selectAll');
        const pageUsers = this.getPageUsers();
        const selectedPageUsers = pageUsers.filter(user => this.selectedUsers.has(user.id));
        
        if (selectedPageUsers.length === 0) {
            selectAllCheckbox.checked = false;
            selectAllCheckbox.indeterminate = false;
        } else if (selectedPageUsers.length === pageUsers.length) {
            selectAllCheckbox.checked = true;
            selectAllCheckbox.indeterminate = false;
        } else {
            selectAllCheckbox.checked = false;
            selectAllCheckbox.indeterminate = true;
        }
    }
    
    updateBulkActionsToolbar() {
        const toolbar = document.getElementById('bulkActionsToolbar');
        const selectedCount = document.getElementById('selectedCount');
        
        if (this.selectedUsers.size > 0) {
            toolbar.style.display = 'flex';
            selectedCount.textContent = this.selectedUsers.size;
        } else {
            toolbar.style.display = 'none';
        }
    }
    
    getPageUsers() {
        const startIndex = (this.currentPage - 1) * this.pageSize;
        const endIndex = startIndex + this.pageSize;
        return this.filteredUsers.slice(startIndex, endIndex);
    }
    
    // 分页方法
    goToPage(page) {
        const totalPages = Math.ceil(this.filteredUsers.length / this.pageSize);
        if (page >= 1 && page <= totalPages) {
            this.currentPage = page;
            this.renderTable();
            this.renderPagination();
        }
    }
    
    // 筛选方法
    setFilter(key, value) {
        this.filters[key] = value;
        this.applyFilters();
    }
    
    // 排序方法
    sortBy(field) {
        if (this.sortField === field) {
            this.sortDirection = this.sortDirection === 'asc' ? 'desc' : 'asc';
        } else {
            this.sortField = field;
            this.sortDirection = 'asc';
        }
        this.applyFilters();
    }
}

// 全局用户管理器实例
let userManager = null;

// 用户操作方法
function filterUsers() {
    if (!userManager) return;

    const search = document.getElementById('userSearch').value;
    const role = document.getElementById('roleFilter').value;
    const status = document.getElementById('statusFilter').value;

    userManager.setFilter('search', search);
    userManager.setFilter('role', role);
    userManager.setFilter('status', status);
}

function toggleAdvancedFilters() {
    const advancedFilters = document.getElementById('advancedFilters');
    const button = event.target.closest('button');
    const icon = button.querySelector('i');

    if (advancedFilters.style.display === 'none') {
        advancedFilters.style.display = 'block';
        icon.className = 'fas fa-chevron-up';
        button.innerHTML = '<i class="fas fa-chevron-up"></i> 收起筛选';
    } else {
        advancedFilters.style.display = 'none';
        icon.className = 'fas fa-sliders-h';
        button.innerHTML = '<i class="fas fa-sliders-h"></i> 高级筛选';
    }
}

function applyAdvancedFilters() {
    if (!userManager) return;

    const registerDateFrom = document.getElementById('registerDateFrom').value;
    const registerDateTo = document.getElementById('registerDateTo').value;
    const lastLogin = document.getElementById('lastLoginFilter').value;
    const permission = document.getElementById('permissionFilter').value;

    userManager.setFilter('registerDateFrom', registerDateFrom);
    userManager.setFilter('registerDateTo', registerDateTo);
    userManager.setFilter('lastLogin', lastLogin);
    userManager.setFilter('permission', permission);

    showNotification('高级筛选已应用', 'success');
}

function clearAdvancedFilters() {
    if (!userManager) return;

    document.getElementById('registerDateFrom').value = '';
    document.getElementById('registerDateTo').value = '';
    document.getElementById('lastLoginFilter').value = '';
    document.getElementById('permissionFilter').value = '';

    userManager.setFilter('registerDateFrom', '');
    userManager.setFilter('registerDateTo', '');
    userManager.setFilter('lastLogin', '');
    userManager.setFilter('permission', '');

    showNotification('筛选条件已清除', 'info');
}

function refreshUsers() {
    if (!userManager) return;

    userManager.initializeUsers();
    showNotification('用户列表已刷新', 'success');
}

function toggleSelectAll() {
    if (!userManager) return;
    userManager.toggleSelectAll();
}

// 批量操作方法
function bulkActivateUsers() {
    if (!userManager || userManager.selectedUsers.size === 0) return;

    if (confirm(`确定要激活选中的 ${userManager.selectedUsers.size} 个用户吗？`)) {
        userManager.selectedUsers.forEach(userId => {
            const user = userManager.users.find(u => u.id === userId);
            if (user) user.status = 'active';
        });

        userManager.selectedUsers.clear();
        userManager.applyFilters();
        showNotification('用户已批量激活', 'success');
    }
}

function bulkDeactivateUsers() {
    if (!userManager || userManager.selectedUsers.size === 0) return;

    if (confirm(`确定要停用选中的 ${userManager.selectedUsers.size} 个用户吗？`)) {
        userManager.selectedUsers.forEach(userId => {
            const user = userManager.users.find(u => u.id === userId);
            if (user) user.status = 'inactive';
        });

        userManager.selectedUsers.clear();
        userManager.applyFilters();
        showNotification('用户已批量停用', 'warning');
    }
}

function bulkChangeRole() {
    if (!userManager || userManager.selectedUsers.size === 0) return;

    const newRole = prompt('请输入新角色（系统管理员/部门经理/普通用户/测试用户/演示用户）：');
    if (newRole && ['系统管理员', '部门经理', '普通用户', '测试用户', '演示用户'].includes(newRole)) {
        userManager.selectedUsers.forEach(userId => {
            const user = userManager.users.find(u => u.id === userId);
            if (user) user.role = newRole;
        });

        userManager.selectedUsers.clear();
        userManager.applyFilters();
        showNotification(`已批量更改为 ${newRole}`, 'success');
    } else if (newRole) {
        showNotification('无效的角色类型', 'error');
    }
}

function bulkExportUsers() {
    if (!userManager || userManager.selectedUsers.size === 0) return;

    const selectedUsersData = userManager.users.filter(u => userManager.selectedUsers.has(u.id));
    const csvContent = generateUserCSV(selectedUsersData);
    downloadCSV(csvContent, `selected_users_${new Date().toISOString().slice(0, 10)}.csv`);

    showNotification(`已导出 ${selectedUsersData.length} 个用户`, 'success');
}

function bulkDeleteUsers() {
    if (!userManager || userManager.selectedUsers.size === 0) return;

    if (confirm(`确定要删除选中的 ${userManager.selectedUsers.size} 个用户吗？此操作不可撤销！`)) {
        userManager.users = userManager.users.filter(u => !userManager.selectedUsers.has(u.id));
        userManager.selectedUsers.clear();
        userManager.updateStats();
        userManager.applyFilters();
        showNotification('用户已批量删除', 'error');
    }
}

// 单个用户操作方法
function editUser(userId) {
    const user = userManager.users.find(u => u.id === userId);
    if (!user) return;

    // 这里可以打开编辑用户的模态框
    showNotification(`编辑用户: ${user.name}`, 'info');
}

function viewUserDetails(userId) {
    const user = userManager.users.find(u => u.id === userId);
    if (!user) return;

    // 这里可以打开用户详情的模态框
    showNotification(`查看用户详情: ${user.name}`, 'info');
}

function toggleUserStatus(userId) {
    const user = userManager.users.find(u => u.id === userId);
    if (!user) return;

    const newStatus = user.status === 'active' ? 'inactive' : 'active';
    user.status = newStatus;

    userManager.applyFilters();
    showNotification(`用户 ${user.name} 已${newStatus === 'active' ? '激活' : '停用'}`,
                    newStatus === 'active' ? 'success' : 'warning');
}

function deleteUser(userId) {
    const user = userManager.users.find(u => u.id === userId);
    if (!user) return;

    if (confirm(`确定要删除用户 ${user.name} 吗？此操作不可撤销！`)) {
        userManager.users = userManager.users.filter(u => u.id !== userId);
        userManager.updateStats();
        userManager.applyFilters();
        showNotification(`用户 ${user.name} 已删除`, 'error');
    }
}

// 导出功能
function exportUsers() {
    if (!userManager) return;

    const csvContent = generateUserCSV(userManager.filteredUsers);
    downloadCSV(csvContent, `users_export_${new Date().toISOString().slice(0, 10)}.csv`);

    showNotification(`已导出 ${userManager.filteredUsers.length} 个用户`, 'success');
}

function generateUserCSV(users) {
    const headers = ['ID', '用户名', '姓名', '邮箱', '角色', '状态', '最后登录', '注册时间', '登录次数'];
    const rows = users.map(user => [
        user.id,
        user.username,
        user.name,
        user.email,
        user.role,
        user.status,
        user.lastLogin ? user.lastLogin.toISOString() : '',
        user.registerDate.toISOString(),
        user.loginCount
    ]);

    return [headers, ...rows].map(row =>
        row.map(field => `"${String(field).replace(/"/g, '""')}"`).join(',')
    ).join('\n');
}

function downloadCSV(content, filename) {
    const blob = new Blob(['\ufeff' + content], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);

    link.setAttribute('href', url);
    link.setAttribute('download', filename);
    link.style.visibility = 'hidden';

    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
}

function showAddUserModal() {
    showNotification('添加用户功能开发中...', 'info');
}

// 初始化用户管理
function initializeUserManagement() {
    userManager = new EnhancedUserManager();
    console.log('✅ 增强用户管理器已初始化');
}
