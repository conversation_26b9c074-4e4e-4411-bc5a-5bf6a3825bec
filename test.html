<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>功能测试页面</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; }
        button { padding: 10px 15px; margin: 5px; cursor: pointer; }
        .success { color: green; }
        .error { color: red; }
        .info { color: blue; }
        #output { background: #f5f5f5; padding: 10px; margin: 10px 0; min-height: 100px; }
    </style>
</head>
<body>
    <h1>🔧 系统功能测试页面</h1>
    
    <div class="test-section">
        <h2>📋 基础检查</h2>
        <button onclick="checkBasics()">检查基础功能</button>
        <button onclick="checkLogin()">检查登录状态</button>
        <button onclick="testNavigation()">测试导航</button>
    </div>
    
    <div class="test-section">
        <h2>🧠 NER功能测试</h2>
        <button onclick="testNERGenerator()">测试NER生成器</button>
        <button onclick="generateTestData()">生成测试数据</button>
        <button onclick="showTestResults()">显示测试结果</button>
    </div>
    
    <div class="test-section">
        <h2>🎯 快速操作</h2>
        <button onclick="goToLogin()">跳转到登录页</button>
        <button onclick="goToMain()">跳转到主页</button>
        <button onclick="clearStorage()">清除存储</button>
    </div>
    
    <div id="output"></div>
    
    <script>
        const output = document.getElementById('output');
        
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const div = document.createElement('div');
            div.className = type;
            div.innerHTML = `[${timestamp}] ${message}`;
            output.appendChild(div);
            output.scrollTop = output.scrollHeight;
        }
        
        function checkBasics() {
            log('🔍 开始基础检查...');
            
            // 检查localStorage
            try {
                localStorage.setItem('test', 'value');
                localStorage.removeItem('test');
                log('✅ localStorage 正常', 'success');
            } catch (e) {
                log('❌ localStorage 异常: ' + e.message, 'error');
            }
            
            // 检查必要的文件
            const files = ['index.html', 'login.html', 'app.js', 'styles.css'];
            files.forEach(file => {
                fetch(file)
                    .then(response => {
                        if (response.ok) {
                            log(`✅ 文件存在: ${file}`, 'success');
                        } else {
                            log(`❌ 文件不存在: ${file}`, 'error');
                        }
                    })
                    .catch(e => log(`❌ 检查文件失败 ${file}: ${e.message}`, 'error'));
            });
        }
        
        function checkLogin() {
            log('🔐 检查登录状态...');
            
            const isLoggedIn = localStorage.getItem('isLoggedIn');
            const currentUser = localStorage.getItem('currentUser');
            
            if (isLoggedIn === 'true' && currentUser) {
                const user = JSON.parse(currentUser);
                log(`✅ 已登录用户: ${user.name} (${user.role})`, 'success');
            } else {
                log('❌ 未登录或登录信息无效', 'error');
                
                // 设置测试登录状态
                localStorage.setItem('isLoggedIn', 'true');
                localStorage.setItem('currentUser', JSON.stringify({
                    username: 'admin',
                    name: '管理员',
                    role: '系统管理员'
                }));
                log('✅ 已设置测试登录状态', 'success');
            }
        }
        
        function testNavigation() {
            log('🧭 测试导航功能...');
            
            // 模拟点击导航
            const testPages = ['dashboard', 'users', 'analytics', 'ner'];
            
            testPages.forEach(page => {
                setTimeout(() => {
                    log(`📄 测试页面: ${page}`);
                    
                    // 检查页面元素是否存在
                    fetch('index.html')
                        .then(response => response.text())
                        .then(html => {
                            if (html.includes(`id="${page}"`)) {
                                log(`✅ 页面元素存在: ${page}`, 'success');
                            } else {
                                log(`❌ 页面元素不存在: ${page}`, 'error');
                            }
                        });
                }, testPages.indexOf(page) * 500);
            });
        }
        
        function testNERGenerator() {
            log('🧠 测试NER生成器...');
            
            // 简单的NER生成器测试
            const testGenerator = {
                names: ['张三', '李四', '王五'],
                locations: ['北京', '上海', '广州'],
                orgs: ['阿里巴巴', '腾讯', '百度'],
                
                generateSample() {
                    const name = this.names[Math.floor(Math.random() * this.names.length)];
                    const location = this.locations[Math.floor(Math.random() * this.locations.length)];
                    const org = this.orgs[Math.floor(Math.random() * this.orgs.length)];
                    
                    return `${name}在${location}的${org}工作。`;
                }
            };
            
            try {
                const sample = testGenerator.generateSample();
                log(`✅ NER样本生成成功: ${sample}`, 'success');
                
                // 测试实体识别
                const entities = [];
                testGenerator.names.forEach(name => {
                    if (sample.includes(name)) entities.push(`${name}(PERSON)`);
                });
                testGenerator.locations.forEach(loc => {
                    if (sample.includes(loc)) entities.push(`${loc}(LOCATION)`);
                });
                testGenerator.orgs.forEach(org => {
                    if (sample.includes(org)) entities.push(`${org}(ORGANIZATION)`);
                });
                
                log(`✅ 识别到实体: ${entities.join(', ')}`, 'success');
                
            } catch (e) {
                log(`❌ NER生成器测试失败: ${e.message}`, 'error');
            }
        }
        
        function generateTestData() {
            log('📊 生成测试数据...');
            
            const testData = [];
            for (let i = 0; i < 5; i++) {
                testData.push({
                    id: i + 1,
                    text: `这是第${i + 1}个测试样本。`,
                    entities: Math.floor(Math.random() * 3) + 1
                });
            }
            
            localStorage.setItem('testNERData', JSON.stringify(testData));
            log(`✅ 生成了 ${testData.length} 条测试数据`, 'success');
        }
        
        function showTestResults() {
            log('📈 显示测试结果...');
            
            const testData = localStorage.getItem('testNERData');
            if (testData) {
                const data = JSON.parse(testData);
                data.forEach(item => {
                    log(`📝 样本 ${item.id}: ${item.text} (${item.entities} 个实体)`);
                });
            } else {
                log('❌ 没有测试数据，请先生成', 'error');
            }
        }
        
        function goToLogin() {
            window.location.href = 'login.html';
        }
        
        function goToMain() {
            window.location.href = 'index.html';
        }
        
        function clearStorage() {
            localStorage.clear();
            log('✅ 已清除所有存储数据', 'success');
        }
        
        // 页面加载时自动运行基础检查
        window.addEventListener('load', () => {
            log('🚀 测试页面已加载');
            checkBasics();
        });
    </script>
</body>
</html>
