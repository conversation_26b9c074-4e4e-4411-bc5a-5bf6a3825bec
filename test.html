<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>系统测试页面</title>
    <style>
        body {
            font-family: 'Inter', sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f8fafc;
        }
        .test-container {
            background: white;
            border-radius: 12px;
            padding: 24px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
        }
        .test-title {
            color: #1e293b;
            margin-bottom: 16px;
            font-size: 18px;
            font-weight: 600;
        }
        .account-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 16px;
            margin-bottom: 20px;
        }
        .account-card {
            border: 2px solid #e2e8f0;
            border-radius: 8px;
            padding: 16px;
            cursor: pointer;
            transition: all 0.2s;
        }
        .account-card:hover {
            border-color: #6366f1;
            background: #f8fafc;
        }
        .account-username {
            font-weight: 600;
            color: #1e293b;
            margin-bottom: 4px;
        }
        .account-password {
            font-family: monospace;
            background: #f1f5f9;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 14px;
            margin-bottom: 8px;
        }
        .account-role {
            color: #64748b;
            font-size: 14px;
        }
        .btn {
            background: #6366f1;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 500;
            margin: 8px;
            transition: all 0.2s;
        }
        .btn:hover {
            background: #4f46e5;
        }
        .btn-secondary {
            background: #64748b;
        }
        .btn-secondary:hover {
            background: #475569;
        }
        .status {
            padding: 12px;
            border-radius: 8px;
            margin: 12px 0;
            font-weight: 500;
        }
        .status.success {
            background: #dcfce7;
            color: #166534;
            border: 1px solid #bbf7d0;
        }
        .status.error {
            background: #fef2f2;
            color: #dc2626;
            border: 1px solid #fecaca;
        }
        .status.info {
            background: #eff6ff;
            color: #1d4ed8;
            border: 1px solid #dbeafe;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1 class="test-title">🔐 现代企业管理系统 - 测试页面</h1>
        <p>点击下方的测试账号卡片可以快速测试登录功能</p>
    </div>

    <div class="test-container">
        <h2 class="test-title">📋 可用测试账号</h2>
        <div class="account-grid" id="accountGrid">
            <!-- 账号卡片将通过JavaScript动态生成 -->
        </div>
        <div>
            <button class="btn" onclick="openLoginPage()">🚀 打开登录页面</button>
            <button class="btn" onclick="openDashboard()">📊 打开后台管理</button>
            <button class="btn btn-secondary" onclick="clearLoginData()">🗑️ 清除登录数据</button>
        </div>
    </div>

    <div class="test-container">
        <h2 class="test-title">📊 系统状态</h2>
        <div id="systemStatus">
            <div class="status info">正在检查系统状态...</div>
        </div>
    </div>

    <div class="test-container">
        <h2 class="test-title">🔧 快速操作</h2>
        <p>以下操作可以帮助您快速测试系统功能：</p>
        <ul>
            <li><strong>自动登录测试</strong>: 点击账号卡片会自动使用该账号登录</li>
            <li><strong>登录状态检查</strong>: 系统会自动检查当前登录状态</li>
            <li><strong>数据清理</strong>: 可以清除所有登录相关的本地数据</li>
            <li><strong>页面跳转</strong>: 直接跳转到登录页面或后台管理</li>
        </ul>
    </div>

    <script>
        // 测试账号数据
        const TEST_ACCOUNTS = [
            {
                username: 'admin',
                password: 'admin123',
                role: '系统管理员',
                name: '张管理员'
            },
            {
                username: 'manager',
                password: 'manager123',
                role: '部门经理',
                name: '李经理'
            },
            {
                username: 'user',
                password: 'user123',
                role: '普通用户',
                name: '王用户'
            },
            {
                username: 'test',
                password: 'test123',
                role: '测试用户',
                name: '测试员'
            },
            {
                username: 'demo',
                password: 'demo123',
                role: '演示用户',
                name: '演示员'
            }
        ];

        // 生成账号卡片
        function generateAccountCards() {
            const grid = document.getElementById('accountGrid');
            grid.innerHTML = TEST_ACCOUNTS.map(account => `
                <div class="account-card" onclick="testLogin('${account.username}', '${account.password}', '${account.name}')">
                    <div class="account-username">${account.username}</div>
                    <div class="account-password">${account.password}</div>
                    <div class="account-role">${account.role}</div>
                </div>
            `).join('');
        }

        // 测试登录
        function testLogin(username, password, name) {
            // 模拟登录过程
            const userInfo = {
                username: username,
                name: name,
                role: TEST_ACCOUNTS.find(acc => acc.username === username).role
            };

            // 保存登录状态
            localStorage.setItem('isLoggedIn', 'true');
            localStorage.setItem('currentUser', JSON.stringify(userInfo));

            updateSystemStatus(`✅ 已使用账号 "${username}" 登录成功！用户: ${name}`, 'success');
            
            setTimeout(() => {
                if (confirm(`登录成功！是否跳转到后台管理页面？\n\n用户: ${name}\n角色: ${userInfo.role}`)) {
                    window.open('index.html', '_blank');
                }
            }, 1000);
        }

        // 打开登录页面
        function openLoginPage() {
            window.open('login.html', '_blank');
        }

        // 打开后台管理
        function openDashboard() {
            window.open('index.html', '_blank');
        }

        // 清除登录数据
        function clearLoginData() {
            localStorage.removeItem('isLoggedIn');
            localStorage.removeItem('currentUser');
            localStorage.removeItem('rememberLogin');
            localStorage.removeItem('savedUsername');
            
            updateSystemStatus('🗑️ 已清除所有登录数据', 'info');
        }

        // 更新系统状态
        function updateSystemStatus(message, type = 'info') {
            const statusDiv = document.getElementById('systemStatus');
            statusDiv.innerHTML = `<div class="status ${type}">${message}</div>`;
        }

        // 检查系统状态
        function checkSystemStatus() {
            const isLoggedIn = localStorage.getItem('isLoggedIn');
            const currentUser = localStorage.getItem('currentUser');
            
            if (isLoggedIn === 'true' && currentUser) {
                const user = JSON.parse(currentUser);
                updateSystemStatus(`✅ 当前已登录: ${user.name} (${user.role})`, 'success');
            } else {
                updateSystemStatus('❌ 当前未登录', 'error');
            }
        }

        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', function() {
            generateAccountCards();
            checkSystemStatus();
            
            // 每5秒检查一次登录状态
            setInterval(checkSystemStatus, 5000);
        });
    </script>
</body>
</html>
