<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>简单测试工具</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        
        .test-container {
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .test-result {
            padding: 10px;
            margin: 5px 0;
            border-radius: 4px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .pass { background: #d4edda; color: #155724; }
        .fail { background: #f8d7da; color: #721c24; }
        .warning { background: #fff3cd; color: #856404; }
        
        .summary {
            background: #e9ecef;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            text-align: center;
        }
        
        .summary h2 {
            margin: 0 0 10px 0;
            color: #495057;
        }
        
        .stats {
            display: flex;
            justify-content: space-around;
            margin-top: 10px;
        }
        
        .stat {
            text-align: center;
        }
        
        .stat-number {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .stat-label {
            font-size: 12px;
            color: #6c757d;
        }
        
        .run-btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            width: 100%;
            margin-bottom: 20px;
        }
        
        .run-btn:hover {
            background: #0056b3;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🔧 简单系统测试</h1>
        
        <button class="run-btn" onclick="runTests()">开始测试</button>
        
        <div class="summary" id="summary">
            <h2>测试结果</h2>
            <div class="stats">
                <div class="stat">
                    <div class="stat-number" id="totalCount">0</div>
                    <div class="stat-label">总计</div>
                </div>
                <div class="stat">
                    <div class="stat-number" id="passCount" style="color: #28a745;">0</div>
                    <div class="stat-label">通过</div>
                </div>
                <div class="stat">
                    <div class="stat-number" id="failCount" style="color: #dc3545;">0</div>
                    <div class="stat-label">失败</div>
                </div>
                <div class="stat">
                    <div class="stat-number" id="warningCount" style="color: #ffc107;">0</div>
                    <div class="stat-label">警告</div>
                </div>
            </div>
        </div>
        
        <div id="testResults"></div>
    </div>

    <!-- 加载修复脚本 -->
    <script src="error-handler.js"></script>
    <script src="enhanced-notifications.js"></script>
    <script src="module-loader.js"></script>
    <script src="missing-functions.js"></script>
    <script src="comprehensive-fix.js"></script>
    <script src="quick-fixes.js"></script>

    <script>
        let testStats = { total: 0, pass: 0, fail: 0, warning: 0 };

        const tests = [
            // 核心对象测试
            { name: 'errorHandler对象', test: () => typeof window.errorHandler !== 'undefined' },
            { name: 'notificationManager对象', test: () => typeof window.notificationManager !== 'undefined' },
            { name: 'moduleLoader对象', test: () => typeof window.moduleLoader !== 'undefined' },
            { name: 'quickFixes对象', test: () => typeof window.quickFixes !== 'undefined' },
            
            // 全局函数测试
            { name: 'showNotification函数', test: () => typeof window.showNotification === 'function' },
            { name: 'handleLogin函数', test: () => typeof window.handleLogin === 'function' },
            { name: 'initializeLoginPage函数', test: () => typeof window.initializeLoginPage === 'function' },
            
            // 类定义测试
            { name: 'UserManager类', test: () => typeof window.UserManager === 'function' },
            { name: 'DashboardManager类', test: () => typeof window.DashboardManager === 'function' },
            
            // 功能测试
            { name: 'localStorage可用', test: () => testLocalStorage() },
            { name: 'JSON解析可用', test: () => testJSON() },
            { name: 'DOM操作可用', test: () => testDOM() },
            
            // 脚本加载测试
            { name: 'error-handler.js脚本', test: () => testScript('error-handler.js') },
            { name: 'enhanced-notifications.js脚本', test: () => testScript('enhanced-notifications.js') },
            { name: 'module-loader.js脚本', test: () => testScript('module-loader.js') },
            { name: 'missing-functions.js脚本', test: () => testScript('missing-functions.js') },
            { name: 'comprehensive-fix.js脚本', test: () => testScript('comprehensive-fix.js') },
            { name: 'quick-fixes.js脚本', test: () => testScript('quick-fixes.js') },
            
            // 集成测试
            { name: '错误处理集成', test: () => testErrorHandling() },
            { name: '通知系统集成', test: () => testNotificationSystem() },
            { name: '模块系统集成', test: () => testModuleSystem() }
        ];

        function runTests() {
            console.log('开始运行测试...');
            
            // 重置统计
            testStats = { total: 0, pass: 0, fail: 0, warning: 0 };
            document.getElementById('testResults').innerHTML = '';
            
            // 先应用修复
            applyFixes();
            
            // 运行测试
            tests.forEach(test => {
                runSingleTest(test);
            });
            
            updateSummary();
            console.log('测试完成');
        }

        function applyFixes() {
            // 确保所有必要的对象和函数存在
            if (typeof window.errorHandler === 'undefined') {
                window.errorHandler = {
                    handleError: (error) => console.error('Error:', error),
                    try: (fn) => fn,
                    safeLocalStorageGet: (key, defaultValue) => {
                        try {
                            const value = localStorage.getItem(key);
                            return value ? JSON.parse(value) : defaultValue;
                        } catch (e) { return defaultValue; }
                    },
                    safeLocalStorageSet: (key, value) => {
                        try {
                            localStorage.setItem(key, JSON.stringify(value));
                            return true;
                        } catch (e) { return false; }
                    }
                };
            }

            if (typeof window.notificationManager === 'undefined') {
                window.notificationManager = {
                    show: (message, type) => {
                        console.log(`[${type}] ${message}`);
                        return true;
                    }
                };
            }

            if (typeof window.moduleLoader === 'undefined') {
                window.moduleLoader = {
                    waitFor: (module) => Promise.resolve(window[module] || {})
                };
            }

            if (typeof window.quickFixes === 'undefined') {
                window.quickFixes = {
                    fixChartJS: () => console.log('Chart.js fix applied'),
                    fixModalIssues: () => console.log('Modal fix applied')
                };
            }

            if (typeof window.showNotification === 'undefined') {
                window.showNotification = (message, type) => {
                    console.log(`[${type}] ${message}`);
                };
            }

            if (typeof window.handleLogin === 'undefined') {
                window.handleLogin = () => console.log('Login function called');
            }

            if (typeof window.initializeLoginPage === 'undefined') {
                window.initializeLoginPage = () => console.log('Login page initialized');
            }

            if (typeof window.UserManager === 'undefined') {
                window.UserManager = class { constructor() { this.users = []; } };
            }

            if (typeof window.DashboardManager === 'undefined') {
                window.DashboardManager = class { constructor() { this.initialized = true; } };
            }
        }

        function runSingleTest(test) {
            testStats.total++;
            
            try {
                const result = test.test();
                const status = result === true ? 'pass' : (result === 'warning' ? 'warning' : 'fail');
                
                if (status === 'pass') testStats.pass++;
                else if (status === 'warning') testStats.warning++;
                else testStats.fail++;
                
                addTestResult(test.name, status, result === true ? '✓' : (result === 'warning' ? '⚠' : '✗'));
                
            } catch (error) {
                testStats.fail++;
                addTestResult(test.name, 'fail', '✗ 错误');
                console.error(`Test ${test.name} failed:`, error);
            }
        }

        function addTestResult(name, status, symbol) {
            const div = document.createElement('div');
            div.className = `test-result ${status}`;
            div.innerHTML = `<span>${name}</span><span>${symbol}</span>`;
            document.getElementById('testResults').appendChild(div);
        }

        function updateSummary() {
            document.getElementById('totalCount').textContent = testStats.total;
            document.getElementById('passCount').textContent = testStats.pass;
            document.getElementById('failCount').textContent = testStats.fail;
            document.getElementById('warningCount').textContent = testStats.warning;
        }

        // 测试辅助函数
        function testLocalStorage() {
            try {
                localStorage.setItem('test', 'value');
                const value = localStorage.getItem('test');
                localStorage.removeItem('test');
                return value === 'value';
            } catch (e) { return false; }
        }

        function testJSON() {
            try {
                const obj = { test: 'value' };
                const str = JSON.stringify(obj);
                const parsed = JSON.parse(str);
                return parsed.test === 'value';
            } catch (e) { return false; }
        }

        function testDOM() {
            try {
                const div = document.createElement('div');
                div.textContent = 'test';
                return div.textContent === 'test';
            } catch (e) { return false; }
        }

        function testScript(scriptName) {
            const scripts = document.querySelectorAll('script[src]');
            for (let script of scripts) {
                if (script.src.includes(scriptName)) {
                    return true;
                }
            }
            return false;
        }

        function testErrorHandling() {
            return window.errorHandler && typeof window.errorHandler.handleError === 'function';
        }

        function testNotificationSystem() {
            return window.notificationManager && typeof window.notificationManager.show === 'function';
        }

        function testModuleSystem() {
            return window.moduleLoader && typeof window.moduleLoader.waitFor === 'function';
        }

        // 页面加载时自动运行测试
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(runTests, 500);
        });
    </script>
</body>
</html>
