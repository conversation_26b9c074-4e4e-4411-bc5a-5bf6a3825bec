// 高级数据分析与BI系统
class AdvancedAnalytics {
    constructor() {
        this.dashboards = [];
        this.widgets = [];
        this.dataSources = [];
        this.reports = [];
        this.kpis = [];
        this.currentDashboard = null;
        this.draggedWidget = null;
        this.chartInstances = new Map();
        
        this.initializeAnalytics();
        this.setupDataSources();
        this.loadDefaultDashboards();
    }

    initializeAnalytics() {
        this.createAnalyticsInterface();
        this.bindAnalyticsEvents();
        this.setupDragAndDrop();
        this.loadStoredData();
    }

    createAnalyticsInterface() {
        const analyticsPanel = document.createElement('div');
        analyticsPanel.id = 'analyticsPanel';
        analyticsPanel.className = 'analytics-panel';
        analyticsPanel.innerHTML = `
            <div class="analytics-header">
                <h3>
                    <i class="fas fa-chart-line"></i>
                    高级数据分析
                </h3>
                <div class="analytics-controls">
                    <select id="dashboardSelector" onchange="advancedAnalytics.switchDashboard(this.value)">
                        <option value="">选择仪表板</option>
                    </select>
                    <button class="btn-secondary" onclick="advancedAnalytics.createDashboard()">
                        <i class="fas fa-plus"></i>
                        新建仪表板
                    </button>
                    <button class="btn-secondary" onclick="advancedAnalytics.showReportBuilder()">
                        <i class="fas fa-file-alt"></i>
                        报表生成器
                    </button>
                    <button class="btn-primary" onclick="advancedAnalytics.showDataMining()">
                        <i class="fas fa-search"></i>
                        数据挖掘
                    </button>
                    <button class="btn-icon" onclick="advancedAnalytics.closeAnalyticsPanel()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            </div>
            
            <div class="analytics-body">
                <div class="analytics-sidebar">
                    <div class="sidebar-section">
                        <h4>
                            <i class="fas fa-puzzle-piece"></i>
                            组件库
                        </h4>
                        <div class="widget-library" id="widgetLibrary">
                            <div class="widget-item" draggable="true" data-type="chart-line">
                                <i class="fas fa-chart-line"></i>
                                <span>折线图</span>
                            </div>
                            <div class="widget-item" draggable="true" data-type="chart-bar">
                                <i class="fas fa-chart-bar"></i>
                                <span>柱状图</span>
                            </div>
                            <div class="widget-item" draggable="true" data-type="chart-pie">
                                <i class="fas fa-chart-pie"></i>
                                <span>饼图</span>
                            </div>
                            <div class="widget-item" draggable="true" data-type="chart-area">
                                <i class="fas fa-chart-area"></i>
                                <span>面积图</span>
                            </div>
                            <div class="widget-item" draggable="true" data-type="metric-card">
                                <i class="fas fa-tachometer-alt"></i>
                                <span>指标卡</span>
                            </div>
                            <div class="widget-item" draggable="true" data-type="data-table">
                                <i class="fas fa-table"></i>
                                <span>数据表格</span>
                            </div>
                            <div class="widget-item" draggable="true" data-type="gauge">
                                <i class="fas fa-gauge"></i>
                                <span>仪表盘</span>
                            </div>
                            <div class="widget-item" draggable="true" data-type="heatmap">
                                <i class="fas fa-th"></i>
                                <span>热力图</span>
                            </div>
                            <div class="widget-item" draggable="true" data-type="funnel">
                                <i class="fas fa-filter"></i>
                                <span>漏斗图</span>
                            </div>
                            <div class="widget-item" draggable="true" data-type="radar">
                                <i class="fas fa-spider"></i>
                                <span>雷达图</span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="sidebar-section">
                        <h4>
                            <i class="fas fa-database"></i>
                            数据源
                        </h4>
                        <div class="data-sources" id="dataSources">
                            <!-- 数据源列表 -->
                        </div>
                        <button class="btn-secondary btn-small" onclick="advancedAnalytics.addDataSource()">
                            <i class="fas fa-plus"></i>
                            添加数据源
                        </button>
                    </div>
                    
                    <div class="sidebar-section">
                        <h4>
                            <i class="fas fa-bullseye"></i>
                            KPI监控
                        </h4>
                        <div class="kpi-list" id="kpiList">
                            <!-- KPI列表 -->
                        </div>
                        <button class="btn-secondary btn-small" onclick="advancedAnalytics.createKPI()">
                            <i class="fas fa-plus"></i>
                            创建KPI
                        </button>
                    </div>
                </div>
                
                <div class="analytics-main">
                    <div class="dashboard-toolbar">
                        <div class="toolbar-left">
                            <span class="dashboard-title" id="dashboardTitle">选择或创建仪表板</span>
                            <button class="btn-icon" onclick="advancedAnalytics.editDashboardTitle()" title="编辑标题">
                                <i class="fas fa-edit"></i>
                            </button>
                        </div>
                        <div class="toolbar-right">
                            <button class="btn-secondary" onclick="advancedAnalytics.toggleEditMode()" id="editModeBtn">
                                <i class="fas fa-edit"></i>
                                编辑模式
                            </button>
                            <button class="btn-secondary" onclick="advancedAnalytics.refreshDashboard()">
                                <i class="fas fa-sync"></i>
                                刷新
                            </button>
                            <button class="btn-secondary" onclick="advancedAnalytics.exportDashboard()">
                                <i class="fas fa-download"></i>
                                导出
                            </button>
                            <button class="btn-secondary" onclick="advancedAnalytics.shareDashboard()">
                                <i class="fas fa-share"></i>
                                分享
                            </button>
                        </div>
                    </div>
                    
                    <div class="dashboard-canvas" id="dashboardCanvas">
                        <div class="empty-dashboard">
                            <div class="empty-icon">
                                <i class="fas fa-chart-line"></i>
                            </div>
                            <h3>开始创建您的数据仪表板</h3>
                            <p>从左侧组件库拖拽图表组件到此处，或选择现有仪表板</p>
                            <div class="quick-actions">
                                <button class="btn-primary" onclick="advancedAnalytics.createDashboard()">
                                    创建新仪表板
                                </button>
                                <button class="btn-secondary" onclick="advancedAnalytics.loadTemplate()">
                                    使用模板
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
        
        document.body.appendChild(analyticsPanel);
        
        // 创建组件配置面板
        this.createWidgetConfigPanel();
        
        // 创建报表生成器
        this.createReportBuilder();
        
        // 创建数据挖掘工具
        this.createDataMiningTool();
    }

    createWidgetConfigPanel() {
        const configPanel = document.createElement('div');
        configPanel.id = 'widgetConfigPanel';
        configPanel.className = 'widget-config-panel';
        configPanel.innerHTML = `
            <div class="config-header">
                <h4>组件配置</h4>
                <button class="btn-icon" onclick="advancedAnalytics.closeWidgetConfig()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="config-body">
                <div class="config-section">
                    <h5>基本设置</h5>
                    <div class="form-group">
                        <label>组件标题</label>
                        <input type="text" id="widgetTitle" placeholder="输入组件标题">
                    </div>
                    <div class="form-group">
                        <label>数据源</label>
                        <select id="widgetDataSource">
                            <option value="">选择数据源</option>
                        </select>
                    </div>
                </div>
                
                <div class="config-section">
                    <h5>图表设置</h5>
                    <div class="form-group">
                        <label>X轴字段</label>
                        <select id="widgetXAxis">
                            <option value="">选择字段</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>Y轴字段</label>
                        <select id="widgetYAxis">
                            <option value="">选择字段</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>聚合方式</label>
                        <select id="widgetAggregation">
                            <option value="sum">求和</option>
                            <option value="avg">平均值</option>
                            <option value="count">计数</option>
                            <option value="max">最大值</option>
                            <option value="min">最小值</option>
                        </select>
                    </div>
                </div>
                
                <div class="config-section">
                    <h5>样式设置</h5>
                    <div class="form-group">
                        <label>主题色彩</label>
                        <select id="widgetTheme">
                            <option value="default">默认</option>
                            <option value="blue">蓝色</option>
                            <option value="green">绿色</option>
                            <option value="orange">橙色</option>
                            <option value="purple">紫色</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label class="checkbox-label">
                            <input type="checkbox" id="widgetShowLegend" checked>
                            <span class="checkmark"></span>
                            显示图例
                        </label>
                    </div>
                    <div class="form-group">
                        <label class="checkbox-label">
                            <input type="checkbox" id="widgetShowGrid" checked>
                            <span class="checkmark"></span>
                            显示网格
                        </label>
                    </div>
                </div>
                
                <div class="config-section">
                    <h5>高级设置</h5>
                    <div class="form-group">
                        <label>刷新间隔</label>
                        <select id="widgetRefreshInterval">
                            <option value="0">不自动刷新</option>
                            <option value="30">30秒</option>
                            <option value="60">1分钟</option>
                            <option value="300">5分钟</option>
                            <option value="900">15分钟</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>数据过滤</label>
                        <textarea id="widgetFilter" placeholder="输入过滤条件（JSON格式）"></textarea>
                    </div>
                </div>
            </div>
            <div class="config-footer">
                <button class="btn-secondary" onclick="advancedAnalytics.closeWidgetConfig()">取消</button>
                <button class="btn-primary" onclick="advancedAnalytics.saveWidgetConfig()">保存</button>
            </div>
        `;
        
        document.body.appendChild(configPanel);
    }

    createReportBuilder() {
        const reportBuilder = document.createElement('div');
        reportBuilder.id = 'reportBuilder';
        reportBuilder.className = 'report-builder-modal';
        reportBuilder.innerHTML = `
            <div class="modal-content large">
                <div class="modal-header">
                    <h3>报表生成器</h3>
                    <button class="modal-close" onclick="advancedAnalytics.closeReportBuilder()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="modal-body">
                    <div class="report-builder-content">
                        <div class="report-sidebar">
                            <div class="report-section">
                                <h4>报表类型</h4>
                                <div class="report-types">
                                    <div class="report-type-item" data-type="table">
                                        <i class="fas fa-table"></i>
                                        <span>表格报表</span>
                                    </div>
                                    <div class="report-type-item" data-type="chart">
                                        <i class="fas fa-chart-bar"></i>
                                        <span>图表报表</span>
                                    </div>
                                    <div class="report-type-item" data-type="summary">
                                        <i class="fas fa-file-alt"></i>
                                        <span>汇总报表</span>
                                    </div>
                                    <div class="report-type-item" data-type="dashboard">
                                        <i class="fas fa-tachometer-alt"></i>
                                        <span>仪表板报表</span>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="report-section">
                                <h4>数据字段</h4>
                                <div class="field-list" id="reportFieldList">
                                    <!-- 字段列表 -->
                                </div>
                            </div>
                        </div>
                        
                        <div class="report-main">
                            <div class="report-config">
                                <div class="config-tabs">
                                    <button class="tab-btn active" data-tab="data">数据</button>
                                    <button class="tab-btn" data-tab="format">格式</button>
                                    <button class="tab-btn" data-tab="filter">筛选</button>
                                    <button class="tab-btn" data-tab="schedule">定时</button>
                                </div>
                                
                                <div class="config-content">
                                    <div class="tab-content active" id="dataTab">
                                        <div class="form-group">
                                            <label>报表名称</label>
                                            <input type="text" id="reportName" placeholder="输入报表名称">
                                        </div>
                                        <div class="form-group">
                                            <label>数据源</label>
                                            <select id="reportDataSource">
                                                <option value="">选择数据源</option>
                                            </select>
                                        </div>
                                        <div class="form-group">
                                            <label>选择字段</label>
                                            <div class="field-selector" id="reportFieldSelector">
                                                <!-- 字段选择器 -->
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="tab-content" id="formatTab">
                                        <div class="form-group">
                                            <label>输出格式</label>
                                            <select id="reportFormat">
                                                <option value="html">HTML</option>
                                                <option value="pdf">PDF</option>
                                                <option value="excel">Excel</option>
                                                <option value="csv">CSV</option>
                                            </select>
                                        </div>
                                        <div class="form-group">
                                            <label>页面方向</label>
                                            <select id="reportOrientation">
                                                <option value="portrait">纵向</option>
                                                <option value="landscape">横向</option>
                                            </select>
                                        </div>
                                    </div>
                                    
                                    <div class="tab-content" id="filterTab">
                                        <div class="filter-builder" id="reportFilterBuilder">
                                            <!-- 筛选条件构建器 -->
                                        </div>
                                    </div>
                                    
                                    <div class="tab-content" id="scheduleTab">
                                        <div class="form-group">
                                            <label class="checkbox-label">
                                                <input type="checkbox" id="enableSchedule">
                                                <span class="checkmark"></span>
                                                启用定时生成
                                            </label>
                                        </div>
                                        <div class="form-group">
                                            <label>生成频率</label>
                                            <select id="scheduleFrequency">
                                                <option value="daily">每日</option>
                                                <option value="weekly">每周</option>
                                                <option value="monthly">每月</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="report-preview" id="reportPreview">
                                <h4>报表预览</h4>
                                <div class="preview-content">
                                    <p>配置报表参数后将显示预览</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button class="btn-secondary" onclick="advancedAnalytics.closeReportBuilder()">取消</button>
                    <button class="btn-secondary" onclick="advancedAnalytics.previewReport()">预览</button>
                    <button class="btn-primary" onclick="advancedAnalytics.generateReport()">生成报表</button>
                </div>
            </div>
        `;
        
        document.body.appendChild(reportBuilder);
    }

    createDataMiningTool() {
        const dataMining = document.createElement('div');
        dataMining.id = 'dataMiningTool';
        dataMining.className = 'data-mining-modal';
        dataMining.innerHTML = `
            <div class="modal-content large">
                <div class="modal-header">
                    <h3>数据挖掘工具</h3>
                    <button class="modal-close" onclick="advancedAnalytics.closeDataMining()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="modal-body">
                    <div class="mining-content">
                        <div class="mining-sidebar">
                            <div class="mining-section">
                                <h4>分析类型</h4>
                                <div class="analysis-types">
                                    <div class="analysis-item" data-type="trend">
                                        <i class="fas fa-chart-line"></i>
                                        <span>趋势分析</span>
                                    </div>
                                    <div class="analysis-item" data-type="correlation">
                                        <i class="fas fa-project-diagram"></i>
                                        <span>相关性分析</span>
                                    </div>
                                    <div class="analysis-item" data-type="clustering">
                                        <i class="fas fa-object-group"></i>
                                        <span>聚类分析</span>
                                    </div>
                                    <div class="analysis-item" data-type="prediction">
                                        <i class="fas fa-crystal-ball"></i>
                                        <span>预测分析</span>
                                    </div>
                                    <div class="analysis-item" data-type="anomaly">
                                        <i class="fas fa-exclamation-triangle"></i>
                                        <span>异常检测</span>
                                    </div>
                                    <div class="analysis-item" data-type="pattern">
                                        <i class="fas fa-search"></i>
                                        <span>模式识别</span>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="mining-section">
                                <h4>算法选择</h4>
                                <div class="algorithm-list" id="algorithmList">
                                    <!-- 算法列表 -->
                                </div>
                            </div>
                        </div>
                        
                        <div class="mining-main">
                            <div class="mining-config">
                                <div class="config-section">
                                    <h4>数据配置</h4>
                                    <div class="form-group">
                                        <label>数据源</label>
                                        <select id="miningDataSource">
                                            <option value="">选择数据源</option>
                                        </select>
                                    </div>
                                    <div class="form-group">
                                        <label>目标字段</label>
                                        <select id="miningTargetField">
                                            <option value="">选择目标字段</option>
                                        </select>
                                    </div>
                                    <div class="form-group">
                                        <label>特征字段</label>
                                        <div class="field-checkboxes" id="miningFeatureFields">
                                            <!-- 特征字段复选框 -->
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="config-section">
                                    <h4>参数设置</h4>
                                    <div class="parameter-config" id="parameterConfig">
                                        <!-- 参数配置 -->
                                    </div>
                                </div>
                            </div>
                            
                            <div class="mining-results" id="miningResults">
                                <h4>分析结果</h4>
                                <div class="results-content">
                                    <p>配置参数并运行分析后将显示结果</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button class="btn-secondary" onclick="advancedAnalytics.closeDataMining()">取消</button>
                    <button class="btn-secondary" onclick="advancedAnalytics.validateMiningConfig()">验证配置</button>
                    <button class="btn-primary" onclick="advancedAnalytics.runDataMining()">运行分析</button>
                </div>
            </div>
        `;
        
        document.body.appendChild(dataMining);
    }

    setupDataSources() {
        // 设置默认数据源
        this.dataSources = [
            {
                id: 'users',
                name: '用户数据',
                type: 'local',
                description: '系统用户信息',
                fields: ['id', 'name', 'email', 'department', 'role', 'created_at', 'last_login'],
                data: this.generateUserData()
            },
            {
                id: 'orders',
                name: '订单数据',
                type: 'local',
                description: '订单交易信息',
                fields: ['id', 'customer_id', 'amount', 'status', 'created_at', 'product_category'],
                data: this.generateOrderData()
            },
            {
                id: 'performance',
                name: '性能数据',
                type: 'local',
                description: '系统性能指标',
                fields: ['timestamp', 'cpu_usage', 'memory_usage', 'response_time', 'error_rate'],
                data: this.generatePerformanceData()
            },
            {
                id: 'sales',
                name: '销售数据',
                type: 'local',
                description: '销售业绩数据',
                fields: ['date', 'region', 'product', 'sales_amount', 'quantity', 'salesperson'],
                data: this.generateSalesData()
            }
        ];
        
        this.renderDataSources();
    }

    generateUserData() {
        const departments = ['技术部', '销售部', '人事部', '财务部', '市场部'];
        const roles = ['管理员', '普通用户', '经理', '专员'];
        const data = [];
        
        for (let i = 1; i <= 100; i++) {
            data.push({
                id: i,
                name: `用户${i}`,
                email: `user${i}@company.com`,
                department: departments[Math.floor(Math.random() * departments.length)],
                role: roles[Math.floor(Math.random() * roles.length)],
                created_at: new Date(Date.now() - Math.random() * 365 * 24 * 60 * 60 * 1000),
                last_login: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000)
            });
        }
        
        return data;
    }

    generateOrderData() {
        const statuses = ['pending', 'processing', 'completed', 'cancelled'];
        const categories = ['电子产品', '服装', '食品', '图书', '家居'];
        const data = [];
        
        for (let i = 1; i <= 500; i++) {
            data.push({
                id: i,
                customer_id: Math.floor(Math.random() * 100) + 1,
                amount: Math.floor(Math.random() * 1000) + 50,
                status: statuses[Math.floor(Math.random() * statuses.length)],
                created_at: new Date(Date.now() - Math.random() * 90 * 24 * 60 * 60 * 1000),
                product_category: categories[Math.floor(Math.random() * categories.length)]
            });
        }
        
        return data;
    }

    generatePerformanceData() {
        const data = [];
        const now = new Date();
        
        for (let i = 0; i < 168; i++) { // 一周的小时数据
            const timestamp = new Date(now.getTime() - i * 60 * 60 * 1000);
            data.push({
                timestamp: timestamp,
                cpu_usage: Math.random() * 100,
                memory_usage: Math.random() * 100,
                response_time: Math.random() * 1000 + 100,
                error_rate: Math.random() * 5
            });
        }
        
        return data.reverse();
    }

    generateSalesData() {
        const regions = ['华北', '华东', '华南', '华中', '西南', '西北', '东北'];
        const products = ['产品A', '产品B', '产品C', '产品D', '产品E'];
        const salespeople = ['张三', '李四', '王五', '赵六', '钱七'];
        const data = [];
        
        for (let i = 0; i < 365; i++) {
            const date = new Date(Date.now() - i * 24 * 60 * 60 * 1000);
            const recordsPerDay = Math.floor(Math.random() * 10) + 1;
            
            for (let j = 0; j < recordsPerDay; j++) {
                data.push({
                    date: date,
                    region: regions[Math.floor(Math.random() * regions.length)],
                    product: products[Math.floor(Math.random() * products.length)],
                    sales_amount: Math.floor(Math.random() * 10000) + 1000,
                    quantity: Math.floor(Math.random() * 100) + 1,
                    salesperson: salespeople[Math.floor(Math.random() * salespeople.length)]
                });
            }
        }
        
        return data.reverse();
    }

    loadDefaultDashboards() {
        // 加载默认仪表板
        this.dashboards = [
            {
                id: 'overview',
                name: '业务概览',
                description: '整体业务数据概览',
                widgets: [
                    {
                        id: 'widget1',
                        type: 'metric-card',
                        title: '总用户数',
                        position: { x: 0, y: 0, w: 3, h: 2 },
                        config: {
                            dataSource: 'users',
                            metric: 'count',
                            field: 'id'
                        }
                    },
                    {
                        id: 'widget2',
                        type: 'metric-card',
                        title: '总订单数',
                        position: { x: 3, y: 0, w: 3, h: 2 },
                        config: {
                            dataSource: 'orders',
                            metric: 'count',
                            field: 'id'
                        }
                    },
                    {
                        id: 'widget3',
                        type: 'chart-line',
                        title: '订单趋势',
                        position: { x: 0, y: 2, w: 6, h: 4 },
                        config: {
                            dataSource: 'orders',
                            xAxis: 'created_at',
                            yAxis: 'amount',
                            aggregation: 'sum'
                        }
                    }
                ]
            },
            {
                id: 'sales',
                name: '销售分析',
                description: '销售业绩分析仪表板',
                widgets: [
                    {
                        id: 'sales1',
                        type: 'chart-bar',
                        title: '各地区销售额',
                        position: { x: 0, y: 0, w: 6, h: 4 },
                        config: {
                            dataSource: 'sales',
                            xAxis: 'region',
                            yAxis: 'sales_amount',
                            aggregation: 'sum'
                        }
                    },
                    {
                        id: 'sales2',
                        type: 'chart-pie',
                        title: '产品销售占比',
                        position: { x: 6, y: 0, w: 6, h: 4 },
                        config: {
                            dataSource: 'sales',
                            xAxis: 'product',
                            yAxis: 'sales_amount',
                            aggregation: 'sum'
                        }
                    }
                ]
            }
        ];
        
        this.updateDashboardSelector();
    }

    bindAnalyticsEvents() {
        // 绑定事件监听器
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('widget-item')) {
                this.selectWidgetType(e.target);
            }
            
            if (e.target.classList.contains('analysis-item')) {
                this.selectAnalysisType(e.target);
            }
            
            if (e.target.classList.contains('report-type-item')) {
                this.selectReportType(e.target);
            }
        });
    }

    setupDragAndDrop() {
        // 设置拖拽功能
        const widgetLibrary = document.getElementById('widgetLibrary');
        const dashboardCanvas = document.getElementById('dashboardCanvas');
        
        if (widgetLibrary) {
            widgetLibrary.addEventListener('dragstart', (e) => {
                if (e.target.classList.contains('widget-item')) {
                    this.draggedWidget = {
                        type: e.target.dataset.type,
                        name: e.target.querySelector('span').textContent
                    };
                }
            });
        }
        
        if (dashboardCanvas) {
            dashboardCanvas.addEventListener('dragover', (e) => {
                e.preventDefault();
            });
            
            dashboardCanvas.addEventListener('drop', (e) => {
                e.preventDefault();
                if (this.draggedWidget) {
                    this.addWidgetToDashboard(this.draggedWidget, e);
                    this.draggedWidget = null;
                }
            });
        }
    }

    renderDataSources() {
        const container = document.getElementById('dataSources');
        if (!container) return;
        
        container.innerHTML = this.dataSources.map(source => `
            <div class="data-source-item">
                <div class="source-icon">
                    <i class="fas fa-${this.getDataSourceIcon(source.type)}"></i>
                </div>
                <div class="source-info">
                    <div class="source-name">${source.name}</div>
                    <div class="source-desc">${source.description}</div>
                    <div class="source-meta">${source.data.length} 条记录</div>
                </div>
            </div>
        `).join('');
    }

    getDataSourceIcon(type) {
        const icons = {
            'local': 'database',
            'api': 'cloud',
            'file': 'file-csv'
        };
        return icons[type] || 'database';
    }

    updateDashboardSelector() {
        const selector = document.getElementById('dashboardSelector');
        if (!selector) return;
        
        selector.innerHTML = '<option value="">选择仪表板</option>' +
            this.dashboards.map(dashboard => 
                `<option value="${dashboard.id}">${dashboard.name}</option>`
            ).join('');
    }

    // 面板控制方法
    showAnalyticsPanel() {
        const panel = document.getElementById('analyticsPanel');
        if (panel) {
            panel.classList.add('show');
        }
    }

    closeAnalyticsPanel() {
        const panel = document.getElementById('analyticsPanel');
        if (panel) {
            panel.classList.remove('show');
        }
    }

    // 仪表板方法
    switchDashboard(dashboardId) {
        if (!dashboardId) return;
        
        const dashboard = this.dashboards.find(d => d.id === dashboardId);
        if (dashboard) {
            this.currentDashboard = dashboard;
            this.renderDashboard(dashboard);
        }
    }

    renderDashboard(dashboard) {
        const canvas = document.getElementById('dashboardCanvas');
        const title = document.getElementById('dashboardTitle');
        
        if (title) {
            title.textContent = dashboard.name;
        }
        
        if (canvas) {
            canvas.innerHTML = '';
            canvas.classList.remove('empty');
            
            // 创建网格布局
            const grid = document.createElement('div');
            grid.className = 'dashboard-grid';
            
            dashboard.widgets.forEach(widget => {
                const widgetElement = this.createWidgetElement(widget);
                grid.appendChild(widgetElement);
            });
            
            canvas.appendChild(grid);
        }
    }

    createWidgetElement(widget) {
        const element = document.createElement('div');
        element.className = 'dashboard-widget';
        element.dataset.widgetId = widget.id;
        element.style.gridColumn = `span ${widget.position.w}`;
        element.style.gridRow = `span ${widget.position.h}`;
        
        element.innerHTML = `
            <div class="widget-header">
                <h4>${widget.title}</h4>
                <div class="widget-actions">
                    <button class="btn-icon" onclick="advancedAnalytics.configureWidget('${widget.id}')" title="配置">
                        <i class="fas fa-cog"></i>
                    </button>
                    <button class="btn-icon" onclick="advancedAnalytics.refreshWidget('${widget.id}')" title="刷新">
                        <i class="fas fa-sync"></i>
                    </button>
                    <button class="btn-icon" onclick="advancedAnalytics.removeWidget('${widget.id}')" title="删除">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </div>
            <div class="widget-content" id="widget-content-${widget.id}">
                <!-- 组件内容将在这里渲染 -->
            </div>
        `;
        
        // 渲染组件内容
        setTimeout(() => {
            this.renderWidgetContent(widget);
        }, 100);
        
        return element;
    }

    renderWidgetContent(widget) {
        const container = document.getElementById(`widget-content-${widget.id}`);
        if (!container) return;
        
        const dataSource = this.dataSources.find(ds => ds.id === widget.config.dataSource);
        if (!dataSource) {
            container.innerHTML = '<p class="widget-error">数据源不存在</p>';
            return;
        }
        
        switch (widget.type) {
            case 'metric-card':
                this.renderMetricCard(container, widget, dataSource);
                break;
            case 'chart-line':
            case 'chart-bar':
            case 'chart-pie':
            case 'chart-area':
                this.renderChart(container, widget, dataSource);
                break;
            case 'data-table':
                this.renderDataTable(container, widget, dataSource);
                break;
            default:
                container.innerHTML = '<p class="widget-placeholder">组件类型暂不支持</p>';
        }
    }

    renderMetricCard(container, widget, dataSource) {
        const data = dataSource.data;
        let value = 0;
        
        switch (widget.config.metric) {
            case 'count':
                value = data.length;
                break;
            case 'sum':
                value = data.reduce((sum, item) => sum + (parseFloat(item[widget.config.field]) || 0), 0);
                break;
            case 'avg':
                const total = data.reduce((sum, item) => sum + (parseFloat(item[widget.config.field]) || 0), 0);
                value = data.length > 0 ? total / data.length : 0;
                break;
        }
        
        container.innerHTML = `
            <div class="metric-card-content">
                <div class="metric-value">${this.formatNumber(value)}</div>
                <div class="metric-label">${widget.title}</div>
                <div class="metric-trend">
                    <i class="fas fa-arrow-up"></i>
                    <span>+5.2%</span>
                </div>
            </div>
        `;
    }

    renderChart(container, widget, dataSource) {
        // 创建图表容器
        const canvas = document.createElement('canvas');
        canvas.id = `chart-${widget.id}`;
        container.appendChild(canvas);
        
        // 准备图表数据
        const chartData = this.prepareChartData(widget, dataSource);
        
        // 创建图表
        this.createChart(canvas, widget.type, chartData, widget.config);
    }

    prepareChartData(widget, dataSource) {
        const data = dataSource.data;
        const xField = widget.config.xAxis;
        const yField = widget.config.yAxis;
        const aggregation = widget.config.aggregation || 'sum';
        
        // 按X轴字段分组
        const grouped = {};
        data.forEach(item => {
            const xValue = item[xField];
            const yValue = parseFloat(item[yField]) || 0;
            
            if (!grouped[xValue]) {
                grouped[xValue] = [];
            }
            grouped[xValue].push(yValue);
        });
        
        // 聚合数据
        const labels = Object.keys(grouped);
        const values = labels.map(label => {
            const values = grouped[label];
            switch (aggregation) {
                case 'sum':
                    return values.reduce((sum, val) => sum + val, 0);
                case 'avg':
                    return values.reduce((sum, val) => sum + val, 0) / values.length;
                case 'count':
                    return values.length;
                case 'max':
                    return Math.max(...values);
                case 'min':
                    return Math.min(...values);
                default:
                    return values.reduce((sum, val) => sum + val, 0);
            }
        });
        
        return { labels, values };
    }

    createChart(canvas, type, data, config) {
        // 这里使用Chart.js创建图表
        // 简化实现，实际项目中需要引入Chart.js库
        const ctx = canvas.getContext('2d');
        
        // 模拟图表渲染
        ctx.fillStyle = '#667eea';
        ctx.fillRect(10, 10, canvas.width - 20, canvas.height - 20);
        
        ctx.fillStyle = 'white';
        ctx.font = '14px Arial';
        ctx.textAlign = 'center';
        ctx.fillText(`${type} 图表`, canvas.width / 2, canvas.height / 2);
        ctx.fillText(`数据点: ${data.values.length}`, canvas.width / 2, canvas.height / 2 + 20);
    }

    renderDataTable(container, widget, dataSource) {
        const data = dataSource.data.slice(0, 10); // 只显示前10条
        const fields = dataSource.fields.slice(0, 5); // 只显示前5个字段
        
        container.innerHTML = `
            <div class="data-table-wrapper">
                <table class="data-table">
                    <thead>
                        <tr>
                            ${fields.map(field => `<th>${field}</th>`).join('')}
                        </tr>
                    </thead>
                    <tbody>
                        ${data.map(row => `
                            <tr>
                                ${fields.map(field => `<td>${this.formatCellValue(row[field])}</td>`).join('')}
                            </tr>
                        `).join('')}
                    </tbody>
                </table>
            </div>
        `;
    }

    formatNumber(num) {
        if (num >= 1000000) {
            return (num / 1000000).toFixed(1) + 'M';
        } else if (num >= 1000) {
            return (num / 1000).toFixed(1) + 'K';
        }
        return num.toLocaleString();
    }

    formatCellValue(value) {
        if (value instanceof Date) {
            return value.toLocaleDateString();
        }
        if (typeof value === 'number') {
            return value.toLocaleString();
        }
        return value || '';
    }

    // 仪表板管理方法
    createDashboard() {
        const name = prompt('请输入仪表板名称:');
        if (!name) return;

        const dashboard = {
            id: 'dashboard_' + Date.now(),
            name: name,
            description: '',
            widgets: [],
            created_at: new Date()
        };

        this.dashboards.push(dashboard);
        this.updateDashboardSelector();
        this.switchDashboard(dashboard.id);
        this.saveData();

        if (window.showNotification) {
            showNotification(`仪表板 "${name}" 创建成功`, 'success');
        }
    }

    loadTemplate() {
        const templates = [
            {
                name: '业务概览模板',
                description: '包含关键业务指标的概览仪表板',
                widgets: [
                    { type: 'metric-card', title: '总用户数', position: { x: 0, y: 0, w: 3, h: 2 } },
                    { type: 'metric-card', title: '总订单数', position: { x: 3, y: 0, w: 3, h: 2 } },
                    { type: 'chart-line', title: '趋势分析', position: { x: 0, y: 2, w: 6, h: 4 } }
                ]
            },
            {
                name: '销售分析模板',
                description: '销售数据分析仪表板',
                widgets: [
                    { type: 'chart-bar', title: '销售额对比', position: { x: 0, y: 0, w: 6, h: 4 } },
                    { type: 'chart-pie', title: '产品占比', position: { x: 6, y: 0, w: 6, h: 4 } }
                ]
            }
        ];

        const modal = document.createElement('div');
        modal.className = 'modal template-modal';
        modal.innerHTML = `
            <div class="modal-content">
                <div class="modal-header">
                    <h3>选择仪表板模板</h3>
                    <button class="modal-close" onclick="this.closest('.modal').remove()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="modal-body">
                    <div class="template-grid">
                        ${templates.map((template, index) => `
                            <div class="template-item" onclick="advancedAnalytics.applyTemplate(${index})">
                                <div class="template-preview">
                                    <i class="fas fa-chart-line"></i>
                                </div>
                                <div class="template-info">
                                    <h4>${template.name}</h4>
                                    <p>${template.description}</p>
                                </div>
                            </div>
                        `).join('')}
                    </div>
                </div>
            </div>
        `;

        document.body.appendChild(modal);
        modal.style.display = 'flex';

        // 存储模板数据
        this.templates = templates;
    }

    applyTemplate(templateIndex) {
        const template = this.templates[templateIndex];
        if (!template) return;

        const name = prompt('请输入仪表板名称:', template.name);
        if (!name) return;

        const dashboard = {
            id: 'dashboard_' + Date.now(),
            name: name,
            description: template.description,
            widgets: template.widgets.map((widget, index) => ({
                ...widget,
                id: 'widget_' + Date.now() + '_' + index,
                config: {
                    dataSource: 'users', // 默认数据源
                    xAxis: 'created_at',
                    yAxis: 'id',
                    aggregation: 'count'
                }
            })),
            created_at: new Date()
        };

        this.dashboards.push(dashboard);
        this.updateDashboardSelector();
        this.switchDashboard(dashboard.id);
        this.saveData();

        // 关闭模板选择框
        document.querySelector('.template-modal').remove();

        if (window.showNotification) {
            showNotification(`仪表板 "${name}" 创建成功`, 'success');
        }
    }

    addWidgetToDashboard(widget, event) {
        if (!this.currentDashboard) {
            if (window.showNotification) {
                showNotification('请先选择或创建一个仪表板', 'warning');
            }
            return;
        }

        const rect = event.target.getBoundingClientRect();
        const x = Math.floor((event.clientX - rect.left) / 200); // 假设每个网格单元200px
        const y = Math.floor((event.clientY - rect.top) / 150);  // 假设每个网格单元150px

        const newWidget = {
            id: 'widget_' + Date.now(),
            type: widget.type,
            title: widget.name,
            position: { x: x, y: y, w: 4, h: 3 }, // 默认大小
            config: {
                dataSource: this.dataSources[0]?.id || '',
                xAxis: '',
                yAxis: '',
                aggregation: 'sum'
            }
        };

        this.currentDashboard.widgets.push(newWidget);
        this.renderDashboard(this.currentDashboard);
        this.saveData();

        // 自动打开配置面板
        setTimeout(() => {
            this.configureWidget(newWidget.id);
        }, 100);
    }

    configureWidget(widgetId) {
        const widget = this.currentDashboard?.widgets.find(w => w.id === widgetId);
        if (!widget) return;

        this.currentWidget = widget;

        // 显示配置面板
        const configPanel = document.getElementById('widgetConfigPanel');
        if (configPanel) {
            configPanel.classList.add('show');

            // 填充当前配置
            document.getElementById('widgetTitle').value = widget.title;
            document.getElementById('widgetDataSource').value = widget.config.dataSource || '';
            document.getElementById('widgetXAxis').value = widget.config.xAxis || '';
            document.getElementById('widgetYAxis').value = widget.config.yAxis || '';
            document.getElementById('widgetAggregation').value = widget.config.aggregation || 'sum';

            // 更新数据源选项
            this.updateDataSourceOptions();
            this.updateFieldOptions(widget.config.dataSource);
        }
    }

    updateDataSourceOptions() {
        const select = document.getElementById('widgetDataSource');
        if (select) {
            select.innerHTML = '<option value="">选择数据源</option>' +
                this.dataSources.map(ds =>
                    `<option value="${ds.id}">${ds.name}</option>`
                ).join('');
        }

        // 绑定数据源变化事件
        select.addEventListener('change', (e) => {
            this.updateFieldOptions(e.target.value);
        });
    }

    updateFieldOptions(dataSourceId) {
        const dataSource = this.dataSources.find(ds => ds.id === dataSourceId);
        const xAxisSelect = document.getElementById('widgetXAxis');
        const yAxisSelect = document.getElementById('widgetYAxis');

        if (dataSource && xAxisSelect && yAxisSelect) {
            const fieldOptions = dataSource.fields.map(field =>
                `<option value="${field}">${field}</option>`
            ).join('');

            xAxisSelect.innerHTML = '<option value="">选择字段</option>' + fieldOptions;
            yAxisSelect.innerHTML = '<option value="">选择字段</option>' + fieldOptions;
        }
    }

    saveWidgetConfig() {
        if (!this.currentWidget) return;

        // 更新组件配置
        this.currentWidget.title = document.getElementById('widgetTitle').value;
        this.currentWidget.config.dataSource = document.getElementById('widgetDataSource').value;
        this.currentWidget.config.xAxis = document.getElementById('widgetXAxis').value;
        this.currentWidget.config.yAxis = document.getElementById('widgetYAxis').value;
        this.currentWidget.config.aggregation = document.getElementById('widgetAggregation').value;

        // 重新渲染组件
        this.renderWidgetContent(this.currentWidget);

        // 更新组件标题
        const widgetElement = document.querySelector(`[data-widget-id="${this.currentWidget.id}"]`);
        if (widgetElement) {
            const titleElement = widgetElement.querySelector('.widget-header h4');
            if (titleElement) {
                titleElement.textContent = this.currentWidget.title;
            }
        }

        this.saveData();
        this.closeWidgetConfig();

        if (window.showNotification) {
            showNotification('组件配置已保存', 'success');
        }
    }

    closeWidgetConfig() {
        const configPanel = document.getElementById('widgetConfigPanel');
        if (configPanel) {
            configPanel.classList.remove('show');
        }
        this.currentWidget = null;
    }

    refreshWidget(widgetId) {
        const widget = this.currentDashboard?.widgets.find(w => w.id === widgetId);
        if (widget) {
            this.renderWidgetContent(widget);

            if (window.showNotification) {
                showNotification('组件已刷新', 'info');
            }
        }
    }

    removeWidget(widgetId) {
        if (!this.currentDashboard) return;

        if (confirm('确定要删除这个组件吗？')) {
            this.currentDashboard.widgets = this.currentDashboard.widgets.filter(w => w.id !== widgetId);
            this.renderDashboard(this.currentDashboard);
            this.saveData();

            if (window.showNotification) {
                showNotification('组件已删除', 'success');
            }
        }
    }

    // 仪表板操作方法
    editDashboardTitle() {
        if (!this.currentDashboard) return;

        const newTitle = prompt('请输入新的仪表板名称:', this.currentDashboard.name);
        if (newTitle && newTitle !== this.currentDashboard.name) {
            this.currentDashboard.name = newTitle;
            document.getElementById('dashboardTitle').textContent = newTitle;
            this.updateDashboardSelector();
            this.saveData();
        }
    }

    toggleEditMode() {
        const btn = document.getElementById('editModeBtn');
        const canvas = document.getElementById('dashboardCanvas');

        if (canvas.classList.contains('edit-mode')) {
            canvas.classList.remove('edit-mode');
            btn.innerHTML = '<i class="fas fa-edit"></i> 编辑模式';
        } else {
            canvas.classList.add('edit-mode');
            btn.innerHTML = '<i class="fas fa-eye"></i> 预览模式';
        }
    }

    refreshDashboard() {
        if (this.currentDashboard) {
            this.renderDashboard(this.currentDashboard);

            if (window.showNotification) {
                showNotification('仪表板已刷新', 'info');
            }
        }
    }

    exportDashboard() {
        if (!this.currentDashboard) return;

        const exportData = {
            dashboard: this.currentDashboard,
            exportTime: new Date(),
            version: '1.0'
        };

        const blob = new Blob([JSON.stringify(exportData, null, 2)], {
            type: 'application/json'
        });
        const url = URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = `dashboard-${this.currentDashboard.name}-${new Date().toISOString().split('T')[0]}.json`;
        link.click();
        URL.revokeObjectURL(url);

        if (window.showNotification) {
            showNotification('仪表板导出成功', 'success');
        }
    }

    shareDashboard() {
        if (!this.currentDashboard) return;

        const shareUrl = `${window.location.origin}${window.location.pathname}?dashboard=${this.currentDashboard.id}`;

        const modal = document.createElement('div');
        modal.className = 'modal share-modal';
        modal.innerHTML = `
            <div class="modal-content">
                <div class="modal-header">
                    <h3>分享仪表板</h3>
                    <button class="modal-close" onclick="this.closest('.modal').remove()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="modal-body">
                    <div class="share-options">
                        <div class="share-option">
                            <h4>分享链接</h4>
                            <div class="share-url">
                                <input type="text" value="${shareUrl}" readonly>
                                <button class="btn-secondary" onclick="navigator.clipboard.writeText('${shareUrl}')">
                                    <i class="fas fa-copy"></i>
                                    复制
                                </button>
                            </div>
                        </div>

                        <div class="share-option">
                            <h4>嵌入代码</h4>
                            <textarea readonly>&lt;iframe src="${shareUrl}" width="800" height="600"&gt;&lt;/iframe&gt;</textarea>
                        </div>

                        <div class="share-option">
                            <h4>权限设置</h4>
                            <label class="checkbox-label">
                                <input type="checkbox" checked>
                                <span class="checkmark"></span>
                                允许查看
                            </label>
                            <label class="checkbox-label">
                                <input type="checkbox">
                                <span class="checkmark"></span>
                                允许编辑
                            </label>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button class="btn-secondary" onclick="this.closest('.modal').remove()">关闭</button>
                    <button class="btn-primary" onclick="this.closest('.modal').remove()">确定</button>
                </div>
            </div>
        `;

        document.body.appendChild(modal);
        modal.style.display = 'flex';
    }

    showReportBuilder() {
        const modal = document.getElementById('reportBuilder');
        if (modal) {
            modal.style.display = 'flex';
        }
    }

    closeReportBuilder() {
        const modal = document.getElementById('reportBuilder');
        if (modal) {
            modal.style.display = 'none';
        }
    }

    showDataMining() {
        const modal = document.getElementById('dataMiningTool');
        if (modal) {
            modal.style.display = 'flex';
        }
    }

    closeDataMining() {
        const modal = document.getElementById('dataMiningTool');
        if (modal) {
            modal.style.display = 'none';
        }
    }

    // 报表生成器方法
    selectReportType(element) {
        // 移除其他选中状态
        document.querySelectorAll('.report-type-item').forEach(item => {
            item.classList.remove('selected');
        });

        // 添加选中状态
        element.classList.add('selected');

        const reportType = element.dataset.type;
        this.currentReportType = reportType;

        // 更新字段列表
        this.updateReportFieldList();
    }

    updateReportFieldList() {
        const fieldList = document.getElementById('reportFieldList');
        const fieldSelector = document.getElementById('reportFieldSelector');

        if (!fieldList || !fieldSelector) return;

        // 获取所有可用字段
        const allFields = [];
        this.dataSources.forEach(ds => {
            ds.fields.forEach(field => {
                allFields.push({
                    dataSource: ds.id,
                    dataSourceName: ds.name,
                    field: field
                });
            });
        });

        // 渲染字段列表
        fieldList.innerHTML = allFields.map(item => `
            <div class="field-item" draggable="true" data-source="${item.dataSource}" data-field="${item.field}">
                <i class="fas fa-grip-vertical"></i>
                <span class="field-name">${item.field}</span>
                <span class="field-source">${item.dataSourceName}</span>
            </div>
        `).join('');

        // 渲染字段选择器
        fieldSelector.innerHTML = allFields.map(item => `
            <label class="checkbox-label">
                <input type="checkbox" value="${item.dataSource}.${item.field}">
                <span class="checkmark"></span>
                <span class="field-label">${item.dataSourceName}.${item.field}</span>
            </label>
        `).join('');
    }

    previewReport() {
        const reportName = document.getElementById('reportName').value;
        const reportDataSource = document.getElementById('reportDataSource').value;
        const reportFormat = document.getElementById('reportFormat').value;

        if (!reportName || !reportDataSource) {
            if (window.showNotification) {
                showNotification('请填写报表名称和选择数据源', 'warning');
            }
            return;
        }

        // 获取选中的字段
        const selectedFields = Array.from(document.querySelectorAll('#reportFieldSelector input:checked'))
            .map(input => input.value);

        if (selectedFields.length === 0) {
            if (window.showNotification) {
                showNotification('请至少选择一个字段', 'warning');
            }
            return;
        }

        // 生成预览
        const preview = this.generateReportPreview({
            name: reportName,
            dataSource: reportDataSource,
            fields: selectedFields,
            type: this.currentReportType,
            format: reportFormat
        });

        const previewContainer = document.getElementById('reportPreview');
        if (previewContainer) {
            previewContainer.innerHTML = `
                <h4>报表预览 - ${reportName}</h4>
                <div class="preview-content">
                    ${preview}
                </div>
            `;
        }
    }

    generateReportPreview(config) {
        const dataSource = this.dataSources.find(ds => ds.id === config.dataSource);
        if (!dataSource) return '<p>数据源不存在</p>';

        const data = dataSource.data.slice(0, 5); // 预览前5条数据
        const fields = config.fields.map(field => field.split('.')[1]); // 提取字段名

        switch (config.type) {
            case 'table':
                return this.generateTablePreview(data, fields);
            case 'chart':
                return this.generateChartPreview(data, fields);
            case 'summary':
                return this.generateSummaryPreview(data, fields);
            case 'dashboard':
                return this.generateDashboardPreview(data, fields);
            default:
                return '<p>不支持的报表类型</p>';
        }
    }

    generateTablePreview(data, fields) {
        return `
            <div class="report-table-preview">
                <table class="preview-table">
                    <thead>
                        <tr>
                            ${fields.map(field => `<th>${field}</th>`).join('')}
                        </tr>
                    </thead>
                    <tbody>
                        ${data.map(row => `
                            <tr>
                                ${fields.map(field => `<td>${this.formatCellValue(row[field])}</td>`).join('')}
                            </tr>
                        `).join('')}
                    </tbody>
                </table>
                <p class="preview-note">预览显示前5条数据，实际报表将包含所有数据</p>
            </div>
        `;
    }

    generateChartPreview(data, fields) {
        return `
            <div class="report-chart-preview">
                <div class="chart-placeholder">
                    <i class="fas fa-chart-bar"></i>
                    <p>图表预览</p>
                    <p>字段: ${fields.join(', ')}</p>
                    <p>数据点: ${data.length}</p>
                </div>
            </div>
        `;
    }

    generateSummaryPreview(data, fields) {
        const summary = {};
        fields.forEach(field => {
            const values = data.map(row => row[field]).filter(val => val != null);
            summary[field] = {
                count: values.length,
                unique: new Set(values).size
            };

            // 如果是数字字段，计算统计信息
            const numericValues = values.filter(val => !isNaN(parseFloat(val))).map(val => parseFloat(val));
            if (numericValues.length > 0) {
                summary[field].sum = numericValues.reduce((a, b) => a + b, 0);
                summary[field].avg = summary[field].sum / numericValues.length;
                summary[field].min = Math.min(...numericValues);
                summary[field].max = Math.max(...numericValues);
            }
        });

        return `
            <div class="report-summary-preview">
                <h5>数据汇总</h5>
                ${Object.entries(summary).map(([field, stats]) => `
                    <div class="summary-item">
                        <h6>${field}</h6>
                        <div class="stats">
                            <span>记录数: ${stats.count}</span>
                            <span>唯一值: ${stats.unique}</span>
                            ${stats.sum !== undefined ? `
                                <span>总和: ${stats.sum.toFixed(2)}</span>
                                <span>平均: ${stats.avg.toFixed(2)}</span>
                                <span>最小: ${stats.min}</span>
                                <span>最大: ${stats.max}</span>
                            ` : ''}
                        </div>
                    </div>
                `).join('')}
            </div>
        `;
    }

    generateDashboardPreview(data, fields) {
        return `
            <div class="report-dashboard-preview">
                <div class="dashboard-widgets">
                    <div class="widget-preview">
                        <h6>数据概览</h6>
                        <p>总记录数: ${data.length}</p>
                    </div>
                    <div class="widget-preview">
                        <h6>字段分析</h6>
                        <p>分析字段: ${fields.length}</p>
                    </div>
                </div>
            </div>
        `;
    }

    generateReport() {
        const reportConfig = {
            name: document.getElementById('reportName').value,
            dataSource: document.getElementById('reportDataSource').value,
            format: document.getElementById('reportFormat').value,
            orientation: document.getElementById('reportOrientation').value,
            fields: Array.from(document.querySelectorAll('#reportFieldSelector input:checked'))
                .map(input => input.value),
            type: this.currentReportType,
            enableSchedule: document.getElementById('enableSchedule').checked,
            scheduleFrequency: document.getElementById('scheduleFrequency').value
        };

        if (!reportConfig.name || !reportConfig.dataSource || reportConfig.fields.length === 0) {
            if (window.showNotification) {
                showNotification('请完善报表配置', 'warning');
            }
            return;
        }

        // 生成报表
        this.createReport(reportConfig);
    }

    createReport(config) {
        const dataSource = this.dataSources.find(ds => ds.id === config.dataSource);
        if (!dataSource) return;

        const fields = config.fields.map(field => field.split('.')[1]);
        const data = dataSource.data;

        let reportContent = '';

        switch (config.format) {
            case 'html':
                reportContent = this.generateHTMLReport(config, data, fields);
                this.downloadReport(reportContent, `${config.name}.html`, 'text/html');
                break;
            case 'csv':
                reportContent = this.generateCSVReport(data, fields);
                this.downloadReport(reportContent, `${config.name}.csv`, 'text/csv');
                break;
            case 'excel':
                reportContent = this.generateExcelReport(data, fields);
                this.downloadReport(reportContent, `${config.name}.xlsx`, 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
                break;
            case 'pdf':
                this.generatePDFReport(config, data, fields);
                break;
        }

        // 如果启用了定时生成，保存配置
        if (config.enableSchedule) {
            this.saveReportSchedule(config);
        }

        this.closeReportBuilder();

        if (window.showNotification) {
            showNotification(`报表 "${config.name}" 生成成功`, 'success');
        }
    }

    generateHTMLReport(config, data, fields) {
        return `
            <!DOCTYPE html>
            <html>
            <head>
                <title>${config.name}</title>
                <style>
                    body { font-family: Arial, sans-serif; margin: 20px; }
                    table { border-collapse: collapse; width: 100%; }
                    th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
                    th { background-color: #f2f2f2; }
                    .report-header { margin-bottom: 20px; }
                    .report-meta { color: #666; font-size: 12px; }
                </style>
            </head>
            <body>
                <div class="report-header">
                    <h1>${config.name}</h1>
                    <div class="report-meta">
                        生成时间: ${new Date().toLocaleString()}<br>
                        数据源: ${config.dataSource}<br>
                        记录数: ${data.length}
                    </div>
                </div>

                <table>
                    <thead>
                        <tr>
                            ${fields.map(field => `<th>${field}</th>`).join('')}
                        </tr>
                    </thead>
                    <tbody>
                        ${data.map(row => `
                            <tr>
                                ${fields.map(field => `<td>${this.formatCellValue(row[field])}</td>`).join('')}
                            </tr>
                        `).join('')}
                    </tbody>
                </table>
            </body>
            </html>
        `;
    }

    generateCSVReport(data, fields) {
        const header = fields.join(',');
        const rows = data.map(row =>
            fields.map(field => {
                const value = row[field];
                // 处理包含逗号的值
                if (typeof value === 'string' && value.includes(',')) {
                    return `"${value}"`;
                }
                return value || '';
            }).join(',')
        );

        return [header, ...rows].join('\n');
    }

    generateExcelReport(data, fields) {
        // 简化的Excel生成（实际项目中需要使用专门的库如SheetJS）
        return this.generateCSVReport(data, fields);
    }

    generatePDFReport(config, data, fields) {
        // PDF生成需要专门的库如jsPDF
        if (window.showNotification) {
            showNotification('PDF报表功能需要额外的库支持', 'info');
        }
    }

    downloadReport(content, filename, mimeType) {
        const blob = new Blob([content], { type: mimeType });
        const url = URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = filename;
        link.click();
        URL.revokeObjectURL(url);
    }

    saveReportSchedule(config) {
        // 保存定时报表配置
        const schedules = JSON.parse(localStorage.getItem('reportSchedules') || '[]');
        schedules.push({
            ...config,
            id: 'schedule_' + Date.now(),
            created_at: new Date(),
            next_run: this.calculateNextRun(config.scheduleFrequency)
        });
        localStorage.setItem('reportSchedules', JSON.stringify(schedules));
    }

    calculateNextRun(frequency) {
        const now = new Date();
        switch (frequency) {
            case 'daily':
                return new Date(now.getTime() + 24 * 60 * 60 * 1000);
            case 'weekly':
                return new Date(now.getTime() + 7 * 24 * 60 * 60 * 1000);
            case 'monthly':
                const nextMonth = new Date(now);
                nextMonth.setMonth(nextMonth.getMonth() + 1);
                return nextMonth;
            default:
                return new Date(now.getTime() + 24 * 60 * 60 * 1000);
        }
    }

    // 数据挖掘方法
    selectAnalysisType(element) {
        // 移除其他选中状态
        document.querySelectorAll('.analysis-item').forEach(item => {
            item.classList.remove('selected');
        });

        // 添加选中状态
        element.classList.add('selected');

        const analysisType = element.dataset.type;
        this.currentAnalysisType = analysisType;

        // 更新算法列表
        this.updateAlgorithmList(analysisType);

        // 更新参数配置
        this.updateParameterConfig(analysisType);
    }

    updateAlgorithmList(analysisType) {
        const algorithmList = document.getElementById('algorithmList');
        if (!algorithmList) return;

        const algorithms = this.getAlgorithmsForType(analysisType);

        algorithmList.innerHTML = algorithms.map(algorithm => `
            <div class="algorithm-item" data-algorithm="${algorithm.id}">
                <div class="algorithm-name">${algorithm.name}</div>
                <div class="algorithm-desc">${algorithm.description}</div>
                <div class="algorithm-complexity">复杂度: ${algorithm.complexity}</div>
            </div>
        `).join('');

        // 绑定算法选择事件
        algorithmList.addEventListener('click', (e) => {
            const algorithmItem = e.target.closest('.algorithm-item');
            if (algorithmItem) {
                this.selectAlgorithm(algorithmItem);
            }
        });
    }

    getAlgorithmsForType(analysisType) {
        const algorithmMap = {
            trend: [
                { id: 'linear_regression', name: '线性回归', description: '分析数据的线性趋势', complexity: '低' },
                { id: 'polynomial_regression', name: '多项式回归', description: '分析数据的非线性趋势', complexity: '中' },
                { id: 'moving_average', name: '移动平均', description: '平滑数据趋势', complexity: '低' }
            ],
            correlation: [
                { id: 'pearson', name: 'Pearson相关', description: '计算线性相关系数', complexity: '低' },
                { id: 'spearman', name: 'Spearman相关', description: '计算等级相关系数', complexity: '低' },
                { id: 'kendall', name: 'Kendall相关', description: '计算Kendall tau相关系数', complexity: '中' }
            ],
            clustering: [
                { id: 'kmeans', name: 'K-Means', description: 'K均值聚类算法', complexity: '中' },
                { id: 'hierarchical', name: '层次聚类', description: '层次聚类算法', complexity: '高' },
                { id: 'dbscan', name: 'DBSCAN', description: '基于密度的聚类算法', complexity: '中' }
            ],
            prediction: [
                { id: 'arima', name: 'ARIMA', description: '自回归积分滑动平均模型', complexity: '高' },
                { id: 'exponential_smoothing', name: '指数平滑', description: '指数平滑预测', complexity: '中' },
                { id: 'neural_network', name: '神经网络', description: '深度学习预测模型', complexity: '高' }
            ],
            anomaly: [
                { id: 'isolation_forest', name: '孤立森林', description: '基于孤立的异常检测', complexity: '中' },
                { id: 'one_class_svm', name: 'One-Class SVM', description: '单类支持向量机', complexity: '高' },
                { id: 'statistical', name: '统计方法', description: '基于统计的异常检测', complexity: '低' }
            ],
            pattern: [
                { id: 'apriori', name: 'Apriori算法', description: '关联规则挖掘', complexity: '中' },
                { id: 'fp_growth', name: 'FP-Growth', description: '频繁模式增长算法', complexity: '中' },
                { id: 'sequence_mining', name: '序列挖掘', description: '时序模式挖掘', complexity: '高' }
            ]
        };

        return algorithmMap[analysisType] || [];
    }

    selectAlgorithm(algorithmItem) {
        // 移除其他选中状态
        document.querySelectorAll('.algorithm-item').forEach(item => {
            item.classList.remove('selected');
        });

        // 添加选中状态
        algorithmItem.classList.add('selected');

        this.currentAlgorithm = algorithmItem.dataset.algorithm;

        // 更新参数配置
        this.updateParameterConfig(this.currentAnalysisType, this.currentAlgorithm);
    }

    updateParameterConfig(analysisType, algorithm = null) {
        const parameterConfig = document.getElementById('parameterConfig');
        if (!parameterConfig) return;

        let parameters = [];

        if (algorithm) {
            parameters = this.getParametersForAlgorithm(algorithm);
        } else {
            parameters = this.getDefaultParameters(analysisType);
        }

        parameterConfig.innerHTML = parameters.map(param => {
            switch (param.type) {
                case 'number':
                    return `
                        <div class="parameter-item">
                            <label>${param.label}</label>
                            <input type="number" id="param_${param.id}" value="${param.default}"
                                   min="${param.min || ''}" max="${param.max || ''}" step="${param.step || 'any'}">
                            <small>${param.description}</small>
                        </div>
                    `;
                case 'select':
                    return `
                        <div class="parameter-item">
                            <label>${param.label}</label>
                            <select id="param_${param.id}">
                                ${param.options.map(option =>
                                    `<option value="${option.value}" ${option.value === param.default ? 'selected' : ''}>${option.label}</option>`
                                ).join('')}
                            </select>
                            <small>${param.description}</small>
                        </div>
                    `;
                case 'checkbox':
                    return `
                        <div class="parameter-item">
                            <label class="checkbox-label">
                                <input type="checkbox" id="param_${param.id}" ${param.default ? 'checked' : ''}>
                                <span class="checkmark"></span>
                                ${param.label}
                            </label>
                            <small>${param.description}</small>
                        </div>
                    `;
                default:
                    return '';
            }
        }).join('');
    }

    getParametersForAlgorithm(algorithm) {
        const parameterMap = {
            kmeans: [
                { id: 'k', type: 'number', label: '聚类数量(K)', default: 3, min: 2, max: 10, description: '要分成的聚类数量' },
                { id: 'max_iter', type: 'number', label: '最大迭代次数', default: 100, min: 10, max: 1000, description: '算法的最大迭代次数' }
            ],
            linear_regression: [
                { id: 'fit_intercept', type: 'checkbox', label: '拟合截距', default: true, description: '是否计算截距项' },
                { id: 'normalize', type: 'checkbox', label: '标准化', default: false, description: '是否对特征进行标准化' }
            ],
            arima: [
                { id: 'p', type: 'number', label: 'AR阶数(p)', default: 1, min: 0, max: 5, description: '自回归项的阶数' },
                { id: 'd', type: 'number', label: '差分阶数(d)', default: 1, min: 0, max: 2, description: '差分的阶数' },
                { id: 'q', type: 'number', label: 'MA阶数(q)', default: 1, min: 0, max: 5, description: '移动平均项的阶数' }
            ],
            isolation_forest: [
                { id: 'contamination', type: 'number', label: '异常比例', default: 0.1, min: 0.01, max: 0.5, step: 0.01, description: '预期的异常数据比例' },
                { id: 'n_estimators', type: 'number', label: '树的数量', default: 100, min: 10, max: 500, description: '孤立树的数量' }
            ]
        };

        return parameterMap[algorithm] || [];
    }

    getDefaultParameters(analysisType) {
        const defaultMap = {
            trend: [
                { id: 'window_size', type: 'number', label: '窗口大小', default: 7, min: 3, max: 30, description: '分析窗口的大小' }
            ],
            correlation: [
                { id: 'method', type: 'select', label: '相关方法', default: 'pearson',
                  options: [
                      { value: 'pearson', label: 'Pearson' },
                      { value: 'spearman', label: 'Spearman' },
                      { value: 'kendall', label: 'Kendall' }
                  ], description: '相关性计算方法' }
            ],
            clustering: [
                { id: 'n_clusters', type: 'number', label: '聚类数量', default: 3, min: 2, max: 10, description: '目标聚类数量' }
            ],
            prediction: [
                { id: 'forecast_periods', type: 'number', label: '预测期数', default: 10, min: 1, max: 100, description: '要预测的时间期数' }
            ],
            anomaly: [
                { id: 'threshold', type: 'number', label: '异常阈值', default: 2.0, min: 1.0, max: 5.0, step: 0.1, description: '异常检测的阈值' }
            ],
            pattern: [
                { id: 'min_support', type: 'number', label: '最小支持度', default: 0.1, min: 0.01, max: 1.0, step: 0.01, description: '模式的最小支持度' }
            ]
        };

        return defaultMap[analysisType] || [];
    }

    validateMiningConfig() {
        const dataSource = document.getElementById('miningDataSource').value;
        const targetField = document.getElementById('miningTargetField').value;

        if (!dataSource) {
            if (window.showNotification) {
                showNotification('请选择数据源', 'warning');
            }
            return false;
        }

        if (!this.currentAnalysisType) {
            if (window.showNotification) {
                showNotification('请选择分析类型', 'warning');
            }
            return false;
        }

        if (!this.currentAlgorithm) {
            if (window.showNotification) {
                showNotification('请选择算法', 'warning');
            }
            return false;
        }

        // 检查特征字段
        const featureFields = Array.from(document.querySelectorAll('#miningFeatureFields input:checked'));
        if (featureFields.length === 0 && this.currentAnalysisType !== 'anomaly') {
            if (window.showNotification) {
                showNotification('请至少选择一个特征字段', 'warning');
            }
            return false;
        }

        if (window.showNotification) {
            showNotification('配置验证通过', 'success');
        }
        return true;
    }

    runDataMining() {
        if (!this.validateMiningConfig()) {
            return;
        }

        const config = {
            dataSource: document.getElementById('miningDataSource').value,
            targetField: document.getElementById('miningTargetField').value,
            featureFields: Array.from(document.querySelectorAll('#miningFeatureFields input:checked'))
                .map(input => input.value),
            analysisType: this.currentAnalysisType,
            algorithm: this.currentAlgorithm,
            parameters: this.collectParameters()
        };

        // 显示加载状态
        const resultsContainer = document.getElementById('miningResults');
        if (resultsContainer) {
            resultsContainer.innerHTML = `
                <h4>分析结果</h4>
                <div class="mining-loading">
                    <div class="loading-spinner"></div>
                    <p>正在运行数据挖掘分析...</p>
                </div>
            `;
        }

        // 模拟异步分析过程
        setTimeout(() => {
            const results = this.performDataMining(config);
            this.displayMiningResults(results);
        }, 2000);
    }

    collectParameters() {
        const parameters = {};
        document.querySelectorAll('#parameterConfig input, #parameterConfig select').forEach(input => {
            const paramId = input.id.replace('param_', '');
            if (input.type === 'checkbox') {
                parameters[paramId] = input.checked;
            } else if (input.type === 'number') {
                parameters[paramId] = parseFloat(input.value);
            } else {
                parameters[paramId] = input.value;
            }
        });
        return parameters;
    }

    performDataMining(config) {
        const dataSource = this.dataSources.find(ds => ds.id === config.dataSource);
        if (!dataSource) return null;

        const data = dataSource.data;

        // 根据分析类型执行相应的算法
        switch (config.analysisType) {
            case 'trend':
                return this.performTrendAnalysis(data, config);
            case 'correlation':
                return this.performCorrelationAnalysis(data, config);
            case 'clustering':
                return this.performClusteringAnalysis(data, config);
            case 'prediction':
                return this.performPredictionAnalysis(data, config);
            case 'anomaly':
                return this.performAnomalyDetection(data, config);
            case 'pattern':
                return this.performPatternMining(data, config);
            default:
                return { error: '不支持的分析类型' };
        }
    }

    performTrendAnalysis(data, config) {
        // 简化的趋势分析实现
        const targetField = config.targetField;
        const values = data.map(row => parseFloat(row[targetField])).filter(val => !isNaN(val));

        if (values.length < 2) {
            return { error: '数据不足，无法进行趋势分析' };
        }

        // 计算简单的线性趋势
        const n = values.length;
        const x = Array.from({ length: n }, (_, i) => i);
        const sumX = x.reduce((a, b) => a + b, 0);
        const sumY = values.reduce((a, b) => a + b, 0);
        const sumXY = x.reduce((sum, xi, i) => sum + xi * values[i], 0);
        const sumXX = x.reduce((sum, xi) => sum + xi * xi, 0);

        const slope = (n * sumXY - sumX * sumY) / (n * sumXX - sumX * sumX);
        const intercept = (sumY - slope * sumX) / n;

        const trend = slope > 0 ? '上升' : slope < 0 ? '下降' : '平稳';
        const correlation = this.calculateCorrelation(x, values);

        return {
            type: 'trend',
            algorithm: config.algorithm,
            results: {
                trend: trend,
                slope: slope.toFixed(4),
                intercept: intercept.toFixed(4),
                correlation: correlation.toFixed(4),
                dataPoints: n,
                equation: `y = ${slope.toFixed(4)}x + ${intercept.toFixed(4)}`
            },
            visualization: {
                type: 'line',
                data: values,
                trendLine: x.map(xi => slope * xi + intercept)
            }
        };
    }

    performCorrelationAnalysis(data, config) {
        const featureFields = config.featureFields;
        if (featureFields.length < 2) {
            return { error: '需要至少两个字段进行相关性分析' };
        }

        const correlations = [];

        for (let i = 0; i < featureFields.length; i++) {
            for (let j = i + 1; j < featureFields.length; j++) {
                const field1 = featureFields[i];
                const field2 = featureFields[j];

                const values1 = data.map(row => parseFloat(row[field1])).filter(val => !isNaN(val));
                const values2 = data.map(row => parseFloat(row[field2])).filter(val => !isNaN(val));

                if (values1.length === values2.length && values1.length > 0) {
                    const correlation = this.calculateCorrelation(values1, values2);
                    correlations.push({
                        field1: field1,
                        field2: field2,
                        correlation: correlation,
                        strength: this.getCorrelationStrength(correlation)
                    });
                }
            }
        }

        return {
            type: 'correlation',
            algorithm: config.algorithm,
            results: {
                correlations: correlations,
                summary: `分析了 ${correlations.length} 对字段的相关性`
            },
            visualization: {
                type: 'heatmap',
                data: correlations
            }
        };
    }

    calculateCorrelation(x, y) {
        const n = Math.min(x.length, y.length);
        if (n === 0) return 0;

        const meanX = x.reduce((a, b) => a + b, 0) / n;
        const meanY = y.reduce((a, b) => a + b, 0) / n;

        let numerator = 0;
        let denomX = 0;
        let denomY = 0;

        for (let i = 0; i < n; i++) {
            const dx = x[i] - meanX;
            const dy = y[i] - meanY;
            numerator += dx * dy;
            denomX += dx * dx;
            denomY += dy * dy;
        }

        const denominator = Math.sqrt(denomX * denomY);
        return denominator === 0 ? 0 : numerator / denominator;
    }

    getCorrelationStrength(correlation) {
        const abs = Math.abs(correlation);
        if (abs >= 0.8) return '强相关';
        if (abs >= 0.5) return '中等相关';
        if (abs >= 0.3) return '弱相关';
        return '无相关';
    }

    performClusteringAnalysis(data, config) {
        // 简化的K-means聚类实现
        const featureFields = config.featureFields;
        const k = config.parameters.k || 3;

        // 提取数值特征
        const features = data.map(row =>
            featureFields.map(field => parseFloat(row[field]) || 0)
        ).filter(row => row.some(val => !isNaN(val)));

        if (features.length < k) {
            return { error: '数据点数量少于聚类数量' };
        }

        // 简化的聚类结果（实际实现需要完整的K-means算法）
        const clusters = Array.from({ length: k }, (_, i) => ({
            id: i,
            center: featureFields.map(() => Math.random() * 100),
            points: [],
            size: Math.floor(features.length / k)
        }));

        return {
            type: 'clustering',
            algorithm: config.algorithm,
            results: {
                clusters: clusters,
                k: k,
                totalPoints: features.length,
                features: featureFields
            },
            visualization: {
                type: 'scatter',
                data: features,
                clusters: clusters
            }
        };
    }

    performPredictionAnalysis(data, config) {
        const targetField = config.targetField;
        const forecastPeriods = config.parameters.forecast_periods || 10;

        const values = data.map(row => parseFloat(row[targetField])).filter(val => !isNaN(val));

        if (values.length < 10) {
            return { error: '历史数据不足，无法进行预测' };
        }

        // 简化的预测（使用移动平均）
        const windowSize = Math.min(5, Math.floor(values.length / 4));
        const recentValues = values.slice(-windowSize);
        const average = recentValues.reduce((a, b) => a + b, 0) / recentValues.length;

        const predictions = Array.from({ length: forecastPeriods }, (_, i) => ({
            period: i + 1,
            value: average + (Math.random() - 0.5) * average * 0.1, // 添加一些随机变化
            confidence: Math.max(0.6, 0.9 - i * 0.05) // 置信度随时间递减
        }));

        return {
            type: 'prediction',
            algorithm: config.algorithm,
            results: {
                predictions: predictions,
                baseValue: average,
                forecastPeriods: forecastPeriods,
                historicalData: values.length
            },
            visualization: {
                type: 'forecast',
                historical: values,
                predictions: predictions
            }
        };
    }

    performAnomalyDetection(data, config) {
        const targetField = config.targetField;
        const threshold = config.parameters.threshold || 2.0;

        const values = data.map((row, index) => ({
            index: index,
            value: parseFloat(row[targetField]) || 0,
            record: row
        })).filter(item => !isNaN(item.value));

        if (values.length === 0) {
            return { error: '没有有效的数值数据' };
        }

        // 计算统计信息
        const mean = values.reduce((sum, item) => sum + item.value, 0) / values.length;
        const variance = values.reduce((sum, item) => sum + Math.pow(item.value - mean, 2), 0) / values.length;
        const stdDev = Math.sqrt(variance);

        // 检测异常值（使用Z-score方法）
        const anomalies = values.filter(item => {
            const zScore = Math.abs((item.value - mean) / stdDev);
            return zScore > threshold;
        });

        return {
            type: 'anomaly',
            algorithm: config.algorithm,
            results: {
                anomalies: anomalies.map(item => ({
                    index: item.index,
                    value: item.value,
                    zScore: ((item.value - mean) / stdDev).toFixed(3),
                    record: item.record
                })),
                totalRecords: values.length,
                anomalyCount: anomalies.length,
                anomalyRate: (anomalies.length / values.length * 100).toFixed(2),
                statistics: {
                    mean: mean.toFixed(3),
                    stdDev: stdDev.toFixed(3),
                    threshold: threshold
                }
            },
            visualization: {
                type: 'anomaly',
                data: values,
                anomalies: anomalies,
                threshold: threshold
            }
        };
    }

    performPatternMining(data, config) {
        // 简化的模式挖掘实现
        const featureFields = config.featureFields;
        const minSupport = config.parameters.min_support || 0.1;

        // 统计字段值的频率
        const patterns = {};
        featureFields.forEach(field => {
            const values = data.map(row => row[field]).filter(val => val != null);
            const frequency = {};

            values.forEach(value => {
                frequency[value] = (frequency[value] || 0) + 1;
            });

            Object.entries(frequency).forEach(([value, count]) => {
                const support = count / data.length;
                if (support >= minSupport) {
                    patterns[`${field}=${value}`] = {
                        field: field,
                        value: value,
                        count: count,
                        support: support,
                        confidence: support // 简化计算
                    };
                }
            });
        });

        const frequentPatterns = Object.values(patterns)
            .sort((a, b) => b.support - a.support)
            .slice(0, 20); // 只显示前20个模式

        return {
            type: 'pattern',
            algorithm: config.algorithm,
            results: {
                patterns: frequentPatterns,
                totalPatterns: frequentPatterns.length,
                minSupport: minSupport,
                dataSize: data.length
            },
            visualization: {
                type: 'patterns',
                data: frequentPatterns
            }
        };
    }

    displayMiningResults(results) {
        const resultsContainer = document.getElementById('miningResults');
        if (!resultsContainer || !results) return;

        if (results.error) {
            resultsContainer.innerHTML = `
                <h4>分析结果</h4>
                <div class="mining-error">
                    <i class="fas fa-exclamation-triangle"></i>
                    <p>${results.error}</p>
                </div>
            `;
            return;
        }

        resultsContainer.innerHTML = `
            <h4>分析结果 - ${results.type}</h4>
            <div class="mining-success">
                <div class="results-summary">
                    <h5>算法: ${results.algorithm}</h5>
                    ${this.renderResultsSummary(results)}
                </div>

                <div class="results-details">
                    ${this.renderResultsDetails(results)}
                </div>

                <div class="results-actions">
                    <button class="btn-secondary" onclick="advancedAnalytics.exportMiningResults()">
                        <i class="fas fa-download"></i>
                        导出结果
                    </button>
                    <button class="btn-secondary" onclick="advancedAnalytics.saveMiningModel()">
                        <i class="fas fa-save"></i>
                        保存模型
                    </button>
                    <button class="btn-primary" onclick="advancedAnalytics.createResultsDashboard()">
                        <i class="fas fa-chart-line"></i>
                        创建仪表板
                    </button>
                </div>
            </div>
        `;

        // 存储结果用于后续操作
        this.lastMiningResults = results;
    }

    renderResultsSummary(results) {
        switch (results.type) {
            case 'trend':
                return `
                    <div class="summary-item">
                        <span class="summary-label">趋势方向:</span>
                        <span class="summary-value">${results.results.trend}</span>
                    </div>
                    <div class="summary-item">
                        <span class="summary-label">相关系数:</span>
                        <span class="summary-value">${results.results.correlation}</span>
                    </div>
                    <div class="summary-item">
                        <span class="summary-label">数据点数:</span>
                        <span class="summary-value">${results.results.dataPoints}</span>
                    </div>
                `;
            case 'correlation':
                return `
                    <div class="summary-item">
                        <span class="summary-label">分析对数:</span>
                        <span class="summary-value">${results.results.correlations.length}</span>
                    </div>
                    <div class="summary-item">
                        <span class="summary-label">最强相关:</span>
                        <span class="summary-value">${Math.max(...results.results.correlations.map(c => Math.abs(c.correlation))).toFixed(3)}</span>
                    </div>
                `;
            case 'clustering':
                return `
                    <div class="summary-item">
                        <span class="summary-label">聚类数量:</span>
                        <span class="summary-value">${results.results.k}</span>
                    </div>
                    <div class="summary-item">
                        <span class="summary-label">数据点数:</span>
                        <span class="summary-value">${results.results.totalPoints}</span>
                    </div>
                `;
            case 'prediction':
                return `
                    <div class="summary-item">
                        <span class="summary-label">预测期数:</span>
                        <span class="summary-value">${results.results.forecastPeriods}</span>
                    </div>
                    <div class="summary-item">
                        <span class="summary-label">基准值:</span>
                        <span class="summary-value">${results.results.baseValue.toFixed(2)}</span>
                    </div>
                `;
            case 'anomaly':
                return `
                    <div class="summary-item">
                        <span class="summary-label">异常数量:</span>
                        <span class="summary-value">${results.results.anomalyCount}</span>
                    </div>
                    <div class="summary-item">
                        <span class="summary-label">异常率:</span>
                        <span class="summary-value">${results.results.anomalyRate}%</span>
                    </div>
                `;
            case 'pattern':
                return `
                    <div class="summary-item">
                        <span class="summary-label">频繁模式:</span>
                        <span class="summary-value">${results.results.totalPatterns}</span>
                    </div>
                    <div class="summary-item">
                        <span class="summary-label">最小支持度:</span>
                        <span class="summary-value">${results.results.minSupport}</span>
                    </div>
                `;
            default:
                return '<p>结果摘要</p>';
        }
    }

    renderResultsDetails(results) {
        switch (results.type) {
            case 'trend':
                return `
                    <h6>趋势方程</h6>
                    <p class="equation">${results.results.equation}</p>
                    <div class="trend-metrics">
                        <div class="metric">
                            <span class="metric-label">斜率:</span>
                            <span class="metric-value">${results.results.slope}</span>
                        </div>
                        <div class="metric">
                            <span class="metric-label">截距:</span>
                            <span class="metric-value">${results.results.intercept}</span>
                        </div>
                    </div>
                `;
            case 'correlation':
                return `
                    <h6>相关性矩阵</h6>
                    <div class="correlation-table">
                        ${results.results.correlations.map(corr => `
                            <div class="correlation-row">
                                <span class="field-pair">${corr.field1} ↔ ${corr.field2}</span>
                                <span class="correlation-value ${this.getCorrelationClass(corr.correlation)}">${corr.correlation.toFixed(3)}</span>
                                <span class="correlation-strength">${corr.strength}</span>
                            </div>
                        `).join('')}
                    </div>
                `;
            case 'clustering':
                return `
                    <h6>聚类结果</h6>
                    <div class="cluster-summary">
                        ${results.results.clusters.map(cluster => `
                            <div class="cluster-item">
                                <span class="cluster-id">聚类 ${cluster.id + 1}</span>
                                <span class="cluster-size">${cluster.size} 个数据点</span>
                            </div>
                        `).join('')}
                    </div>
                `;
            case 'prediction':
                return `
                    <h6>预测结果</h6>
                    <div class="prediction-table">
                        ${results.results.predictions.slice(0, 5).map(pred => `
                            <div class="prediction-row">
                                <span class="period">期间 ${pred.period}</span>
                                <span class="predicted-value">${pred.value.toFixed(2)}</span>
                                <span class="confidence">${(pred.confidence * 100).toFixed(1)}%</span>
                            </div>
                        `).join('')}
                        ${results.results.predictions.length > 5 ? '<div class="more-predictions">...</div>' : ''}
                    </div>
                `;
            case 'anomaly':
                return `
                    <h6>异常检测结果</h6>
                    <div class="anomaly-stats">
                        <div class="stat-item">
                            <span class="stat-label">平均值:</span>
                            <span class="stat-value">${results.results.statistics.mean}</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">标准差:</span>
                            <span class="stat-value">${results.results.statistics.stdDev}</span>
                        </div>
                    </div>
                    <div class="anomaly-list">
                        ${results.results.anomalies.slice(0, 5).map(anomaly => `
                            <div class="anomaly-item">
                                <span class="anomaly-index">记录 ${anomaly.index}</span>
                                <span class="anomaly-value">${anomaly.value}</span>
                                <span class="anomaly-score">Z-Score: ${anomaly.zScore}</span>
                            </div>
                        `).join('')}
                        ${results.results.anomalies.length > 5 ? '<div class="more-anomalies">...</div>' : ''}
                    </div>
                `;
            case 'pattern':
                return `
                    <h6>频繁模式</h6>
                    <div class="pattern-list">
                        ${results.results.patterns.slice(0, 10).map(pattern => `
                            <div class="pattern-item">
                                <span class="pattern-rule">${pattern.field} = ${pattern.value}</span>
                                <span class="pattern-support">支持度: ${(pattern.support * 100).toFixed(1)}%</span>
                                <span class="pattern-count">出现: ${pattern.count} 次</span>
                            </div>
                        `).join('')}
                    </div>
                `;
            default:
                return '<p>详细结果</p>';
        }
    }

    getCorrelationClass(correlation) {
        const abs = Math.abs(correlation);
        if (abs >= 0.8) return 'strong';
        if (abs >= 0.5) return 'medium';
        if (abs >= 0.3) return 'weak';
        return 'none';
    }

    // 数据挖掘结果操作方法
    exportMiningResults() {
        if (!this.lastMiningResults) return;

        const exportData = {
            results: this.lastMiningResults,
            exportTime: new Date(),
            version: '1.0'
        };

        const blob = new Blob([JSON.stringify(exportData, null, 2)], {
            type: 'application/json'
        });
        const url = URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = `mining-results-${this.lastMiningResults.type}-${new Date().toISOString().split('T')[0]}.json`;
        link.click();
        URL.revokeObjectURL(url);

        if (window.showNotification) {
            showNotification('分析结果导出成功', 'success');
        }
    }

    saveMiningModel() {
        if (!this.lastMiningResults) return;

        const models = JSON.parse(localStorage.getItem('miningModels') || '[]');
        const model = {
            id: 'model_' + Date.now(),
            name: `${this.lastMiningResults.type}_${this.lastMiningResults.algorithm}`,
            type: this.lastMiningResults.type,
            algorithm: this.lastMiningResults.algorithm,
            results: this.lastMiningResults,
            created_at: new Date()
        };

        models.push(model);
        localStorage.setItem('miningModels', JSON.stringify(models));

        if (window.showNotification) {
            showNotification('模型保存成功', 'success');
        }
    }

    createResultsDashboard() {
        if (!this.lastMiningResults) return;

        const dashboardName = `${this.lastMiningResults.type}分析结果`;
        const dashboard = {
            id: 'dashboard_' + Date.now(),
            name: dashboardName,
            description: `基于${this.lastMiningResults.algorithm}算法的${this.lastMiningResults.type}分析结果`,
            widgets: this.generateResultsWidgets(this.lastMiningResults),
            created_at: new Date()
        };

        this.dashboards.push(dashboard);
        this.updateDashboardSelector();
        this.switchDashboard(dashboard.id);
        this.saveData();

        // 关闭数据挖掘工具
        this.closeDataMining();

        if (window.showNotification) {
            showNotification(`仪表板 "${dashboardName}" 创建成功`, 'success');
        }
    }

    generateResultsWidgets(results) {
        const widgets = [];

        // 根据分析类型生成相应的组件
        switch (results.type) {
            case 'trend':
                widgets.push({
                    id: 'trend_chart',
                    type: 'chart-line',
                    title: '趋势分析图',
                    position: { x: 0, y: 0, w: 8, h: 4 },
                    config: { dataSource: 'custom', data: results.visualization }
                });
                widgets.push({
                    id: 'trend_metrics',
                    type: 'metric-card',
                    title: '趋势指标',
                    position: { x: 8, y: 0, w: 4, h: 2 },
                    config: { value: results.results.correlation, label: '相关系数' }
                });
                break;

            case 'correlation':
                widgets.push({
                    id: 'correlation_heatmap',
                    type: 'heatmap',
                    title: '相关性热力图',
                    position: { x: 0, y: 0, w: 6, h: 4 },
                    config: { dataSource: 'custom', data: results.visualization }
                });
                widgets.push({
                    id: 'correlation_table',
                    type: 'data-table',
                    title: '相关性详情',
                    position: { x: 6, y: 0, w: 6, h: 4 },
                    config: { dataSource: 'custom', data: results.results.correlations }
                });
                break;

            case 'clustering':
                widgets.push({
                    id: 'cluster_scatter',
                    type: 'chart-scatter',
                    title: '聚类散点图',
                    position: { x: 0, y: 0, w: 8, h: 4 },
                    config: { dataSource: 'custom', data: results.visualization }
                });
                widgets.push({
                    id: 'cluster_summary',
                    type: 'metric-card',
                    title: '聚类数量',
                    position: { x: 8, y: 0, w: 4, h: 2 },
                    config: { value: results.results.k, label: '聚类' }
                });
                break;

            case 'prediction':
                widgets.push({
                    id: 'prediction_chart',
                    type: 'chart-line',
                    title: '预测结果',
                    position: { x: 0, y: 0, w: 8, h: 4 },
                    config: { dataSource: 'custom', data: results.visualization }
                });
                widgets.push({
                    id: 'prediction_metrics',
                    type: 'metric-card',
                    title: '预测期数',
                    position: { x: 8, y: 0, w: 4, h: 2 },
                    config: { value: results.results.forecastPeriods, label: '期数' }
                });
                break;

            case 'anomaly':
                widgets.push({
                    id: 'anomaly_chart',
                    type: 'chart-scatter',
                    title: '异常检测图',
                    position: { x: 0, y: 0, w: 8, h: 4 },
                    config: { dataSource: 'custom', data: results.visualization }
                });
                widgets.push({
                    id: 'anomaly_rate',
                    type: 'metric-card',
                    title: '异常率',
                    position: { x: 8, y: 0, w: 4, h: 2 },
                    config: { value: results.results.anomalyRate + '%', label: '异常率' }
                });
                break;

            case 'pattern':
                widgets.push({
                    id: 'pattern_chart',
                    type: 'chart-bar',
                    title: '频繁模式',
                    position: { x: 0, y: 0, w: 8, h: 4 },
                    config: { dataSource: 'custom', data: results.visualization }
                });
                widgets.push({
                    id: 'pattern_count',
                    type: 'metric-card',
                    title: '模式数量',
                    position: { x: 8, y: 0, w: 4, h: 2 },
                    config: { value: results.results.totalPatterns, label: '模式' }
                });
                break;
        }

        return widgets;
    }

    loadStoredData() {
        // 从localStorage加载数据
        try {
            const stored = localStorage.getItem('analyticsData');
            if (stored) {
                const data = JSON.parse(stored);
                this.dashboards = data.dashboards || this.dashboards;
                this.widgets = data.widgets || this.widgets;
            }
        } catch (error) {
            console.error('加载分析数据失败:', error);
        }
    }

    saveData() {
        // 保存数据到localStorage
        try {
            const data = {
                dashboards: this.dashboards,
                widgets: this.widgets,
                timestamp: new Date()
            };
            localStorage.setItem('analyticsData', JSON.stringify(data));
        } catch (error) {
            console.error('保存分析数据失败:', error);
        }
    }
}

// 全局高级分析实例
let advancedAnalytics = null;

// 初始化高级分析系统
function initializeAdvancedAnalytics() {
    advancedAnalytics = new AdvancedAnalytics();
    console.log('✅ 高级数据分析系统已初始化');
}

// 显示分析面板
function showAdvancedAnalytics() {
    if (advancedAnalytics) {
        advancedAnalytics.showAnalyticsPanel();
    }
}

// 页面加载时初始化
document.addEventListener('DOMContentLoaded', function() {
    setTimeout(initializeAdvancedAnalytics, 1600);
});
