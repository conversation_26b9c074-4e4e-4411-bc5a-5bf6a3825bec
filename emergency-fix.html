<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>紧急修复</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            margin: 0;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .fix-container {
            background: white;
            padding: 40px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            max-width: 600px;
            width: 100%;
            text-align: center;
        }
        h1 {
            color: #e74c3c;
            margin-bottom: 20px;
            font-size: 28px;
        }
        .error-icon {
            font-size: 48px;
            color: #e74c3c;
            margin-bottom: 20px;
        }
        .btn {
            display: inline-block;
            padding: 15px 30px;
            margin: 10px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 600;
            text-decoration: none;
            transition: all 0.3s ease;
            min-width: 200px;
        }
        .btn-danger {
            background: #e74c3c;
            color: white;
        }
        .btn-warning {
            background: #f39c12;
            color: white;
        }
        .btn-success {
            background: #27ae60;
            color: white;
        }
        .btn-primary {
            background: #3498db;
            color: white;
        }
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        .status {
            margin: 15px 0;
            padding: 15px;
            border-radius: 8px;
            font-weight: 500;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .step {
            margin: 20px 0;
            text-align: left;
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            border-left: 4px solid #e74c3c;
        }
        .step h3 {
            margin-top: 0;
            color: #e74c3c;
        }
    </style>
</head>
<body>
    <div class="fix-container">
        <div class="error-icon">⚠️</div>
        <h1>系统紧急修复</h1>
        <p>检测到系统出现问题，请按以下步骤进行紧急修复：</p>
        
        <div id="status"></div>
        
        <div class="step">
            <h3>步骤 1: 清除所有数据</h3>
            <p>清除可能损坏的本地数据</p>
            <button class="btn btn-danger" onclick="clearAllData()">清除所有数据</button>
        </div>
        
        <div class="step">
            <h3>步骤 2: 重新设置登录状态</h3>
            <p>设置默认的登录状态</p>
            <button class="btn btn-warning" onclick="resetLoginState()">重置登录状态</button>
        </div>
        
        <div class="step">
            <h3>步骤 3: 测试基本功能</h3>
            <p>验证系统基本功能是否正常</p>
            <button class="btn btn-primary" onclick="testBasicFunctions()">测试功能</button>
        </div>
        
        <div class="step">
            <h3>步骤 4: 进入系统</h3>
            <p>尝试进入管理系统</p>
            <button class="btn btn-success" onclick="enterSystem()">进入系统</button>
        </div>
        
        <div class="step">
            <h3>备用方案</h3>
            <p>如果以上步骤无效，使用备用登录页面</p>
            <button class="btn btn-primary" onclick="goToLogin()">使用登录页面</button>
        </div>
    </div>

    <script>
        function showStatus(message, type = 'info') {
            const statusDiv = document.getElementById('status');
            statusDiv.innerHTML = `<div class="status ${type}">${message}</div>`;
        }

        function clearAllData() {
            try {
                localStorage.clear();
                sessionStorage.clear();
                showStatus('✅ 所有本地数据已清除', 'success');
            } catch (error) {
                showStatus('❌ 清除数据失败: ' + error.message, 'error');
            }
        }

        function resetLoginState() {
            try {
                // 设置登录状态
                localStorage.setItem('isLoggedIn', 'true');
                
                // 设置用户信息
                const userInfo = {
                    username: 'admin',
                    name: '张管理员',
                    role: '系统管理员',
                    email: '<EMAIL>',
                    phone: '13800138001',
                    department: '技术部',
                    avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=40&h=40&fit=crop&crop=face'
                };
                
                localStorage.setItem('currentUser', JSON.stringify(userInfo));
                
                showStatus('✅ 登录状态已重置', 'success');
                
            } catch (error) {
                showStatus('❌ 重置失败: ' + error.message, 'error');
            }
        }

        function testBasicFunctions() {
            try {
                // 测试localStorage
                localStorage.setItem('test', 'ok');
                const testValue = localStorage.getItem('test');
                localStorage.removeItem('test');
                
                if (testValue === 'ok') {
                    showStatus('✅ 基本功能测试通过', 'success');
                } else {
                    showStatus('❌ 基本功能测试失败', 'error');
                }
                
            } catch (error) {
                showStatus('❌ 功能测试失败: ' + error.message, 'error');
            }
        }

        function enterSystem() {
            // 确保登录状态
            if (localStorage.getItem('isLoggedIn') !== 'true') {
                resetLoginState();
            }
            
            showStatus('🚀 正在进入系统...', 'info');
            
            setTimeout(() => {
                try {
                    window.location.href = 'index.html';
                } catch (error) {
                    showStatus('❌ 进入系统失败，请尝试其他方法', 'error');
                }
            }, 1500);
        }

        function goToLogin() {
            showStatus('🔑 正在跳转到登录页面...', 'info');
            
            setTimeout(() => {
                try {
                    window.location.href = 'login.html';
                } catch (error) {
                    showStatus('❌ 跳转失败，请手动访问 login.html', 'error');
                }
            }, 1000);
        }

        // 页面加载时显示状态
        window.onload = function() {
            showStatus('⚠️ 系统出现问题，请按步骤进行修复', 'warning');
        };
    </script>
</body>
</html>