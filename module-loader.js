/**
 * 模块加载器
 * 解决JavaScript模块依赖和加载顺序问题
 */

class ModuleLoader {
    constructor() {
        this.modules = new Map();
        this.loadedModules = new Set();
        this.loadingModules = new Set();
        this.dependencies = new Map();
        this.callbacks = new Map();
        this.init();
    }

    init() {
        this.setupGlobalNamespace();
        this.registerCoreModules();
    }

    setupGlobalNamespace() {
        // 创建全局命名空间，避免变量污染
        if (!window.App) {
            window.App = {
                modules: {},
                utils: {},
                config: {},
                data: {}
            };
        }
    }

    registerCoreModules() {
        // 注册核心模块
        this.register('errorHandler', [], () => window.errorHandler);
        this.register('notificationManager', [], () => window.notificationManager);
        this.register('utils', [], () => this.createUtilsModule());
    }

    createUtilsModule() {
        return {
            // 安全的元素选择
            $(selector) {
                return window.errorHandler ? 
                    window.errorHandler.safeQuerySelector(selector) :
                    document.querySelector(selector);
            },

            $$(selector) {
                return window.errorHandler ? 
                    window.errorHandler.safeQuerySelectorAll(selector) :
                    document.querySelectorAll(selector);
            },

            // 安全的事件绑定
            on(element, event, handler) {
                if (!element) return;
                
                const safeHandler = window.errorHandler ? 
                    window.errorHandler.try(handler) : 
                    handler;
                
                element.addEventListener(event, safeHandler);
            },

            // 安全的AJAX请求
            async request(url, options = {}) {
                try {
                    const response = await fetch(url, options);
                    if (!response.ok) {
                        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                    }
                    return await response.json();
                } catch (error) {
                    if (window.errorHandler) {
                        window.errorHandler.handleError({
                            type: 'network',
                            message: `Request failed: ${url}`,
                            error: error,
                            timestamp: new Date()
                        });
                    }
                    throw error;
                }
            },

            // 防抖函数
            debounce(func, wait) {
                let timeout;
                return function executedFunction(...args) {
                    const later = () => {
                        clearTimeout(timeout);
                        func(...args);
                    };
                    clearTimeout(timeout);
                    timeout = setTimeout(later, wait);
                };
            },

            // 节流函数
            throttle(func, limit) {
                let inThrottle;
                return function(...args) {
                    if (!inThrottle) {
                        func.apply(this, args);
                        inThrottle = true;
                        setTimeout(() => inThrottle = false, limit);
                    }
                };
            },

            // 格式化日期
            formatDate(date, format = 'YYYY-MM-DD HH:mm:ss') {
                const d = new Date(date);
                const year = d.getFullYear();
                const month = String(d.getMonth() + 1).padStart(2, '0');
                const day = String(d.getDate()).padStart(2, '0');
                const hours = String(d.getHours()).padStart(2, '0');
                const minutes = String(d.getMinutes()).padStart(2, '0');
                const seconds = String(d.getSeconds()).padStart(2, '0');

                return format
                    .replace('YYYY', year)
                    .replace('MM', month)
                    .replace('DD', day)
                    .replace('HH', hours)
                    .replace('mm', minutes)
                    .replace('ss', seconds);
            },

            // 生成UUID
            generateUUID() {
                return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
                    const r = Math.random() * 16 | 0;
                    const v = c == 'x' ? r : (r & 0x3 | 0x8);
                    return v.toString(16);
                });
            }
        };
    }

    // 注册模块
    register(name, dependencies = [], factory = null) {
        this.modules.set(name, {
            name,
            dependencies,
            factory,
            instance: null
        });

        this.dependencies.set(name, dependencies);
        
        // 如果没有依赖，立即加载
        if (dependencies.length === 0 && factory) {
            this.loadModule(name);
        }
    }

    // 加载模块
    async loadModule(name) {
        if (this.loadedModules.has(name)) {
            return this.getModule(name);
        }

        if (this.loadingModules.has(name)) {
            // 等待模块加载完成
            return new Promise((resolve) => {
                const callbacks = this.callbacks.get(name) || [];
                callbacks.push(resolve);
                this.callbacks.set(name, callbacks);
            });
        }

        this.loadingModules.add(name);

        const module = this.modules.get(name);
        if (!module) {
            throw new Error(`Module ${name} not found`);
        }

        try {
            // 加载依赖
            const dependencies = await this.loadDependencies(module.dependencies);
            
            // 创建模块实例
            let instance;
            if (module.factory) {
                instance = typeof module.factory === 'function' ? 
                    module.factory(...dependencies) : 
                    module.factory;
            } else {
                instance = {};
            }

            module.instance = instance;
            this.loadedModules.add(name);
            this.loadingModules.delete(name);

            // 将模块添加到全局命名空间
            window.App.modules[name] = instance;

            // 执行回调
            const callbacks = this.callbacks.get(name) || [];
            callbacks.forEach(callback => callback(instance));
            this.callbacks.delete(name);

            return instance;
        } catch (error) {
            this.loadingModules.delete(name);
            throw error;
        }
    }

    // 加载依赖
    async loadDependencies(dependencies) {
        const results = [];
        for (const dep of dependencies) {
            const instance = await this.loadModule(dep);
            results.push(instance);
        }
        return results;
    }

    // 获取模块
    getModule(name) {
        const module = this.modules.get(name);
        return module ? module.instance : null;
    }

    // 检查模块是否已加载
    isLoaded(name) {
        return this.loadedModules.has(name);
    }

    // 等待模块加载
    async waitFor(name) {
        if (this.isLoaded(name)) {
            return this.getModule(name);
        }
        return await this.loadModule(name);
    }

    // 批量加载模块
    async loadModules(names) {
        const promises = names.map(name => this.loadModule(name));
        return await Promise.all(promises);
    }

    // 定义模块（兼容AMD风格）
    define(name, dependencies, factory) {
        if (typeof name !== 'string') {
            // 匿名模块
            factory = dependencies;
            dependencies = name;
            name = 'anonymous_' + Date.now();
        }

        if (typeof dependencies === 'function') {
            factory = dependencies;
            dependencies = [];
        }

        this.register(name, dependencies, factory);
        return this.loadModule(name);
    }

    // 获取模块状态
    getStatus() {
        return {
            registered: Array.from(this.modules.keys()),
            loaded: Array.from(this.loadedModules),
            loading: Array.from(this.loadingModules),
            dependencies: Object.fromEntries(this.dependencies)
        };
    }

    // 重置模块系统
    reset() {
        this.modules.clear();
        this.loadedModules.clear();
        this.loadingModules.clear();
        this.dependencies.clear();
        this.callbacks.clear();
        window.App.modules = {};
        this.registerCoreModules();
    }
}

// 创建全局实例
if (typeof window !== 'undefined') {
    window.moduleLoader = new ModuleLoader();
    
    // 提供全局便捷方法
    window.define = window.moduleLoader.define.bind(window.moduleLoader);
    window.require = window.moduleLoader.waitFor.bind(window.moduleLoader);
    
    // 兼容旧的模块定义方式
    window.App.define = window.define;
    window.App.require = window.require;
}

// 导出模块（如果支持）
if (typeof module !== 'undefined' && module.exports) {
    module.exports = ModuleLoader;
}
