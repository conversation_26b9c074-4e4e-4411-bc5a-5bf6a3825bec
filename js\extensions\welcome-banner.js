// 欢迎横幅功能
class WelcomeBanner {
    constructor() {
        this.greetings = {
            morning: ['早上好', '上午好', '晨安'],
            afternoon: ['下午好', '午安'],
            evening: ['晚上好', '晚安', '夜安']
        };
        
        this.motivationalMessages = [
            '今天是美好的一天，让我们一起创造更多价值',
            '每一个新的开始都充满无限可能',
            '成功源于每一天的坚持和努力',
            '让我们用智慧和热情点亮今天',
            '新的一天，新的机遇，新的成就',
            '用积极的心态迎接每一个挑战',
            '今天的努力是明天成功的基石'
        ];
        
        this.initializeBanner();
        this.startRealTimeUpdates();
    }
    
    initializeBanner() {
        this.updateGreeting();
        this.updateUserName();
        this.updateMotivationalMessage();
        this.updateStats();
    }
    
    updateGreeting() {
        const now = new Date();
        const hour = now.getHours();
        
        let timeOfDay;
        if (hour >= 5 && hour < 12) {
            timeOfDay = 'morning';
        } else if (hour >= 12 && hour < 18) {
            timeOfDay = 'afternoon';
        } else {
            timeOfDay = 'evening';
        }
        
        const greetings = this.greetings[timeOfDay];
        const randomGreeting = greetings[Math.floor(Math.random() * greetings.length)];
        
        const greetingElement = document.getElementById('greetingText');
        if (greetingElement) {
            greetingElement.textContent = randomGreeting;
        }
    }
    
    updateUserName() {
        const currentUser = localStorage.getItem('currentUser');
        if (currentUser) {
            try {
                const user = JSON.parse(currentUser);
                const userName = user.name || user.username || '用户';
                
                const userNameElement = document.getElementById('welcomeUserName');
                if (userNameElement) {
                    userNameElement.textContent = userName;
                }
            } catch (error) {
                console.error('解析用户信息失败:', error);
            }
        }
    }
    
    updateMotivationalMessage() {
        const messageElement = document.querySelector('.welcome-subtitle');
        if (messageElement) {
            const randomMessage = this.motivationalMessages[
                Math.floor(Math.random() * this.motivationalMessages.length)
            ];
            messageElement.textContent = randomMessage;
        }
    }
    
    updateStats() {
        // 模拟实时统计数据
        const todayVisits = Math.floor(Math.random() * 2000) + 1000;
        const activeUsers = Math.floor(Math.random() * 100) + 50;
        const systemStatus = Math.random() > 0.1 ? '正常' : '维护中';
        
        const todayVisitsElement = document.getElementById('todayVisits');
        const activeUsersElement = document.getElementById('activeUsers');
        const systemStatusElement = document.getElementById('systemStatus');
        
        if (todayVisitsElement) {
            this.animateNumber(todayVisitsElement, parseInt(todayVisitsElement.textContent.replace(/,/g, '')), todayVisits);
        }
        
        if (activeUsersElement) {
            this.animateNumber(activeUsersElement, parseInt(activeUsersElement.textContent), activeUsers);
        }
        
        if (systemStatusElement) {
            systemStatusElement.textContent = systemStatus;
            systemStatusElement.className = systemStatus === '正常' ? 'stat-value status-normal' : 'stat-value status-maintenance';
        }
    }
    
    animateNumber(element, start, end) {
        const duration = 1000; // 1秒动画
        const startTime = performance.now();
        
        const animate = (currentTime) => {
            const elapsed = currentTime - startTime;
            const progress = Math.min(elapsed / duration, 1);
            
            // 使用缓动函数
            const easeOutQuart = 1 - Math.pow(1 - progress, 4);
            const current = Math.floor(start + (end - start) * easeOutQuart);
            
            element.textContent = current.toLocaleString();
            
            if (progress < 1) {
                requestAnimationFrame(animate);
            }
        };
        
        requestAnimationFrame(animate);
    }
    
    startRealTimeUpdates() {
        // 每分钟更新问候语
        setInterval(() => {
            this.updateGreeting();
        }, 60000);
        
        // 每30秒更新统计数据
        setInterval(() => {
            this.updateStats();
        }, 30000);
        
        // 每5分钟更新励志消息
        setInterval(() => {
            this.updateMotivationalMessage();
        }, 300000);
    }
    
    // 添加特殊日期的问候
    getSpecialGreeting() {
        const now = new Date();
        const month = now.getMonth() + 1;
        const day = now.getDate();
        
        // 特殊节日问候
        const specialDays = {
            '1-1': '新年快乐！🎉',
            '2-14': '情人节快乐！💝',
            '3-8': '妇女节快乐！🌸',
            '5-1': '劳动节快乐！🎊',
            '6-1': '儿童节快乐！🎈',
            '10-1': '国庆节快乐！🇨🇳',
            '12-25': '圣诞节快乐！🎄'
        };
        
        const dateKey = `${month}-${day}`;
        return specialDays[dateKey] || null;
    }
    
    // 根据天气调整问候（模拟）
    getWeatherBasedGreeting() {
        const weather = ['sunny', 'cloudy', 'rainy', 'snowy'];
        const randomWeather = weather[Math.floor(Math.random() * weather.length)];
        
        const weatherGreetings = {
            sunny: '阳光明媚的一天！☀️',
            cloudy: '多云的天气，心情依然美好！☁️',
            rainy: '雨天也有雨天的美好！🌧️',
            snowy: '雪花纷飞，别有一番风味！❄️'
        };
        
        return weatherGreetings[randomWeather];
    }
    
    // 添加成就徽章显示
    showAchievementBadge(achievement) {
        const banner = document.querySelector('.welcome-banner');
        if (!banner) return;
        
        const badge = document.createElement('div');
        badge.className = 'achievement-badge';
        badge.innerHTML = `
            <div class="badge-icon">🏆</div>
            <div class="badge-text">${achievement}</div>
        `;
        
        banner.appendChild(badge);
        
        // 动画显示
        setTimeout(() => {
            badge.classList.add('show');
        }, 100);
        
        // 3秒后自动消失
        setTimeout(() => {
            badge.classList.remove('show');
            setTimeout(() => {
                badge.remove();
            }, 300);
        }, 3000);
    }
    
    // 添加快捷操作按钮
    addQuickActions() {
        const welcomeText = document.querySelector('.welcome-text');
        if (!welcomeText) return;
        
        const quickActions = document.createElement('div');
        quickActions.className = 'quick-actions';
        quickActions.innerHTML = `
            <button class="quick-action-btn" onclick="showPage('users')">
                <i class="fas fa-users"></i>
                <span>用户管理</span>
            </button>
            <button class="quick-action-btn" onclick="showPage('orders')">
                <i class="fas fa-shopping-cart"></i>
                <span>订单管理</span>
            </button>
            <button class="quick-action-btn" onclick="showPage('analytics')">
                <i class="fas fa-chart-bar"></i>
                <span>数据分析</span>
            </button>
            <button class="quick-action-btn" onclick="showPage('files')">
                <i class="fas fa-folder"></i>
                <span>文件管理</span>
            </button>
        `;
        
        welcomeText.appendChild(quickActions);
    }
}

// 全局欢迎横幅实例
let welcomeBanner = null;

// 初始化欢迎横幅
function initializeWelcomeBanner() {
    welcomeBanner = new WelcomeBanner();
    
    // 添加快捷操作
    if (welcomeBanner) {
        welcomeBanner.addQuickActions();
    }
    
    console.log('✅ 欢迎横幅已初始化');
}

// 页面加载时初始化
document.addEventListener('DOMContentLoaded', function() {
    // 延迟初始化，确保页面元素已加载
    setTimeout(() => {
        if (document.getElementById('dashboard')) {
            initializeWelcomeBanner();
        }
    }, 500);
});

// 模拟成就系统
function triggerAchievement(type) {
    if (!welcomeBanner) return;
    
    const achievements = {
        login: '连续登录7天！',
        orders: '处理订单100个！',
        users: '用户数量突破1000！',
        files: '文件上传成功！'
    };
    
    const achievement = achievements[type];
    if (achievement) {
        welcomeBanner.showAchievementBadge(achievement);
    }
}
