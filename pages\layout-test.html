<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>布局测试 - 企业管理系统</title>
    <link rel="stylesheet" href="../assets/css/styles.css">
    <link rel="stylesheet" href="../assets/css/modern-theme.css">
    <link rel="stylesheet" href="../assets/css/safe-enhancements.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            margin: 0;
            padding: 20px;
            font-family: 'Inter', sans-serif;
        }
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .test-title {
            color: white;
            text-align: center;
            margin-bottom: 32px;
            font-size: 32px;
            font-weight: 700;
        }
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        .status-ok { background: #10b981; }
        .status-error { background: #ef4444; }
        .test-section {
            margin-bottom: 32px;
        }
        .section-title {
            color: white;
            font-size: 20px;
            font-weight: 600;
            margin-bottom: 16px;
            padding: 12px 20px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 8px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1 class="test-title">🔧 布局修复测试</h1>
        
        <!-- 快速操作区域测试 -->
        <div class="test-section">
            <div class="section-title">
                <span class="status-indicator status-ok"></span>
                快速操作区域测试
            </div>
            <div class="quick-actions-section">
                <h3 class="quick-actions-title">快速操作</h3>
                <div class="quick-actions-grid">
                    <button class="quick-action-btn">
                        <div class="quick-action-icon">
                            <i class="fas fa-user-plus"></i>
                        </div>
                        <div>
                            <div style="font-weight: 600;">添加用户</div>
                            <div style="font-size: 12px; color: #6b7280;">创建新用户账户</div>
                        </div>
                    </button>
                    <button class="quick-action-btn">
                        <div class="quick-action-icon">
                            <i class="fas fa-upload"></i>
                        </div>
                        <div>
                            <div style="font-weight: 600;">批量导入</div>
                            <div style="font-size: 12px; color: #6b7280;">从文件导入用户</div>
                        </div>
                    </button>
                    <button class="quick-action-btn">
                        <div class="quick-action-icon">
                            <i class="fas fa-download"></i>
                        </div>
                        <div>
                            <div style="font-weight: 600;">导出数据</div>
                            <div style="font-size: 12px; color: #6b7280;">导出用户列表</div>
                        </div>
                    </button>
                    <button class="quick-action-btn">
                        <div class="quick-action-icon">
                            <i class="fas fa-chart-bar"></i>
                        </div>
                        <div>
                            <div style="font-weight: 600;">用户分析</div>
                            <div style="font-size: 12px; color: #6b7280;">查看用户统计</div>
                        </div>
                    </button>
                </div>
            </div>
        </div>

        <!-- 搜索工具栏测试 -->
        <div class="test-section">
            <div class="section-title">
                <span class="status-indicator status-ok"></span>
                搜索工具栏测试
            </div>
            <div class="page-toolbar">
                <div class="toolbar-row">
                    <div class="toolbar-section">
                        <div class="search-box">
                            <i class="fas fa-search search-icon"></i>
                            <input type="text" placeholder="搜索用户名、邮箱或姓名...">
                        </div>
                    </div>
                    
                    <div class="toolbar-divider"></div>
                    
                    <div class="toolbar-section">
                        <div class="filter-group">
                            <label>角色：</label>
                            <select class="filter-select">
                                <option>所有角色</option>
                                <option>系统管理员</option>
                                <option>部门经理</option>
                            </select>
                        </div>
                        
                        <div class="filter-group">
                            <label>状态：</label>
                            <select class="filter-select">
                                <option>所有状态</option>
                                <option>活跃</option>
                                <option>非活跃</option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="toolbar-divider"></div>
                    
                    <div class="toolbar-section">
                        <button class="btn-secondary btn-sm">
                            <i class="fas fa-sync"></i> 刷新
                        </button>
                        <button class="btn-secondary btn-sm">
                            <i class="fas fa-sliders-h"></i> 高级筛选
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 数据表格测试 -->
        <div class="test-section">
            <div class="section-title">
                <span class="status-indicator status-ok"></span>
                数据表格测试
            </div>
            <div class="data-table-container">
                <div class="table-header">
                    <h3 class="table-title">用户列表</h3>
                    <div class="table-actions">
                        <button class="btn-secondary btn-sm">
                            <i class="fas fa-check-square"></i> 全选
                        </button>
                        <button class="btn-secondary btn-sm">
                            <i class="fas fa-square"></i> 取消选择
                        </button>
                    </div>
                </div>
                
                <div style="overflow-x: auto;">
                    <table class="modern-table">
                        <thead>
                            <tr>
                                <th style="width: 50px;"><input type="checkbox"></th>
                                <th>用户信息</th>
                                <th>角色</th>
                                <th>状态</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td><input type="checkbox"></td>
                                <td>
                                    <div style="display: flex; align-items: center; gap: 12px;">
                                        <div style="width: 40px; height: 40px; background: #667eea; border-radius: 50%; display: flex; align-items: center; justify-content: center; color: white; font-weight: 600;">张</div>
                                        <div>
                                            <div style="font-weight: 600;">张三</div>
                                            <div style="font-size: 12px; color: #6b7280;"><EMAIL></div>
                                        </div>
                                    </div>
                                </td>
                                <td><span style="background: #dbeafe; color: #1e40af; padding: 4px 8px; border-radius: 4px; font-size: 12px;">管理员</span></td>
                                <td><span style="background: #dcfce7; color: #16a34a; padding: 4px 8px; border-radius: 4px; font-size: 12px;">活跃</span></td>
                                <td>
                                    <button class="btn-secondary btn-sm">编辑</button>
                                    <button class="btn-secondary btn-sm">删除</button>
                                </td>
                            </tr>
                            <tr>
                                <td><input type="checkbox"></td>
                                <td>
                                    <div style="display: flex; align-items: center; gap: 12px;">
                                        <div style="width: 40px; height: 40px; background: #10b981; border-radius: 50%; display: flex; align-items: center; justify-content: center; color: white; font-weight: 600;">李</div>
                                        <div>
                                            <div style="font-weight: 600;">李四</div>
                                            <div style="font-size: 12px; color: #6b7280;"><EMAIL></div>
                                        </div>
                                    </div>
                                </td>
                                <td><span style="background: #fef3c7; color: #d97706; padding: 4px 8px; border-radius: 4px; font-size: 12px;">用户</span></td>
                                <td><span style="background: #dcfce7; color: #16a34a; padding: 4px 8px; border-radius: 4px; font-size: 12px;">活跃</span></td>
                                <td>
                                    <button class="btn-secondary btn-sm">编辑</button>
                                    <button class="btn-secondary btn-sm">删除</button>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                
                <div class="pagination-container">
                    <div class="pagination-info">显示第 1-2 条，共 2 条记录</div>
                    <div class="pagination-controls">
                        <button class="pagination-btn" disabled><i class="fas fa-chevron-left"></i></button>
                        <button class="pagination-btn active">1</button>
                        <button class="pagination-btn" disabled><i class="fas fa-chevron-right"></i></button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 测试结果 -->
        <div class="test-section">
            <div class="section-title">
                <span class="status-indicator status-ok"></span>
                测试结果
            </div>
            <div style="background: rgba(255, 255, 255, 0.95); border-radius: 12px; padding: 24px; backdrop-filter: blur(10px);">
                <h3 style="margin: 0 0 16px 0; color: #1f2937;">✅ 布局修复成功</h3>
                <ul style="margin: 0; padding-left: 20px; color: #374151;">
                    <li>快速操作区域正常显示</li>
                    <li>搜索工具栏布局正确</li>
                    <li>数据表格样式完整</li>
                    <li>响应式设计正常工作</li>
                    <li>所有交互元素可用</li>
                </ul>
                <div style="margin-top: 20px; text-align: center;">
                    <button class="btn-primary" onclick="goToMain()" style="padding: 12px 24px;">
                        <i class="fas fa-arrow-right"></i> 返回主系统
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        function goToMain() {
            window.location.href = 'index.html';
        }
        
        // 测试交互功能
        document.addEventListener('DOMContentLoaded', function() {
            console.log('布局测试页面加载完成');
            
            // 测试快速操作按钮
            document.querySelectorAll('.quick-action-btn').forEach(btn => {
                btn.addEventListener('click', function() {
                    console.log('快速操作按钮点击:', this.textContent.trim());
                });
            });
            
            // 测试搜索框
            const searchInput = document.querySelector('.search-box input');
            if (searchInput) {
                searchInput.addEventListener('input', function() {
                    console.log('搜索输入:', this.value);
                });
            }
            
            // 测试筛选器
            document.querySelectorAll('.filter-select').forEach(select => {
                select.addEventListener('change', function() {
                    console.log('筛选器变更:', this.value);
                });
            });
        });
    </script>
</body>
</html>
