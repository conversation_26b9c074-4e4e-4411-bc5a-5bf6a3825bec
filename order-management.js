// 订单管理功能

// 模拟订单数据
let orders = [
    {
        id: 'ORD-2024-001',
        customer: {
            name: '张三',
            email: '<EMAIL>',
            phone: '13800138001',
            avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=32&h=32&fit=crop&crop=face'
        },
        items: [
            { name: 'iPhone 15 Pro', quantity: 1, price: 8999, image: 'https://images.unsplash.com/photo-1592750475338-74b7b21085ab?w=50&h=50&fit=crop' }
        ],
        total: 8999,
        status: 'completed',
        createdAt: '2024-01-15 10:30:00',
        updatedAt: '2024-01-15 14:20:00',
        shippingAddress: '北京市朝阳区xxx街道xxx号',
        paymentMethod: '支付宝',
        notes: '客户要求加急处理'
    },
    {
        id: 'ORD-2024-002',
        customer: {
            name: '李四',
            email: '<EMAIL>',
            phone: '13800138002',
            avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=32&h=32&fit=crop&crop=face'
        },
        items: [
            { name: 'MacBook Air M2', quantity: 1, price: 8999, image: 'https://images.unsplash.com/photo-1517336714731-489689fd1ca8?w=50&h=50&fit=crop' },
            { name: 'Magic Mouse', quantity: 1, price: 799, image: 'https://images.unsplash.com/photo-1527864550417-7fd91fc51a46?w=50&h=50&fit=crop' }
        ],
        total: 9798,
        status: 'processing',
        createdAt: '2024-01-14 16:45:00',
        updatedAt: '2024-01-15 09:15:00',
        shippingAddress: '上海市浦东新区xxx路xxx号',
        paymentMethod: '微信支付',
        notes: ''
    },
    {
        id: 'ORD-2024-003',
        customer: {
            name: '王五',
            email: '<EMAIL>',
            phone: '13800138003',
            avatar: 'https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=32&h=32&fit=crop&crop=face'
        },
        items: [
            { name: 'iPad Air', quantity: 1, price: 4399, image: 'https://images.unsplash.com/photo-1544244015-0df4b3ffc6b0?w=50&h=50&fit=crop' },
            { name: 'Apple Pencil', quantity: 1, price: 899, image: 'https://images.unsplash.com/photo-1625842268584-8f3296236761?w=50&h=50&fit=crop' }
        ],
        total: 5298,
        status: 'pending',
        createdAt: '2024-01-13 11:30:00',
        updatedAt: '2024-01-13 11:30:00',
        shippingAddress: '广州市天河区xxx大道xxx号',
        paymentMethod: '银行卡',
        notes: '请在工作日配送'
    },
    {
        id: 'ORD-2024-004',
        customer: {
            name: '赵六',
            email: '<EMAIL>',
            phone: '13800138004',
            avatar: 'https://images.unsplash.com/photo-1535713875002-d1d0cf377fde?w=32&h=32&fit=crop&crop=face'
        },
        items: [
            { name: 'AirPods Pro', quantity: 2, price: 1999, image: 'https://images.unsplash.com/photo-1606220945770-b5b6c2c55bf1?w=50&h=50&fit=crop' }
        ],
        total: 3998,
        status: 'shipped',
        createdAt: '2024-01-12 14:20:00',
        updatedAt: '2024-01-14 10:30:00',
        shippingAddress: '深圳市南山区xxx科技园xxx栋',
        paymentMethod: '支付宝',
        notes: '公司采购'
    },
    {
        id: 'ORD-2024-005',
        customer: {
            name: '孙七',
            email: '<EMAIL>',
            phone: '13800138005',
            avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=32&h=32&fit=crop&crop=face'
        },
        items: [
            { name: 'Apple Watch Series 9', quantity: 1, price: 2999, image: 'https://images.unsplash.com/photo-1434493789847-2f02dc6ca35d?w=50&h=50&fit=crop' }
        ],
        total: 2999,
        status: 'cancelled',
        createdAt: '2024-01-11 09:15:00',
        updatedAt: '2024-01-12 16:45:00',
        shippingAddress: '杭州市西湖区xxx路xxx号',
        paymentMethod: '微信支付',
        notes: '客户主动取消'
    }
];

// 分页配置
let currentOrderPage = 1;
const ordersPerPage = 10;
let filteredOrders = [...orders];
let currentOrderId = null;

// 初始化订单管理
function initializeOrderManagement() {
    updateOrderStats();
    renderOrdersTable();
    renderOrdersPagination();
}

// 更新订单统计
function updateOrderStats() {
    const totalOrders = orders.length;
    const completedOrders = orders.filter(order => order.status === 'completed').length;
    const pendingOrders = orders.filter(order => order.status === 'pending' || order.status === 'processing').length;
    const totalValue = orders.reduce((sum, order) => sum + order.total, 0);

    document.getElementById('totalOrdersCount').textContent = totalOrders;
    document.getElementById('completedOrders').textContent = completedOrders;
    document.getElementById('pendingOrders').textContent = pendingOrders;
    document.getElementById('totalOrderValue').textContent = formatCurrency(totalValue);
}

// 渲染订单表格
function renderOrdersTable() {
    const tbody = document.getElementById('ordersTableBody');
    if (!tbody) return;
    
    const startIndex = (currentOrderPage - 1) * ordersPerPage;
    const endIndex = startIndex + ordersPerPage;
    const pageOrders = filteredOrders.slice(startIndex, endIndex);

    tbody.innerHTML = pageOrders.map(order => `
        <tr>
            <td>
                <input type="checkbox" class="order-checkbox" value="${order.id}">
            </td>
            <td>
                <div class="order-info">
                    <div class="order-id">${order.id}</div>
                    <div class="order-date">${formatDateTime(order.createdAt)}</div>
                </div>
            </td>
            <td>
                <div class="customer-info">
                    <div class="customer-avatar">
                        <img src="${order.customer.avatar}" alt="${order.customer.name}">
                    </div>
                    <div class="customer-details">
                        <div class="customer-name">${order.customer.name}</div>
                        <div class="customer-email">${order.customer.email}</div>
                    </div>
                </div>
            </td>
            <td>
                <div class="order-items">
                    ${order.items.slice(0, 2).map(item => `
                        <div class="item-summary">
                            <img src="${item.image}" alt="${item.name}" class="item-image">
                            <span>${item.name} x${item.quantity}</span>
                        </div>
                    `).join('')}
                    ${order.items.length > 2 ? `<div class="more-items">+${order.items.length - 2} 更多</div>` : ''}
                </div>
            </td>
            <td>
                <div class="order-amount">${formatCurrency(order.total)}</div>
            </td>
            <td>
                <span class="status-badge ${order.status}">
                    <i class="fas ${getOrderStatusIcon(order.status)}"></i>
                    ${getOrderStatusText(order.status)}
                </span>
            </td>
            <td>${formatDateTime(order.createdAt)}</td>
            <td>
                <div class="action-buttons">
                    <button class="btn-icon" title="查看详情" onclick="viewOrderDetail('${order.id}')">
                        <i class="fas fa-eye"></i>
                    </button>
                    <button class="btn-icon" title="更新状态" onclick="showOrderStatusModal('${order.id}')">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="btn-icon danger" title="删除订单" onclick="deleteOrder('${order.id}')">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </td>
        </tr>
    `).join('');
}

// 获取订单状态图标
function getOrderStatusIcon(status) {
    const iconMap = {
        'pending': 'fa-clock',
        'processing': 'fa-spinner',
        'shipped': 'fa-truck',
        'completed': 'fa-check-circle',
        'cancelled': 'fa-times-circle',
        'refunded': 'fa-undo'
    };
    return iconMap[status] || 'fa-question-circle';
}

// 获取订单状态文本
function getOrderStatusText(status) {
    const textMap = {
        'pending': '待付款',
        'processing': '处理中',
        'shipped': '已发货',
        'completed': '已完成',
        'cancelled': '已取消',
        'refunded': '已退款'
    };
    return textMap[status] || '未知';
}

// 格式化货币
function formatCurrency(amount) {
    return '¥' + amount.toLocaleString();
}

// 格式化日期时间
function formatDateTime(dateString) {
    const date = new Date(dateString);
    return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
    });
}

// 渲染分页
function renderOrdersPagination() {
    const pagination = document.getElementById('ordersPagination');
    if (!pagination) return;
    
    const totalPages = Math.ceil(filteredOrders.length / ordersPerPage);
    
    if (totalPages <= 1) {
        pagination.innerHTML = '';
        return;
    }

    let paginationHTML = `
        <button class="pagination-btn" ${currentOrderPage === 1 ? 'disabled' : ''} onclick="changeOrderPage(${currentOrderPage - 1})">
            <i class="fas fa-chevron-left"></i>
        </button>
    `;

    for (let i = 1; i <= totalPages; i++) {
        if (i === 1 || i === totalPages || (i >= currentOrderPage - 2 && i <= currentOrderPage + 2)) {
            paginationHTML += `
                <button class="pagination-btn ${i === currentOrderPage ? 'active' : ''}" onclick="changeOrderPage(${i})">
                    ${i}
                </button>
            `;
        } else if (i === currentOrderPage - 3 || i === currentOrderPage + 3) {
            paginationHTML += '<span class="pagination-ellipsis">...</span>';
        }
    }

    paginationHTML += `
        <button class="pagination-btn" ${currentOrderPage === totalPages ? 'disabled' : ''} onclick="changeOrderPage(${currentOrderPage + 1})">
            <i class="fas fa-chevron-right"></i>
        </button>
        <div class="pagination-info">
            显示 ${(currentOrderPage - 1) * ordersPerPage + 1}-${Math.min(currentOrderPage * ordersPerPage, filteredOrders.length)} 
            共 ${filteredOrders.length} 条
        </div>
    `;

    pagination.innerHTML = paginationHTML;
}

// 切换页面
function changeOrderPage(page) {
    const totalPages = Math.ceil(filteredOrders.length / ordersPerPage);
    if (page >= 1 && page <= totalPages) {
        currentOrderPage = page;
        renderOrdersTable();
        renderOrdersPagination();
    }
}

// 筛选订单
function filterOrders() {
    const searchTerm = document.getElementById('orderSearch').value.toLowerCase();
    const statusFilter = document.getElementById('orderStatusFilter').value;
    const dateFilter = document.getElementById('orderDateFilter').value;

    filteredOrders = orders.filter(order => {
        const matchesSearch = !searchTerm || 
            order.id.toLowerCase().includes(searchTerm) ||
            order.customer.name.toLowerCase().includes(searchTerm) ||
            order.customer.email.toLowerCase().includes(searchTerm);
        
        const matchesStatus = !statusFilter || order.status === statusFilter;
        
        let matchesDate = true;
        if (dateFilter) {
            const orderDate = new Date(order.createdAt);
            const now = new Date();
            
            switch (dateFilter) {
                case 'today':
                    matchesDate = orderDate.toDateString() === now.toDateString();
                    break;
                case 'week':
                    const weekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
                    matchesDate = orderDate >= weekAgo;
                    break;
                case 'month':
                    matchesDate = orderDate.getMonth() === now.getMonth() && 
                                 orderDate.getFullYear() === now.getFullYear();
                    break;
                case 'quarter':
                    const quarter = Math.floor(now.getMonth() / 3);
                    const orderQuarter = Math.floor(orderDate.getMonth() / 3);
                    matchesDate = orderQuarter === quarter && 
                                 orderDate.getFullYear() === now.getFullYear();
                    break;
            }
        }

        return matchesSearch && matchesStatus && matchesDate;
    });

    currentOrderPage = 1;
    renderOrdersTable();
    renderOrdersPagination();
}

// 全选/取消全选
function toggleSelectAllOrders() {
    const selectAll = document.getElementById('selectAllOrders');
    const checkboxes = document.querySelectorAll('.order-checkbox');
    
    checkboxes.forEach(checkbox => {
        checkbox.checked = selectAll.checked;
    });
}

// 查看订单详情
function viewOrderDetail(orderId) {
    const order = orders.find(o => o.id === orderId);
    if (!order) return;

    const detailContent = document.getElementById('orderDetailContent');
    detailContent.innerHTML = `
        <div class="order-detail">
            <div class="detail-section">
                <h4>订单信息</h4>
                <div class="detail-grid">
                    <div class="detail-item">
                        <label>订单号:</label>
                        <span>${order.id}</span>
                    </div>
                    <div class="detail-item">
                        <label>创建时间:</label>
                        <span>${formatDateTime(order.createdAt)}</span>
                    </div>
                    <div class="detail-item">
                        <label>更新时间:</label>
                        <span>${formatDateTime(order.updatedAt)}</span>
                    </div>
                    <div class="detail-item">
                        <label>订单状态:</label>
                        <span class="status-badge ${order.status}">
                            <i class="fas ${getOrderStatusIcon(order.status)}"></i>
                            ${getOrderStatusText(order.status)}
                        </span>
                    </div>
                </div>
            </div>
            
            <div class="detail-section">
                <h4>客户信息</h4>
                <div class="customer-detail">
                    <img src="${order.customer.avatar}" alt="${order.customer.name}" class="customer-avatar-large">
                    <div class="customer-info-detail">
                        <div><strong>${order.customer.name}</strong></div>
                        <div>${order.customer.email}</div>
                        <div>${order.customer.phone}</div>
                    </div>
                </div>
            </div>
            
            <div class="detail-section">
                <h4>商品清单</h4>
                <div class="items-detail">
                    ${order.items.map(item => `
                        <div class="item-detail">
                            <img src="${item.image}" alt="${item.name}" class="item-image-large">
                            <div class="item-info">
                                <div class="item-name">${item.name}</div>
                                <div class="item-price">单价: ${formatCurrency(item.price)}</div>
                                <div class="item-quantity">数量: ${item.quantity}</div>
                                <div class="item-total">小计: ${formatCurrency(item.price * item.quantity)}</div>
                            </div>
                        </div>
                    `).join('')}
                </div>
                <div class="order-total">
                    <strong>订单总额: ${formatCurrency(order.total)}</strong>
                </div>
            </div>
            
            <div class="detail-section">
                <h4>配送信息</h4>
                <div class="shipping-detail">
                    <div><strong>配送地址:</strong> ${order.shippingAddress}</div>
                    <div><strong>支付方式:</strong> ${order.paymentMethod}</div>
                    ${order.notes ? `<div><strong>备注:</strong> ${order.notes}</div>` : ''}
                </div>
            </div>
        </div>
    `;

    document.getElementById('orderDetailModal').classList.add('active');
}

// 关闭订单详情模态框
function closeOrderDetailModal() {
    document.getElementById('orderDetailModal').classList.remove('active');
}

// 显示订单状态更新模态框
function showOrderStatusModal(orderId) {
    currentOrderId = orderId;
    const order = orders.find(o => o.id === orderId);
    if (order) {
        document.getElementById('newOrderStatus').value = order.status;
        document.getElementById('statusNote').value = '';
    }
    document.getElementById('orderStatusModal').classList.add('active');
}

// 关闭订单状态模态框
function closeOrderStatusModal() {
    document.getElementById('orderStatusModal').classList.remove('active');
    currentOrderId = null;
}

// 更新订单状态
function updateOrderStatus() {
    if (!currentOrderId) return;

    const newStatus = document.getElementById('newOrderStatus').value;
    const note = document.getElementById('statusNote').value;

    const orderIndex = orders.findIndex(o => o.id === currentOrderId);
    if (orderIndex !== -1) {
        orders[orderIndex].status = newStatus;
        orders[orderIndex].updatedAt = new Date().toISOString();
        
        showNotification(`订单 ${currentOrderId} 状态已更新为 ${getOrderStatusText(newStatus)}`, 'success');
        
        closeOrderStatusModal();
        filterOrders();
        updateOrderStats();
    }
}

// 删除订单
function deleteOrder(orderId) {
    if (confirm(`确定要删除订单 ${orderId} 吗？此操作不可撤销！`)) {
        const orderIndex = orders.findIndex(o => o.id === orderId);
        if (orderIndex !== -1) {
            orders.splice(orderIndex, 1);
            showNotification('订单删除成功', 'success');
            filterOrders();
            updateOrderStats();
        }
    }
}

// 导出订单
function exportOrders() {
    let csvContent = "data:text/csv;charset=utf-8,";
    csvContent += "订单号,客户姓名,客户邮箱,商品数量,订单金额,订单状态,创建时间,配送地址\n";
    
    filteredOrders.forEach(order => {
        const itemCount = order.items.reduce((sum, item) => sum + item.quantity, 0);
        csvContent += `${order.id},${order.customer.name},${order.customer.email},${itemCount},${order.total},${getOrderStatusText(order.status)},${order.createdAt},"${order.shippingAddress}"\n`;
    });
    
    const encodedUri = encodeURI(csvContent);
    const link = document.createElement("a");
    link.setAttribute("href", encodedUri);
    link.setAttribute("download", "orders.csv");
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    
    showNotification('订单数据导出成功', 'success');
}

// 批量处理
function batchProcess() {
    const selectedOrders = Array.from(document.querySelectorAll('.order-checkbox:checked')).map(cb => cb.value);
    
    if (selectedOrders.length === 0) {
        showNotification('请先选择要处理的订单', 'warning');
        return;
    }
    
    showNotification(`已选择 ${selectedOrders.length} 个订单进行批量处理`, 'info');
    // 这里可以实现具体的批量处理逻辑
}

// 显示添加订单模态框
function showAddOrderModal() {
    showNotification('添加订单功能开发中...', 'info');
}

// 打印订单
function printOrder() {
    showNotification('正在准备打印订单...', 'info');
    // 这里可以实现打印功能
}

// 页面加载时初始化
document.addEventListener('DOMContentLoaded', function() {
    // 检查是否在订单管理页面
    if (document.getElementById('ordersTable')) {
        initializeOrderManagement();
    }
});
