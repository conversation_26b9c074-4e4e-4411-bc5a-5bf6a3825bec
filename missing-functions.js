/**
 * 缺失函数补充文件
 * 提供系统中缺失的关键函数和类
 */

// 确保全局函数存在
if (typeof showNotification === 'undefined') {
    window.showNotification = function(message, type = 'info', options = {}) {
        if (window.notificationManager) {
            return window.notificationManager.show(message, type, options);
        }
        
        // 降级处理
        const typeMap = {
            'error': '错误',
            'success': '成功', 
            'warning': '警告',
            'info': '提示'
        };
        alert(`${typeMap[type] || '通知'}: ${message}`);
    };
}

// 确保handleLogin函数存在
if (typeof handleLogin === 'undefined') {
    window.handleLogin = function() {
        console.log('handleLogin function called');
        if (window.showNotification) {
            window.showNotification('登录功能已加载', 'info');
        }
    };
}

// 确保initializeLoginPage函数存在
if (typeof initializeLoginPage === 'undefined') {
    window.initializeLoginPage = function() {
        console.log('initializeLoginPage function called');
        
        // 基本的登录页面初始化
        const tabBtns = document.querySelectorAll('.tab-btn');
        const tabContents = document.querySelectorAll('.tab-content');
        
        // 标签页切换
        tabBtns.forEach(btn => {
            btn.addEventListener('click', () => {
                const targetTab = btn.getAttribute('data-tab');
                
                // 移除所有活动状态
                tabBtns.forEach(b => b.classList.remove('active'));
                tabContents.forEach(c => c.classList.remove('active'));
                
                // 添加活动状态
                btn.classList.add('active');
                const targetContent = document.getElementById(targetTab);
                if (targetContent) {
                    targetContent.classList.add('active');
                }
            });
        });
        
        console.log('登录页面初始化完成');
    };
}

// UserManager类
if (typeof UserManager === 'undefined') {
    window.UserManager = class UserManager {
        constructor() {
            this.users = [];
            this.currentUser = null;
            this.init();
        }
        
        init() {
            this.loadUsers();
            this.loadCurrentUser();
        }
        
        loadUsers() {
            try {
                const stored = localStorage.getItem('registeredUsers');
                this.users = stored ? JSON.parse(stored) : [];
            } catch (error) {
                console.error('Failed to load users:', error);
                this.users = [];
            }
        }
        
        loadCurrentUser() {
            try {
                const stored = localStorage.getItem('currentUser');
                this.currentUser = stored ? JSON.parse(stored) : null;
            } catch (error) {
                console.error('Failed to load current user:', error);
                this.currentUser = null;
            }
        }
        
        addUser(userData) {
            this.users.push(userData);
            this.saveUsers();
        }
        
        saveUsers() {
            try {
                localStorage.setItem('registeredUsers', JSON.stringify(this.users));
            } catch (error) {
                console.error('Failed to save users:', error);
            }
        }
        
        login(username, password) {
            const user = this.users.find(u => u.username === username);
            if (user) {
                this.currentUser = user;
                localStorage.setItem('currentUser', JSON.stringify(user));
                localStorage.setItem('isLoggedIn', 'true');
                return true;
            }
            return false;
        }
        
        logout() {
            this.currentUser = null;
            localStorage.removeItem('currentUser');
            localStorage.removeItem('isLoggedIn');
        }
        
        isLoggedIn() {
            return localStorage.getItem('isLoggedIn') === 'true';
        }
    };
}

// DashboardManager类
if (typeof DashboardManager === 'undefined') {
    window.DashboardManager = class DashboardManager {
        constructor() {
            this.charts = {};
            this.widgets = [];
            this.init();
        }
        
        init() {
            this.initializeWidgets();
            this.loadDashboardData();
        }
        
        initializeWidgets() {
            // 初始化仪表盘小部件
            const widgetElements = document.querySelectorAll('.widget, .dashboard-card');
            widgetElements.forEach((element, index) => {
                this.widgets.push({
                    id: `widget_${index}`,
                    element: element,
                    type: element.dataset.type || 'default'
                });
            });
        }
        
        loadDashboardData() {
            // 模拟加载仪表盘数据
            const mockData = {
                totalUsers: 1250,
                totalOrders: 3420,
                totalRevenue: 125000,
                growthRate: 12.5
            };
            
            this.updateWidgets(mockData);
        }
        
        updateWidgets(data) {
            // 更新小部件数据
            Object.keys(data).forEach(key => {
                const element = document.querySelector(`[data-metric="${key}"]`);
                if (element) {
                    element.textContent = data[key];
                }
            });
        }
        
        createChart(elementId, config) {
            if (typeof Chart !== 'undefined') {
                const ctx = document.getElementById(elementId);
                if (ctx) {
                    this.charts[elementId] = new Chart(ctx, config);
                }
            } else {
                console.warn('Chart.js not loaded, cannot create chart');
            }
        }
        
        updateChart(chartId, newData) {
            if (this.charts[chartId]) {
                this.charts[chartId].data = newData;
                this.charts[chartId].update();
            }
        }
        
        refreshDashboard() {
            this.loadDashboardData();
            Object.keys(this.charts).forEach(chartId => {
                if (this.charts[chartId]) {
                    this.charts[chartId].update();
                }
            });
        }
    };
}

// 页面管理器
if (typeof PageManager === 'undefined') {
    window.PageManager = class PageManager {
        constructor() {
            this.currentPage = 'dashboard';
            this.pages = new Map();
            this.init();
        }
        
        init() {
            this.registerPages();
            this.setupNavigation();
        }
        
        registerPages() {
            const pageElements = document.querySelectorAll('[data-page], .page-content');
            pageElements.forEach(element => {
                const pageId = element.dataset.page || element.id;
                if (pageId) {
                    this.pages.set(pageId, {
                        element: element,
                        title: element.dataset.title || pageId,
                        loaded: false
                    });
                }
            });
        }
        
        setupNavigation() {
            const navItems = document.querySelectorAll('.nav-item, [data-page-nav]');
            navItems.forEach(item => {
                item.addEventListener('click', (e) => {
                    e.preventDefault();
                    const pageId = item.dataset.page || item.dataset.pageNav;
                    if (pageId) {
                        this.showPage(pageId);
                    }
                });
            });
        }
        
        showPage(pageId) {
            // 隐藏所有页面
            this.pages.forEach((page, id) => {
                if (page.element) {
                    page.element.style.display = 'none';
                }
            });
            
            // 显示目标页面
            const targetPage = this.pages.get(pageId);
            if (targetPage && targetPage.element) {
                targetPage.element.style.display = 'block';
                this.currentPage = pageId;
                
                // 更新导航状态
                this.updateNavigation(pageId);
                
                // 触发页面加载事件
                if (!targetPage.loaded) {
                    this.loadPage(pageId);
                    targetPage.loaded = true;
                }
            }
        }
        
        updateNavigation(activePageId) {
            const navItems = document.querySelectorAll('.nav-item, [data-page-nav]');
            navItems.forEach(item => {
                const pageId = item.dataset.page || item.dataset.pageNav;
                if (pageId === activePageId) {
                    item.classList.add('active');
                } else {
                    item.classList.remove('active');
                }
            });
        }
        
        loadPage(pageId) {
            // 触发页面特定的加载逻辑
            const event = new CustomEvent('pageLoad', {
                detail: { pageId: pageId }
            });
            document.dispatchEvent(event);
        }
        
        getCurrentPage() {
            return this.currentPage;
        }
    };
}

// 初始化管理器实例
document.addEventListener('DOMContentLoaded', function() {
    // 等待核心模块加载
    setTimeout(() => {
        if (!window.userManager) {
            window.userManager = new UserManager();
        }
        
        if (!window.dashboardManager) {
            window.dashboardManager = new DashboardManager();
        }
        
        if (!window.pageManager) {
            window.pageManager = new PageManager();
        }
        
        console.log('Missing functions and classes initialized');
    }, 500);
});

// 导出到全局作用域
window.MissingFunctions = {
    UserManager,
    DashboardManager,
    PageManager,
    showNotification,
    handleLogin,
    initializeLoginPage
};
