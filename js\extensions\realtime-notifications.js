// 实时通知系统
class RealtimeNotificationSystem {
    constructor() {
        this.notifications = [];
        this.unreadCount = 0;
        this.isConnected = false;
        this.reconnectAttempts = 0;
        this.maxReconnectAttempts = 5;
        this.reconnectDelay = 1000;
        this.notificationTypes = {
            'system': { icon: 'fas fa-cog', color: '#6366f1' },
            'user': { icon: 'fas fa-user', color: '#10b981' },
            'order': { icon: 'fas fa-shopping-cart', color: '#f59e0b' },
            'security': { icon: 'fas fa-shield-alt', color: '#ef4444' },
            'info': { icon: 'fas fa-info-circle', color: '#3b82f6' },
            'warning': { icon: 'fas fa-exclamation-triangle', color: '#f59e0b' },
            'error': { icon: 'fas fa-times-circle', color: '#ef4444' },
            'success': { icon: 'fas fa-check-circle', color: '#10b981' }
        };
        
        this.initializeNotificationSystem();
        this.startRealtimeConnection();
        this.bindEvents();
    }

    initializeNotificationSystem() {
        this.createNotificationContainer();
        this.createNotificationPanel();
        this.loadStoredNotifications();
        this.updateNotificationBadge();
    }

    createNotificationContainer() {
        // 创建通知容器
        if (!document.getElementById('notificationContainer')) {
            const container = document.createElement('div');
            container.id = 'notificationContainer';
            container.className = 'notification-container';
            document.body.appendChild(container);
        }
    }

    createNotificationPanel() {
        // 创建通知面板
        const panel = document.createElement('div');
        panel.id = 'notificationPanel';
        panel.className = 'notification-panel';
        panel.innerHTML = `
            <div class="notification-panel-header">
                <h3>
                    <i class="fas fa-bell"></i>
                    通知中心
                </h3>
                <div class="notification-panel-actions">
                    <button class="btn-icon" onclick="realtimeNotifications.markAllAsRead()" title="全部标记为已读">
                        <i class="fas fa-check-double"></i>
                    </button>
                    <button class="btn-icon" onclick="realtimeNotifications.clearAllNotifications()" title="清空所有通知">
                        <i class="fas fa-trash"></i>
                    </button>
                    <button class="btn-icon" onclick="realtimeNotifications.closeNotificationPanel()" title="关闭">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            </div>
            <div class="notification-panel-body">
                <div class="notification-filters">
                    <button class="filter-btn active" data-filter="all">全部</button>
                    <button class="filter-btn" data-filter="unread">未读</button>
                    <button class="filter-btn" data-filter="system">系统</button>
                    <button class="filter-btn" data-filter="user">用户</button>
                    <button class="filter-btn" data-filter="order">订单</button>
                </div>
                <div class="notification-list" id="notificationList">
                    <!-- 通知列表将在这里动态生成 -->
                </div>
            </div>
            <div class="notification-panel-footer">
                <div class="connection-status" id="connectionStatus">
                    <i class="fas fa-circle"></i>
                    <span>连接状态: 连接中...</span>
                </div>
            </div>
        `;
        
        document.body.appendChild(panel);
    }

    bindEvents() {
        // 绑定通知按钮点击事件
        const notificationBtn = document.querySelector('.notification-btn');
        if (notificationBtn) {
            notificationBtn.addEventListener('click', () => {
                this.toggleNotificationPanel();
            });
        }

        // 绑定筛选按钮事件
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('filter-btn')) {
                this.filterNotifications(e.target.dataset.filter);
                
                // 更新筛选按钮状态
                document.querySelectorAll('.filter-btn').forEach(btn => {
                    btn.classList.remove('active');
                });
                e.target.classList.add('active');
            }
        });

        // 点击外部关闭通知面板
        document.addEventListener('click', (e) => {
            const panel = document.getElementById('notificationPanel');
            const notificationBtn = document.querySelector('.notification-btn');
            
            if (panel && !panel.contains(e.target) && !notificationBtn?.contains(e.target)) {
                this.closeNotificationPanel();
            }
        });
    }

    startRealtimeConnection() {
        // 模拟WebSocket连接
        this.simulateRealtimeConnection();
        this.updateConnectionStatus('connected');
    }

    simulateRealtimeConnection() {
        // 模拟实时通知
        setInterval(() => {
            if (Math.random() < 0.3) { // 30% 概率生成新通知
                this.generateRandomNotification();
            }
        }, 10000); // 每10秒检查一次

        // 模拟系统状态通知
        setInterval(() => {
            this.generateSystemStatusNotification();
        }, 60000); // 每分钟生成系统状态通知
    }

    generateRandomNotification() {
        const types = ['user', 'order', 'system', 'info'];
        const type = types[Math.floor(Math.random() * types.length)];
        
        const messages = {
            user: [
                '新用户注册: 张三',
                '用户李四更新了个人资料',
                '用户王五申请权限升级'
            ],
            order: [
                '新订单: #ORD-2024-' + Math.floor(Math.random() * 1000),
                '订单已完成: #ORD-2024-' + Math.floor(Math.random() * 1000),
                '订单需要处理: #ORD-2024-' + Math.floor(Math.random() * 1000)
            ],
            system: [
                '系统备份已完成',
                '数据库优化完成',
                '系统更新可用'
            ],
            info: [
                '今日访问量突破1000',
                '月度报告已生成',
                '新功能上线通知'
            ]
        };

        const typeMessages = messages[type];
        const message = typeMessages[Math.floor(Math.random() * typeMessages.length)];

        this.addNotification({
            type: type,
            title: this.getNotificationTitle(type),
            message: message,
            timestamp: new Date(),
            read: false,
            priority: Math.random() > 0.8 ? 'high' : 'normal'
        });
    }

    generateSystemStatusNotification() {
        const cpu = Math.floor(Math.random() * 100);
        const memory = Math.floor(Math.random() * 100);
        
        let type = 'info';
        let message = `系统运行正常 - CPU: ${cpu}%, 内存: ${memory}%`;
        
        if (cpu > 80 || memory > 80) {
            type = 'warning';
            message = `系统资源使用率较高 - CPU: ${cpu}%, 内存: ${memory}%`;
        }
        
        if (cpu > 95 || memory > 95) {
            type = 'error';
            message = `系统资源严重不足 - CPU: ${cpu}%, 内存: ${memory}%`;
        }

        this.addNotification({
            type: type,
            title: '系统监控',
            message: message,
            timestamp: new Date(),
            read: false,
            priority: type === 'error' ? 'high' : 'normal'
        });
    }

    getNotificationTitle(type) {
        const titles = {
            system: '系统通知',
            user: '用户活动',
            order: '订单更新',
            security: '安全警告',
            info: '信息提示',
            warning: '警告',
            error: '错误',
            success: '成功'
        };
        return titles[type] || '通知';
    }

    addNotification(notification) {
        // 添加唯一ID和时间戳
        notification.id = 'notif_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
        notification.timestamp = notification.timestamp || new Date();

        // 添加到通知列表开头
        this.notifications.unshift(notification);

        // 限制通知数量
        if (this.notifications.length > 100) {
            this.notifications = this.notifications.slice(0, 100);
        }

        // 更新未读计数
        if (!notification.read) {
            this.unreadCount++;
        }

        // 更新界面
        this.updateNotificationBadge();
        this.updateNotificationList();
        this.showToastNotification(notification);
        this.saveNotifications();

        // 播放通知声音
        this.playNotificationSound(notification.priority);
    }

    showToastNotification(notification) {
        const toast = document.createElement('div');
        toast.className = `notification-toast ${notification.type} ${notification.priority}`;
        toast.innerHTML = `
            <div class="toast-icon">
                <i class="${this.notificationTypes[notification.type]?.icon || 'fas fa-bell'}"></i>
            </div>
            <div class="toast-content">
                <div class="toast-title">${notification.title}</div>
                <div class="toast-message">${notification.message}</div>
            </div>
            <button class="toast-close" onclick="this.parentElement.remove()">
                <i class="fas fa-times"></i>
            </button>
        `;

        const container = document.getElementById('notificationContainer');
        container.appendChild(toast);

        // 自动移除
        setTimeout(() => {
            if (toast.parentElement) {
                toast.classList.add('fade-out');
                setTimeout(() => {
                    toast.remove();
                }, 300);
            }
        }, notification.priority === 'high' ? 8000 : 5000);

        // 点击关闭
        toast.addEventListener('click', () => {
            toast.remove();
        });
    }

    updateNotificationBadge() {
        const badges = document.querySelectorAll('.notification-badge');
        badges.forEach(badge => {
            if (this.unreadCount > 0) {
                badge.textContent = this.unreadCount > 99 ? '99+' : this.unreadCount;
                badge.style.display = 'block';
            } else {
                badge.style.display = 'none';
            }
        });

        // 更新页面标题
        if (this.unreadCount > 0) {
            document.title = `(${this.unreadCount}) 恋雪二游总导航页`;
        } else {
            document.title = '恋雪二游总导航页';
        }
    }

    updateNotificationList() {
        const list = document.getElementById('notificationList');
        if (!list) return;

        if (this.notifications.length === 0) {
            list.innerHTML = `
                <div class="no-notifications">
                    <i class="fas fa-bell-slash"></i>
                    <p>暂无通知</p>
                </div>
            `;
            return;
        }

        list.innerHTML = this.notifications.map(notification => `
            <div class="notification-item ${notification.read ? 'read' : 'unread'} ${notification.priority}" 
                 data-id="${notification.id}" data-type="${notification.type}">
                <div class="notification-icon">
                    <i class="${this.notificationTypes[notification.type]?.icon || 'fas fa-bell'}" 
                       style="color: ${this.notificationTypes[notification.type]?.color || '#6366f1'}"></i>
                </div>
                <div class="notification-content">
                    <div class="notification-header">
                        <span class="notification-title">${notification.title}</span>
                        <span class="notification-time">${this.formatTime(notification.timestamp)}</span>
                    </div>
                    <div class="notification-message">${notification.message}</div>
                </div>
                <div class="notification-actions">
                    <button class="btn-icon" onclick="realtimeNotifications.markAsRead('${notification.id}')" title="标记为已读">
                        <i class="fas fa-check"></i>
                    </button>
                    <button class="btn-icon" onclick="realtimeNotifications.deleteNotification('${notification.id}')" title="删除">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </div>
        `).join('');
    }

    formatTime(timestamp) {
        const now = new Date();
        const time = new Date(timestamp);
        const diff = now - time;

        if (diff < 60000) { // 1分钟内
            return '刚刚';
        } else if (diff < 3600000) { // 1小时内
            return Math.floor(diff / 60000) + '分钟前';
        } else if (diff < 86400000) { // 24小时内
            return Math.floor(diff / 3600000) + '小时前';
        } else {
            return time.toLocaleDateString('zh-CN');
        }
    }

    toggleNotificationPanel() {
        const panel = document.getElementById('notificationPanel');
        if (panel) {
            panel.classList.toggle('show');
        }
    }

    closeNotificationPanel() {
        const panel = document.getElementById('notificationPanel');
        if (panel) {
            panel.classList.remove('show');
        }
    }

    filterNotifications(filter) {
        const items = document.querySelectorAll('.notification-item');
        
        items.forEach(item => {
            const type = item.dataset.type;
            const isRead = item.classList.contains('read');
            
            let show = true;
            
            switch (filter) {
                case 'unread':
                    show = !isRead;
                    break;
                case 'all':
                    show = true;
                    break;
                default:
                    show = type === filter;
            }
            
            item.style.display = show ? 'flex' : 'none';
        });
    }

    markAsRead(notificationId) {
        const notification = this.notifications.find(n => n.id === notificationId);
        if (notification && !notification.read) {
            notification.read = true;
            this.unreadCount--;
            this.updateNotificationBadge();
            this.updateNotificationList();
            this.saveNotifications();
        }
    }

    markAllAsRead() {
        this.notifications.forEach(notification => {
            notification.read = true;
        });
        this.unreadCount = 0;
        this.updateNotificationBadge();
        this.updateNotificationList();
        this.saveNotifications();
    }

    deleteNotification(notificationId) {
        const index = this.notifications.findIndex(n => n.id === notificationId);
        if (index !== -1) {
            const notification = this.notifications[index];
            if (!notification.read) {
                this.unreadCount--;
            }
            this.notifications.splice(index, 1);
            this.updateNotificationBadge();
            this.updateNotificationList();
            this.saveNotifications();
        }
    }

    clearAllNotifications() {
        if (confirm('确定要清空所有通知吗？')) {
            this.notifications = [];
            this.unreadCount = 0;
            this.updateNotificationBadge();
            this.updateNotificationList();
            this.saveNotifications();
        }
    }

    playNotificationSound(priority = 'normal') {
        // 创建音频上下文
        if (typeof AudioContext !== 'undefined' || typeof webkitAudioContext !== 'undefined') {
            const AudioContextClass = AudioContext || webkitAudioContext;
            const audioContext = new AudioContextClass();
            
            // 生成通知音效
            const oscillator = audioContext.createOscillator();
            const gainNode = audioContext.createGain();
            
            oscillator.connect(gainNode);
            gainNode.connect(audioContext.destination);
            
            // 根据优先级设置不同的音调
            if (priority === 'high') {
                oscillator.frequency.setValueAtTime(800, audioContext.currentTime);
                oscillator.frequency.setValueAtTime(600, audioContext.currentTime + 0.1);
            } else {
                oscillator.frequency.setValueAtTime(600, audioContext.currentTime);
            }
            
            gainNode.gain.setValueAtTime(0.1, audioContext.currentTime);
            gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.2);
            
            oscillator.start(audioContext.currentTime);
            oscillator.stop(audioContext.currentTime + 0.2);
        }
    }

    updateConnectionStatus(status) {
        const statusElement = document.getElementById('connectionStatus');
        if (statusElement) {
            const icon = statusElement.querySelector('i');
            const text = statusElement.querySelector('span');
            
            switch (status) {
                case 'connected':
                    icon.className = 'fas fa-circle';
                    icon.style.color = '#10b981';
                    text.textContent = '连接状态: 已连接';
                    this.isConnected = true;
                    break;
                case 'disconnected':
                    icon.className = 'fas fa-circle';
                    icon.style.color = '#ef4444';
                    text.textContent = '连接状态: 已断开';
                    this.isConnected = false;
                    break;
                case 'connecting':
                    icon.className = 'fas fa-circle';
                    icon.style.color = '#f59e0b';
                    text.textContent = '连接状态: 连接中...';
                    this.isConnected = false;
                    break;
            }
        }
    }

    saveNotifications() {
        try {
            localStorage.setItem('notifications', JSON.stringify(this.notifications));
            localStorage.setItem('unreadCount', this.unreadCount.toString());
        } catch (error) {
            console.error('保存通知失败:', error);
        }
    }

    loadStoredNotifications() {
        try {
            const stored = localStorage.getItem('notifications');
            const unreadCount = localStorage.getItem('unreadCount');
            
            if (stored) {
                this.notifications = JSON.parse(stored);
                // 转换时间戳
                this.notifications.forEach(notification => {
                    notification.timestamp = new Date(notification.timestamp);
                });
            }
            
            if (unreadCount) {
                this.unreadCount = parseInt(unreadCount);
            }
        } catch (error) {
            console.error('加载通知失败:', error);
            this.notifications = [];
            this.unreadCount = 0;
        }
    }

    // 手动添加通知的公共方法
    notify(type, title, message, priority = 'normal') {
        this.addNotification({
            type: type,
            title: title,
            message: message,
            priority: priority
        });
    }
}

// 全局实时通知系统实例
let realtimeNotifications = null;

// 初始化实时通知系统
function initializeRealtimeNotifications() {
    realtimeNotifications = new RealtimeNotificationSystem();
    console.log('✅ 实时通知系统已初始化');
}

// 页面加载时初始化
document.addEventListener('DOMContentLoaded', function() {
    setTimeout(initializeRealtimeNotifications, 400);
});
