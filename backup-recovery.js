// 数据备份恢复系统
class BackupRecovery {
    constructor() {
        this.backupHistory = [];
        this.backupSchedules = [];
        this.currentBackup = null;
        this.backupTypes = {};
        this.compressionEnabled = true;
        this.encryptionEnabled = false;
        this.maxBackupSize = 100 * 1024 * 1024; // 100MB
        
        this.initializeBackupSystem();
        this.loadBackupHistory();
        this.setupAutoBackup();
    }

    initializeBackupSystem() {
        this.createBackupInterface();
        this.bindBackupEvents();
        this.setupBackupTypes();
        this.loadBackupSchedules();
    }

    createBackupInterface() {
        const backupPanel = document.createElement('div');
        backupPanel.id = 'backupPanel';
        backupPanel.className = 'backup-panel';
        backupPanel.innerHTML = `
            <div class="backup-header">
                <h3>
                    <i class="fas fa-database"></i>
                    数据备份恢复
                </h3>
                <div class="backup-controls">
                    <button class="btn-secondary" onclick="backupRecovery.scheduleBackup()">
                        <i class="fas fa-clock"></i>
                        定时备份
                    </button>
                    <button class="btn-primary" onclick="backupRecovery.createBackup()">
                        <i class="fas fa-save"></i>
                        立即备份
                    </button>
                    <button class="btn-icon" onclick="backupRecovery.closeBackupPanel()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            </div>
            
            <div class="backup-body">
                <div class="backup-tabs">
                    <button class="tab-btn active" data-tab="backup">
                        <i class="fas fa-save"></i>
                        创建备份
                    </button>
                    <button class="tab-btn" data-tab="restore">
                        <i class="fas fa-undo"></i>
                        恢复数据
                    </button>
                    <button class="tab-btn" data-tab="history">
                        <i class="fas fa-history"></i>
                        备份历史
                    </button>
                    <button class="tab-btn" data-tab="schedule">
                        <i class="fas fa-calendar-alt"></i>
                        定时任务
                    </button>
                    <button class="tab-btn" data-tab="settings">
                        <i class="fas fa-cog"></i>
                        备份设置
                    </button>
                </div>
                
                <div class="backup-content">
                    <!-- 创建备份 -->
                    <div class="tab-content active" id="backupTab">
                        <div class="backup-section">
                            <h4>选择备份内容</h4>
                            <div class="backup-options">
                                <label class="checkbox-label">
                                    <input type="checkbox" id="backupUsers" checked>
                                    <span class="checkmark"></span>
                                    <div class="option-info">
                                        <span class="option-name">用户数据</span>
                                        <span class="option-desc">用户信息、角色权限</span>
                                    </div>
                                </label>
                                
                                <label class="checkbox-label">
                                    <input type="checkbox" id="backupOrders" checked>
                                    <span class="checkmark"></span>
                                    <div class="option-info">
                                        <span class="option-name">订单数据</span>
                                        <span class="option-desc">订单记录、交易信息</span>
                                    </div>
                                </label>
                                
                                <label class="checkbox-label">
                                    <input type="checkbox" id="backupFiles" checked>
                                    <span class="checkmark"></span>
                                    <div class="option-info">
                                        <span class="option-name">文件数据</span>
                                        <span class="option-desc">上传的文件和文档</span>
                                    </div>
                                </label>
                                
                                <label class="checkbox-label">
                                    <input type="checkbox" id="backupSettings" checked>
                                    <span class="checkmark"></span>
                                    <div class="option-info">
                                        <span class="option-name">系统设置</span>
                                        <span class="option-desc">配置信息、主题设置</span>
                                    </div>
                                </label>
                                
                                <label class="checkbox-label">
                                    <input type="checkbox" id="backupWorkflows">
                                    <span class="checkmark"></span>
                                    <div class="option-info">
                                        <span class="option-name">工作流数据</span>
                                        <span class="option-desc">工作流定义和历史</span>
                                    </div>
                                </label>
                                
                                <label class="checkbox-label">
                                    <input type="checkbox" id="backupLogs">
                                    <span class="checkmark"></span>
                                    <div class="option-info">
                                        <span class="option-name">系统日志</span>
                                        <span class="option-desc">操作日志、审计记录</span>
                                    </div>
                                </label>
                            </div>
                        </div>
                        
                        <div class="backup-section">
                            <h4>备份选项</h4>
                            <div class="backup-config">
                                <div class="config-group">
                                    <label>备份名称</label>
                                    <input type="text" id="backupName" placeholder="自动生成备份名称">
                                </div>
                                
                                <div class="config-group">
                                    <label>备份描述</label>
                                    <textarea id="backupDescription" placeholder="描述此次备份的目的或内容"></textarea>
                                </div>
                                
                                <div class="config-group">
                                    <label>备份格式</label>
                                    <select id="backupFormat">
                                        <option value="json">JSON格式</option>
                                        <option value="zip">ZIP压缩包</option>
                                        <option value="sql">SQL脚本</option>
                                    </select>
                                </div>
                                
                                <div class="config-options">
                                    <label class="checkbox-label">
                                        <input type="checkbox" id="enableCompression" checked>
                                        <span class="checkmark"></span>
                                        启用压缩 (减少文件大小)
                                    </label>
                                    
                                    <label class="checkbox-label">
                                        <input type="checkbox" id="enableEncryption">
                                        <span class="checkmark"></span>
                                        启用加密 (保护敏感数据)
                                    </label>
                                    
                                    <label class="checkbox-label">
                                        <input type="checkbox" id="includeMetadata" checked>
                                        <span class="checkmark"></span>
                                        包含元数据 (备份时间、版本等)
                                    </label>
                                </div>
                            </div>
                        </div>
                        
                        <div class="backup-actions">
                            <button class="btn-secondary" onclick="backupRecovery.previewBackup()">
                                <i class="fas fa-eye"></i>
                                预览备份内容
                            </button>
                            <button class="btn-primary" onclick="backupRecovery.startBackup()">
                                <i class="fas fa-play"></i>
                                开始备份
                            </button>
                        </div>
                        
                        <div class="backup-progress" id="backupProgress" style="display: none;">
                            <div class="progress-header">
                                <h5>正在创建备份...</h5>
                                <span class="progress-percentage" id="backupProgressPercentage">0%</span>
                            </div>
                            <div class="progress-bar">
                                <div class="progress-fill" id="backupProgressFill"></div>
                            </div>
                            <div class="progress-details" id="backupProgressDetails">
                                准备备份数据...
                            </div>
                        </div>
                    </div>
                    
                    <!-- 恢复数据 -->
                    <div class="tab-content" id="restoreTab">
                        <div class="restore-section">
                            <h4>选择恢复方式</h4>
                            <div class="restore-options">
                                <div class="restore-option" onclick="backupRecovery.selectRestoreMethod('file')">
                                    <div class="option-icon">
                                        <i class="fas fa-file-upload"></i>
                                    </div>
                                    <div class="option-content">
                                        <h5>从文件恢复</h5>
                                        <p>上传备份文件进行数据恢复</p>
                                    </div>
                                </div>
                                
                                <div class="restore-option" onclick="backupRecovery.selectRestoreMethod('history')">
                                    <div class="option-icon">
                                        <i class="fas fa-history"></i>
                                    </div>
                                    <div class="option-content">
                                        <h5>从历史备份恢复</h5>
                                        <p>选择之前创建的备份进行恢复</p>
                                    </div>
                                </div>
                                
                                <div class="restore-option" onclick="backupRecovery.selectRestoreMethod('cloud')">
                                    <div class="option-icon">
                                        <i class="fas fa-cloud"></i>
                                    </div>
                                    <div class="option-content">
                                        <h5>从云端恢复</h5>
                                        <p>从云存储服务恢复备份</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="restore-config" id="restoreConfig" style="display: none;">
                            <!-- 恢复配置将根据选择的方式动态生成 -->
                        </div>
                    </div>
                    
                    <!-- 备份历史 -->
                    <div class="tab-content" id="historyTab">
                        <div class="history-section">
                            <div class="history-filters">
                                <select id="historyTypeFilter">
                                    <option value="all">所有类型</option>
                                    <option value="manual">手动备份</option>
                                    <option value="scheduled">定时备份</option>
                                    <option value="auto">自动备份</option>
                                </select>
                                <input type="date" id="historyDateFilter">
                                <input type="text" id="historySearchInput" placeholder="搜索备份...">
                            </div>
                            
                            <div class="backup-list" id="backupHistoryList">
                                <!-- 备份历史列表 -->
                            </div>
                        </div>
                    </div>
                    
                    <!-- 定时任务 -->
                    <div class="tab-content" id="scheduleTab">
                        <div class="schedule-section">
                            <div class="section-header">
                                <h4>定时备份任务</h4>
                                <button class="btn-primary" onclick="backupRecovery.createSchedule()">
                                    <i class="fas fa-plus"></i>
                                    新建任务
                                </button>
                            </div>
                            
                            <div class="schedule-list" id="scheduleList">
                                <!-- 定时任务列表 -->
                            </div>
                        </div>
                    </div>
                    
                    <!-- 备份设置 -->
                    <div class="tab-content" id="settingsTab">
                        <div class="settings-section">
                            <h4>备份设置</h4>
                            <div class="settings-form">
                                <div class="setting-group">
                                    <label>默认备份位置</label>
                                    <select id="defaultBackupLocation">
                                        <option value="local">本地存储</option>
                                        <option value="cloud">云端存储</option>
                                        <option value="both">本地+云端</option>
                                    </select>
                                </div>
                                
                                <div class="setting-group">
                                    <label>备份保留期限</label>
                                    <select id="backupRetention">
                                        <option value="7">7天</option>
                                        <option value="30" selected>30天</option>
                                        <option value="90">90天</option>
                                        <option value="365">1年</option>
                                        <option value="0">永久保留</option>
                                    </select>
                                </div>
                                
                                <div class="setting-group">
                                    <label>最大备份文件大小</label>
                                    <select id="maxBackupSize">
                                        <option value="50">50MB</option>
                                        <option value="100" selected>100MB</option>
                                        <option value="500">500MB</option>
                                        <option value="1000">1GB</option>
                                        <option value="0">无限制</option>
                                    </select>
                                </div>
                                
                                <div class="setting-group">
                                    <label>自动备份频率</label>
                                    <select id="autoBackupFrequency">
                                        <option value="disabled">禁用</option>
                                        <option value="daily" selected>每日</option>
                                        <option value="weekly">每周</option>
                                        <option value="monthly">每月</option>
                                    </select>
                                </div>
                                
                                <div class="setting-options">
                                    <label class="checkbox-label">
                                        <input type="checkbox" id="notifyOnBackup" checked>
                                        <span class="checkmark"></span>
                                        备份完成时发送通知
                                    </label>
                                    
                                    <label class="checkbox-label">
                                        <input type="checkbox" id="verifyBackup" checked>
                                        <span class="checkmark"></span>
                                        备份后验证数据完整性
                                    </label>
                                    
                                    <label class="checkbox-label">
                                        <input type="checkbox" id="cleanupOldBackups" checked>
                                        <span class="checkmark"></span>
                                        自动清理过期备份
                                    </label>
                                </div>
                            </div>
                            
                            <div class="settings-actions">
                                <button class="btn-secondary" onclick="backupRecovery.resetSettings()">
                                    <i class="fas fa-undo"></i>
                                    重置设置
                                </button>
                                <button class="btn-primary" onclick="backupRecovery.saveSettings()">
                                    <i class="fas fa-save"></i>
                                    保存设置
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
        
        document.body.appendChild(backupPanel);
    }

    setupBackupTypes() {
        this.backupTypes = {
            users: {
                name: '用户数据',
                description: '用户信息、角色权限',
                size: 0,
                getData: () => this.getUsersData()
            },
            orders: {
                name: '订单数据',
                description: '订单记录、交易信息',
                size: 0,
                getData: () => this.getOrdersData()
            },
            files: {
                name: '文件数据',
                description: '上传的文件和文档',
                size: 0,
                getData: () => this.getFilesData()
            },
            settings: {
                name: '系统设置',
                description: '配置信息、主题设置',
                size: 0,
                getData: () => this.getSettingsData()
            },
            workflows: {
                name: '工作流数据',
                description: '工作流定义和历史',
                size: 0,
                getData: () => this.getWorkflowsData()
            },
            logs: {
                name: '系统日志',
                description: '操作日志、审计记录',
                size: 0,
                getData: () => this.getLogsData()
            }
        };
    }

    bindBackupEvents() {
        // 标签切换
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('tab-btn') && e.target.closest('.backup-panel')) {
                this.switchBackupTab(e.target);
            }
        });
        
        // 备份选项变化
        document.addEventListener('change', (e) => {
            if (e.target.type === 'checkbox' && e.target.id.startsWith('backup')) {
                this.updateBackupPreview();
            }
        });
        
        // 历史筛选
        document.getElementById('historyTypeFilter')?.addEventListener('change', () => {
            this.filterBackupHistory();
        });
        
        document.getElementById('historyDateFilter')?.addEventListener('change', () => {
            this.filterBackupHistory();
        });
        
        document.getElementById('historySearchInput')?.addEventListener('input', () => {
            this.filterBackupHistory();
        });
    }

    switchBackupTab(tabBtn) {
        const container = tabBtn.closest('.backup-panel');
        const tabName = tabBtn.dataset.tab;
        
        // 更新标签状态
        container.querySelectorAll('.tab-btn').forEach(btn => btn.classList.remove('active'));
        container.querySelectorAll('.tab-content').forEach(content => content.classList.remove('active'));
        
        tabBtn.classList.add('active');
        container.querySelector(`#${tabName}Tab`).classList.add('active');
        
        // 加载对应内容
        this.loadTabContent(tabName);
    }

    loadTabContent(tabName) {
        switch (tabName) {
            case 'backup':
                this.updateBackupPreview();
                break;
            case 'restore':
                this.loadRestoreOptions();
                break;
            case 'history':
                this.renderBackupHistory();
                break;
            case 'schedule':
                this.renderScheduleList();
                break;
            case 'settings':
                this.loadBackupSettings();
                break;
        }
    }

    updateBackupPreview() {
        // 计算备份大小和内容预览
        let totalSize = 0;
        let selectedTypes = [];
        
        Object.keys(this.backupTypes).forEach(type => {
            const checkbox = document.getElementById(`backup${type.charAt(0).toUpperCase() + type.slice(1)}`);
            if (checkbox && checkbox.checked) {
                selectedTypes.push(type);
                totalSize += this.estimateDataSize(type);
            }
        });
        
        // 更新预览信息（如果有预览区域的话）
        console.log('备份预览:', { selectedTypes, totalSize });
    }

    estimateDataSize(type) {
        // 估算数据大小（KB）
        const estimates = {
            users: 50,
            orders: 200,
            files: 1000,
            settings: 10,
            workflows: 100,
            logs: 500
        };
        
        return estimates[type] || 0;
    }

    previewBackup() {
        const selectedTypes = this.getSelectedBackupTypes();
        
        if (selectedTypes.length === 0) {
            if (window.showNotification) {
                showNotification('请至少选择一种备份内容', 'warning');
            }
            return;
        }
        
        // 创建预览模态框
        const modal = document.createElement('div');
        modal.className = 'modal backup-preview-modal';
        modal.innerHTML = `
            <div class="modal-content">
                <div class="modal-header">
                    <h3>备份内容预览</h3>
                    <button class="modal-close" onclick="this.closest('.modal').remove()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="modal-body">
                    <div class="preview-summary">
                        <div class="summary-item">
                            <span class="summary-label">备份项目:</span>
                            <span class="summary-value">${selectedTypes.length} 项</span>
                        </div>
                        <div class="summary-item">
                            <span class="summary-label">预估大小:</span>
                            <span class="summary-value">${this.formatSize(this.calculateTotalSize(selectedTypes))}</span>
                        </div>
                        <div class="summary-item">
                            <span class="summary-label">备份格式:</span>
                            <span class="summary-value">${document.getElementById('backupFormat').value.toUpperCase()}</span>
                        </div>
                    </div>
                    
                    <div class="preview-details">
                        <h4>备份内容详情</h4>
                        <div class="content-list">
                            ${selectedTypes.map(type => {
                                const typeInfo = this.backupTypes[type];
                                return `
                                    <div class="content-item">
                                        <div class="content-name">${typeInfo.name}</div>
                                        <div class="content-desc">${typeInfo.description}</div>
                                        <div class="content-size">${this.formatSize(this.estimateDataSize(type) * 1024)}</div>
                                    </div>
                                `;
                            }).join('')}
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button class="btn-secondary" onclick="this.closest('.modal').remove()">关闭</button>
                    <button class="btn-primary" onclick="this.closest('.modal').remove(); backupRecovery.startBackup()">开始备份</button>
                </div>
            </div>
        `;
        
        document.body.appendChild(modal);
        modal.style.display = 'flex';
    }

    getSelectedBackupTypes() {
        const selectedTypes = [];
        Object.keys(this.backupTypes).forEach(type => {
            const checkbox = document.getElementById(`backup${type.charAt(0).toUpperCase() + type.slice(1)}`);
            if (checkbox && checkbox.checked) {
                selectedTypes.push(type);
            }
        });
        return selectedTypes;
    }

    calculateTotalSize(types) {
        return types.reduce((total, type) => total + this.estimateDataSize(type) * 1024, 0);
    }

    formatSize(bytes) {
        if (bytes === 0) return '0 B';
        const k = 1024;
        const sizes = ['B', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    async startBackup() {
        const selectedTypes = this.getSelectedBackupTypes();
        
        if (selectedTypes.length === 0) {
            if (window.showNotification) {
                showNotification('请至少选择一种备份内容', 'warning');
            }
            return;
        }
        
        // 显示进度条
        const progressContainer = document.getElementById('backupProgress');
        const progressFill = document.getElementById('backupProgressFill');
        const progressPercentage = document.getElementById('backupProgressPercentage');
        const progressDetails = document.getElementById('backupProgressDetails');
        
        progressContainer.style.display = 'block';
        
        try {
            const backupData = {
                id: 'backup_' + Date.now(),
                name: document.getElementById('backupName').value || `备份_${new Date().toLocaleString()}`,
                description: document.getElementById('backupDescription').value || '',
                timestamp: new Date(),
                format: document.getElementById('backupFormat').value,
                compression: document.getElementById('enableCompression').checked,
                encryption: document.getElementById('enableEncryption').checked,
                metadata: document.getElementById('includeMetadata').checked,
                types: selectedTypes,
                data: {}
            };
            
            // 模拟备份过程
            for (let i = 0; i < selectedTypes.length; i++) {
                const type = selectedTypes[i];
                const progress = ((i + 1) / selectedTypes.length) * 100;
                
                progressFill.style.width = progress + '%';
                progressPercentage.textContent = Math.round(progress) + '%';
                progressDetails.textContent = `正在备份 ${this.backupTypes[type].name}...`;
                
                // 获取数据
                backupData.data[type] = await this.backupTypes[type].getData();
                
                // 模拟延迟
                await new Promise(resolve => setTimeout(resolve, 1000));
            }
            
            progressDetails.textContent = '正在生成备份文件...';
            
            // 生成备份文件
            await this.generateBackupFile(backupData);
            
            // 保存到历史记录
            this.backupHistory.unshift(backupData);
            this.saveBackupHistory();
            
            progressContainer.style.display = 'none';
            
            if (window.showNotification) {
                showNotification('备份创建成功', 'success');
            }
            
        } catch (error) {
            progressContainer.style.display = 'none';
            
            if (window.showNotification) {
                showNotification('备份失败: ' + error.message, 'error');
            }
        }
    }

    async generateBackupFile(backupData) {
        let fileContent;
        let fileName;
        let mimeType;
        
        switch (backupData.format) {
            case 'json':
                fileContent = JSON.stringify(backupData, null, 2);
                fileName = `${backupData.name}.json`;
                mimeType = 'application/json';
                break;
            case 'zip':
                // 简化处理，实际应用中需要使用ZIP库
                fileContent = JSON.stringify(backupData, null, 2);
                fileName = `${backupData.name}.zip`;
                mimeType = 'application/zip';
                break;
            case 'sql':
                fileContent = this.generateSQLScript(backupData);
                fileName = `${backupData.name}.sql`;
                mimeType = 'text/sql';
                break;
        }
        
        // 下载文件
        const blob = new Blob([fileContent], { type: mimeType });
        const url = URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = fileName;
        link.click();
        URL.revokeObjectURL(url);
        
        return fileName;
    }

    generateSQLScript(backupData) {
        let sql = `-- 数据备份脚本\n-- 备份时间: ${backupData.timestamp}\n-- 备份名称: ${backupData.name}\n\n`;
        
        // 简化的SQL生成逻辑
        Object.entries(backupData.data).forEach(([type, data]) => {
            sql += `-- ${this.backupTypes[type].name}\n`;
            sql += `-- ${JSON.stringify(data)}\n\n`;
        });
        
        return sql;
    }

    // 数据获取方法
    getUsersData() {
        // 从localStorage或API获取用户数据
        return JSON.parse(localStorage.getItem('users') || '[]');
    }

    getOrdersData() {
        // 从localStorage或API获取订单数据
        return JSON.parse(localStorage.getItem('orders') || '[]');
    }

    getFilesData() {
        // 获取文件列表（不包含实际文件内容）
        return JSON.parse(localStorage.getItem('files') || '[]');
    }

    getSettingsData() {
        // 获取系统设置
        return {
            theme: localStorage.getItem('currentTheme'),
            language: localStorage.getItem('selectedLanguage'),
            customThemes: localStorage.getItem('customThemes'),
            userPreferences: localStorage.getItem('userPreferences')
        };
    }

    getWorkflowsData() {
        // 获取工作流数据
        return {
            workflows: JSON.parse(localStorage.getItem('workflows') || '[]'),
            workflowDrafts: JSON.parse(localStorage.getItem('workflowDrafts') || '[]')
        };
    }

    getLogsData() {
        // 获取系统日志
        return {
            auditLog: JSON.parse(localStorage.getItem('auditLog') || '[]'),
            apiHistory: JSON.parse(localStorage.getItem('apiHistory') || '[]'),
            assistantHistory: JSON.parse(localStorage.getItem('assistantHistory') || '[]')
        };
    }

    renderBackupHistory() {
        const historyList = document.getElementById('backupHistoryList');
        if (!historyList) return;
        
        if (this.backupHistory.length === 0) {
            historyList.innerHTML = `
                <div class="empty-state">
                    <i class="fas fa-database"></i>
                    <p>暂无备份历史</p>
                </div>
            `;
            return;
        }
        
        historyList.innerHTML = this.backupHistory.map(backup => `
            <div class="backup-item">
                <div class="backup-info">
                    <div class="backup-header">
                        <h5>${backup.name}</h5>
                        <span class="backup-date">${new Date(backup.timestamp).toLocaleString()}</span>
                    </div>
                    <div class="backup-description">${backup.description || '无描述'}</div>
                    <div class="backup-meta">
                        <span class="backup-format">${backup.format.toUpperCase()}</span>
                        <span class="backup-types">${backup.types.length} 项内容</span>
                        <span class="backup-size">${this.formatSize(this.calculateTotalSize(backup.types))}</span>
                    </div>
                </div>
                <div class="backup-actions">
                    <button class="btn-icon" onclick="backupRecovery.downloadBackup('${backup.id}')" title="下载">
                        <i class="fas fa-download"></i>
                    </button>
                    <button class="btn-icon" onclick="backupRecovery.restoreFromBackup('${backup.id}')" title="恢复">
                        <i class="fas fa-undo"></i>
                    </button>
                    <button class="btn-icon" onclick="backupRecovery.deleteBackup('${backup.id}')" title="删除">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </div>
        `).join('');
    }

    setupAutoBackup() {
        // 设置自动备份
        const frequency = localStorage.getItem('autoBackupFrequency') || 'daily';
        
        if (frequency !== 'disabled') {
            this.scheduleAutoBackup(frequency);
        }
    }

    scheduleAutoBackup(frequency) {
        const intervals = {
            daily: 24 * 60 * 60 * 1000,
            weekly: 7 * 24 * 60 * 60 * 1000,
            monthly: 30 * 24 * 60 * 60 * 1000
        };
        
        const interval = intervals[frequency];
        if (interval) {
            setInterval(() => {
                this.performAutoBackup();
            }, interval);
        }
    }

    performAutoBackup() {
        // 执行自动备份
        console.log('执行自动备份...');
        
        // 模拟自动备份逻辑
        const autoBackupData = {
            id: 'auto_backup_' + Date.now(),
            name: `自动备份_${new Date().toLocaleString()}`,
            description: '系统自动创建的备份',
            timestamp: new Date(),
            format: 'json',
            compression: true,
            encryption: false,
            metadata: true,
            types: ['users', 'orders', 'settings'],
            data: {}
        };
        
        // 添加到历史记录
        this.backupHistory.unshift(autoBackupData);
        this.saveBackupHistory();
        
        if (window.showNotification) {
            showNotification('自动备份已完成', 'info');
        }
    }

    // 面板控制方法
    showBackupPanel() {
        const panel = document.getElementById('backupPanel');
        if (panel) {
            panel.classList.add('show');
        }
    }

    closeBackupPanel() {
        const panel = document.getElementById('backupPanel');
        if (panel) {
            panel.classList.remove('show');
        }
    }

    // 存储方法
    saveBackupHistory() {
        try {
            localStorage.setItem('backupHistory', JSON.stringify(this.backupHistory));
        } catch (error) {
            console.error('保存备份历史失败:', error);
        }
    }

    loadBackupHistory() {
        try {
            const stored = localStorage.getItem('backupHistory');
            if (stored) {
                this.backupHistory = JSON.parse(stored);
            }
        } catch (error) {
            console.error('加载备份历史失败:', error);
            this.backupHistory = [];
        }
    }

    loadBackupSchedules() {
        try {
            const stored = localStorage.getItem('backupSchedules');
            if (stored) {
                this.backupSchedules = JSON.parse(stored);
            }
        } catch (error) {
            console.error('加载备份计划失败:', error);
            this.backupSchedules = [];
        }
    }

    // 其他方法的占位符
    createBackup() {
        this.showBackupPanel();
    }

    scheduleBackup() {
        // 显示定时备份设置
        this.switchBackupTab(document.querySelector('[data-tab="schedule"]'));
        this.showBackupPanel();
    }

    selectRestoreMethod(method) {
        console.log('选择恢复方式:', method);
    }

    filterBackupHistory() {
        // 实现备份历史筛选
        this.renderBackupHistory();
    }

    renderScheduleList() {
        // 渲染定时任务列表
        console.log('渲染定时任务列表');
    }

    loadBackupSettings() {
        // 加载备份设置
        console.log('加载备份设置');
    }

    downloadBackup(backupId) {
        console.log('下载备份:', backupId);
    }

    restoreFromBackup(backupId) {
        console.log('从备份恢复:', backupId);
    }

    deleteBackup(backupId) {
        if (confirm('确定要删除这个备份吗？')) {
            this.backupHistory = this.backupHistory.filter(b => b.id !== backupId);
            this.saveBackupHistory();
            this.renderBackupHistory();
            
            if (window.showNotification) {
                showNotification('备份已删除', 'success');
            }
        }
    }

    createSchedule() {
        console.log('创建定时任务');
    }

    resetSettings() {
        console.log('重置设置');
    }

    saveSettings() {
        console.log('保存设置');
    }
}

// 全局备份恢复实例
let backupRecovery = null;

// 初始化备份恢复系统
function initializeBackupRecovery() {
    backupRecovery = new BackupRecovery();
    console.log('✅ 数据备份恢复系统已初始化');
}

// 显示备份面板
function showBackupPanel() {
    if (backupRecovery) {
        backupRecovery.showBackupPanel();
    }
}

// 页面加载时初始化
document.addEventListener('DOMContentLoaded', function() {
    setTimeout(initializeBackupRecovery, 1300);
});
