// 调试脚本 - 检查页面功能
console.log('🔍 开始调试检查...');

// 检查页面加载状态
document.addEventListener('DOMContentLoaded', function() {
    console.log('✅ DOM已加载');
    
    // 检查导航元素
    const navItems = document.querySelectorAll('.nav-item');
    console.log(`📋 找到 ${navItems.length} 个导航项`);
    
    // 检查页面元素
    const pages = document.querySelectorAll('.page');
    console.log(`📄 找到 ${pages.length} 个页面`);
    
    // 检查NER页面
    const nerPage = document.getElementById('ner');
    console.log('🧠 NER页面:', nerPage ? '存在' : '不存在');
    
    // 检查NER导航项
    const nerNavItem = document.querySelector('.nav-item[data-page="ner"]');
    console.log('🔗 NER导航项:', nerNavItem ? '存在' : '不存在');
    
    // 添加点击事件监听器来调试
    navItems.forEach((item, index) => {
        const pageName = item.getAttribute('data-page');
        console.log(`📌 导航项 ${index + 1}: ${pageName}`);
        
        item.addEventListener('click', function(e) {
            console.log(`🖱️ 点击了导航项: ${pageName}`);
            e.preventDefault();
            
            // 手动切换页面
            pages.forEach(page => page.classList.remove('active'));
            navItems.forEach(nav => nav.classList.remove('active'));
            
            const targetPage = document.getElementById(pageName);
            if (targetPage) {
                targetPage.classList.add('active');
                this.classList.add('active');
                console.log(`✅ 切换到页面: ${pageName}`);
                
                // 如果是NER页面，初始化功能
                if (pageName === 'ner') {
                    console.log('🧠 初始化NER页面...');
                    if (typeof initializeNERManagement === 'function') {
                        initializeNERManagement();
                        console.log('✅ NER管理器已初始化');
                    } else {
                        console.log('❌ initializeNERManagement函数不存在');
                    }
                }
            } else {
                console.log(`❌ 找不到页面: ${pageName}`);
            }
        });
    });
    
    // 检查必要的函数是否存在
    const functions = [
        'initializeNERManagement',
        'generateNERData',
        'downloadNERData',
        'clearNERData'
    ];
    
    functions.forEach(funcName => {
        if (typeof window[funcName] === 'function') {
            console.log(`✅ 函数存在: ${funcName}`);
        } else {
            console.log(`❌ 函数不存在: ${funcName}`);
        }
    });
    
    // 检查Chart.js是否加载
    if (typeof Chart !== 'undefined') {
        console.log('✅ Chart.js已加载');
    } else {
        console.log('❌ Chart.js未加载');
    }
    
    console.log('🔍 调试检查完成');
});

// 添加全局错误监听器
window.addEventListener('error', function(e) {
    console.error('❌ JavaScript错误:', e.error);
    console.error('📍 错误位置:', e.filename, '行', e.lineno);
});

// 手动测试NER功能
window.testNER = function() {
    console.log('🧪 测试NER功能...');
    
    if (typeof generateNERData === 'function') {
        try {
            generateNERData();
            console.log('✅ NER数据生成测试成功');
        } catch (error) {
            console.error('❌ NER数据生成测试失败:', error);
        }
    } else {
        console.log('❌ generateNERData函数不存在');
    }
};

console.log('🔧 调试脚本已加载，可以在控制台运行 testNER() 来测试NER功能');
