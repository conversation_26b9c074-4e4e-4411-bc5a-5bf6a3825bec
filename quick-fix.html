<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>快速修复</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            margin: 0;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .fix-container {
            background: white;
            padding: 40px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            max-width: 500px;
            width: 100%;
            text-align: center;
        }
        h1 {
            color: #333;
            margin-bottom: 20px;
            font-size: 24px;
        }
        .btn {
            display: inline-block;
            padding: 15px 30px;
            margin: 10px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 600;
            text-decoration: none;
            transition: all 0.3s ease;
            min-width: 200px;
        }
        .btn-primary {
            background: #6366f1;
            color: white;
        }
        .btn-success {
            background: #10b981;
            color: white;
        }
        .btn-warning {
            background: #f59e0b;
            color: white;
        }
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        .status {
            margin: 15px 0;
            padding: 15px;
            border-radius: 8px;
            font-weight: 500;
        }
        .success {
            background: #d1fae5;
            color: #065f46;
            border: 1px solid #10b981;
        }
        .error {
            background: #fee2e2;
            color: #991b1b;
            border: 1px solid #ef4444;
        }
        .info {
            background: #dbeafe;
            color: #1e40af;
            border: 1px solid #3b82f6;
        }
        .step {
            margin: 20px 0;
            text-align: left;
            background: #f8fafc;
            padding: 20px;
            border-radius: 10px;
            border-left: 4px solid #6366f1;
        }
    </style>
</head>
<body>
    <div class="fix-container">
        <h1>🔧 系统快速修复</h1>
        <p>检测到管理界面无法正常访问，请按以下步骤修复：</p>
        
        <div id="status"></div>
        
        <div class="step">
            <strong>步骤 1:</strong> 设置登录状态
            <br><br>
            <button class="btn btn-success" onclick="setLoginState()">设置登录状态</button>
        </div>
        
        <div class="step">
            <strong>步骤 2:</strong> 进入系统
            <br><br>
            <button class="btn btn-primary" onclick="goToSystem()">进入管理系统</button>
            <button class="btn btn-warning" onclick="goToLogin()">进入登录页面</button>
        </div>
    </div>

    <script>
        function showStatus(message, type = 'info') {
            const statusDiv = document.getElementById('status');
            statusDiv.innerHTML = `<div class="status ${type}">${message}</div>`;
        }

        function setLoginState() {
            try {
                // 设置登录状态
                localStorage.setItem('isLoggedIn', 'true');
                
                // 设置用户信息
                const userInfo = {
                    username: 'admin',
                    name: '张管理员',
                    role: '系统管理员',
                    email: '<EMAIL>',
                    phone: '13800138001',
                    department: '技术部',
                    avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=40&h=40&fit=crop&crop=face'
                };
                
                localStorage.setItem('currentUser', JSON.stringify(userInfo));
                
                showStatus('✅ 登录状态设置成功！现在可以进入管理系统了。', 'success');
                
            } catch (error) {
                showStatus('❌ 设置失败: ' + error.message, 'error');
            }
        }

        function goToSystem() {
            // 确保登录状态
            if (localStorage.getItem('isLoggedIn') !== 'true') {
                setLoginState();
            }
            
            showStatus('🚀 正在跳转到管理系统...', 'info');
            
            setTimeout(() => {
                window.location.href = 'index.html';
            }, 1500);
        }

        function goToLogin() {
            showStatus('🔑 正在跳转到登录页面...', 'info');
            
            setTimeout(() => {
                window.location.href = 'login.html';
            }, 1000);
        }

        // 页面加载时检查状态
        window.onload = function() {
            const isLoggedIn = localStorage.getItem('isLoggedIn');
            const currentUser = localStorage.getItem('currentUser');
            
            if (isLoggedIn === 'true' && currentUser) {
                try {
                    const user = JSON.parse(currentUser);
                    showStatus(`ℹ️ 检测到已登录用户: ${user.name || user.username}`, 'info');
                } catch (e) {
                    showStatus('⚠️ 登录数据格式错误，需要重新设置', 'error');
                }
            } else {
                showStatus('ℹ️ 未检测到登录状态，请先设置登录状态', 'info');
            }
        };
    </script>
</body>
</html>