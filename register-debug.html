<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>注册功能调试页面</title>
    <link rel="stylesheet" href="login.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        .debug-panel {
            position: fixed;
            top: 10px;
            right: 10px;
            background: rgba(0, 0, 0, 0.9);
            color: white;
            padding: 15px;
            border-radius: 8px;
            font-size: 12px;
            z-index: 10000;
            max-width: 300px;
            max-height: 400px;
            overflow-y: auto;
        }
        .debug-panel h4 {
            margin: 0 0 10px 0;
            color: #10b981;
        }
        .debug-log {
            background: #1f2937;
            padding: 8px;
            border-radius: 4px;
            margin-top: 10px;
            font-family: monospace;
            font-size: 11px;
            max-height: 200px;
            overflow-y: auto;
        }
        .test-buttons {
            margin-top: 10px;
        }
        .test-btn {
            background: #3b82f6;
            color: white;
            border: none;
            padding: 5px 10px;
            border-radius: 4px;
            font-size: 11px;
            cursor: pointer;
            margin-right: 5px;
            margin-bottom: 5px;
        }
        .test-btn:hover {
            background: #2563eb;
        }
    </style>
</head>
<body>
    <!-- 调试面板 -->
    <div class="debug-panel">
        <h4>注册功能调试</h4>
        <div>
            <strong>状态:</strong> <span id="debugStatus">初始化中...</span>
        </div>
        <div class="test-buttons">
            <button class="test-btn" onclick="testRegisterFunction()">测试注册函数</button>
            <button class="test-btn" onclick="fillTestData()">填充测试数据</button>
            <button class="test-btn" onclick="clearLog()">清空日志</button>
        </div>
        <div class="debug-log" id="debugLog">等待日志...</div>
    </div>

    <!-- 背景动画 -->
    <div class="login-background">
        <div class="background-shapes">
            <div class="shape shape-1"></div>
            <div class="shape shape-2"></div>
            <div class="shape shape-3"></div>
        </div>
    </div>

    <div class="login-container">
        <div class="login-card">
            <div class="login-header">
                <div class="logo">
                    <div class="logo-icon">
                        <i class="fas fa-shield-alt"></i>
                    </div>
                    <div class="logo-text">
                        <h1>注册调试</h1>
                        <span>功能测试页面</span>
                    </div>
                </div>
                <p>专门用于调试注册功能的测试页面</p>
            </div>

            <form class="login-form" id="loginForm">
                <div class="form-tabs">
                    <button type="button" class="tab-btn" data-tab="login">
                        <i class="fas fa-sign-in-alt"></i>
                        <span>登录</span>
                    </button>
                    <button type="button" class="tab-btn active" data-tab="register">
                        <i class="fas fa-user-plus"></i>
                        <span>注册</span>
                    </button>
                </div>

                <!-- 登录表单（简化版） -->
                <div class="tab-content" id="login">
                    <div class="form-group">
                        <div class="input-wrapper">
                            <i class="fas fa-user"></i>
                            <input type="text" id="loginUsername" placeholder="用户名" required>
                        </div>
                    </div>
                    <div class="form-group">
                        <div class="input-wrapper">
                            <i class="fas fa-lock"></i>
                            <input type="password" id="loginPassword" placeholder="密码" required>
                        </div>
                    </div>
                    <button type="submit" class="btn-primary">
                        <i class="fas fa-sign-in-alt"></i>
                        <span>登录</span>
                    </button>
                </div>

                <!-- 注册表单 -->
                <div class="tab-content active" id="register">
                    <div class="form-row">
                        <div class="form-group">
                            <label for="firstName">姓名</label>
                            <div class="input-wrapper">
                                <i class="fas fa-user"></i>
                                <input type="text" id="firstName" name="firstName" placeholder="请输入您的姓名" required>
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="lastName">姓氏</label>
                            <div class="input-wrapper">
                                <i class="fas fa-user"></i>
                                <input type="text" id="lastName" name="lastName" placeholder="请输入您的姓氏" required>
                            </div>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="regUsername">用户名</label>
                        <div class="input-wrapper">
                            <i class="fas fa-at"></i>
                            <input type="text" id="regUsername" name="regUsername" placeholder="至少6个字符，字母数字组合" required>
                        </div>
                        <div class="error-message" id="usernameError"></div>
                    </div>

                    <div class="form-group">
                        <label for="email">邮箱地址</label>
                        <div class="input-wrapper">
                            <i class="fas fa-envelope"></i>
                            <input type="email" id="email" name="email" placeholder="请输入有效的邮箱地址" required>
                        </div>
                        <div class="error-message" id="emailError"></div>
                    </div>

                    <div class="form-group">
                        <label for="regPassword">密码</label>
                        <div class="input-wrapper">
                            <i class="fas fa-lock"></i>
                            <input type="password" id="regPassword" name="regPassword" placeholder="至少8位，包含大小写字母和数字" required>
                            <button type="button" class="toggle-password">
                                <i class="fas fa-eye"></i>
                            </button>
                        </div>
                        <div class="password-strength">
                            <div class="strength-bar">
                                <div class="strength-fill"></div>
                            </div>
                            <span class="strength-text">密码强度</span>
                        </div>
                        <div class="error-message" id="passwordError"></div>
                    </div>

                    <div class="form-group">
                        <label for="confirmPassword">确认密码</label>
                        <div class="input-wrapper">
                            <i class="fas fa-lock"></i>
                            <input type="password" id="confirmPassword" name="confirmPassword" placeholder="请再次输入密码" required>
                        </div>
                        <div class="error-message" id="confirmError"></div>
                    </div>

                    <div class="form-group">
                        <label class="checkbox-wrapper">
                            <input type="checkbox" name="terms" required>
                            <span class="checkmark">
                                <i class="fas fa-check"></i>
                            </span>
                            <span class="checkbox-label">
                                我同意 <a href="#" class="terms-link">服务条款</a> 和 <a href="#" class="terms-link">隐私政策</a>
                            </span>
                        </label>
                    </div>

                    <button type="submit" class="btn-primary" id="registerSubmitBtn">
                        <i class="fas fa-user-plus"></i>
                        <span>创建账户</span>
                    </button>

                    <div style="margin-top: 15px;">
                        <button type="button" class="test-btn" onclick="testRegisterFunction()" style="width: 100%; padding: 10px;">
                            直接测试注册函数
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <script src="login.js"></script>
    <script>
        // 调试日志系统
        const debugLog = document.getElementById('debugLog');
        const debugStatus = document.getElementById('debugStatus');
        
        function addLog(message) {
            const timestamp = new Date().toLocaleTimeString();
            debugLog.innerHTML += `<div>[${timestamp}] ${message}</div>`;
            debugLog.scrollTop = debugLog.scrollHeight;
        }
        
        function clearLog() {
            debugLog.innerHTML = '';
        }
        
        function updateStatus(status) {
            debugStatus.textContent = status;
        }
        
        // 重写console.log来捕获调试信息
        const originalConsoleLog = console.log;
        console.log = function(...args) {
            originalConsoleLog.apply(console, args);
            addLog(args.join(' '));
        };
        
        // 测试函数
        function testRegisterFunction() {
            addLog('=== 开始测试注册函数 ===');
            
            // 检查函数是否存在
            if (typeof handleRegister === 'function') {
                addLog('✅ handleRegister 函数存在');
                
                // 填充测试数据
                fillTestData();
                
                // 调用注册函数
                try {
                    handleRegister();
                    addLog('✅ 注册函数调用成功');
                } catch (error) {
                    addLog('❌ 注册函数调用失败: ' + error.message);
                }
            } else {
                addLog('❌ handleRegister 函数不存在');
            }
        }
        
        function fillTestData() {
            addLog('填充测试数据...');
            document.getElementById('firstName').value = '张';
            document.getElementById('lastName').value = '三';
            document.getElementById('regUsername').value = 'testuser123';
            document.getElementById('email').value = '<EMAIL>';
            document.getElementById('regPassword').value = 'TestPass123';
            document.getElementById('confirmPassword').value = 'TestPass123';
            document.querySelector('input[name="terms"]').checked = true;
            addLog('✅ 测试数据填充完成');
        }
        
        // 页面加载完成后的检查
        document.addEventListener('DOMContentLoaded', function() {
            addLog('页面加载完成');
            updateStatus('已加载');
            
            // 检查关键元素
            const form = document.getElementById('loginForm');
            const registerTab = document.getElementById('register');
            const registerBtn = document.querySelector('#register .btn-primary');
            
            addLog('检查关键元素:');
            addLog(`表单: ${form ? '✅ 存在' : '❌ 不存在'}`);
            addLog(`注册标签页: ${registerTab ? '✅ 存在' : '❌ 不存在'}`);
            addLog(`注册按钮: ${registerBtn ? '✅ 存在' : '❌ 不存在'}`);
            
            // 检查函数
            addLog('检查函数:');
            addLog(`handleRegister: ${typeof handleRegister === 'function' ? '✅ 存在' : '❌ 不存在'}`);
            addLog(`initializeFormSubmission: ${typeof initializeFormSubmission === 'function' ? '✅ 存在' : '❌ 不存在'}`);
            
            // 添加额外的事件监听器
            if (registerBtn) {
                registerBtn.addEventListener('click', function(e) {
                    addLog('注册按钮点击事件触发');
                });
            }
            
            if (form) {
                form.addEventListener('submit', function(e) {
                    addLog('表单提交事件触发');
                });
            }
            
            updateStatus('检查完成');
        });
        
        // 监听所有点击事件
        document.addEventListener('click', function(e) {
            if (e.target.matches('#register .btn-primary')) {
                addLog('注册按钮被点击 (通过事件委托)');
            }
        });
    </script>
</body>
</html>
