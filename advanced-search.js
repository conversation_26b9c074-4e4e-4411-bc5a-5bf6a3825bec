// 高级搜索和筛选系统
class AdvancedSearchSystem {
    constructor() {
        this.searchHistory = [];
        this.savedSearches = [];
        this.searchFilters = {};
        this.searchResults = [];
        this.currentQuery = '';
        this.searchIndex = {};
        this.searchableFields = {
            users: ['username', 'name', 'email', 'role', 'department'],
            orders: ['orderId', 'customerName', 'status', 'product', 'amount'],
            files: ['filename', 'type', 'size', 'uploadedBy', 'tags'],
            notifications: ['title', 'message', 'type', 'sender']
        };
        
        this.initializeSearchSystem();
        this.buildSearchIndex();
    }

    initializeSearchSystem() {
        this.createSearchInterface();
        this.bindSearchEvents();
        this.loadSearchHistory();
        this.loadSavedSearches();
    }

    createSearchInterface() {
        // 创建全局搜索框
        this.createGlobalSearchBox();
        
        // 创建高级搜索面板
        this.createAdvancedSearchPanel();
        
        // 创建搜索结果面板
        this.createSearchResultsPanel();
    }

    createGlobalSearchBox() {
        const searchBtn = document.querySelector('.action-btn[title="搜索"]');
        if (searchBtn) {
            searchBtn.addEventListener('click', () => {
                this.showGlobalSearch();
            });
        }

        // 创建全局搜索覆盖层
        const searchOverlay = document.createElement('div');
        searchOverlay.id = 'globalSearchOverlay';
        searchOverlay.className = 'global-search-overlay';
        searchOverlay.innerHTML = `
            <div class="global-search-container">
                <div class="global-search-header">
                    <div class="search-input-container">
                        <i class="fas fa-search search-icon"></i>
                        <input type="text" id="globalSearchInput" placeholder="搜索用户、订单、文件..." autocomplete="off">
                        <button class="search-clear" id="clearSearch" style="display: none;">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                    <button class="search-close" onclick="advancedSearch.hideGlobalSearch()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                
                <div class="search-suggestions" id="searchSuggestions">
                    <!-- 搜索建议将在这里显示 -->
                </div>
                
                <div class="search-quick-filters">
                    <span class="filter-label">快速筛选:</span>
                    <button class="quick-filter" data-type="users">用户</button>
                    <button class="quick-filter" data-type="orders">订单</button>
                    <button class="quick-filter" data-type="files">文件</button>
                    <button class="quick-filter" data-type="notifications">通知</button>
                </div>
                
                <div class="search-history" id="searchHistory">
                    <!-- 搜索历史将在这里显示 -->
                </div>
            </div>
        `;
        
        document.body.appendChild(searchOverlay);
    }

    createAdvancedSearchPanel() {
        const panel = document.createElement('div');
        panel.id = 'advancedSearchPanel';
        panel.className = 'advanced-search-panel';
        panel.innerHTML = `
            <div class="advanced-search-header">
                <h3>高级搜索</h3>
                <button class="panel-close" onclick="advancedSearch.hideAdvancedSearch()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            
            <div class="advanced-search-body">
                <div class="search-section">
                    <label>搜索范围</label>
                    <div class="search-scope">
                        <label class="checkbox-label">
                            <input type="checkbox" name="scope" value="users" checked>
                            <span class="checkmark"></span>
                            用户管理
                        </label>
                        <label class="checkbox-label">
                            <input type="checkbox" name="scope" value="orders" checked>
                            <span class="checkmark"></span>
                            订单管理
                        </label>
                        <label class="checkbox-label">
                            <input type="checkbox" name="scope" value="files" checked>
                            <span class="checkmark"></span>
                            文件管理
                        </label>
                        <label class="checkbox-label">
                            <input type="checkbox" name="scope" value="notifications">
                            <span class="checkmark"></span>
                            通知中心
                        </label>
                    </div>
                </div>
                
                <div class="search-section">
                    <label>时间范围</label>
                    <div class="date-range">
                        <input type="date" id="startDate" placeholder="开始日期">
                        <span>至</span>
                        <input type="date" id="endDate" placeholder="结束日期">
                    </div>
                </div>
                
                <div class="search-section">
                    <label>排序方式</label>
                    <select id="sortBy">
                        <option value="relevance">相关性</option>
                        <option value="date">日期</option>
                        <option value="name">名称</option>
                        <option value="size">大小</option>
                    </select>
                </div>
                
                <div class="search-section">
                    <label>结果数量</label>
                    <select id="resultLimit">
                        <option value="10">10条</option>
                        <option value="25" selected>25条</option>
                        <option value="50">50条</option>
                        <option value="100">100条</option>
                    </select>
                </div>
            </div>
            
            <div class="advanced-search-footer">
                <button class="btn-secondary" onclick="advancedSearch.resetAdvancedSearch()">重置</button>
                <button class="btn-primary" onclick="advancedSearch.executeAdvancedSearch()">搜索</button>
            </div>
        `;
        
        document.body.appendChild(panel);
    }

    createSearchResultsPanel() {
        const panel = document.createElement('div');
        panel.id = 'searchResultsPanel';
        panel.className = 'search-results-panel';
        panel.innerHTML = `
            <div class="search-results-header">
                <h3>搜索结果</h3>
                <div class="results-actions">
                    <button class="btn-icon" onclick="advancedSearch.exportSearchResults()" title="导出结果">
                        <i class="fas fa-download"></i>
                    </button>
                    <button class="btn-icon" onclick="advancedSearch.saveCurrentSearch()" title="保存搜索">
                        <i class="fas fa-bookmark"></i>
                    </button>
                    <button class="panel-close" onclick="advancedSearch.hideSearchResults()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            </div>
            
            <div class="search-results-body">
                <div class="results-summary" id="resultsSummary">
                    <!-- 搜索结果摘要 -->
                </div>
                
                <div class="results-filters">
                    <div class="filter-group">
                        <label>类型筛选:</label>
                        <div class="filter-buttons" id="typeFilters">
                            <!-- 类型筛选按钮 -->
                        </div>
                    </div>
                </div>
                
                <div class="results-list" id="searchResultsList">
                    <!-- 搜索结果列表 -->
                </div>
            </div>
        `;
        
        document.body.appendChild(panel);
    }

    bindSearchEvents() {
        // 全局搜索输入事件
        document.addEventListener('input', (e) => {
            if (e.target.id === 'globalSearchInput') {
                this.handleSearchInput(e.target.value);
            }
        });

        // 搜索快捷键
        document.addEventListener('keydown', (e) => {
            // Ctrl+K 或 Cmd+K 打开搜索
            if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
                e.preventDefault();
                this.showGlobalSearch();
            }
            
            // ESC 关闭搜索
            if (e.key === 'Escape') {
                this.hideGlobalSearch();
                this.hideAdvancedSearch();
                this.hideSearchResults();
            }
        });

        // 快速筛选按钮
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('quick-filter')) {
                this.applyQuickFilter(e.target.dataset.type);
            }
        });

        // 点击外部关闭
        document.addEventListener('click', (e) => {
            const overlay = document.getElementById('globalSearchOverlay');
            if (overlay && e.target === overlay) {
                this.hideGlobalSearch();
            }
        });
    }

    buildSearchIndex() {
        // 构建搜索索引
        this.searchIndex = {
            users: this.indexUsers(),
            orders: this.indexOrders(),
            files: this.indexFiles(),
            notifications: this.indexNotifications()
        };
    }

    indexUsers() {
        // 模拟用户数据索引
        return [
            { id: 1, username: 'admin', name: '张管理员', email: '<EMAIL>', role: '系统管理员', department: '技术部' },
            { id: 2, username: 'manager', name: '李经理', email: '<EMAIL>', role: '部门经理', department: '销售部' },
            { id: 3, username: 'user1', name: '王用户', email: '<EMAIL>', role: '普通用户', department: '市场部' },
            { id: 4, username: 'test', name: '测试员', email: '<EMAIL>', role: '测试用户', department: '技术部' }
        ];
    }

    indexOrders() {
        // 模拟订单数据索引
        return [
            { id: 1, orderId: 'ORD-2024-001', customerName: '张三', status: '已完成', product: '产品A', amount: 2599 },
            { id: 2, orderId: 'ORD-2024-002', customerName: '李四', status: '处理中', product: '产品B', amount: 1299 },
            { id: 3, orderId: 'ORD-2024-003', customerName: '王五', status: '待处理', product: '产品C', amount: 899 }
        ];
    }

    indexFiles() {
        // 模拟文件数据索引
        return [
            { id: 1, filename: '用户手册.pdf', type: 'PDF', size: '2.5MB', uploadedBy: '张管理员', tags: ['文档', '手册'] },
            { id: 2, filename: '系统截图.png', type: 'PNG', size: '1.2MB', uploadedBy: '李经理', tags: ['图片', '截图'] },
            { id: 3, filename: '数据报告.xlsx', type: 'Excel', size: '856KB', uploadedBy: '王用户', tags: ['报告', '数据'] }
        ];
    }

    indexNotifications() {
        // 模拟通知数据索引
        return [
            { id: 1, title: '系统更新', message: '系统将在今晚进行更新维护', type: 'system', sender: '系统' },
            { id: 2, title: '新用户注册', message: '用户张三已成功注册', type: 'user', sender: '系统' },
            { id: 3, title: '订单提醒', message: '订单ORD-2024-001需要处理', type: 'order', sender: '系统' }
        ];
    }

    handleSearchInput(query) {
        this.currentQuery = query.trim();
        
        if (this.currentQuery.length === 0) {
            this.showSearchHistory();
            this.hideSearchSuggestions();
            return;
        }

        if (this.currentQuery.length >= 2) {
            this.showSearchSuggestions();
            this.performInstantSearch();
        }
    }

    performInstantSearch() {
        const results = this.searchInIndex(this.currentQuery);
        this.displaySearchSuggestions(results);
    }

    searchInIndex(query) {
        const results = [];
        const queryLower = query.toLowerCase();

        // 搜索所有索引
        Object.keys(this.searchIndex).forEach(type => {
            const items = this.searchIndex[type];
            const typeResults = items.filter(item => {
                return this.searchableFields[type].some(field => {
                    const value = item[field];
                    if (typeof value === 'string') {
                        return value.toLowerCase().includes(queryLower);
                    }
                    if (Array.isArray(value)) {
                        return value.some(v => v.toLowerCase().includes(queryLower));
                    }
                    return false;
                });
            });

            typeResults.forEach(item => {
                results.push({
                    type: type,
                    item: item,
                    relevance: this.calculateRelevance(item, query, type)
                });
            });
        });

        // 按相关性排序
        return results.sort((a, b) => b.relevance - a.relevance);
    }

    calculateRelevance(item, query, type) {
        let relevance = 0;
        const queryLower = query.toLowerCase();

        this.searchableFields[type].forEach(field => {
            const value = item[field];
            if (typeof value === 'string') {
                const valueLower = value.toLowerCase();
                if (valueLower === queryLower) {
                    relevance += 100; // 完全匹配
                } else if (valueLower.startsWith(queryLower)) {
                    relevance += 50; // 开头匹配
                } else if (valueLower.includes(queryLower)) {
                    relevance += 25; // 包含匹配
                }
            }
        });

        return relevance;
    }

    displaySearchSuggestions(results) {
        const container = document.getElementById('searchSuggestions');
        if (!container) return;

        if (results.length === 0) {
            container.innerHTML = `
                <div class="no-results">
                    <i class="fas fa-search"></i>
                    <p>未找到相关结果</p>
                </div>
            `;
            return;
        }

        const limitedResults = results.slice(0, 8);
        container.innerHTML = limitedResults.map(result => {
            const { type, item } = result;
            return this.renderSearchSuggestion(type, item);
        }).join('');
    }

    renderSearchSuggestion(type, item) {
        const typeConfig = {
            users: { icon: 'fas fa-user', label: '用户', primary: 'name', secondary: 'email' },
            orders: { icon: 'fas fa-shopping-cart', label: '订单', primary: 'orderId', secondary: 'customerName' },
            files: { icon: 'fas fa-file', label: '文件', primary: 'filename', secondary: 'type' },
            notifications: { icon: 'fas fa-bell', label: '通知', primary: 'title', secondary: 'message' }
        };

        const config = typeConfig[type];
        return `
            <div class="search-suggestion" onclick="advancedSearch.selectSuggestion('${type}', ${item.id})">
                <div class="suggestion-icon">
                    <i class="${config.icon}"></i>
                </div>
                <div class="suggestion-content">
                    <div class="suggestion-primary">${item[config.primary]}</div>
                    <div class="suggestion-secondary">${item[config.secondary]}</div>
                </div>
                <div class="suggestion-type">${config.label}</div>
            </div>
        `;
    }

    selectSuggestion(type, id) {
        // 处理建议选择
        this.addToSearchHistory(this.currentQuery);
        this.hideGlobalSearch();
        
        // 跳转到相应页面并高亮项目
        this.navigateToItem(type, id);
    }

    navigateToItem(type, id) {
        // 跳转到相应页面
        if (window.showPage) {
            showPage(type);
        }
        
        // 高亮显示项目
        setTimeout(() => {
            this.highlightItem(type, id);
        }, 500);
    }

    highlightItem(type, id) {
        const selector = `[data-id="${id}"], [data-${type}-id="${id}"]`;
        const element = document.querySelector(selector);
        
        if (element) {
            element.scrollIntoView({ behavior: 'smooth', block: 'center' });
            element.classList.add('search-highlight');
            
            setTimeout(() => {
                element.classList.remove('search-highlight');
            }, 3000);
        }
    }

    showGlobalSearch() {
        const overlay = document.getElementById('globalSearchOverlay');
        if (overlay) {
            overlay.classList.add('show');
            const input = document.getElementById('globalSearchInput');
            if (input) {
                input.focus();
            }
            this.showSearchHistory();
        }
    }

    hideGlobalSearch() {
        const overlay = document.getElementById('globalSearchOverlay');
        if (overlay) {
            overlay.classList.remove('show');
            const input = document.getElementById('globalSearchInput');
            if (input) {
                input.value = '';
            }
            this.hideSearchSuggestions();
        }
    }

    showSearchSuggestions() {
        const container = document.getElementById('searchSuggestions');
        const history = document.getElementById('searchHistory');
        
        if (container) container.style.display = 'block';
        if (history) history.style.display = 'none';
    }

    hideSearchSuggestions() {
        const container = document.getElementById('searchSuggestions');
        if (container) container.style.display = 'none';
    }

    showSearchHistory() {
        const container = document.getElementById('searchHistory');
        const suggestions = document.getElementById('searchSuggestions');
        
        if (suggestions) suggestions.style.display = 'none';
        if (container) {
            container.style.display = 'block';
            this.renderSearchHistory();
        }
    }

    renderSearchHistory() {
        const container = document.getElementById('searchHistory');
        if (!container) return;

        if (this.searchHistory.length === 0) {
            container.innerHTML = `
                <div class="search-history-empty">
                    <p>暂无搜索历史</p>
                </div>
            `;
            return;
        }

        container.innerHTML = `
            <div class="search-history-header">
                <span>最近搜索</span>
                <button onclick="advancedSearch.clearSearchHistory()">清空</button>
            </div>
            <div class="search-history-list">
                ${this.searchHistory.slice(0, 5).map(query => `
                    <div class="search-history-item" onclick="advancedSearch.useHistoryQuery('${query}')">
                        <i class="fas fa-history"></i>
                        <span>${query}</span>
                    </div>
                `).join('')}
            </div>
        `;
    }

    addToSearchHistory(query) {
        if (query && !this.searchHistory.includes(query)) {
            this.searchHistory.unshift(query);
            this.searchHistory = this.searchHistory.slice(0, 10); // 保留最近10条
            this.saveSearchHistory();
        }
    }

    useHistoryQuery(query) {
        const input = document.getElementById('globalSearchInput');
        if (input) {
            input.value = query;
            this.handleSearchInput(query);
        }
    }

    clearSearchHistory() {
        this.searchHistory = [];
        this.saveSearchHistory();
        this.renderSearchHistory();
    }

    applyQuickFilter(type) {
        // 应用快速筛选
        const results = this.searchIndex[type] || [];
        this.displaySearchSuggestions(results.map(item => ({ type, item, relevance: 50 })));
    }

    saveSearchHistory() {
        try {
            localStorage.setItem('searchHistory', JSON.stringify(this.searchHistory));
        } catch (error) {
            console.error('保存搜索历史失败:', error);
        }
    }

    loadSearchHistory() {
        try {
            const stored = localStorage.getItem('searchHistory');
            if (stored) {
                this.searchHistory = JSON.parse(stored);
            }
        } catch (error) {
            console.error('加载搜索历史失败:', error);
            this.searchHistory = [];
        }
    }

    loadSavedSearches() {
        try {
            const stored = localStorage.getItem('savedSearches');
            if (stored) {
                this.savedSearches = JSON.parse(stored);
            }
        } catch (error) {
            console.error('加载保存的搜索失败:', error);
            this.savedSearches = [];
        }
    }

    // 高级搜索相关方法
    showAdvancedSearch() {
        const panel = document.getElementById('advancedSearchPanel');
        if (panel) {
            panel.classList.add('show');
        }
    }

    hideAdvancedSearch() {
        const panel = document.getElementById('advancedSearchPanel');
        if (panel) {
            panel.classList.remove('show');
        }
    }

    executeAdvancedSearch() {
        // 执行高级搜索
        const scope = Array.from(document.querySelectorAll('input[name="scope"]:checked')).map(cb => cb.value);
        const startDate = document.getElementById('startDate').value;
        const endDate = document.getElementById('endDate').value;
        const sortBy = document.getElementById('sortBy').value;
        const limit = parseInt(document.getElementById('resultLimit').value);

        const results = this.performAdvancedSearch({
            scope,
            startDate,
            endDate,
            sortBy,
            limit,
            query: this.currentQuery
        });

        this.displaySearchResults(results);
        this.hideAdvancedSearch();
        this.showSearchResults();
    }

    performAdvancedSearch(params) {
        // 执行高级搜索逻辑
        let results = [];

        params.scope.forEach(type => {
            if (this.searchIndex[type]) {
                const typeResults = this.searchIndex[type].map(item => ({
                    type,
                    item,
                    relevance: params.query ? this.calculateRelevance(item, params.query, type) : 50
                }));
                results = results.concat(typeResults);
            }
        });

        // 应用排序
        results = this.sortResults(results, params.sortBy);

        // 应用限制
        return results.slice(0, params.limit);
    }

    sortResults(results, sortBy) {
        switch (sortBy) {
            case 'date':
                return results.sort((a, b) => new Date(b.item.createdAt || Date.now()) - new Date(a.item.createdAt || Date.now()));
            case 'name':
                return results.sort((a, b) => {
                    const aName = a.item.name || a.item.filename || a.item.title || '';
                    const bName = b.item.name || b.item.filename || b.item.title || '';
                    return aName.localeCompare(bName);
                });
            case 'relevance':
            default:
                return results.sort((a, b) => b.relevance - a.relevance);
        }
    }

    displaySearchResults(results) {
        const container = document.getElementById('searchResultsList');
        const summary = document.getElementById('resultsSummary');
        
        if (summary) {
            summary.innerHTML = `找到 ${results.length} 个结果`;
        }

        if (!container) return;

        if (results.length === 0) {
            container.innerHTML = `
                <div class="no-results">
                    <i class="fas fa-search"></i>
                    <p>未找到匹配的结果</p>
                </div>
            `;
            return;
        }

        container.innerHTML = results.map(result => {
            return this.renderSearchResult(result.type, result.item);
        }).join('');
    }

    renderSearchResult(type, item) {
        const typeConfig = {
            users: { icon: 'fas fa-user', color: '#10b981' },
            orders: { icon: 'fas fa-shopping-cart', color: '#f59e0b' },
            files: { icon: 'fas fa-file', color: '#3b82f6' },
            notifications: { icon: 'fas fa-bell', color: '#6366f1' }
        };

        const config = typeConfig[type];
        return `
            <div class="search-result-item" onclick="advancedSearch.selectResult('${type}', ${item.id})">
                <div class="result-icon" style="background: ${config.color}">
                    <i class="${config.icon}"></i>
                </div>
                <div class="result-content">
                    <div class="result-title">${this.getResultTitle(type, item)}</div>
                    <div class="result-description">${this.getResultDescription(type, item)}</div>
                    <div class="result-meta">${this.getResultMeta(type, item)}</div>
                </div>
                <div class="result-type">${this.getTypeLabel(type)}</div>
            </div>
        `;
    }

    getResultTitle(type, item) {
        switch (type) {
            case 'users': return item.name;
            case 'orders': return item.orderId;
            case 'files': return item.filename;
            case 'notifications': return item.title;
            default: return 'Unknown';
        }
    }

    getResultDescription(type, item) {
        switch (type) {
            case 'users': return `${item.role} - ${item.email}`;
            case 'orders': return `客户: ${item.customerName} - 状态: ${item.status}`;
            case 'files': return `类型: ${item.type} - 大小: ${item.size}`;
            case 'notifications': return item.message;
            default: return '';
        }
    }

    getResultMeta(type, item) {
        switch (type) {
            case 'users': return `部门: ${item.department}`;
            case 'orders': return `金额: ¥${item.amount}`;
            case 'files': return `上传者: ${item.uploadedBy}`;
            case 'notifications': return `发送者: ${item.sender}`;
            default: return '';
        }
    }

    getTypeLabel(type) {
        const labels = {
            users: '用户',
            orders: '订单',
            files: '文件',
            notifications: '通知'
        };
        return labels[type] || type;
    }

    selectResult(type, id) {
        this.hideSearchResults();
        this.navigateToItem(type, id);
    }

    showSearchResults() {
        const panel = document.getElementById('searchResultsPanel');
        if (panel) {
            panel.classList.add('show');
        }
    }

    hideSearchResults() {
        const panel = document.getElementById('searchResultsPanel');
        if (panel) {
            panel.classList.remove('show');
        }
    }

    resetAdvancedSearch() {
        // 重置高级搜索表单
        document.querySelectorAll('input[name="scope"]').forEach(cb => cb.checked = true);
        document.getElementById('startDate').value = '';
        document.getElementById('endDate').value = '';
        document.getElementById('sortBy').value = 'relevance';
        document.getElementById('resultLimit').value = '25';
    }

    exportSearchResults() {
        // 导出搜索结果
        if (this.searchResults.length === 0) {
            if (window.showNotification) {
                showNotification('没有可导出的搜索结果', 'warning');
            }
            return;
        }

        const csv = this.convertResultsToCSV(this.searchResults);
        this.downloadCSV(csv, 'search-results.csv');
    }

    convertResultsToCSV(results) {
        const headers = ['类型', '标题', '描述', '详情'];
        const rows = results.map(result => [
            this.getTypeLabel(result.type),
            this.getResultTitle(result.type, result.item),
            this.getResultDescription(result.type, result.item),
            this.getResultMeta(result.type, result.item)
        ]);

        return [headers, ...rows].map(row => 
            row.map(cell => `"${cell}"`).join(',')
        ).join('\n');
    }

    downloadCSV(csv, filename) {
        const blob = new Blob([csv], { type: 'text/csv;charset=utf-8;' });
        const link = document.createElement('a');
        link.href = URL.createObjectURL(blob);
        link.download = filename;
        link.click();
    }

    saveCurrentSearch() {
        // 保存当前搜索
        const searchName = prompt('请输入搜索名称:');
        if (searchName) {
            const searchConfig = {
                name: searchName,
                query: this.currentQuery,
                scope: Array.from(document.querySelectorAll('input[name="scope"]:checked')).map(cb => cb.value),
                filters: this.searchFilters,
                timestamp: new Date()
            };

            this.savedSearches.push(searchConfig);
            this.saveSavedSearches();

            if (window.showNotification) {
                showNotification('搜索已保存', 'success');
            }
        }
    }

    saveSavedSearches() {
        try {
            localStorage.setItem('savedSearches', JSON.stringify(this.savedSearches));
        } catch (error) {
            console.error('保存搜索配置失败:', error);
        }
    }
}

// 全局高级搜索实例
let advancedSearch = null;

// 初始化高级搜索系统
function initializeAdvancedSearch() {
    advancedSearch = new AdvancedSearchSystem();
    console.log('✅ 高级搜索系统已初始化');
}

// 页面加载时初始化
document.addEventListener('DOMContentLoaded', function() {
    setTimeout(initializeAdvancedSearch, 500);
});
