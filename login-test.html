<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>登录功能测试</title>
    <style>
        body {
            font-family: 'Inter', sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f8fafc;
        }
        .test-container {
            background: white;
            border-radius: 12px;
            padding: 24px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
        }
        .test-title {
            color: #1e293b;
            margin-bottom: 16px;
            font-size: 18px;
            font-weight: 600;
        }
        .test-form {
            display: flex;
            flex-direction: column;
            gap: 16px;
            max-width: 400px;
        }
        .form-group {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }
        .form-group label {
            font-weight: 500;
            color: #374151;
        }
        .form-group input {
            padding: 12px;
            border: 2px solid #e5e7eb;
            border-radius: 8px;
            font-size: 14px;
        }
        .form-group input:focus {
            outline: none;
            border-color: #6366f1;
        }
        .btn {
            background: #6366f1;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 500;
            transition: all 0.2s;
        }
        .btn:hover {
            background: #4f46e5;
        }
        .btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }
        .test-accounts {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 12px;
            margin-top: 16px;
        }
        .account-card {
            border: 2px solid #e5e7eb;
            border-radius: 8px;
            padding: 12px;
            cursor: pointer;
            transition: all 0.2s;
        }
        .account-card:hover {
            border-color: #6366f1;
            background: #f8fafc;
        }
        .account-username {
            font-weight: 600;
            color: #1e293b;
        }
        .account-password {
            font-family: monospace;
            background: #f1f5f9;
            padding: 2px 6px;
            border-radius: 4px;
            font-size: 12px;
            margin: 4px 0;
        }
        .account-role {
            color: #64748b;
            font-size: 12px;
        }
        .status {
            padding: 12px;
            border-radius: 8px;
            margin: 12px 0;
            font-weight: 500;
        }
        .status.success {
            background: #dcfce7;
            color: #166534;
            border: 1px solid #bbf7d0;
        }
        .status.error {
            background: #fef2f2;
            color: #dc2626;
            border: 1px solid #fecaca;
        }
        .status.info {
            background: #eff6ff;
            color: #1d4ed8;
            border: 1px solid #dbeafe;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1 class="test-title">🔐 登录功能测试</h1>
        <p>测试登录功能是否正常工作</p>
        
        <form class="test-form" id="testLoginForm">
            <div class="form-group">
                <label for="testUsername">用户名</label>
                <input type="text" id="testUsername" placeholder="请输入用户名">
            </div>
            <div class="form-group">
                <label for="testPassword">密码</label>
                <input type="password" id="testPassword" placeholder="请输入密码">
            </div>
            <button type="submit" class="btn" id="testLoginBtn">测试登录</button>
        </form>
        
        <div id="testResult"></div>
    </div>

    <div class="test-container">
        <h2 class="test-title">📋 测试账号</h2>
        <p>点击下方账号卡片可自动填入登录表单</p>
        <div class="test-accounts" id="testAccounts">
            <!-- 账号卡片将通过JavaScript生成 -->
        </div>
    </div>

    <div class="test-container">
        <h2 class="test-title">🔗 快速操作</h2>
        <button class="btn" onclick="openLoginPage()">打开登录页面</button>
        <button class="btn" onclick="openDashboard()">打开后台管理</button>
        <button class="btn" onclick="clearLoginData()">清除登录数据</button>
    </div>

    <script>
        // 测试账号数据
        const TEST_ACCOUNTS = [
            { username: 'admin', password: 'admin123', role: '系统管理员', name: '张管理员' },
            { username: 'manager', password: 'manager123', role: '部门经理', name: '李经理' },
            { username: 'user', password: 'user123', role: '普通用户', name: '王用户' },
            { username: 'test', password: 'test123', role: '测试用户', name: '测试员' },
            { username: 'demo', password: 'demo123', role: '演示用户', name: '演示员' }
        ];

        // 显示结果
        function showResult(message, type = 'info') {
            const resultDiv = document.getElementById('testResult');
            resultDiv.innerHTML = `<div class="status ${type}">${message}</div>`;
        }

        // 测试登录功能
        function testLogin(username, password) {
            showResult('正在测试登录...', 'info');
            
            // 验证登录凭据
            const validAccount = TEST_ACCOUNTS.find(account => 
                account.username === username && account.password === password
            );

            setTimeout(() => {
                if (validAccount) {
                    // 保存登录状态
                    localStorage.setItem('isLoggedIn', 'true');
                    localStorage.setItem('currentUser', JSON.stringify({
                        username: validAccount.username,
                        name: validAccount.name,
                        role: validAccount.role
                    }));
                    
                    showResult(`✅ 登录成功！欢迎 ${validAccount.name} (${validAccount.role})`, 'success');
                    
                    setTimeout(() => {
                        if (confirm('登录成功！是否跳转到后台管理页面？')) {
                            window.open('index.html', '_blank');
                        }
                    }, 1000);
                } else {
                    showResult('❌ 登录失败：用户名或密码错误', 'error');
                }
            }, 1000);
        }

        // 生成账号卡片
        function generateAccountCards() {
            const container = document.getElementById('testAccounts');
            container.innerHTML = TEST_ACCOUNTS.map(account => `
                <div class="account-card" onclick="fillAccount('${account.username}', '${account.password}')">
                    <div class="account-username">${account.username}</div>
                    <div class="account-password">${account.password}</div>
                    <div class="account-role">${account.role}</div>
                </div>
            `).join('');
        }

        // 填入账号信息
        function fillAccount(username, password) {
            document.getElementById('testUsername').value = username;
            document.getElementById('testPassword').value = password;
            showResult(`已填入账号: ${username}`, 'info');
        }

        // 打开登录页面
        function openLoginPage() {
            window.open('login.html', '_blank');
        }

        // 打开后台管理
        function openDashboard() {
            window.open('index.html', '_blank');
        }

        // 清除登录数据
        function clearLoginData() {
            localStorage.removeItem('isLoggedIn');
            localStorage.removeItem('currentUser');
            localStorage.removeItem('rememberLogin');
            localStorage.removeItem('savedUsername');
            showResult('已清除所有登录数据', 'info');
        }

        // 页面初始化
        document.addEventListener('DOMContentLoaded', function() {
            generateAccountCards();
            
            // 绑定表单提交事件
            document.getElementById('testLoginForm').addEventListener('submit', function(e) {
                e.preventDefault();
                const username = document.getElementById('testUsername').value.trim();
                const password = document.getElementById('testPassword').value;
                
                if (!username || !password) {
                    showResult('请填写用户名和密码', 'error');
                    return;
                }
                
                testLogin(username, password);
            });
            
            // 检查当前登录状态
            const isLoggedIn = localStorage.getItem('isLoggedIn');
            if (isLoggedIn === 'true') {
                const currentUser = JSON.parse(localStorage.getItem('currentUser') || '{}');
                showResult(`当前已登录: ${currentUser.name || '未知用户'} (${currentUser.role || '未知角色'})`, 'success');
            } else {
                showResult('当前未登录', 'info');
            }
        });
    </script>
</body>
</html>
