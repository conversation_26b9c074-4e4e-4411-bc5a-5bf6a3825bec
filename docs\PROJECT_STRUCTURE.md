# 项目结构说明

## 整理后的项目结构

```
├── index.html                 # 项目入口页面
├── pages/                     # 页面文件
│   ├── login.html            # 登录页面
│   ├── index.html            # 主管理界面
│   ├── ner.html              # NER数据管理页面
│   ├── role-test.html        # 角色测试页面
│   └── test.html             # 测试页面
├── assets/                    # 静态资源
│   └── css/                  # 样式文件
│       ├── styles.css        # 主样式
│       ├── login.css         # 登录页样式
│       └── modern-theme.css  # 现代主题样式
├── js/                       # JavaScript文件
│   ├── core/                 # 核心功能
│   │   ├── app.js           # 主应用逻辑
│   │   └── login.js         # 登录功能
│   ├── modules/             # 功能模块
│   │   ├── user-management.js      # 用户管理
│   │   ├── analytics.js            # 数据分析
│   │   ├── data-visualization.js   # 数据可视化
│   │   ├── order-management.js     # 订单管理
│   │   ├── inventory-management.js # 库存管理
│   │   ├── reports-management.js   # 报表管理
│   │   ├── settings.js             # 系统设置
│   │   ├── notifications.js        # 通知系统
│   │   ├── file-management.js      # 文件管理
│   │   ├── permission-management.js # 权限管理
│   │   └── ner-management.js       # NER数据管理
│   ├── extensions/          # 扩展功能
│   │   ├── enhanced-user-management.js  # 增强用户管理
│   │   ├── enhanced-analytics.js        # 增强分析
│   │   ├── enhanced-order-management.js # 增强订单管理
│   │   ├── user-menu.js                 # 用户菜单
│   │   ├── system-settings.js           # 系统设置
│   │   ├── notification-system.js       # 通知系统
│   │   ├── welcome-banner.js            # 欢迎横幅
│   │   ├── user-account.js              # 用户账户
│   │   ├── role-based-access.js         # 基于角色的访问
│   │   ├── advanced-charts.js           # 高级图表
│   │   ├── realtime-notifications.js    # 实时通知
│   │   ├── advanced-search.js           # 高级搜索
│   │   ├── system-monitor.js            # 系统监控
│   │   ├── data-import-export.js        # 数据导入导出
│   │   ├── workflow-management.js       # 工作流管理
│   │   ├── internationalization.js     # 国际化
│   │   ├── theme-customization.js      # 主题定制
│   │   ├── intelligent-assistant.js    # 智能助手
│   │   ├── api-management.js            # API管理
│   │   ├── backup-recovery.js           # 备份恢复
│   │   ├── performance-analyzer.js     # 性能分析
│   │   ├── plugin-system.js             # 插件系统
│   │   ├── advanced-analytics.js       # 高级分析
│   │   ├── realtime-communication.js   # 实时通信
│   │   ├── security-center.js           # 安全中心
│   │   ├── workflow-engine.js           # 工作流引擎
│   │   └── ai-assistant.js              # AI助手
│   └── utils/               # 工具函数
│       ├── ui-enhancements.js  # UI增强
│       ├── debug.js            # 调试工具
│       └── ner-frontend-only.js # NER前端工具
├── backend/                 # 后端文件
│   ├── api/                # API接口
│   ├── scripts/            # 脚本文件
│   │   └── quick_start.bat # 快速启动脚本
│   ├── *.py               # Python后端文件
│   ├── requirements.txt   # Python依赖
│   └── *.json            # 配置和数据文件
└── docs/                  # 文档
    ├── README.md          # 项目说明
    ├── PROJECT_STRUCTURE.md # 项目结构说明
    └── INTEGRATION_GUIDE.md # 集成指南
```

## 文件分类说明

### 1. 核心文件 (js/core/)
- **app.js**: 主应用程序逻辑，系统初始化
- **login.js**: 登录认证功能

### 2. 功能模块 (js/modules/)
- **用户管理**: user-management.js, permission-management.js
- **数据分析**: analytics.js, data-visualization.js
- **业务管理**: order-management.js, inventory-management.js
- **系统功能**: settings.js, notifications.js, file-management.js
- **报表管理**: reports-management.js
- **NER功能**: ner-management.js

### 3. 扩展功能 (js/extensions/)
- **增强模块**: enhanced-*.js 系列文件
- **用户界面**: user-menu.js, welcome-banner.js, user-account.js
- **系统管理**: system-settings.js, system-monitor.js
- **高级功能**: advanced-*.js 系列文件
- **实时功能**: realtime-*.js 系列文件
- **工作流**: workflow-*.js 系列文件
- **AI功能**: ai-assistant.js, intelligent-assistant.js

### 4. 工具函数 (js/utils/)
- **UI工具**: ui-enhancements.js
- **调试工具**: debug.js
- **NER工具**: ner-frontend-only.js

### 5. 后端服务 (backend/)
- **Python API**: *.py 文件
- **配置文件**: *.json 文件
- **依赖管理**: requirements.txt
- **启动脚本**: scripts/quick_start.bat

## 整理成果

1. **删除了40个无用文件**，包括测试文件、修复文件、临时文件等
2. **创建了专业的文件夹结构**，按功能分类组织
3. **更新了所有文件路径引用**，确保系统正常运行
4. **分离了前后端代码**，便于维护和部署
5. **添加了项目入口页面**，提供清晰的导航

## 使用建议

1. 从根目录的 `index.html` 开始使用系统
2. 核心功能优先加载，扩展功能按需加载
3. 后端服务独立部署，通过API与前端交互
4. 定期清理不需要的扩展功能，保持系统精简
