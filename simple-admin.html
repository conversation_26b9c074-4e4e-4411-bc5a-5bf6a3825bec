<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>简化管理系统</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #f5f5f5;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            text-align: center;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .container {
            max-width: 1200px;
            margin: 20px auto;
            padding: 0 20px;
        }
        
        .nav-tabs {
            display: flex;
            background: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        
        .nav-tab {
            flex: 1;
            padding: 15px 20px;
            background: white;
            border: none;
            cursor: pointer;
            font-size: 16px;
            font-weight: 500;
            color: #666;
            transition: all 0.3s ease;
            border-bottom: 3px solid transparent;
        }
        
        .nav-tab:hover {
            background: #f8f9fa;
            color: #333;
        }
        
        .nav-tab.active {
            background: #667eea;
            color: white;
            border-bottom-color: #764ba2;
        }
        
        .tab-content {
            display: none;
            background: white;
            border-radius: 10px;
            padding: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .tab-content.active {
            display: block;
        }
        
        .card {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            margin: 15px 0;
            border-left: 4px solid #667eea;
        }
        
        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            margin: 5px;
            transition: all 0.3s ease;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.2);
        }
        
        .btn-success { background: linear-gradient(135deg, #28a745 0%, #20c997 100%); }
        .btn-warning { background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%); }
        .btn-danger { background: linear-gradient(135deg, #dc3545 0%, #e83e8c 100%); }
        
        .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        
        .status-card {
            background: white;
            border-radius: 10px;
            padding: 20px;
            text-align: center;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            border-top: 4px solid #667eea;
        }
        
        .status-number {
            font-size: 32px;
            font-weight: bold;
            color: #667eea;
            margin-bottom: 10px;
        }
        
        .status-label {
            color: #666;
            font-size: 14px;
        }
        
        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            background: #28a745;
            color: white;
            padding: 15px 20px;
            border-radius: 6px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.2);
            z-index: 1000;
            transform: translateX(400px);
            transition: transform 0.3s ease;
        }
        
        .notification.show {
            transform: translateX(0);
        }
        
        .user-info {
            background: rgba(255,255,255,0.1);
            padding: 10px 20px;
            border-radius: 20px;
            margin-top: 10px;
            display: inline-block;
        }
        
        .function-test {
            margin: 20px 0;
            padding: 20px;
            background: #e3f2fd;
            border-radius: 8px;
            border-left: 4px solid #2196f3;
        }
        
        .test-result {
            margin: 5px 0;
            padding: 8px 12px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
        }
        
        .test-success { background: #d4edda; color: #155724; }
        .test-error { background: #f8d7da; color: #721c24; }
        .test-warning { background: #fff3cd; color: #856404; }
    </style>
</head>
<body>
    <div class="header">
        <h1><i class="fas fa-tachometer-alt"></i> 简化管理系统</h1>
        <div class="user-info">
            <i class="fas fa-user"></i> <span id="userName">张管理员</span> | 
            <i class="fas fa-shield-alt"></i> <span id="userRole">系统管理员</span>
        </div>
    </div>
    
    <div class="container">
        <div class="nav-tabs">
            <button class="nav-tab active" onclick="showTab('dashboard')">
                <i class="fas fa-chart-pie"></i> 仪表盘
            </button>
            <button class="nav-tab" onclick="showTab('users')">
                <i class="fas fa-users"></i> 用户管理
            </button>
            <button class="nav-tab" onclick="showTab('analytics')">
                <i class="fas fa-chart-line"></i> 数据分析
            </button>
            <button class="nav-tab" onclick="showTab('settings')">
                <i class="fas fa-cog"></i> 系统设置
            </button>
            <button class="nav-tab" onclick="showTab('tools')">
                <i class="fas fa-tools"></i> 修复工具
            </button>
        </div>
        
        <!-- 仪表盘 -->
        <div class="tab-content active" id="dashboard">
            <h2><i class="fas fa-chart-pie"></i> 系统仪表盘</h2>
            
            <div class="status-grid">
                <div class="status-card">
                    <div class="status-number">5</div>
                    <div class="status-label">测试账号</div>
                </div>
                <div class="status-card">
                    <div class="status-number">正常</div>
                    <div class="status-label">系统状态</div>
                </div>
                <div class="status-card">
                    <div class="status-number">100%</div>
                    <div class="status-label">可用性</div>
                </div>
                <div class="status-card">
                    <div class="status-number">本地</div>
                    <div class="status-label">数据存储</div>
                </div>
            </div>
            
            <div class="card">
                <h3><i class="fas fa-info-circle"></i> 系统信息</h3>
                <p><strong>版本：</strong> 简化版 v1.0</p>
                <p><strong>状态：</strong> <span style="color: #28a745;">运行正常</span></p>
                <p><strong>最后更新：</strong> <span id="lastUpdate"></span></p>
                <p><strong>用户：</strong> 已登录为管理员</p>
            </div>
            
            <div class="card">
                <h3><i class="fas fa-rocket"></i> 快速操作</h3>
                <button class="btn" onclick="testFunction('用户管理')">测试用户管理</button>
                <button class="btn" onclick="testFunction('数据分析')">测试数据分析</button>
                <button class="btn" onclick="testFunction('系统设置')">测试系统设置</button>
                <button class="btn btn-success" onclick="showNotification('系统运行正常！', 'success')">测试通知</button>
            </div>
        </div>
        
        <!-- 用户管理 -->
        <div class="tab-content" id="users">
            <h2><i class="fas fa-users"></i> 用户管理</h2>
            
            <div class="card">
                <h3>测试账号列表</h3>
                <div id="usersList">
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin: 20px 0;">
                        <div class="status-card">
                            <strong>admin</strong><br>
                            <small>系统管理员</small><br>
                            <span style="color: #28a745;">●</span> 在线
                        </div>
                        <div class="status-card">
                            <strong>manager</strong><br>
                            <small>部门经理</small><br>
                            <span style="color: #6c757d;">●</span> 离线
                        </div>
                        <div class="status-card">
                            <strong>user</strong><br>
                            <small>普通用户</small><br>
                            <span style="color: #6c757d;">●</span> 离线
                        </div>
                    </div>
                </div>
                
                <button class="btn" onclick="showNotification('用户管理功能正常', 'success')">刷新用户列表</button>
                <button class="btn btn-success" onclick="showNotification('添加用户功能开发中', 'warning')">添加用户</button>
            </div>
        </div>
        
        <!-- 数据分析 -->
        <div class="tab-content" id="analytics">
            <h2><i class="fas fa-chart-line"></i> 数据分析</h2>
            
            <div class="card">
                <h3>系统统计</h3>
                <div class="status-grid">
                    <div class="status-card">
                        <div class="status-number">2,847</div>
                        <div class="status-label">总访问量</div>
                    </div>
                    <div class="status-card">
                        <div class="status-number">156</div>
                        <div class="status-label">今日访问</div>
                    </div>
                    <div class="status-card">
                        <div class="status-number">98.5%</div>
                        <div class="status-label">系统稳定性</div>
                    </div>
                </div>
                
                <button class="btn" onclick="showNotification('数据分析功能正常', 'success')">生成报表</button>
                <button class="btn btn-warning" onclick="showNotification('图表功能开发中', 'warning')">查看图表</button>
            </div>
        </div>
        
        <!-- 系统设置 -->
        <div class="tab-content" id="settings">
            <h2><i class="fas fa-cog"></i> 系统设置</h2>
            
            <div class="card">
                <h3>基本设置</h3>
                <p><strong>系统主题：</strong> 
                    <select onchange="showNotification('主题设置已保存', 'success')">
                        <option>浅色主题</option>
                        <option>深色主题</option>
                    </select>
                </p>
                <p><strong>语言设置：</strong> 
                    <select onchange="showNotification('语言设置已保存', 'success')">
                        <option>中文</option>
                        <option>English</option>
                    </select>
                </p>
                <p><strong>通知设置：</strong> 
                    <label><input type="checkbox" checked onchange="showNotification('通知设置已更新', 'success')"> 启用通知</label>
                </p>
            </div>
            
            <div class="card">
                <h3>账户设置</h3>
                <button class="btn" onclick="showNotification('个人资料功能正常', 'success')">编辑个人资料</button>
                <button class="btn btn-warning" onclick="showNotification('密码修改功能正常', 'success')">修改密码</button>
                <button class="btn btn-danger" onclick="confirmLogout()">退出登录</button>
            </div>
        </div>
        
        <!-- 修复工具 -->
        <div class="tab-content" id="tools">
            <h2><i class="fas fa-tools"></i> 系统修复工具</h2>
            
            <div class="function-test">
                <h3>功能测试结果</h3>
                <div id="testResults"></div>
                <button class="btn" onclick="runSystemTest()">运行系统测试</button>
            </div>
            
            <div class="card">
                <h3>修复选项</h3>
                <button class="btn btn-warning" onclick="clearData()">清除本地数据</button>
                <button class="btn btn-success" onclick="resetLogin()">重置登录状态</button>
                <button class="btn" onclick="window.location.href='ultimate-fix.html'">使用高级修复工具</button>
                <button class="btn" onclick="window.location.href='function-test.html'">详细功能诊断</button>
            </div>
            
            <div class="card">
                <h3>系统访问</h3>
                <button class="btn" onclick="window.location.href='index.html'">尝试完整系统</button>
                <button class="btn btn-success" onclick="window.location.href='login.html'">使用登录页面</button>
                <button class="btn btn-warning" onclick="window.location.reload()">刷新当前页面</button>
            </div>
        </div>
    </div>

    <script>
        // 设置登录状态
        function ensureLoggedIn() {
            if (localStorage.getItem('isLoggedIn') !== 'true') {
                localStorage.setItem('isLoggedIn', 'true');
                localStorage.setItem('currentUser', JSON.stringify({
                    username: 'admin',
                    name: '张管理员',
                    role: '系统管理员',
                    email: '<EMAIL>'
                }));
            }
        }

        // 显示通知
        function showNotification(message, type = 'success') {
            const notification = document.createElement('div');
            notification.className = 'notification';
            notification.innerHTML = `<i class="fas fa-${type === 'success' ? 'check' : type === 'warning' ? 'exclamation' : 'info'}-circle"></i> ${message}`;
            
            if (type === 'warning') notification.style.background = '#ffc107';
            if (type === 'error') notification.style.background = '#dc3545';
            if (type === 'info') notification.style.background = '#17a2b8';
            
            document.body.appendChild(notification);
            
            setTimeout(() => notification.classList.add('show'), 100);
            setTimeout(() => {
                notification.classList.remove('show');
                setTimeout(() => document.body.removeChild(notification), 300);
            }, 3000);
        }

        // 切换标签页
        function showTab(tabName) {
            // 隐藏所有标签内容
            document.querySelectorAll('.tab-content').forEach(tab => {
                tab.classList.remove('active');
            });
            
            // 移除所有标签按钮的活动状态
            document.querySelectorAll('.nav-tab').forEach(btn => {
                btn.classList.remove('active');
            });
            
            // 显示选中的标签内容
            document.getElementById(tabName).classList.add('active');
            
            // 激活对应的标签按钮
            event.target.classList.add('active');
            
            showNotification(`已切换到${event.target.textContent.trim()}`, 'info');
        }

        // 测试功能
        function testFunction(functionName) {
            showNotification(`${functionName}功能测试通过`, 'success');
        }

        // 运行系统测试
        function runSystemTest() {
            const results = document.getElementById('testResults');
            results.innerHTML = '';
            
            const tests = [
                { name: 'LocalStorage', test: () => {
                    localStorage.setItem('test', 'ok');
                    const result = localStorage.getItem('test') === 'ok';
                    localStorage.removeItem('test');
                    return result;
                }},
                { name: 'DOM操作', test: () => {
                    const div = document.createElement('div');
                    return div !== null;
                }},
                { name: '事件处理', test: () => {
                    let fired = false;
                    const btn = document.createElement('button');
                    btn.addEventListener('click', () => fired = true);
                    btn.click();
                    return fired;
                }},
                { name: '用户状态', test: () => {
                    return localStorage.getItem('isLoggedIn') === 'true';
                }}
            ];
            
            tests.forEach(test => {
                try {
                    const result = test.test();
                    const div = document.createElement('div');
                    div.className = result ? 'test-result test-success' : 'test-result test-error';
                    div.textContent = `${result ? '✓' : '✗'} ${test.name}: ${result ? '通过' : '失败'}`;
                    results.appendChild(div);
                } catch (error) {
                    const div = document.createElement('div');
                    div.className = 'test-result test-error';
                    div.textContent = `✗ ${test.name}: 错误 - ${error.message}`;
                    results.appendChild(div);
                }
            });
            
            showNotification('系统测试完成', 'success');
        }

        // 清除数据
        function clearData() {
            if (confirm('确定要清除所有本地数据吗？')) {
                localStorage.clear();
                sessionStorage.clear();
                showNotification('本地数据已清除', 'success');
                setTimeout(() => location.reload(), 1000);
            }
        }

        // 重置登录状态
        function resetLogin() {
            ensureLoggedIn();
            showNotification('登录状态已重置', 'success');
        }

        // 确认退出
        function confirmLogout() {
            if (confirm('确定要退出登录吗？')) {
                localStorage.removeItem('isLoggedIn');
                localStorage.removeItem('currentUser');
                showNotification('已退出登录', 'info');
                setTimeout(() => {
                    window.location.href = 'login.html';
                }, 1000);
            }
        }

        // 页面加载时初始化
        window.onload = function() {
            ensureLoggedIn();
            
            // 更新时间
            document.getElementById('lastUpdate').textContent = new Date().toLocaleString();
            
            // 显示欢迎消息
            setTimeout(() => {
                showNotification('简化管理系统已启动', 'success');
            }, 500);
        };
    </script>
</body>
</html>