// 智能助手系统
class IntelligentAssistant {
    constructor() {
        this.isActive = false;
        this.conversationHistory = [];
        this.knowledgeBase = {};
        this.shortcuts = {};
        this.suggestions = [];
        this.currentContext = null;
        this.voiceEnabled = false;
        this.speechRecognition = null;
        this.speechSynthesis = null;
        
        this.initializeAssistant();
        this.loadKnowledgeBase();
        this.setupVoiceSupport();
    }

    initializeAssistant() {
        this.createAssistantInterface();
        this.bindAssistantEvents();
        this.loadConversationHistory();
        this.setupShortcuts();
        this.startContextAnalysis();
    }

    createAssistantInterface() {
        // 创建助手浮动按钮
        const assistantButton = document.createElement('div');
        assistantButton.id = 'assistantButton';
        assistantButton.className = 'assistant-button';
        assistantButton.innerHTML = `
            <div class="assistant-avatar">
                <i class="fas fa-robot"></i>
            </div>
            <div class="assistant-pulse"></div>
        `;
        assistantButton.onclick = () => this.toggleAssistant();
        document.body.appendChild(assistantButton);

        // 创建助手面板
        const assistantPanel = document.createElement('div');
        assistantPanel.id = 'assistantPanel';
        assistantPanel.className = 'assistant-panel';
        assistantPanel.innerHTML = `
            <div class="assistant-header">
                <div class="assistant-info">
                    <div class="assistant-avatar-large">
                        <i class="fas fa-robot"></i>
                    </div>
                    <div class="assistant-details">
                        <h3>智能助手</h3>
                        <span class="assistant-status" id="assistantStatus">在线</span>
                    </div>
                </div>
                <div class="assistant-controls">
                    <button class="control-btn" onclick="intelligentAssistant.toggleVoice()" title="语音助手">
                        <i class="fas fa-microphone" id="voiceIcon"></i>
                    </button>
                    <button class="control-btn" onclick="intelligentAssistant.clearHistory()" title="清空对话">
                        <i class="fas fa-trash"></i>
                    </button>
                    <button class="control-btn" onclick="intelligentAssistant.toggleAssistant()" title="关闭">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            </div>
            
            <div class="assistant-body">
                <div class="conversation-area" id="conversationArea">
                    <div class="welcome-message">
                        <div class="message assistant-message">
                            <div class="message-avatar">
                                <i class="fas fa-robot"></i>
                            </div>
                            <div class="message-content">
                                <p>您好！我是您的智能助手，可以帮助您：</p>
                                <ul>
                                    <li>🔍 快速查找功能和数据</li>
                                    <li>📊 生成报表和分析</li>
                                    <li>⚡ 执行常用操作</li>
                                    <li>💡 提供使用建议</li>
                                    <li>🎯 智能导航和引导</li>
                                </ul>
                                <p>试试问我："如何添加新用户？"或"显示今日订单统计"</p>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="suggestions-area" id="suggestionsArea">
                    <div class="suggestions-title">💡 智能建议</div>
                    <div class="suggestions-list" id="suggestionsList">
                        <!-- 智能建议将在这里显示 -->
                    </div>
                </div>
                
                <div class="input-area">
                    <div class="input-container">
                        <input type="text" id="assistantInput" placeholder="输入您的问题或指令..." 
                               onkeypress="if(event.key==='Enter') intelligentAssistant.sendMessage()">
                        <button class="send-btn" onclick="intelligentAssistant.sendMessage()">
                            <i class="fas fa-paper-plane"></i>
                        </button>
                        <button class="voice-btn" onclick="intelligentAssistant.startVoiceInput()" 
                                id="voiceInputBtn" title="语音输入">
                            <i class="fas fa-microphone"></i>
                        </button>
                    </div>
                    <div class="quick-actions" id="quickActions">
                        <button class="quick-btn" onclick="intelligentAssistant.executeQuickAction('help')">
                            <i class="fas fa-question-circle"></i>
                            帮助
                        </button>
                        <button class="quick-btn" onclick="intelligentAssistant.executeQuickAction('stats')">
                            <i class="fas fa-chart-bar"></i>
                            统计
                        </button>
                        <button class="quick-btn" onclick="intelligentAssistant.executeQuickAction('search')">
                            <i class="fas fa-search"></i>
                            搜索
                        </button>
                        <button class="quick-btn" onclick="intelligentAssistant.executeQuickAction('export')">
                            <i class="fas fa-download"></i>
                            导出
                        </button>
                    </div>
                </div>
            </div>
        `;
        
        document.body.appendChild(assistantPanel);
    }

    loadKnowledgeBase() {
        this.knowledgeBase = {
            // 功能导航
            navigation: {
                '用户管理': { page: 'users', description: '管理系统用户，添加、编辑、删除用户信息' },
                '订单管理': { page: 'orders', description: '查看和处理订单，更新订单状态' },
                '数据分析': { page: 'analytics', description: '查看各种数据统计和分析报表' },
                'NER数据': { page: 'ner', description: '管理命名实体识别训练数据' },
                '文件管理': { page: 'files', description: '上传、下载和管理文件' },
                '工作流': { action: 'showWorkflowPanel', description: '创建和管理业务流程' },
                '系统监控': { action: 'showSystemMonitor', description: '查看系统性能和状态' },
                '通知中心': { action: 'showNotificationPanel', description: '查看系统通知和消息' }
            },
            
            // 常用操作
            actions: {
                '添加用户': { 
                    action: 'addUser', 
                    description: '创建新的系统用户',
                    steps: ['点击用户管理', '点击添加用户按钮', '填写用户信息', '保存']
                },
                '导出数据': { 
                    action: 'exportData', 
                    description: '导出系统数据',
                    steps: ['选择要导出的数据类型', '点击导出按钮', '选择格式', '下载文件']
                },
                '生成报表': { 
                    action: 'generateReport', 
                    description: '生成数据分析报表',
                    steps: ['进入数据分析页面', '选择报表类型', '设置时间范围', '生成报表']
                },
                '创建工作流': { 
                    action: 'createWorkflow', 
                    description: '设计新的业务流程',
                    steps: ['打开工作流管理', '点击创建工作流', '拖拽设计流程', '保存发布']
                }
            },
            
            // 快捷键
            shortcuts: {
                'Ctrl+K': '全局搜索',
                'Ctrl+N': '新建项目',
                'Ctrl+S': '保存当前内容',
                'Ctrl+E': '导出数据',
                'Ctrl+H': '显示帮助',
                'Ctrl+/': '显示快捷键列表'
            },
            
            // 常见问题
            faq: {
                '如何重置密码': '联系管理员或在登录页面点击"忘记密码"',
                '如何备份数据': '进入系统设置，选择数据备份功能',
                '如何添加新用户': '进入用户管理页面，点击"添加用户"按钮',
                '如何查看系统日志': '进入系统监控页面，选择日志查看',
                '如何导出报表': '在数据分析页面选择报表类型，点击导出按钮'
            }
        };
    }

    setupVoiceSupport() {
        // 检查浏览器语音支持
        if ('webkitSpeechRecognition' in window || 'SpeechRecognition' in window) {
            const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
            this.speechRecognition = new SpeechRecognition();
            this.speechRecognition.continuous = false;
            this.speechRecognition.interimResults = false;
            this.speechRecognition.lang = 'zh-CN';
            
            this.speechRecognition.onresult = (event) => {
                const transcript = event.results[0][0].transcript;
                document.getElementById('assistantInput').value = transcript;
                this.sendMessage();
            };
            
            this.speechRecognition.onerror = (event) => {
                console.error('语音识别错误:', event.error);
                this.showMessage('语音识别失败，请重试', 'assistant');
            };
        }
        
        // 语音合成
        if ('speechSynthesis' in window) {
            this.speechSynthesis = window.speechSynthesis;
        }
    }

    bindAssistantEvents() {
        // 监听页面变化
        document.addEventListener('click', (e) => {
            this.analyzeUserAction(e);
        });
        
        // 监听输入焦点
        document.addEventListener('focusin', (e) => {
            if (e.target.tagName === 'INPUT' || e.target.tagName === 'TEXTAREA') {
                this.updateContext('input', e.target);
            }
        });
        
        // 监听页面切换
        const observer = new MutationObserver((mutations) => {
            mutations.forEach((mutation) => {
                if (mutation.type === 'attributes' && mutation.attributeName === 'class') {
                    this.updatePageContext();
                }
            });
        });
        
        observer.observe(document.body, { attributes: true, subtree: true });
    }

    setupShortcuts() {
        this.shortcuts = {
            'ctrl+h': () => this.showHelp(),
            'ctrl+/': () => this.showShortcuts(),
            'ctrl+alt+a': () => this.toggleAssistant(),
            'ctrl+alt+v': () => this.startVoiceInput()
        };
        
        document.addEventListener('keydown', (e) => {
            const key = [];
            if (e.ctrlKey) key.push('ctrl');
            if (e.altKey) key.push('alt');
            if (e.shiftKey) key.push('shift');
            key.push(e.key.toLowerCase());
            
            const shortcut = key.join('+');
            if (this.shortcuts[shortcut]) {
                e.preventDefault();
                this.shortcuts[shortcut]();
            }
        });
    }

    startContextAnalysis() {
        // 定期分析用户行为和上下文
        setInterval(() => {
            this.analyzeContext();
            this.generateSuggestions();
        }, 10000); // 每10秒分析一次
    }

    toggleAssistant() {
        const panel = document.getElementById('assistantPanel');
        const button = document.getElementById('assistantButton');
        
        if (this.isActive) {
            panel.classList.remove('show');
            button.classList.remove('active');
            this.isActive = false;
        } else {
            panel.classList.add('show');
            button.classList.add('active');
            this.isActive = true;
            this.focusInput();
            this.generateSuggestions();
        }
    }

    focusInput() {
        setTimeout(() => {
            const input = document.getElementById('assistantInput');
            if (input) input.focus();
        }, 300);
    }

    sendMessage() {
        const input = document.getElementById('assistantInput');
        const message = input.value.trim();
        
        if (!message) return;
        
        // 显示用户消息
        this.showMessage(message, 'user');
        
        // 清空输入
        input.value = '';
        
        // 处理消息
        this.processMessage(message);
        
        // 保存到历史记录
        this.conversationHistory.push({
            type: 'user',
            message: message,
            timestamp: new Date()
        });
        
        this.saveConversationHistory();
    }

    showMessage(message, type, actions = null) {
        const conversationArea = document.getElementById('conversationArea');
        const messageDiv = document.createElement('div');
        messageDiv.className = `message ${type}-message`;
        
        const avatar = type === 'assistant' ? 
            '<div class="message-avatar"><i class="fas fa-robot"></i></div>' :
            '<div class="message-avatar"><i class="fas fa-user"></i></div>';
        
        let actionsHtml = '';
        if (actions && actions.length > 0) {
            actionsHtml = '<div class="message-actions">';
            actions.forEach(action => {
                actionsHtml += `<button class="action-btn" onclick="${action.callback}">${action.text}</button>`;
            });
            actionsHtml += '</div>';
        }
        
        messageDiv.innerHTML = `
            ${avatar}
            <div class="message-content">
                <div class="message-text">${message}</div>
                ${actionsHtml}
                <div class="message-time">${new Date().toLocaleTimeString()}</div>
            </div>
        `;
        
        conversationArea.appendChild(messageDiv);
        conversationArea.scrollTop = conversationArea.scrollHeight;
        
        // 语音播报（如果启用）
        if (type === 'assistant' && this.voiceEnabled) {
            this.speak(message);
        }
    }

    processMessage(message) {
        const lowerMessage = message.toLowerCase();
        
        // 显示思考状态
        this.showTypingIndicator();
        
        setTimeout(() => {
            this.hideTypingIndicator();
            
            // 意图识别和响应
            if (this.isGreeting(lowerMessage)) {
                this.handleGreeting();
            } else if (this.isNavigationRequest(lowerMessage)) {
                this.handleNavigation(lowerMessage);
            } else if (this.isActionRequest(lowerMessage)) {
                this.handleAction(lowerMessage);
            } else if (this.isQuestionRequest(lowerMessage)) {
                this.handleQuestion(lowerMessage);
            } else if (this.isStatsRequest(lowerMessage)) {
                this.handleStatsRequest(lowerMessage);
            } else {
                this.handleUnknownRequest(message);
            }
        }, 1000);
    }

    isGreeting(message) {
        const greetings = ['你好', 'hello', 'hi', '您好', '早上好', '下午好', '晚上好'];
        return greetings.some(greeting => message.includes(greeting));
    }

    isNavigationRequest(message) {
        const navKeywords = ['打开', '进入', '跳转', '去', '查看', '显示'];
        return navKeywords.some(keyword => message.includes(keyword));
    }

    isActionRequest(message) {
        const actionKeywords = ['添加', '创建', '新建', '删除', '导出', '生成', '执行'];
        return actionKeywords.some(keyword => message.includes(keyword));
    }

    isQuestionRequest(message) {
        const questionKeywords = ['如何', '怎么', '什么', '为什么', '帮助', '?', '？'];
        return questionKeywords.some(keyword => message.includes(keyword));
    }

    isStatsRequest(message) {
        const statsKeywords = ['统计', '数据', '报表', '分析', '图表', '今日', '本月', '总计'];
        return statsKeywords.some(keyword => message.includes(keyword));
    }

    handleGreeting() {
        const greetings = [
            '您好！很高兴为您服务！有什么可以帮助您的吗？',
            '您好！我是您的智能助手，随时为您提供帮助！',
            '您好！欢迎使用智能助手，我可以帮您快速完成各种操作！'
        ];
        
        const greeting = greetings[Math.floor(Math.random() * greetings.length)];
        this.showMessage(greeting, 'assistant');
    }

    handleNavigation(message) {
        let targetPage = null;
        let targetAction = null;
        
        // 查找匹配的页面或功能
        for (const [key, value] of Object.entries(this.knowledgeBase.navigation)) {
            if (message.includes(key.toLowerCase()) || message.includes(key)) {
                if (value.page) {
                    targetPage = value.page;
                } else if (value.action) {
                    targetAction = value.action;
                }
                break;
            }
        }
        
        if (targetPage) {
            const actions = [{
                text: '立即前往',
                callback: `intelligentAssistant.navigateToPage('${targetPage}')`
            }];
            
            this.showMessage(`我来帮您打开${targetPage}页面`, 'assistant', actions);
        } else if (targetAction) {
            const actions = [{
                text: '立即执行',
                callback: `intelligentAssistant.executeAction('${targetAction}')`
            }];
            
            this.showMessage(`我来帮您执行这个操作`, 'assistant', actions);
        } else {
            this.showMessage('抱歉，我没有找到您要访问的页面。您可以说"打开用户管理"或"显示数据分析"等。', 'assistant');
        }
    }

    handleAction(message) {
        let matchedAction = null;
        
        // 查找匹配的操作
        for (const [key, value] of Object.entries(this.knowledgeBase.actions)) {
            if (message.includes(key.toLowerCase()) || message.includes(key)) {
                matchedAction = { name: key, ...value };
                break;
            }
        }
        
        if (matchedAction) {
            let response = `我来帮您${matchedAction.name}。\n\n操作步骤：\n`;
            matchedAction.steps.forEach((step, index) => {
                response += `${index + 1}. ${step}\n`;
            });
            
            const actions = [{
                text: '开始操作',
                callback: `intelligentAssistant.executeAction('${matchedAction.action}')`
            }];
            
            this.showMessage(response, 'assistant', actions);
        } else {
            this.showMessage('我理解您想要执行某个操作，但需要更具体的描述。比如"添加新用户"或"导出订单数据"。', 'assistant');
        }
    }

    handleQuestion(message) {
        let answer = null;
        
        // 查找FAQ中的答案
        for (const [question, ans] of Object.entries(this.knowledgeBase.faq)) {
            if (message.includes(question.toLowerCase()) || 
                question.toLowerCase().includes(message.replace(/[如何怎么什么为什么？?]/g, ''))) {
                answer = ans;
                break;
            }
        }
        
        if (answer) {
            this.showMessage(answer, 'assistant');
        } else if (message.includes('快捷键')) {
            this.showShortcuts();
        } else if (message.includes('帮助')) {
            this.showHelp();
        } else {
            const suggestions = [
                '您可以问我：',
                '• 如何添加新用户？',
                '• 如何导出数据？',
                '• 如何查看系统日志？',
                '• 快捷键有哪些？',
                '• 如何重置密码？'
            ];
            
            this.showMessage(suggestions.join('\n'), 'assistant');
        }
    }

    handleStatsRequest(message) {
        // 模拟获取统计数据
        const stats = {
            users: Math.floor(Math.random() * 1000) + 100,
            orders: Math.floor(Math.random() * 500) + 50,
            revenue: (Math.random() * 100000 + 10000).toFixed(2),
            growth: (Math.random() * 20 + 5).toFixed(1)
        };
        
        let response = '';
        
        if (message.includes('用户')) {
            response = `📊 用户统计：\n当前总用户数：${stats.users}\n今日新增：${Math.floor(Math.random() * 20) + 1}`;
        } else if (message.includes('订单')) {
            response = `📊 订单统计：\n总订单数：${stats.orders}\n今日订单：${Math.floor(Math.random() * 10) + 1}`;
        } else if (message.includes('收入') || message.includes('营收')) {
            response = `💰 收入统计：\n总收入：¥${stats.revenue}\n增长率：${stats.growth}%`;
        } else {
            response = `📊 系统概览：\n• 用户总数：${stats.users}\n• 订单总数：${stats.orders}\n• 总收入：¥${stats.revenue}\n• 增长率：${stats.growth}%`;
        }
        
        const actions = [{
            text: '查看详细报表',
            callback: `intelligentAssistant.navigateToPage('analytics')`
        }];
        
        this.showMessage(response, 'assistant', actions);
    }

    handleUnknownRequest(message) {
        const responses = [
            '抱歉，我没有完全理解您的意思。您可以尝试：\n• 说"帮助"查看我能做什么\n• 使用更具体的描述\n• 问我关于系统功能的问题',
            '我正在学习中，可能没有理解您的需求。您可以：\n• 问我"如何使用某个功能"\n• 说"打开某个页面"\n• 询问系统操作步骤',
            '让我为您提供一些建议：\n• 尝试问"显示用户统计"\n• 说"如何添加用户"\n• 询问"系统有哪些功能"'
        ];
        
        const response = responses[Math.floor(Math.random() * responses.length)];
        this.showMessage(response, 'assistant');
    }

    showTypingIndicator() {
        const conversationArea = document.getElementById('conversationArea');
        const typingDiv = document.createElement('div');
        typingDiv.id = 'typingIndicator';
        typingDiv.className = 'message assistant-message typing';
        typingDiv.innerHTML = `
            <div class="message-avatar">
                <i class="fas fa-robot"></i>
            </div>
            <div class="message-content">
                <div class="typing-dots">
                    <span></span>
                    <span></span>
                    <span></span>
                </div>
            </div>
        `;
        
        conversationArea.appendChild(typingDiv);
        conversationArea.scrollTop = conversationArea.scrollHeight;
    }

    hideTypingIndicator() {
        const typingIndicator = document.getElementById('typingIndicator');
        if (typingIndicator) {
            typingIndicator.remove();
        }
    }

    showHelp() {
        const helpMessage = `
🤖 智能助手使用指南

📋 我可以帮您：
• 快速导航到各个功能页面
• 执行常用操作和任务
• 查询系统数据和统计
• 回答使用问题
• 提供操作指导

💬 对话示例：
• "打开用户管理"
• "如何添加新用户"
• "显示今日订单统计"
• "导出用户数据"
• "创建新工作流"

⌨️ 快捷键：
• Ctrl+Alt+A：打开/关闭助手
• Ctrl+Alt+V：语音输入
• Ctrl+H：显示帮助
• Ctrl+/：显示快捷键

🎤 语音功能：
点击麦克风图标开始语音输入，支持中文语音识别。
        `;
        
        this.showMessage(helpMessage, 'assistant');
    }

    showShortcuts() {
        let shortcutMessage = '⌨️ 系统快捷键：\n\n';
        
        for (const [key, description] of Object.entries(this.knowledgeBase.shortcuts)) {
            shortcutMessage += `• ${key}：${description}\n`;
        }
        
        this.showMessage(shortcutMessage, 'assistant');
    }

    navigateToPage(page) {
        // 切换到指定页面
        const pageElement = document.querySelector(`[data-page="${page}"]`);
        if (pageElement) {
            pageElement.click();
            this.showMessage(`已为您打开${page}页面`, 'assistant');
        } else {
            this.showMessage('抱歉，无法找到指定页面', 'assistant');
        }
    }

    executeAction(action) {
        // 执行指定操作
        try {
            if (typeof window[action] === 'function') {
                window[action]();
                this.showMessage('操作已执行', 'assistant');
            } else {
                this.showMessage('抱歉，无法执行该操作', 'assistant');
            }
        } catch (error) {
            this.showMessage('操作执行失败：' + error.message, 'assistant');
        }
    }

    executeQuickAction(action) {
        switch (action) {
            case 'help':
                this.showHelp();
                break;
            case 'stats':
                this.handleStatsRequest('显示系统统计');
                break;
            case 'search':
                if (window.showAdvancedSearch) {
                    window.showAdvancedSearch();
                    this.showMessage('已为您打开高级搜索', 'assistant');
                }
                break;
            case 'export':
                if (window.showImportExportModal) {
                    window.showImportExportModal('export');
                    this.showMessage('已为您打开数据导出功能', 'assistant');
                }
                break;
        }
    }

    analyzeContext() {
        // 分析当前页面和用户行为
        const currentPage = document.querySelector('.page.active')?.id || 'dashboard';
        const activeElements = document.querySelectorAll(':focus, :hover');
        
        this.currentContext = {
            page: currentPage,
            timestamp: new Date(),
            activeElements: activeElements.length
        };
    }

    generateSuggestions() {
        const suggestions = [];
        const currentPage = document.querySelector('.page.active')?.id || 'dashboard';
        
        // 基于当前页面生成建议
        switch (currentPage) {
            case 'users':
                suggestions.push(
                    { text: '添加新用户', action: 'addUser' },
                    { text: '导出用户数据', action: 'exportUsers' },
                    { text: '查看用户统计', action: 'showUserStats' }
                );
                break;
            case 'orders':
                suggestions.push(
                    { text: '查看今日订单', action: 'showTodayOrders' },
                    { text: '导出订单数据', action: 'exportOrders' },
                    { text: '订单状态统计', action: 'showOrderStats' }
                );
                break;
            case 'analytics':
                suggestions.push(
                    { text: '生成月度报表', action: 'generateMonthlyReport' },
                    { text: '查看趋势分析', action: 'showTrendAnalysis' },
                    { text: '导出分析数据', action: 'exportAnalytics' }
                );
                break;
            default:
                suggestions.push(
                    { text: '查看系统概览', action: 'showOverview' },
                    { text: '打开用户管理', action: 'openUsers' },
                    { text: '查看通知中心', action: 'showNotifications' }
                );
        }
        
        this.updateSuggestions(suggestions);
    }

    updateSuggestions(suggestions) {
        const suggestionsList = document.getElementById('suggestionsList');
        if (!suggestionsList) return;
        
        suggestionsList.innerHTML = suggestions.map(suggestion => `
            <button class="suggestion-item" onclick="intelligentAssistant.executeSuggestion('${suggestion.action}')">
                <i class="fas fa-lightbulb"></i>
                <span>${suggestion.text}</span>
            </button>
        `).join('');
    }

    executeSuggestion(action) {
        // 执行建议的操作
        this.executeAction(action);
    }

    analyzeUserAction(event) {
        // 分析用户点击行为，用于改进建议
        const target = event.target;
        const action = {
            element: target.tagName,
            className: target.className,
            text: target.textContent?.substring(0, 50),
            timestamp: new Date()
        };
        
        // 可以用于机器学习和行为分析
        console.log('用户行为分析:', action);
    }

    updateContext(type, element) {
        this.currentContext = {
            ...this.currentContext,
            type: type,
            element: element.tagName,
            placeholder: element.placeholder
        };
    }

    updatePageContext() {
        const activePage = document.querySelector('.page.active');
        if (activePage) {
            this.currentContext = {
                ...this.currentContext,
                page: activePage.id
            };
            this.generateSuggestions();
        }
    }

    toggleVoice() {
        this.voiceEnabled = !this.voiceEnabled;
        const voiceIcon = document.getElementById('voiceIcon');
        
        if (this.voiceEnabled) {
            voiceIcon.className = 'fas fa-microphone-slash';
            this.showMessage('语音助手已启用', 'assistant');
        } else {
            voiceIcon.className = 'fas fa-microphone';
            this.showMessage('语音助手已禁用', 'assistant');
        }
    }

    startVoiceInput() {
        if (!this.speechRecognition) {
            this.showMessage('您的浏览器不支持语音识别功能', 'assistant');
            return;
        }
        
        const voiceBtn = document.getElementById('voiceInputBtn');
        voiceBtn.classList.add('listening');
        
        this.speechRecognition.start();
        
        this.speechRecognition.onend = () => {
            voiceBtn.classList.remove('listening');
        };
    }

    speak(text) {
        if (!this.speechSynthesis) return;
        
        const utterance = new SpeechSynthesisUtterance(text);
        utterance.lang = 'zh-CN';
        utterance.rate = 0.9;
        utterance.pitch = 1;
        
        this.speechSynthesis.speak(utterance);
    }

    clearHistory() {
        if (confirm('确定要清空对话历史吗？')) {
            this.conversationHistory = [];
            const conversationArea = document.getElementById('conversationArea');
            conversationArea.innerHTML = `
                <div class="welcome-message">
                    <div class="message assistant-message">
                        <div class="message-avatar">
                            <i class="fas fa-robot"></i>
                        </div>
                        <div class="message-content">
                            <p>对话历史已清空。有什么可以帮助您的吗？</p>
                        </div>
                    </div>
                </div>
            `;
            this.saveConversationHistory();
        }
    }

    saveConversationHistory() {
        try {
            localStorage.setItem('assistantHistory', JSON.stringify(this.conversationHistory));
        } catch (error) {
            console.error('保存对话历史失败:', error);
        }
    }

    loadConversationHistory() {
        try {
            const stored = localStorage.getItem('assistantHistory');
            if (stored) {
                this.conversationHistory = JSON.parse(stored);
            }
        } catch (error) {
            console.error('加载对话历史失败:', error);
            this.conversationHistory = [];
        }
    }
}

// 全局智能助手实例
let intelligentAssistant = null;

// 初始化智能助手
function initializeIntelligentAssistant() {
    intelligentAssistant = new IntelligentAssistant();
    console.log('✅ 智能助手系统已初始化');
}

// 显示智能助手
function showIntelligentAssistant() {
    if (intelligentAssistant) {
        intelligentAssistant.toggleAssistant();
    }
}

// 页面加载时初始化
document.addEventListener('DOMContentLoaded', function() {
    setTimeout(initializeIntelligentAssistant, 1100);
});
