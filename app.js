// 现代企业管理系统 JavaScript

document.addEventListener('DOMContentLoaded', function() {
    // 检查登录状态
    if (!checkLoginStatus()) {
        return; // 如果未登录，直接返回，不初始化应用
    }

    // 初始化应用
    initializeApp();
});

// 检查登录状态
function checkLoginStatus() {
    const isLoggedIn = localStorage.getItem('isLoggedIn');
    const currentUser = localStorage.getItem('currentUser');

    if (isLoggedIn !== 'true' || !currentUser) {
        // 未登录，跳转到登录页面
        showNotification('请先登录系统', 'warning');
        setTimeout(() => {
            window.location.href = 'login.html';
        }, 2000);
        return false;
    }

    // 更新用户信息显示
    updateUserInfo(JSON.parse(currentUser));
    return true;
}

// 更新用户信息显示
function updateUserInfo(userInfo) {
    // 更新侧边栏用户信息
    const sidebarUserName = document.querySelector('.sidebar-footer .user-name');
    const sidebarUserRole = document.querySelector('.sidebar-footer .user-role');

    if (sidebarUserName) {
        sidebarUserName.textContent = userInfo.name || '未知用户';
    }
    if (sidebarUserRole) {
        sidebarUserRole.textContent = userInfo.role || '未知角色';
    }

    // 更新顶部用户信息
    const headerUserName = document.querySelector('.header-right .user-name');
    if (headerUserName) {
        headerUserName.textContent = userInfo.name || '未知用户';
    }

    // 添加用户菜单功能
    initializeUserMenu(userInfo);
}

// 初始化用户菜单
function initializeUserMenu(userInfo) {
    const userProfile = document.querySelector('.user-profile');
    const userMenuBtn = document.querySelector('.user-menu-btn');

    if (userProfile) {
        userProfile.addEventListener('click', function() {
            showUserMenu(userInfo);
        });
    }

    if (userMenuBtn) {
        userMenuBtn.addEventListener('click', function(e) {
            e.stopPropagation();
            showUserMenu(userInfo);
        });
    }
}

// 显示用户菜单
function showUserMenu(userInfo) {
    // 移除现有菜单
    const existingMenu = document.querySelector('.user-dropdown-menu');
    if (existingMenu) {
        existingMenu.remove();
        return;
    }

    // 创建用户菜单
    const menu = document.createElement('div');
    menu.className = 'user-dropdown-menu';
    menu.innerHTML = `
        <div class="user-menu-header">
            <div class="user-avatar">
                <img src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=40&h=40&fit=crop&crop=face" alt="用户头像">
            </div>
            <div class="user-details">
                <div class="user-name">${userInfo.name}</div>
                <div class="user-role">${userInfo.role}</div>
            </div>
        </div>
        <div class="user-menu-divider"></div>
        <div class="user-menu-items">
            <a href="#" class="user-menu-item" data-action="profile">
                <i class="fas fa-user"></i>
                <span>个人资料</span>
            </a>
            <a href="#" class="user-menu-item" data-action="password">
                <i class="fas fa-key"></i>
                <span>修改密码</span>
            </a>
            <a href="#" class="user-menu-item" data-action="loginHistory">
                <i class="fas fa-history"></i>
                <span>登录历史</span>
            </a>
            <a href="#" class="user-menu-item" data-action="settings">
                <i class="fas fa-cog"></i>
                <span>账户设置</span>
            </a>
            <a href="#" class="user-menu-item" data-action="help">
                <i class="fas fa-question-circle"></i>
                <span>帮助中心</span>
            </a>
            <div class="user-menu-divider"></div>
            <a href="#" class="user-menu-item logout" data-action="logout">
                <i class="fas fa-sign-out-alt"></i>
                <span>退出登录</span>
            </a>
        </div>
    `;

    // 添加样式
    menu.style.cssText = `
        position: fixed;
        top: 70px;
        right: 20px;
        background: white;
        border-radius: 12px;
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
        border: 1px solid #e2e8f0;
        min-width: 250px;
        z-index: 10000;
        animation: slideDown 0.2s ease-out;
    `;

    // 添加到页面
    document.body.appendChild(menu);

    // 添加菜单项点击事件
    const menuItems = menu.querySelectorAll('.user-menu-item');
    menuItems.forEach(item => {
        item.addEventListener('click', function(e) {
            e.preventDefault();
            const action = this.getAttribute('data-action');
            handleUserMenuAction(action);
            menu.remove();
        });
    });

    // 点击外部关闭菜单
    setTimeout(() => {
        document.addEventListener('click', function closeMenu(e) {
            if (!menu.contains(e.target)) {
                menu.remove();
                document.removeEventListener('click', closeMenu);
            }
        });
    }, 100);
}

// 处理用户菜单操作
function handleUserMenuAction(action) {
    switch (action) {
        case 'profile':
            showProfileModal();
            break;
        case 'password':
            showPasswordModal();
            break;
        case 'loginHistory':
            showLoginHistoryModal();
            break;
        case 'settings':
            showNotification('账户设置功能开发中...', 'info');
            break;
        case 'help':
            // 跳转到帮助页面
            document.querySelector('.nav-item[data-page="help"]').click();
            break;
        case 'logout':
            handleLogout();
            break;
    }
}

// 处理退出登录
function handleLogout() {
    if (confirm('确定要退出登录吗？')) {
        // 清除登录状态
        localStorage.removeItem('isLoggedIn');
        localStorage.removeItem('currentUser');

        showNotification('已成功退出登录', 'success');

        setTimeout(() => {
            window.location.href = 'login.html';
        }, 1500);
    }
}

function initializeApp() {
    // 初始化侧边栏
    initializeSidebar();

    // 初始化导航
    initializeNavigation();

    // 初始化移动端菜单
    initializeMobileMenu();

    // 初始化图表
    initializeCharts();

    // 初始化交互功能
    initializeInteractions();

    // 初始化通知中心
    if (typeof initializeNotifications === 'function') {
        initializeNotifications();
    }

    // 启动实时通知模拟
    if (typeof simulateRealTimeNotifications === 'function') {
        simulateRealTimeNotifications();
    }

    // 初始化新功能管理器
    setTimeout(() => {
        if (document.getElementById('profileModalOverlay')) {
            profileManager = new ProfileManager();
        }
        if (document.getElementById('passwordModalOverlay')) {
            passwordManager = new PasswordManager();
        }
        if (document.getElementById('loginHistoryModalOverlay')) {
            loginHistoryManager = new LoginHistoryManager();
        }
    }, 200);

    console.log('现代企业管理系统已初始化');
}

// 侧边栏功能
function initializeSidebar() {
    const sidebar = document.getElementById('sidebar');
    const sidebarToggle = document.getElementById('sidebarToggle');
    
    if (sidebarToggle) {
        sidebarToggle.addEventListener('click', function() {
            sidebar.classList.toggle('collapsed');
            
            // 保存状态到本地存储
            const isCollapsed = sidebar.classList.contains('collapsed');
            localStorage.setItem('sidebarCollapsed', isCollapsed);
        });
    }
    
    // 恢复侧边栏状态
    const savedState = localStorage.getItem('sidebarCollapsed');
    if (savedState === 'true') {
        sidebar.classList.add('collapsed');
    }
}

// 导航功能
function initializeNavigation() {
    const navItems = document.querySelectorAll('.nav-item');
    const pages = document.querySelectorAll('.page');
    
    navItems.forEach(item => {
        item.addEventListener('click', function(e) {
            e.preventDefault();
            
            const targetPage = this.getAttribute('data-page');
            
            // 更新导航状态
            navItems.forEach(nav => nav.classList.remove('active'));
            this.classList.add('active');
            
            // 显示对应页面
            pages.forEach(page => page.classList.remove('active'));
            const targetPageElement = document.getElementById(targetPage);
            if (targetPageElement) {
                targetPageElement.classList.add('active');
            }

            // 初始化页面特定功能
            initializePageFeatures(targetPage);

            // 更新面包屑
            updateBreadcrumb(targetPage);

            // 在移动端关闭侧边栏
            if (window.innerWidth <= 768) {
                closeMobileSidebar();
            }
        });
    });
}

// 移动端菜单
function initializeMobileMenu() {
    const mobileMenuBtn = document.getElementById('mobileMenuBtn');
    const sidebar = document.getElementById('sidebar');
    const mobileOverlay = document.getElementById('mobileOverlay');
    
    if (mobileMenuBtn) {
        mobileMenuBtn.addEventListener('click', function() {
            sidebar.classList.add('mobile-open');
            mobileOverlay.classList.add('active');
        });
    }
    
    if (mobileOverlay) {
        mobileOverlay.addEventListener('click', function() {
            closeMobileSidebar();
        });
    }
}

function closeMobileSidebar() {
    const sidebar = document.getElementById('sidebar');
    const mobileOverlay = document.getElementById('mobileOverlay');
    
    sidebar.classList.remove('mobile-open');
    mobileOverlay.classList.remove('active');
}

// 初始化页面特定功能
function initializePageFeatures(pageName) {
    switch (pageName) {
        case 'notifications':
            if (typeof initializeNotifications === 'function') {
                initializeNotifications();
            }
            break;
        case 'users':
            if (typeof initializeUserManagement === 'function') {
                initializeUserManagement();
            }
            break;
        case 'analytics':
            if (typeof initializeAnalytics === 'function') {
                initializeAnalytics();
            }
            break;
        case 'orders':
            if (typeof initializeOrderManagement === 'function') {
                initializeOrderManagement();
            }
            break;
        case 'inventory':
            if (typeof initializeInventoryManagement === 'function') {
                initializeInventoryManagement();
            }
            break;
        case 'reports':
            if (typeof initializeReportsManagement === 'function') {
                initializeReportsManagement();
            }
            break;
        case 'files':
            if (typeof initializeFileManagement === 'function') {
                initializeFileManagement();
            }
            break;
        case 'permissions':
            if (typeof initializePermissionManagement === 'function') {
                initializePermissionManagement();
            }
            break;
        case 'settings':
            if (typeof initializeSettings === 'function') {
                initializeSettings();
            }
            break;
        case 'help':
            if (typeof initializeHelpCenter === 'function') {
                initializeHelpCenter();
            }
            break;
    }
}

// 更新面包屑
function updateBreadcrumb(pageName) {
    const breadcrumbCurrent = document.querySelector('.breadcrumb-item.current');
    if (breadcrumbCurrent) {
        const pageNames = {
            'dashboard': '仪表盘',
            'users': '用户管理',
            'analytics': '数据分析',
            'orders': '订单管理',
            'inventory': '库存管理',
            'reports': '报表中心',
            'files': '文件管理',
            'permissions': '权限管理',
            'notifications': '通知中心',
            'settings': '系统设置',
            'help': '帮助中心'
        };

        breadcrumbCurrent.textContent = pageNames[pageName] || '未知页面';
    }
}

// 图表初始化
function initializeCharts() {
    // 模拟图表数据
    const chartData = {
        users: [65, 59, 80, 81, 56, 55, 40],
        revenue: [28, 48, 40, 19, 86, 27, 90],
        orders: [12, 19, 3, 5, 2, 3, 9],
        pending: [2, 3, 1, 4, 2, 1, 3]
    };
    
    // 创建迷你图表
    createMiniCharts(chartData);
    
    // 如果有Canvas元素，创建主图表
    const salesChart = document.getElementById('salesChart');
    if (salesChart) {
        createSalesChart(salesChart);
    }
}

// 创建迷你图表
function createMiniCharts(data) {
    const miniCharts = document.querySelectorAll('.mini-chart');
    
    miniCharts.forEach(chart => {
        const chartType = chart.getAttribute('data-chart');
        const chartData = data[chartType];
        
        if (chartData) {
            createSparkline(chart, chartData);
        }
    });
}

// 创建简单的折线图
function createSparkline(container, data) {
    const width = container.offsetWidth;
    const height = container.offsetHeight;
    const max = Math.max(...data);
    const min = Math.min(...data);
    const range = max - min || 1;
    
    let path = '';
    data.forEach((value, index) => {
        const x = (index / (data.length - 1)) * width;
        const y = height - ((value - min) / range) * height;
        
        if (index === 0) {
            path += `M ${x} ${y}`;
        } else {
            path += ` L ${x} ${y}`;
        }
    });
    
    const svg = `
        <svg width="${width}" height="${height}" style="position: absolute; top: 0; left: 0;">
            <path d="${path}" stroke="currentColor" stroke-width="2" fill="none" opacity="0.7"/>
        </svg>
    `;
    
    container.innerHTML = svg;
}

// 创建销售图表（模拟）
function createSalesChart(canvas) {
    const ctx = canvas.getContext('2d');
    const width = canvas.width = canvas.offsetWidth;
    const height = canvas.height = canvas.offsetHeight;
    
    // 模拟数据
    const data = [
        { label: '1月', value: 12000 },
        { label: '2月', value: 19000 },
        { label: '3月', value: 15000 },
        { label: '4月', value: 25000 },
        { label: '5月', value: 22000 },
        { label: '6月', value: 30000 },
        { label: '7月', value: 28000 }
    ];
    
    // 简单的柱状图
    const barWidth = width / data.length * 0.6;
    const maxValue = Math.max(...data.map(d => d.value));
    
    ctx.fillStyle = '#6366f1';
    
    data.forEach((item, index) => {
        const barHeight = (item.value / maxValue) * (height - 60);
        const x = (index + 0.2) * (width / data.length);
        const y = height - barHeight - 30;
        
        ctx.fillRect(x, y, barWidth, barHeight);
        
        // 标签
        ctx.fillStyle = '#64748b';
        ctx.font = '12px Inter';
        ctx.textAlign = 'center';
        ctx.fillText(item.label, x + barWidth / 2, height - 10);
        
        ctx.fillStyle = '#6366f1';
    });
}

// 交互功能
function initializeInteractions() {
    // 统计卡片悬停效果
    const statCards = document.querySelectorAll('.stat-card');
    statCards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-4px)';
        });
        
        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
        });
    });
    
    // 表格行点击效果
    const tableRows = document.querySelectorAll('.modern-table tbody tr');
    tableRows.forEach(row => {
        row.addEventListener('click', function() {
            // 移除其他行的选中状态
            tableRows.forEach(r => r.classList.remove('selected'));
            // 添加当前行的选中状态
            this.classList.add('selected');
        });
    });
    
    // 快速操作按钮
    const quickActionBtns = document.querySelectorAll('.quick-action-btn');
    quickActionBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            const action = this.querySelector('span').textContent;
            showNotification(`执行操作: ${action}`, 'info');
        });
    });
    
    // 操作按钮
    const actionBtns = document.querySelectorAll('.btn-icon');
    actionBtns.forEach(btn => {
        btn.addEventListener('click', function(e) {
            e.stopPropagation();
            const title = this.getAttribute('title');
            showNotification(`执行操作: ${title}`, 'success');
        });
    });
    
    // 时间筛选器
    const timeFilters = document.querySelectorAll('.time-filter');
    timeFilters.forEach(filter => {
        filter.addEventListener('change', function() {
            const value = this.value;
            showNotification(`时间范围已更改为: ${this.options[this.selectedIndex].text}`, 'info');
            // 这里可以添加实际的数据更新逻辑
        });
    });
}

// 通知系统
function showNotification(message, type = 'info') {
    // 创建通知元素
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.innerHTML = `
        <div class="notification-content">
            <i class="fas fa-${getNotificationIcon(type)}"></i>
            <span>${message}</span>
        </div>
        <button class="notification-close">
            <i class="fas fa-times"></i>
        </button>
    `;
    
    // 添加样式
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: white;
        border-radius: 12px;
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
        border-left: 4px solid ${getNotificationColor(type)};
        padding: 16px;
        z-index: 10000;
        transform: translateX(100%);
        transition: transform 0.3s ease;
        max-width: 400px;
    `;
    
    // 添加到页面
    document.body.appendChild(notification);
    
    // 显示动画
    setTimeout(() => {
        notification.style.transform = 'translateX(0)';
    }, 100);
    
    // 关闭按钮
    const closeBtn = notification.querySelector('.notification-close');
    closeBtn.addEventListener('click', () => {
        removeNotification(notification);
    });
    
    // 自动关闭
    setTimeout(() => {
        removeNotification(notification);
    }, 5000);
}

function removeNotification(notification) {
    notification.style.transform = 'translateX(100%)';
    setTimeout(() => {
        if (notification.parentNode) {
            notification.parentNode.removeChild(notification);
        }
    }, 300);
}

function getNotificationIcon(type) {
    const icons = {
        'success': 'check-circle',
        'error': 'exclamation-circle',
        'warning': 'exclamation-triangle',
        'info': 'info-circle'
    };
    return icons[type] || 'info-circle';
}

function getNotificationColor(type) {
    const colors = {
        'success': '#10b981',
        'error': '#ef4444',
        'warning': '#f59e0b',
        'info': '#3b82f6'
    };
    return colors[type] || '#3b82f6';
}

// 窗口大小改变时的处理
window.addEventListener('resize', function() {
    // 在移动端自动关闭侧边栏
    if (window.innerWidth > 768) {
        closeMobileSidebar();
    }
    
    // 重新初始化图表
    setTimeout(() => {
        initializeCharts();
    }, 100);
});

// 键盘快捷键
document.addEventListener('keydown', function(e) {
    // Ctrl/Cmd + B 切换侧边栏
    if ((e.ctrlKey || e.metaKey) && e.key === 'b') {
        e.preventDefault();
        const sidebar = document.getElementById('sidebar');
        sidebar.classList.toggle('collapsed');
    }
    
    // ESC 关闭移动端侧边栏
    if (e.key === 'Escape') {
        closeMobileSidebar();
    }
});

// ===== 个人资料管理功能 =====

// ProfileManager 类
class ProfileManager {
    constructor() {
        this.currentUser = null;
        this.profileModal = null;
        this.avatarInput = null;
        this.currentTab = 'basic';
        this.originalData = null;
        
        this.init();
    }
    
    init() {
        this.profileModal = document.getElementById('profileModalOverlay');
        this.avatarInput = document.getElementById('avatarInput');
        
        // 绑定事件
        this.bindEvents();
    }
    
    bindEvents() {
        // 标签页切换
        const tabs = document.querySelectorAll('.profile-tab');
        tabs.forEach(tab => {
            tab.addEventListener('click', (e) => {
                this.switchTab(e.target.dataset.tab);
            });
        });
        
        // 头像上传
        const avatarPreview = document.querySelector('.avatar-preview');
        if (avatarPreview) {
            avatarPreview.addEventListener('click', () => {
                this.avatarInput.click();
            });
        }
        
        if (this.avatarInput) {
            this.avatarInput.addEventListener('change', (e) => {
                this.handleAvatarUpload(e);
            });
        }
        
        // 实时验证
        const form = document.getElementById('profileForm');
        if (form) {
            form.addEventListener('input', (e) => {
                this.validateField(e.target);
            });
        }
    }
    
    // 显示个人资料模态框
    showModal() {
        this.loadUserProfile();
        this.profileModal.classList.add('active');
        document.body.style.overflow = 'hidden';
    }
    
    // 关闭个人资料模态框
    closeModal() {
        this.profileModal.classList.remove('active');
        document.body.style.overflow = '';
        this.resetForm();
    }
    
    // 切换标签页
    switchTab(tabName) {
        // 更新标签按钮状态
        document.querySelectorAll('.profile-tab').forEach(tab => {
            tab.classList.remove('active');
        });
        document.querySelector(`[data-tab="${tabName}"]`).classList.add('active');
        
        // 更新内容区域
        document.querySelectorAll('.profile-tab-content').forEach(content => {
            content.classList.remove('active');
        });
        document.getElementById(`${tabName}Tab`).classList.add('active');
        
        this.currentTab = tabName;
    }
    
    // 加载用户资料
    loadUserProfile() {
        const currentUser = localStorage.getItem('currentUser');
        if (currentUser) {
            try {
                this.currentUser = JSON.parse(currentUser);
                this.originalData = { ...this.currentUser };
                this.populateForm();
            } catch (error) {
                console.error('加载用户资料失败:', error);
                showNotification('加载用户资料失败', 'error');
            }
        }
    }
    
    // 填充表单数据
    populateForm() {
        if (!this.currentUser) return;
        
        // 基本信息
        document.getElementById('profileDisplayName').value = this.currentUser.name || '';
        document.getElementById('profileUsername').value = this.currentUser.username || '';
        document.getElementById('profileBio').value = this.currentUser.bio || '';
        
        // 联系方式
        document.getElementById('profileEmail').value = this.currentUser.email || '';
        document.getElementById('profilePhone').value = this.currentUser.phone || '';
        document.getElementById('profileAddress').value = this.currentUser.address || '';
        
        // 工作信息
        document.getElementById('profileDepartment').value = this.currentUser.department || '';
        document.getElementById('profileRole').value = this.currentUser.role || '';
        document.getElementById('profileJoinDate').value = this.currentUser.joinDate || '';
        document.getElementById('profileEmployeeId').value = this.currentUser.employeeId || '';
        
        // 头像
        if (this.currentUser.avatar) {
            document.getElementById('avatarPreview').src = this.currentUser.avatar;
        }
    }
    
    // 处理头像上传
    handleAvatarUpload(event) {
        const file = event.target.files[0];
        if (!file) return;
        
        // 验证文件类型
        const allowedTypes = ['image/jpeg', 'image/png', 'image/gif'];
        if (!allowedTypes.includes(file.type)) {
            showNotification('请选择 JPG、PNG 或 GIF 格式的图片', 'error');
            return;
        }
        
        // 验证文件大小 (2MB)
        if (file.size > 2 * 1024 * 1024) {
            showNotification('图片大小不能超过 2MB', 'error');
            return;
        }
        
        // 读取文件并预览
        const reader = new FileReader();
        reader.onload = (e) => {
            document.getElementById('avatarPreview').src = e.target.result;
            showNotification('头像已更新，请保存更改', 'success');
        };
        reader.readAsDataURL(file);
    }
    
    // 验证单个字段
    validateField(field) {
        const fieldName = field.name;
        const value = field.value.trim();
        const errorElement = document.getElementById(`${fieldName}Error`);
        const formGroup = field.closest('.form-group');
        
        let isValid = true;
        let errorMessage = '';
        
        switch (fieldName) {
            case 'name':
                if (!value) {
                    isValid = false;
                    errorMessage = '显示名称不能为空';
                } else if (value.length < 2) {
                    isValid = false;
                    errorMessage = '显示名称至少需要2个字符';
                }
                break;
                
            case 'email':
                if (!value) {
                    isValid = false;
                    errorMessage = '邮箱地址不能为空';
                } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value)) {
                    isValid = false;
                    errorMessage = '请输入有效的邮箱地址';
                }
                break;
                
            case 'phone':
                if (value && !/^1[3-9]\d{9}$/.test(value)) {
                    isValid = false;
                    errorMessage = '请输入有效的手机号码';
                }
                break;
        }
        
        // 更新UI状态
        if (errorElement) {
            if (isValid) {
                errorElement.classList.remove('show');
                formGroup.classList.remove('error');
            } else {
                errorElement.textContent = errorMessage;
                errorElement.classList.add('show');
                formGroup.classList.add('error');
            }
        }
        
        return isValid;
    }
    
    // 验证整个表单
    validateForm() {
        const form = document.getElementById('profileForm');
        const inputs = form.querySelectorAll('input[required], input[name="phone"]');
        let isValid = true;
        
        inputs.forEach(input => {
            if (!this.validateField(input)) {
                isValid = false;
            }
        });
        
        return isValid;
    }
    
    // 收集表单数据
    collectFormData() {
        return {
            name: document.getElementById('profileDisplayName').value.trim(),
            username: document.getElementById('profileUsername').value.trim(),
            bio: document.getElementById('profileBio').value.trim(),
            email: document.getElementById('profileEmail').value.trim(),
            phone: document.getElementById('profilePhone').value.trim(),
            address: document.getElementById('profileAddress').value.trim(),
            department: document.getElementById('profileDepartment').value,
            role: document.getElementById('profileRole').value.trim(),
            joinDate: document.getElementById('profileJoinDate').value,
            employeeId: document.getElementById('profileEmployeeId').value.trim(),
            avatar: document.getElementById('avatarPreview').src,
            lastModified: new Date().toISOString()
        };
    }
    
    // 保存个人资料
    async saveProfile() {
        if (!this.validateForm()) {
            showNotification('请检查并修正表单中的错误', 'error');
            return;
        }
        
        try {
            const formData = this.collectFormData();
            
            // 合并数据
            const updatedUser = { ...this.currentUser, ...formData };
            
            // 保存到localStorage
            localStorage.setItem('currentUser', JSON.stringify(updatedUser));
            
            // 更新页面显示的用户信息
            this.updateUserDisplay(updatedUser);
            
            // 记录操作日志
            this.logProfileUpdate(formData);
            
            showNotification('个人资料保存成功', 'success');
            this.closeModal();
            
        } catch (error) {
            console.error('保存个人资料失败:', error);
            showNotification('保存失败，请重试', 'error');
        }
    }
    
    // 更新页面用户信息显示
    updateUserDisplay(userInfo) {
        // 更新侧边栏用户信息
        const sidebarUserName = document.querySelector('.sidebar-footer .user-name');
        const sidebarUserRole = document.querySelector('.sidebar-footer .user-role');
        
        if (sidebarUserName) {
            sidebarUserName.textContent = userInfo.name || '未知用户';
        }
        if (sidebarUserRole) {
            sidebarUserRole.textContent = userInfo.role || '未知角色';
        }
        
        // 更新顶部用户信息
        const headerUserName = document.querySelector('.header-right .user-name');
        if (headerUserName) {
            headerUserName.textContent = userInfo.name || '未知用户';
        }
        
        // 更新头像
        const avatars = document.querySelectorAll('.user-avatar img');
        avatars.forEach(avatar => {
            if (userInfo.avatar) {
                avatar.src = userInfo.avatar;
            }
        });
    }
    
    // 记录操作日志
    logProfileUpdate(formData) {
        const operationLog = {
            id: Date.now().toString(),
            userId: this.currentUser.username,
            action: 'profile_update',
            target: 'user_profile',
            details: {
                changedFields: this.getChangedFields(formData),
                timestamp: new Date().toISOString()
            },
            timestamp: new Date().toISOString(),
            ip: 'localhost', // 模拟IP
            userAgent: navigator.userAgent
        };
        
        // 保存到操作日志
        const logs = JSON.parse(localStorage.getItem('operationLogs') || '[]');
        logs.push(operationLog);
        localStorage.setItem('operationLogs', JSON.stringify(logs));
    }
    
    // 获取变更的字段
    getChangedFields(newData) {
        const changes = {};
        for (const key in newData) {
            if (this.originalData[key] !== newData[key]) {
                changes[key] = {
                    from: this.originalData[key],
                    to: newData[key]
                };
            }
        }
        return changes;
    }
    
    // 重置表单
    resetForm() {
        const form = document.getElementById('profileForm');
        if (form) {
            form.reset();
        }
        
        // 清除错误状态
        document.querySelectorAll('.field-error').forEach(error => {
            error.classList.remove('show');
        });
        document.querySelectorAll('.form-group.error').forEach(group => {
            group.classList.remove('error');
        });
        
        // 重置到第一个标签页
        this.switchTab('basic');
    }
}

// 创建全局ProfileManager实例
let profileManager;

// 全局函数
function showProfileModal() {
    if (!profileManager) {
        profileManager = new ProfileManager();
    }
    profileManager.showModal();
}

function closeProfileModal() {
    if (profileManager) {
        profileManager.closeModal();
    }
}

function saveProfile() {
    if (profileManager) {
        profileManager.saveProfile();
    }
}

// ProfileManager初始化将在主DOMContentLoaded中处理

// ===== 密码修改功能 =====

// PasswordManager 类
class PasswordManager {
    constructor() {
        this.passwordModal = null;
        this.currentUser = null;
        this.strengthMeter = null;
        
        this.init();
    }
    
    init() {
        this.passwordModal = document.getElementById('passwordModalOverlay');
        this.strengthMeter = {
            fill: document.getElementById('strengthFill'),
            text: document.getElementById('strengthText')
        };
        
        this.bindEvents();
    }
    
    bindEvents() {
        // 密码强度检查
        const newPasswordInput = document.getElementById('newPassword');
        if (newPasswordInput) {
            newPasswordInput.addEventListener('input', (e) => {
                this.checkPasswordStrength(e.target.value);
                this.validatePasswordMatch();
                this.updateSubmitButton();
            });
        }
        
        // 确认密码验证
        const confirmPasswordInput = document.getElementById('confirmPassword');
        if (confirmPasswordInput) {
            confirmPasswordInput.addEventListener('input', () => {
                this.validatePasswordMatch();
                this.updateSubmitButton();
            });
        }
        
        // 当前密码验证
        const currentPasswordInput = document.getElementById('currentPassword');
        if (currentPasswordInput) {
            currentPasswordInput.addEventListener('blur', (e) => {
                this.validateCurrentPassword(e.target.value);
            });
        }
        
        // 表单提交
        const passwordForm = document.getElementById('passwordForm');
        if (passwordForm) {
            passwordForm.addEventListener('submit', (e) => {
                e.preventDefault();
                this.updatePassword();
            });
        }
    }
    
    // 显示密码修改模态框
    showModal() {
        this.loadCurrentUser();
        this.resetForm();
        this.passwordModal.classList.add('active');
        document.body.style.overflow = 'hidden';
    }
    
    // 关闭密码修改模态框
    closeModal() {
        this.passwordModal.classList.remove('active');
        document.body.style.overflow = '';
        this.resetForm();
    }
    
    // 加载当前用户信息
    loadCurrentUser() {
        const currentUser = localStorage.getItem('currentUser');
        if (currentUser) {
            try {
                this.currentUser = JSON.parse(currentUser);
            } catch (error) {
                console.error('加载用户信息失败:', error);
            }
        }
    }
    
    // 检查密码强度
    checkPasswordStrength(password) {
        const requirements = {
            length: password.length >= 8,
            uppercase: /[A-Z]/.test(password),
            lowercase: /[a-z]/.test(password),
            number: /\d/.test(password),
            special: /[!@#$%^&*(),.?":{}|<>]/.test(password)
        };
        
        // 更新要求列表显示
        this.updateRequirementsList(requirements);
        
        // 计算强度分数
        const score = Object.values(requirements).filter(Boolean).length;
        let strength = 'weak';
        let strengthText = '弱';
        
        if (score >= 5) {
            strength = 'strong';
            strengthText = '强';
        } else if (score >= 4) {
            strength = 'good';
            strengthText = '良好';
        } else if (score >= 3) {
            strength = 'fair';
            strengthText = '一般';
        }
        
        // 更新强度指示器
        if (this.strengthMeter.fill && this.strengthMeter.text) {
            this.strengthMeter.fill.className = `strength-fill ${strength}`;
            this.strengthMeter.text.className = `strength-text ${strength}`;
            this.strengthMeter.text.textContent = `密码强度：${strengthText}`;
        }
        
        return { score, strength, requirements };
    }
    
    // 更新密码要求列表
    updateRequirementsList(requirements) {
        const reqElements = {
            length: document.getElementById('req-length'),
            uppercase: document.getElementById('req-uppercase'),
            lowercase: document.getElementById('req-lowercase'),
            number: document.getElementById('req-number'),
            special: document.getElementById('req-special')
        };
        
        for (const [key, element] of Object.entries(reqElements)) {
            if (element) {
                if (requirements[key]) {
                    element.classList.add('valid');
                } else {
                    element.classList.remove('valid');
                }
            }
        }
    }
    
    // 验证当前密码
    validateCurrentPassword(password) {
        const errorElement = document.getElementById('currentPasswordError');
        const formGroup = document.getElementById('currentPassword').closest('.form-group');
        
        // 这里应该与服务器验证，现在模拟验证
        const isValid = this.currentUser && password === this.getCurrentUserPassword();
        
        if (!password) {
            this.showFieldError(errorElement, formGroup, '请输入当前密码');
            return false;
        } else if (!isValid) {
            this.showFieldError(errorElement, formGroup, '当前密码不正确');
            return false;
        } else {
            this.hideFieldError(errorElement, formGroup);
            return true;
        }
    }
    
    // 获取当前用户密码（模拟）
    getCurrentUserPassword() {
        // 在实际应用中，这应该通过安全的API验证
        // 这里使用测试账号的密码进行模拟
        const testPasswords = {
            'admin': 'admin123',
            'manager': 'manager123',
            'user': 'user123',
            'test': 'test123',
            'demo': 'demo123'
        };
        
        return testPasswords[this.currentUser?.username] || '';
    }
    
    // 验证密码匹配
    validatePasswordMatch() {
        const newPassword = document.getElementById('newPassword').value;
        const confirmPassword = document.getElementById('confirmPassword').value;
        const errorElement = document.getElementById('confirmPasswordError');
        const formGroup = document.getElementById('confirmPassword').closest('.form-group');
        
        if (!confirmPassword) {
            this.hideFieldError(errorElement, formGroup);
            return false;
        }
        
        if (newPassword !== confirmPassword) {
            this.showFieldError(errorElement, formGroup, '两次输入的密码不一致');
            return false;
        } else {
            this.hideFieldError(errorElement, formGroup);
            return true;
        }
    }
    
    // 显示字段错误
    showFieldError(errorElement, formGroup, message) {
        if (errorElement) {
            errorElement.textContent = message;
            errorElement.classList.add('show');
        }
        if (formGroup) {
            formGroup.classList.add('error');
        }
    }
    
    // 隐藏字段错误
    hideFieldError(errorElement, formGroup) {
        if (errorElement) {
            errorElement.classList.remove('show');
        }
        if (formGroup) {
            formGroup.classList.remove('error');
        }
    }
    
    // 更新提交按钮状态
    updateSubmitButton() {
        const submitBtn = document.getElementById('updatePasswordBtn');
        const currentPassword = document.getElementById('currentPassword').value;
        const newPassword = document.getElementById('newPassword').value;
        const confirmPassword = document.getElementById('confirmPassword').value;
        
        // 检查所有必填字段和验证
        const isCurrentPasswordValid = this.validateCurrentPassword(currentPassword);
        const isNewPasswordStrong = this.checkPasswordStrength(newPassword).score >= 3;
        const isPasswordMatch = this.validatePasswordMatch();
        
        const isFormValid = isCurrentPasswordValid && isNewPasswordStrong && isPasswordMatch && 
                           currentPassword && newPassword && confirmPassword;
        
        if (submitBtn) {
            submitBtn.disabled = !isFormValid;
        }
    }
    
    // 更新密码
    async updatePassword() {
        const currentPassword = document.getElementById('currentPassword').value;
        const newPassword = document.getElementById('newPassword').value;
        const confirmPassword = document.getElementById('confirmPassword').value;
        const logoutAllDevices = document.getElementById('logoutAllDevices').checked;
        
        // 最终验证
        if (!this.validateCurrentPassword(currentPassword)) {
            showNotification('当前密码验证失败', 'error');
            return;
        }
        
        if (!this.validatePasswordMatch()) {
            showNotification('密码确认失败', 'error');
            return;
        }
        
        const strengthCheck = this.checkPasswordStrength(newPassword);
        if (strengthCheck.score < 3) {
            showNotification('新密码强度不足，请选择更强的密码', 'error');
            return;
        }
        
        try {
            // 模拟密码更新过程
            await this.performPasswordUpdate(newPassword, logoutAllDevices);
            
            // 记录安全日志
            this.logPasswordChange(logoutAllDevices);
            
            showNotification('密码修改成功', 'success');
            this.closeModal();
            
            // 如果选择注销所有设备，则跳转到登录页面
            if (logoutAllDevices) {
                setTimeout(() => {
                    this.logoutAllSessions();
                }, 1500);
            }
            
        } catch (error) {
            console.error('密码更新失败:', error);
            showNotification('密码更新失败，请重试', 'error');
        }
    }
    
    // 执行密码更新
    async performPasswordUpdate(newPassword, logoutAllDevices) {
        return new Promise((resolve) => {
            // 模拟API调用延迟
            setTimeout(() => {
                // 在实际应用中，这里应该调用安全的API来更新密码
                // 现在只是模拟更新过程
                console.log('密码已更新（模拟）');
                resolve();
            }, 1000);
        });
    }
    
    // 记录密码修改日志
    logPasswordChange(logoutAllDevices) {
        const operationLog = {
            id: Date.now().toString(),
            userId: this.currentUser?.username || 'unknown',
            action: 'password_change',
            target: 'user_security',
            details: {
                logoutAllDevices: logoutAllDevices,
                timestamp: new Date().toISOString(),
                ip: 'localhost', // 模拟IP
                userAgent: navigator.userAgent
            },
            timestamp: new Date().toISOString(),
            ip: 'localhost',
            userAgent: navigator.userAgent
        };
        
        // 保存到安全日志
        const securityLogs = JSON.parse(localStorage.getItem('securityLogs') || '[]');
        securityLogs.push(operationLog);
        localStorage.setItem('securityLogs', JSON.stringify(securityLogs));
        
        // 也保存到操作日志
        const operationLogs = JSON.parse(localStorage.getItem('operationLogs') || '[]');
        operationLogs.push(operationLog);
        localStorage.setItem('operationLogs', JSON.stringify(operationLogs));
    }
    
    // 注销所有会话
    logoutAllSessions() {
        showNotification('正在注销所有设备...', 'info');
        
        setTimeout(() => {
            // 清除登录状态
            localStorage.removeItem('isLoggedIn');
            localStorage.removeItem('currentUser');
            
            // 跳转到登录页面
            window.location.href = 'login.html';
        }, 2000);
    }
    
    // 重置表单
    resetForm() {
        const form = document.getElementById('passwordForm');
        if (form) {
            form.reset();
        }
        
        // 重置强度指示器
        if (this.strengthMeter.fill && this.strengthMeter.text) {
            this.strengthMeter.fill.className = 'strength-fill';
            this.strengthMeter.text.className = 'strength-text';
            this.strengthMeter.text.textContent = '请输入密码';
        }
        
        // 重置要求列表
        const requirements = ['req-length', 'req-uppercase', 'req-lowercase', 'req-number', 'req-special'];
        requirements.forEach(id => {
            const element = document.getElementById(id);
            if (element) {
                element.classList.remove('valid');
            }
        });
        
        // 清除错误状态
        document.querySelectorAll('#passwordModalOverlay .field-error').forEach(error => {
            error.classList.remove('show');
        });
        document.querySelectorAll('#passwordModalOverlay .form-group.error').forEach(group => {
            group.classList.remove('error');
        });
        
        // 重置提交按钮
        const submitBtn = document.getElementById('updatePasswordBtn');
        if (submitBtn) {
            submitBtn.disabled = true;
        }
    }
}

// 创建全局PasswordManager实例
let passwordManager;

// 全局函数
function showPasswordModal() {
    if (!passwordManager) {
        passwordManager = new PasswordManager();
    }
    passwordManager.showModal();
}

function closePasswordModal() {
    if (passwordManager) {
        passwordManager.closeModal();
    }
}

function updatePassword() {
    if (passwordManager) {
        passwordManager.updatePassword();
    }
}

function togglePasswordVisibility(inputId) {
    const input = document.getElementById(inputId);
    const button = input.nextElementSibling;
    const icon = button.querySelector('i');
    
    if (input.type === 'password') {
        input.type = 'text';
        icon.classList.remove('fa-eye');
        icon.classList.add('fa-eye-slash');
    } else {
        input.type = 'password';
        icon.classList.remove('fa-eye-slash');
        icon.classList.add('fa-eye');
    }
}

// PasswordManager初始化将在主DOMContentLoaded中处理// =====
 登录历史查看功能 =====

// LoginHistoryManager 类
class LoginHistoryManager {
    constructor() {
        this.historyModal = null;
        this.currentUser = null;
        this.loginHistory = [];
        this.filteredHistory = [];
        this.currentPage = 1;
        this.pageSize = 10;
        this.filters = {
            timeRange: '30d',
            status: 'all'
        };
        
        this.init();
    }
    
    init() {
        this.historyModal = document.getElementById('loginHistoryModalOverlay');
        this.bindEvents();
        this.generateSampleData();
    }
    
    bindEvents() {
        // 时间范围筛选
        const timeRangeSelect = document.getElementById('historyTimeRange');
        if (timeRangeSelect) {
            timeRangeSelect.addEventListener('change', (e) => {
                this.filters.timeRange = e.target.value;
                this.applyFilters();
            });
        }
        
        // 状态筛选
        const statusSelect = document.getElementById('historyStatus');
        if (statusSelect) {
            statusSelect.addEventListener('change', (e) => {
                this.filters.status = e.target.value;
                this.applyFilters();
            });
        }
    }
    
    // 显示登录历史模态框
    showModal() {
        this.loadCurrentUser();
        this.loadLoginHistory();
        this.applyFilters();
        this.historyModal.classList.add('active');
        document.body.style.overflow = 'hidden';
    }
    
    // 关闭登录历史模态框
    closeModal() {
        this.historyModal.classList.remove('active');
        document.body.style.overflow = '';
    }
    
    // 加载当前用户信息
    loadCurrentUser() {
        const currentUser = localStorage.getItem('currentUser');
        if (currentUser) {
            try {
                this.currentUser = JSON.parse(currentUser);
            } catch (error) {
                console.error('加载用户信息失败:', error);
            }
        }
    }
    
    // 加载登录历史数据
    loadLoginHistory() {
        // 从localStorage获取登录历史
        const storedHistory = localStorage.getItem('loginHistory');
        if (storedHistory) {
            try {
                this.loginHistory = JSON.parse(storedHistory);
            } catch (error) {
                console.error('加载登录历史失败:', error);
                this.loginHistory = [];
            }
        }
        
        // 如果没有历史记录，生成一些示例数据
        if (this.loginHistory.length === 0) {
            this.generateSampleData();
        }
        
        // 添加当前登录记录
        this.addCurrentLoginRecord();
    }
    
    // 生成示例登录历史数据
    generateSampleData() {
        const sampleData = [];
        const now = new Date();
        const ips = ['*************', '*********', '***********', '************', '*************'];
        const locations = [
            { country: '中国', city: '北京', region: '北京市' },
            { country: '中国', city: '上海', region: '上海市' },
            { country: '中国', city: '深圳', region: '广东省' },
            { country: '中国', city: '杭州', region: '浙江省' },
            { country: '美国', city: '纽约', region: '纽约州' }
        ];
        const devices = [
            { type: 'Desktop', os: 'Windows 11', browser: 'Chrome 120.0' },
            { type: 'Mobile', os: 'iOS 17.2', browser: 'Safari 17.0' },
            { type: 'Tablet', os: 'Android 14', browser: 'Chrome Mobile 120.0' },
            { type: 'Desktop', os: 'macOS 14.2', browser: 'Safari 17.2' },
            { type: 'Desktop', os: 'Ubuntu 22.04', browser: 'Firefox 121.0' }
        ];
        
        // 生成最近30天的登录记录
        for (let i = 0; i < 50; i++) {
            const daysAgo = Math.floor(Math.random() * 30);
            const hoursAgo = Math.floor(Math.random() * 24);
            const minutesAgo = Math.floor(Math.random() * 60);
            
            const loginTime = new Date(now);
            loginTime.setDate(loginTime.getDate() - daysAgo);
            loginTime.setHours(loginTime.getHours() - hoursAgo);
            loginTime.setMinutes(loginTime.getMinutes() - minutesAgo);
            
            const ip = ips[Math.floor(Math.random() * ips.length)];
            const location = locations[Math.floor(Math.random() * locations.length)];
            const device = devices[Math.floor(Math.random() * devices.length)];
            const isSuccess = Math.random() > 0.1; // 90% 成功率
            
            sampleData.push({
                id: Date.now() + i,
                userId: this.currentUser?.username || 'admin',
                timestamp: loginTime.toISOString(),
                ip: ip,
                location: location,
                device: device,
                userAgent: `Mozilla/5.0 (${device.os}) ${device.browser}`,
                status: isSuccess ? 'success' : 'failed',
                failureReason: isSuccess ? null : ['密码错误', '账户锁定', '网络超时'][Math.floor(Math.random() * 3)],
                sessionDuration: isSuccess ? Math.floor(Math.random() * 7200) : 0, // 0-2小时
                isSuspicious: this.detectSuspiciousActivity(ip, location, device)
            });
        }
        
        // 按时间倒序排列
        sampleData.sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));
        
        this.loginHistory = sampleData;
        localStorage.setItem('loginHistory', JSON.stringify(this.loginHistory));
    }
    
    // 添加当前登录记录
    addCurrentLoginRecord() {
        const now = new Date();
        const currentRecord = {
            id: Date.now(),
            userId: this.currentUser?.username || 'admin',
            timestamp: now.toISOString(),
            ip: '*************', // 模拟IP
            location: { country: '中国', city: '北京', region: '北京市' },
            device: { type: 'Desktop', os: this.getOS(), browser: this.getBrowser() },
            userAgent: navigator.userAgent,
            status: 'success',
            failureReason: null,
            sessionDuration: 0, // 当前会话
            isSuspicious: false
        };
        
        // 检查是否已存在相似的记录（避免重复）
        const existingRecord = this.loginHistory.find(record => 
            Math.abs(new Date(record.timestamp) - now) < 60000 && // 1分钟内
            record.status === 'success'
        );
        
        if (!existingRecord) {
            this.loginHistory.unshift(currentRecord);
            localStorage.setItem('loginHistory', JSON.stringify(this.loginHistory));
        }
    }
    
    // 检测可疑活动
    detectSuspiciousActivity(ip, location, device) {
        // 简单的可疑活动检测逻辑
        const suspiciousIPs = ['************', '*************'];
        const suspiciousLocations = ['美国'];
        
        return suspiciousIPs.includes(ip) || 
               suspiciousLocations.includes(location.country) ||
               Math.random() < 0.05; // 5% 随机标记为可疑
    }
    
    // 获取操作系统信息
    getOS() {
        const userAgent = navigator.userAgent;
        if (userAgent.includes('Windows')) return 'Windows';
        if (userAgent.includes('Mac')) return 'macOS';
        if (userAgent.includes('Linux')) return 'Linux';
        if (userAgent.includes('Android')) return 'Android';
        if (userAgent.includes('iOS')) return 'iOS';
        return 'Unknown';
    }
    
    // 获取浏览器信息
    getBrowser() {
        const userAgent = navigator.userAgent;
        if (userAgent.includes('Chrome')) return 'Chrome';
        if (userAgent.includes('Firefox')) return 'Firefox';
        if (userAgent.includes('Safari')) return 'Safari';
        if (userAgent.includes('Edge')) return 'Edge';
        return 'Unknown';
    }
    
    // 应用筛选条件
    applyFilters() {
        let filtered = [...this.loginHistory];
        
        // 时间范围筛选
        if (this.filters.timeRange !== 'all') {
            const now = new Date();
            let cutoffDate = new Date();
            
            switch (this.filters.timeRange) {
                case '7d':
                    cutoffDate.setDate(now.getDate() - 7);
                    break;
                case '30d':
                    cutoffDate.setDate(now.getDate() - 30);
                    break;
                case '90d':
                    cutoffDate.setDate(now.getDate() - 90);
                    break;
            }
            
            filtered = filtered.filter(record => 
                new Date(record.timestamp) >= cutoffDate
            );
        }
        
        // 状态筛选
        if (this.filters.status !== 'all') {
            filtered = filtered.filter(record => record.status === this.filters.status);
        }
        
        this.filteredHistory = filtered;
        this.currentPage = 1;
        this.updateDisplay();
    }
    
    // 更新显示
    updateDisplay() {
        this.updateStats();
        this.renderTable();
        this.updatePagination();
        this.checkSecurityAlerts();
    }
    
    // 更新统计信息
    updateStats() {
        const total = this.filteredHistory.length;
        const success = this.filteredHistory.filter(r => r.status === 'success').length;
        const failed = this.filteredHistory.filter(r => r.status === 'failed').length;
        const uniqueIPs = new Set(this.filteredHistory.map(r => r.ip)).size;
        
        document.getElementById('totalLogins').textContent = total;
        document.getElementById('successLogins').textContent = success;
        document.getElementById('failedLogins').textContent = failed;
        document.getElementById('uniqueIPs').textContent = uniqueIPs;
    }
    
    // 渲染表格
    renderTable() {
        const tbody = document.getElementById('loginHistoryTableBody');
        if (!tbody) return;
        
        const startIndex = (this.currentPage - 1) * this.pageSize;
        const endIndex = startIndex + this.pageSize;
        const pageData = this.filteredHistory.slice(startIndex, endIndex);
        
        tbody.innerHTML = pageData.map(record => this.createTableRow(record)).join('');
    }
    
    // 创建表格行
    createTableRow(record) {
        const timestamp = new Date(record.timestamp);
        const timeStr = timestamp.toLocaleString('zh-CN');
        const statusClass = record.status === 'success' ? 'success' : 'failed';
        const suspiciousClass = record.isSuspicious ? 'suspicious' : '';
        
        return `
            <tr class="${suspiciousClass}">
                <td>
                    <div class="timestamp">
                        <div class="date">${timeStr}</div>
                        <div class="relative-time">${this.getRelativeTime(timestamp)}</div>
                    </div>
                </td>
                <td>
                    <div class="ip-info">
                        <span class="ip">${record.ip}</span>
                        ${record.isSuspicious ? '<i class="fas fa-exclamation-triangle text-warning" title="可疑IP"></i>' : ''}
                    </div>
                </td>
                <td>
                    <div class="location-info">
                        <div class="country">${record.location.country}</div>
                        <div class="city">${record.location.city}, ${record.location.region}</div>
                    </div>
                </td>
                <td>
                    <div class="device-info">
                        <div class="device-type">${record.device.type}</div>
                        <div class="os">${record.device.os}</div>
                    </div>
                </td>
                <td>
                    <div class="browser-info">
                        ${record.device.browser}
                    </div>
                </td>
                <td>
                    <span class="status-badge ${statusClass}">
                        <i class="fas ${record.status === 'success' ? 'fa-check' : 'fa-times'}"></i>
                        ${record.status === 'success' ? '成功' : '失败'}
                    </span>
                    ${record.failureReason ? `<div class="failure-reason">${record.failureReason}</div>` : ''}
                </td>
                <td>
                    <div class="action-buttons">
                        <button class="btn-icon" title="查看详情" onclick="showLoginDetail('${record.id}')">
                            <i class="fas fa-eye"></i>
                        </button>
                        ${record.isSuspicious ? `
                            <button class="btn-icon warning" title="标记安全" onclick="markAsSafe('${record.id}')">
                                <i class="fas fa-shield-alt"></i>
                            </button>
                        ` : ''}
                    </div>
                </td>
            </tr>
        `;
    }
    
    // 获取相对时间
    getRelativeTime(timestamp) {
        const now = new Date();
        const diff = now - timestamp;
        const minutes = Math.floor(diff / 60000);
        const hours = Math.floor(diff / 3600000);
        const days = Math.floor(diff / 86400000);
        
        if (minutes < 1) return '刚刚';
        if (minutes < 60) return `${minutes}分钟前`;
        if (hours < 24) return `${hours}小时前`;
        if (days < 30) return `${days}天前`;
        return '很久以前';
    }
    
    // 更新分页
    updatePagination() {
        const totalRecords = this.filteredHistory.length;
        const totalPages = Math.ceil(totalRecords / this.pageSize);
        const startRecord = (this.currentPage - 1) * this.pageSize + 1;
        const endRecord = Math.min(this.currentPage * this.pageSize, totalRecords);
        
        document.getElementById('currentRange').textContent = `${startRecord}-${endRecord}`;
        document.getElementById('totalRecords').textContent = totalRecords;
        document.getElementById('currentPage').textContent = this.currentPage;
        document.getElementById('totalPages').textContent = totalPages;
        
        // 更新按钮状态
        const prevBtn = document.getElementById('prevPage');
        const nextBtn = document.getElementById('nextPage');
        
        if (prevBtn) prevBtn.disabled = this.currentPage <= 1;
        if (nextBtn) nextBtn.disabled = this.currentPage >= totalPages;
    }
    
    // 检查安全警告
    checkSecurityAlerts() {
        const alertsContainer = document.getElementById('securityAlerts');
        if (!alertsContainer) return;
        
        const alerts = [];
        
        // 检查失败登录
        const recentFailed = this.filteredHistory.filter(r => 
            r.status === 'failed' && 
            new Date() - new Date(r.timestamp) < 24 * 60 * 60 * 1000 // 24小时内
        );
        
        if (recentFailed.length > 3) {
            alerts.push({
                type: 'warning',
                icon: 'fas fa-exclamation-triangle',
                title: '频繁登录失败',
                message: `检测到24小时内有${recentFailed.length}次登录失败，建议检查账户安全。`,
                actions: [
                    { text: '修改密码', action: 'changePassword' },
                    { text: '查看详情', action: 'viewDetails' }
                ]
            });
        }
        
        // 检查可疑IP
        const suspiciousLogins = this.filteredHistory.filter(r => r.isSuspicious);
        if (suspiciousLogins.length > 0) {
            alerts.push({
                type: 'danger',
                icon: 'fas fa-shield-alt',
                title: '可疑登录活动',
                message: `检测到${suspiciousLogins.length}次可疑登录活动，请确认是否为本人操作。`,
                actions: [
                    { text: '标记安全', action: 'markSafe' },
                    { text: '立即保护', action: 'secureAccount' }
                ]
            });
        }
        
        // 检查新设备登录
        const uniqueDevices = new Set(this.filteredHistory.map(r => 
            `${r.device.type}-${r.device.os}-${r.device.browser}`
        ));
        
        if (uniqueDevices.size > 3) {
            alerts.push({
                type: 'info',
                icon: 'fas fa-info-circle',
                title: '多设备登录',
                message: `检测到您在${uniqueDevices.size}种不同设备上登录，请确保设备安全。`,
                actions: [
                    { text: '查看设备', action: 'viewDevices' }
                ]
            });
        }
        
        // 渲染警告
        alertsContainer.innerHTML = alerts.map(alert => this.createSecurityAlert(alert)).join('');
    }
    
    // 创建安全警告
    createSecurityAlert(alert) {
        const actionsHtml = alert.actions.map(action => 
            `<button class="btn-sm btn-${alert.type === 'danger' ? 'danger' : 'primary'}" onclick="handleSecurityAction('${action.action}')">${action.text}</button>`
        ).join('');
        
        return `
            <div class="security-alert ${alert.type}">
                <div class="security-alert-icon">
                    <i class="${alert.icon}"></i>
                </div>
                <div class="security-alert-content">
                    <div class="security-alert-title">${alert.title}</div>
                    <div class="security-alert-message">${alert.message}</div>
                    <div class="security-alert-actions">
                        ${actionsHtml}
                    </div>
                </div>
            </div>
        `;
    }
    
    // 切换页面
    changePage(direction) {
        const totalPages = Math.ceil(this.filteredHistory.length / this.pageSize);
        const newPage = this.currentPage + direction;
        
        if (newPage >= 1 && newPage <= totalPages) {
            this.currentPage = newPage;
            this.renderTable();
            this.updatePagination();
        }
    }
    
    // 导出登录历史
    exportHistory() {
        const csvContent = this.generateCSV();
        const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
        const link = document.createElement('a');
        const url = URL.createObjectURL(blob);
        
        link.setAttribute('href', url);
        link.setAttribute('download', `login_history_${new Date().toISOString().split('T')[0]}.csv`);
        link.style.visibility = 'hidden';
        
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        
        showNotification('登录历史已导出', 'success');
    }
    
    // 生成CSV内容
    generateCSV() {
        const headers = ['登录时间', 'IP地址', '地理位置', '设备类型', '操作系统', '浏览器', '状态', '失败原因', '会话时长'];
        const rows = this.filteredHistory.map(record => [
            new Date(record.timestamp).toLocaleString('zh-CN'),
            record.ip,
            `${record.location.country}, ${record.location.city}`,
            record.device.type,
            record.device.os,
            record.device.browser,
            record.status === 'success' ? '成功' : '失败',
            record.failureReason || '',
            record.sessionDuration ? `${Math.floor(record.sessionDuration / 60)}分钟` : ''
        ]);
        
        return [headers, ...rows].map(row => 
            row.map(field => `"${field}"`).join(',')
        ).join('\n');
    }
    
    // 刷新历史记录
    refreshHistory() {
        this.loadLoginHistory();
        this.applyFilters();
        showNotification('登录历史已刷新', 'success');
    }
}

// 创建全局LoginHistoryManager实例
let loginHistoryManager;

// 全局函数
function showLoginHistoryModal() {
    if (!loginHistoryManager) {
        loginHistoryManager = new LoginHistoryManager();
    }
    loginHistoryManager.showModal();
}

function closeLoginHistoryModal() {
    if (loginHistoryManager) {
        loginHistoryManager.closeModal();
    }
}

function changePage(direction) {
    if (loginHistoryManager) {
        loginHistoryManager.changePage(direction);
    }
}

function exportLoginHistory() {
    if (loginHistoryManager) {
        loginHistoryManager.exportHistory();
    }
}

function refreshLoginHistory() {
    if (loginHistoryManager) {
        loginHistoryManager.refreshHistory();
    }
}

function showLoginDetail(recordId) {
    showNotification('登录详情功能开发中...', 'info');
}

function markAsSafe(recordId) {
    showNotification('已标记为安全', 'success');
}

function handleSecurityAction(action) {
    switch (action) {
        case 'changePassword':
            showPasswordModal();
            break;
        case 'viewDetails':
            showNotification('查看详情功能开发中...', 'info');
            break;
        case 'markSafe':
            showNotification('已标记为安全', 'success');
            break;
        case 'secureAccount':
            showNotification('账户保护功能开发中...', 'info');
            break;
        case 'viewDevices':
            showNotification('设备管理功能开发中...', 'info');
            break;
        default:
            showNotification('功能开发中...', 'info');
    }
}

// LoginHistoryManager初始化已在主DOMContentLoaded中处理