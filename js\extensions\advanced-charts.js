// 高级图表组件
class AdvancedCharts {
    constructor() {
        this.charts = {};
        this.themes = {
            light: {
                backgroundColor: '#ffffff',
                textColor: '#374151',
                gridColor: '#e5e7eb',
                primaryColor: '#6366f1',
                secondaryColor: '#8b5cf6'
            },
            dark: {
                backgroundColor: '#1f2937',
                textColor: '#f9fafb',
                gridColor: '#374151',
                primaryColor: '#818cf8',
                secondaryColor: '#a78bfa'
            }
        };
        this.currentTheme = 'light';
        this.initializeCharts();
    }

    initializeCharts() {
        // 等待页面加载完成
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => {
                this.createAllCharts();
            });
        } else {
            this.createAllCharts();
        }
    }

    createAllCharts() {
        this.createSalesChart();
        this.createUserGrowthChart();
        this.createRevenueChart();
        this.createOrderStatusChart();
        this.createTrafficSourceChart();
        this.createPerformanceChart();
    }

    // 销售趋势图表
    createSalesChart() {
        const ctx = document.getElementById('salesChart');
        if (!ctx) return;

        const data = this.generateSalesData();
        
        this.charts.sales = new Chart(ctx, {
            type: 'line',
            data: {
                labels: data.labels,
                datasets: [{
                    label: '销售额',
                    data: data.sales,
                    borderColor: this.themes[this.currentTheme].primaryColor,
                    backgroundColor: this.themes[this.currentTheme].primaryColor + '20',
                    borderWidth: 3,
                    fill: true,
                    tension: 0.4,
                    pointBackgroundColor: this.themes[this.currentTheme].primaryColor,
                    pointBorderColor: '#ffffff',
                    pointBorderWidth: 2,
                    pointRadius: 6,
                    pointHoverRadius: 8
                }, {
                    label: '目标',
                    data: data.target,
                    borderColor: this.themes[this.currentTheme].secondaryColor,
                    backgroundColor: 'transparent',
                    borderWidth: 2,
                    borderDash: [5, 5],
                    pointRadius: 0
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'top',
                        labels: {
                            usePointStyle: true,
                            padding: 20,
                            color: this.themes[this.currentTheme].textColor
                        }
                    },
                    tooltip: {
                        backgroundColor: 'rgba(0, 0, 0, 0.8)',
                        titleColor: '#ffffff',
                        bodyColor: '#ffffff',
                        borderColor: this.themes[this.currentTheme].primaryColor,
                        borderWidth: 1,
                        cornerRadius: 8,
                        displayColors: true,
                        callbacks: {
                            label: function(context) {
                                return context.dataset.label + ': ¥' + context.parsed.y.toLocaleString();
                            }
                        }
                    }
                },
                scales: {
                    x: {
                        grid: {
                            color: this.themes[this.currentTheme].gridColor,
                            borderColor: this.themes[this.currentTheme].gridColor
                        },
                        ticks: {
                            color: this.themes[this.currentTheme].textColor
                        }
                    },
                    y: {
                        grid: {
                            color: this.themes[this.currentTheme].gridColor,
                            borderColor: this.themes[this.currentTheme].gridColor
                        },
                        ticks: {
                            color: this.themes[this.currentTheme].textColor,
                            callback: function(value) {
                                return '¥' + value.toLocaleString();
                            }
                        }
                    }
                },
                interaction: {
                    intersect: false,
                    mode: 'index'
                },
                animation: {
                    duration: 2000,
                    easing: 'easeInOutQuart'
                }
            }
        });
    }

    // 用户增长图表
    createUserGrowthChart() {
        const ctx = document.getElementById('userGrowthChart');
        if (!ctx) return;

        const data = this.generateUserGrowthData();
        
        this.charts.userGrowth = new Chart(ctx, {
            type: 'bar',
            data: {
                labels: data.labels,
                datasets: [{
                    label: '新用户',
                    data: data.newUsers,
                    backgroundColor: this.themes[this.currentTheme].primaryColor + '80',
                    borderColor: this.themes[this.currentTheme].primaryColor,
                    borderWidth: 2,
                    borderRadius: 6,
                    borderSkipped: false
                }, {
                    label: '活跃用户',
                    data: data.activeUsers,
                    backgroundColor: this.themes[this.currentTheme].secondaryColor + '80',
                    borderColor: this.themes[this.currentTheme].secondaryColor,
                    borderWidth: 2,
                    borderRadius: 6,
                    borderSkipped: false
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'top',
                        labels: {
                            usePointStyle: true,
                            padding: 20,
                            color: this.themes[this.currentTheme].textColor
                        }
                    }
                },
                scales: {
                    x: {
                        grid: {
                            display: false
                        },
                        ticks: {
                            color: this.themes[this.currentTheme].textColor
                        }
                    },
                    y: {
                        grid: {
                            color: this.themes[this.currentTheme].gridColor
                        },
                        ticks: {
                            color: this.themes[this.currentTheme].textColor
                        }
                    }
                },
                animation: {
                    duration: 1500,
                    easing: 'easeOutBounce'
                }
            }
        });
    }

    // 收入分析图表
    createRevenueChart() {
        const ctx = document.getElementById('revenueChart');
        if (!ctx) return;

        const data = this.generateRevenueData();
        
        this.charts.revenue = new Chart(ctx, {
            type: 'doughnut',
            data: {
                labels: data.labels,
                datasets: [{
                    data: data.values,
                    backgroundColor: [
                        '#6366f1',
                        '#8b5cf6',
                        '#ec4899',
                        '#f59e0b',
                        '#10b981'
                    ],
                    borderWidth: 0,
                    hoverBorderWidth: 3,
                    hoverBorderColor: '#ffffff'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                cutout: '60%',
                plugins: {
                    legend: {
                        position: 'right',
                        labels: {
                            usePointStyle: true,
                            padding: 20,
                            color: this.themes[this.currentTheme].textColor
                        }
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                const total = context.dataset.data.reduce((a, b) => a + b, 0);
                                const percentage = ((context.parsed / total) * 100).toFixed(1);
                                return context.label + ': ¥' + context.parsed.toLocaleString() + ' (' + percentage + '%)';
                            }
                        }
                    }
                },
                animation: {
                    animateRotate: true,
                    duration: 2000
                }
            }
        });
    }

    // 订单状态图表
    createOrderStatusChart() {
        const ctx = document.getElementById('orderStatusChart');
        if (!ctx) return;

        const data = this.generateOrderStatusData();
        
        this.charts.orderStatus = new Chart(ctx, {
            type: 'polarArea',
            data: {
                labels: data.labels,
                datasets: [{
                    data: data.values,
                    backgroundColor: [
                        '#10b981',
                        '#f59e0b',
                        '#ef4444',
                        '#6b7280'
                    ],
                    borderWidth: 2,
                    borderColor: '#ffffff'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom',
                        labels: {
                            color: this.themes[this.currentTheme].textColor
                        }
                    }
                },
                scales: {
                    r: {
                        grid: {
                            color: this.themes[this.currentTheme].gridColor
                        },
                        ticks: {
                            color: this.themes[this.currentTheme].textColor
                        }
                    }
                }
            }
        });
    }

    // 流量来源图表
    createTrafficSourceChart() {
        const ctx = document.getElementById('trafficSourceChart');
        if (!ctx) return;

        const data = this.generateTrafficData();
        
        this.charts.trafficSource = new Chart(ctx, {
            type: 'radar',
            data: {
                labels: data.labels,
                datasets: [{
                    label: '本月',
                    data: data.thisMonth,
                    borderColor: this.themes[this.currentTheme].primaryColor,
                    backgroundColor: this.themes[this.currentTheme].primaryColor + '30',
                    pointBackgroundColor: this.themes[this.currentTheme].primaryColor,
                    pointBorderColor: '#ffffff',
                    pointBorderWidth: 2
                }, {
                    label: '上月',
                    data: data.lastMonth,
                    borderColor: this.themes[this.currentTheme].secondaryColor,
                    backgroundColor: this.themes[this.currentTheme].secondaryColor + '30',
                    pointBackgroundColor: this.themes[this.currentTheme].secondaryColor,
                    pointBorderColor: '#ffffff',
                    pointBorderWidth: 2
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'top',
                        labels: {
                            color: this.themes[this.currentTheme].textColor
                        }
                    }
                },
                scales: {
                    r: {
                        grid: {
                            color: this.themes[this.currentTheme].gridColor
                        },
                        ticks: {
                            color: this.themes[this.currentTheme].textColor
                        }
                    }
                }
            }
        });
    }

    // 性能监控图表
    createPerformanceChart() {
        const ctx = document.getElementById('performanceChart');
        if (!ctx) return;

        const data = this.generatePerformanceData();
        
        this.charts.performance = new Chart(ctx, {
            type: 'line',
            data: {
                labels: data.labels,
                datasets: [{
                    label: 'CPU使用率',
                    data: data.cpu,
                    borderColor: '#ef4444',
                    backgroundColor: '#ef444420',
                    yAxisID: 'y'
                }, {
                    label: '内存使用率',
                    data: data.memory,
                    borderColor: '#f59e0b',
                    backgroundColor: '#f59e0b20',
                    yAxisID: 'y'
                }, {
                    label: '响应时间(ms)',
                    data: data.responseTime,
                    borderColor: '#10b981',
                    backgroundColor: '#10b98120',
                    yAxisID: 'y1'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'top',
                        labels: {
                            color: this.themes[this.currentTheme].textColor
                        }
                    }
                },
                scales: {
                    x: {
                        grid: {
                            color: this.themes[this.currentTheme].gridColor
                        },
                        ticks: {
                            color: this.themes[this.currentTheme].textColor
                        }
                    },
                    y: {
                        type: 'linear',
                        display: true,
                        position: 'left',
                        max: 100,
                        grid: {
                            color: this.themes[this.currentTheme].gridColor
                        },
                        ticks: {
                            color: this.themes[this.currentTheme].textColor,
                            callback: function(value) {
                                return value + '%';
                            }
                        }
                    },
                    y1: {
                        type: 'linear',
                        display: true,
                        position: 'right',
                        grid: {
                            drawOnChartArea: false
                        },
                        ticks: {
                            color: this.themes[this.currentTheme].textColor,
                            callback: function(value) {
                                return value + 'ms';
                            }
                        }
                    }
                }
            }
        });
    }

    // 数据生成方法
    generateSalesData() {
        const labels = [];
        const sales = [];
        const target = [];
        
        for (let i = 29; i >= 0; i--) {
            const date = new Date();
            date.setDate(date.getDate() - i);
            labels.push(date.toLocaleDateString('zh-CN', { month: 'short', day: 'numeric' }));
            
            sales.push(Math.floor(Math.random() * 50000) + 20000);
            target.push(45000);
        }
        
        return { labels, sales, target };
    }

    generateUserGrowthData() {
        const labels = ['1月', '2月', '3月', '4月', '5月', '6月'];
        const newUsers = [120, 190, 300, 500, 200, 300];
        const activeUsers = [800, 950, 1200, 1400, 1100, 1300];
        
        return { labels, newUsers, activeUsers };
    }

    generateRevenueData() {
        return {
            labels: ['产品销售', '服务收入', '广告收入', '会员费用', '其他'],
            values: [45000, 25000, 15000, 8000, 7000]
        };
    }

    generateOrderStatusData() {
        return {
            labels: ['已完成', '处理中', '已取消', '待付款'],
            values: [65, 25, 8, 12]
        };
    }

    generateTrafficData() {
        return {
            labels: ['直接访问', '搜索引擎', '社交媒体', '邮件营销', '广告推广', '其他'],
            thisMonth: [65, 59, 90, 81, 56, 55],
            lastMonth: [28, 48, 40, 19, 96, 27]
        };
    }

    generatePerformanceData() {
        const labels = [];
        const cpu = [];
        const memory = [];
        const responseTime = [];
        
        for (let i = 23; i >= 0; i--) {
            const time = new Date();
            time.setHours(time.getHours() - i);
            labels.push(time.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' }));
            
            cpu.push(Math.floor(Math.random() * 40) + 20);
            memory.push(Math.floor(Math.random() * 30) + 40);
            responseTime.push(Math.floor(Math.random() * 100) + 50);
        }
        
        return { labels, cpu, memory, responseTime };
    }

    // 主题切换
    switchTheme(theme) {
        this.currentTheme = theme;
        this.updateAllCharts();
    }

    updateAllCharts() {
        Object.values(this.charts).forEach(chart => {
            if (chart) {
                chart.destroy();
            }
        });
        this.charts = {};
        this.createAllCharts();
    }

    // 实时更新数据
    updateChartData(chartName, newData) {
        const chart = this.charts[chartName];
        if (chart) {
            chart.data = newData;
            chart.update('active');
        }
    }

    // 导出图表
    exportChart(chartName, format = 'png') {
        const chart = this.charts[chartName];
        if (chart) {
            const url = chart.toBase64Image();
            const link = document.createElement('a');
            link.download = `${chartName}-chart.${format}`;
            link.href = url;
            link.click();
        }
    }
}

// 全局图表管理器
let advancedCharts = null;

// 初始化图表
function initializeAdvancedCharts() {
    advancedCharts = new AdvancedCharts();
    console.log('✅ 高级图表系统已初始化');
}

// 页面加载时初始化
document.addEventListener('DOMContentLoaded', function() {
    setTimeout(initializeAdvancedCharts, 300);
});
