/* 紧急布局修复 CSS */

/* 重置可能冲突的样式 */
.page {
    padding: 20px !important;
    max-width: none !important;
    margin: 0 !important;
    width: 100% !important;
    min-height: auto !important;
    opacity: 1 !important;
    transform: none !important;
    transition: none !important;
}

/* 确保页面正常显示 */
.page.active {
    display: block !important;
    opacity: 1 !important;
    transform: none !important;
}

/* 修复统计卡片网格 */
.stats-grid {
    display: grid !important;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr)) !important;
    gap: 20px !important;
    margin-bottom: 24px !important;
}

/* 修复工具栏样式 */
.page-toolbar {
    background: rgba(255, 255, 255, 0.95) !important;
    border-radius: 12px !important;
    padding: 16px 20px !important;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1) !important;
    margin-bottom: 20px !important;
    backdrop-filter: blur(10px) !important;
}

.toolbar-row {
    display: flex !important;
    align-items: center !important;
    gap: 16px !important;
    flex-wrap: wrap !important;
}

.toolbar-section {
    display: flex !important;
    align-items: center !important;
    gap: 12px !important;
}

.toolbar-divider {
    width: 1px !important;
    height: 24px !important;
    background: #e5e7eb !important;
    margin: 0 8px !important;
}

/* 修复搜索框 */
.search-box {
    position: relative !important;
    min-width: 300px !important;
}

.search-box input {
    width: 100% !important;
    padding: 12px 16px 12px 44px !important;
    border: 1px solid #e5e7eb !important;
    border-radius: 12px !important;
    font-size: 14px !important;
    transition: all 0.3s ease !important;
}

.search-box .search-icon {
    position: absolute !important;
    left: 16px !important;
    top: 50% !important;
    transform: translateY(-50%) !important;
    color: #6b7280 !important;
    font-size: 16px !important;
}

/* 修复筛选器 */
.filter-group {
    display: flex !important;
    align-items: center !important;
    gap: 8px !important;
}

.filter-select {
    padding: 10px 16px !important;
    border: 1px solid #e5e7eb !important;
    border-radius: 8px !important;
    background: white !important;
    font-size: 14px !important;
    min-width: 120px !important;
    cursor: pointer !important;
}

/* 修复快速操作区域 */
.quick-actions-section {
    background: rgba(255, 255, 255, 0.95) !important;
    border-radius: 12px !important;
    padding: 20px !important;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1) !important;
    margin-bottom: 20px !important;
    backdrop-filter: blur(10px) !important;
}

.quick-actions-title {
    font-size: 18px !important;
    font-weight: 600 !important;
    color: #1f2937 !important;
    margin: 0 0 16px 0 !important;
}

.quick-actions-grid {
    display: grid !important;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)) !important;
    gap: 16px !important;
}

.quick-action-btn {
    display: flex !important;
    align-items: center !important;
    gap: 12px !important;
    padding: 16px !important;
    background: #f8fafc !important;
    border: 1px solid #e5e7eb !important;
    border-radius: 12px !important;
    cursor: pointer !important;
    transition: all 0.3s ease !important;
    text-decoration: none !important;
    color: #374151 !important;
}

.quick-action-btn:hover {
    background: #667eea !important;
    color: white !important;
    transform: translateY(-2px) !important;
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3) !important;
}

.quick-action-icon {
    width: 40px !important;
    height: 40px !important;
    border-radius: 10px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    background: #667eea !important;
    color: white !important;
    font-size: 18px !important;
}

.quick-action-btn:hover .quick-action-icon {
    background: white !important;
    color: #667eea !important;
}

/* 修复数据表格容器 */
.data-table-container {
    background: rgba(255, 255, 255, 0.98) !important;
    border-radius: 16px !important;
    overflow: hidden !important;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08) !important;
}

.table-header {
    padding: 20px 24px !important;
    border-bottom: 1px solid #f3f4f6 !important;
    display: flex !important;
    justify-content: space-between !important;
    align-items: center !important;
}

.table-title {
    font-size: 18px !important;
    font-weight: 600 !important;
    color: #1f2937 !important;
    margin: 0 !important;
}

/* 修复现代表格 */
.modern-table {
    width: 100% !important;
    border-collapse: collapse !important;
    font-size: 14px !important;
}

.modern-table th {
    background: #f9fafb !important;
    padding: 16px !important;
    text-align: left !important;
    font-weight: 600 !important;
    color: #374151 !important;
    border-bottom: 1px solid #e5e7eb !important;
}

.modern-table td {
    padding: 16px !important;
    border-bottom: 1px solid #f3f4f6 !important;
    vertical-align: middle !important;
}

.modern-table tbody tr:hover {
    background-color: #f9fafb !important;
}

/* 修复分页器 */
.pagination-container {
    padding: 20px 24px !important;
    border-top: 1px solid #f3f4f6 !important;
    display: flex !important;
    justify-content: space-between !important;
    align-items: center !important;
}

.pagination-info {
    color: #6b7280 !important;
    font-size: 14px !important;
}

.pagination-controls {
    display: flex !important;
    gap: 8px !important;
}

.pagination-btn {
    padding: 8px 12px !important;
    border: 1px solid #e5e7eb !important;
    background: white !important;
    border-radius: 6px !important;
    cursor: pointer !important;
    font-size: 14px !important;
    transition: all 0.2s ease !important;
}

.pagination-btn:hover:not(:disabled) {
    background: #f3f4f6 !important;
}

.pagination-btn.active {
    background: #667eea !important;
    color: white !important;
    border-color: #667eea !important;
}

/* 响应式修复 */
@media (max-width: 1024px) {
    .page {
        padding: 12px !important;
    }
    
    .toolbar-row {
        flex-direction: column !important;
        align-items: stretch !important;
        gap: 12px !important;
    }
    
    .search-box {
        min-width: auto !important;
    }
    
    .quick-actions-grid {
        grid-template-columns: repeat(auto-fit, minmax(180px, 1fr)) !important;
        gap: 12px !important;
    }
}

@media (max-width: 768px) {
    .quick-actions-grid {
        grid-template-columns: 1fr !important;
    }
    
    .modern-table {
        font-size: 12px !important;
    }
    
    .modern-table th,
    .modern-table td {
        padding: 12px 8px !important;
    }
}
