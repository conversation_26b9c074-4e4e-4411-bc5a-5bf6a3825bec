# 现代企业管理系统

一个现代化的企业管理系统，包含登录认证和后台管理功能。

## 功能特性

- 🔐 用户登录认证系统
- 📊 现代化仪表盘界面
- 👥 用户管理功能
- 📈 数据分析展示
- 🛒 订单管理系统
- ⚙️ 系统设置功能
- 📱 响应式设计，支持移动端

## 测试账号

系统提供了以下固定测试账号，可以直接使用：

| 用户名 | 密码 | 角色 | 说明 |
|--------|------|------|------|
| `admin` | `admin123` | 系统管理员 | 拥有所有权限的管理员账号 |
| `manager` | `manager123` | 部门经理 | 部门管理权限 |
| `user` | `user123` | 普通用户 | 基础用户权限 |
| `test` | `test123` | 测试用户 | 用于功能测试 |
| `demo` | `demo123` | 演示用户 | 用于系统演示 |

## 快速开始

1. **访问登录页面**
   ```
   打开 login.html 文件
   ```

2. **使用测试账号登录**
   - 输入上述任意一组用户名和密码
   - 或者点击页面下方显示的测试账号提示直接填入
   - 点击"立即登录"按钮

3. **访问后台管理**
   - 登录成功后会自动跳转到 index.html
   - 可以浏览各个功能模块
   - 点击右上角用户头像可以查看用户菜单

## 功能说明

### 登录系统
- ✅ 支持用户名/密码登录
- ✅ 记住登录状态功能
- ✅ 登录状态检查
- ✅ 自动跳转功能
- ✅ 错误提示和验证

### 后台管理
- ✅ 侧边栏导航
- ✅ 响应式布局
- ✅ 用户信息显示
- ✅ 退出登录功能
- ✅ 页面权限控制

### 特殊功能
- 🔥 **测试账号提示**: 登录失败后会显示可用的测试账号
- 🔥 **一键填入**: 点击测试账号可自动填入登录表单
- 🔥 **登录状态保持**: 刷新页面不会丢失登录状态
- 🔥 **快捷键支持**: Ctrl+H 显示/隐藏测试账号提示

## 文件结构

```
├── login.html          # 登录页面
├── login.css           # 登录页面样式
├── login.js            # 登录页面逻辑
├── index.html          # 后台管理主页
├── styles.css          # 后台管理样式
├── app.js              # 后台管理逻辑
└── README.md           # 说明文档
```

## 技术栈

- **前端**: HTML5, CSS3, JavaScript (ES6+)
- **样式**: CSS Variables, Flexbox, Grid
- **图标**: Font Awesome 6.4.0
- **字体**: Inter (Google Fonts)
- **存储**: LocalStorage

## 浏览器支持

- Chrome 60+
- Firefox 60+
- Safari 12+
- Edge 79+

## 开发说明

### 添加新的测试账号

在 `login.js` 文件中的 `TEST_ACCOUNTS` 数组中添加新账号：

```javascript
const TEST_ACCOUNTS = [
    // 现有账号...
    {
        username: 'newuser',
        password: 'newpass123',
        role: '新角色',
        name: '新用户'
    }
];
```

### 自定义登录验证

修改 `login.js` 中的 `handleLogin` 函数来实现自定义验证逻辑。

### 添加新的管理页面

1. 在 `index.html` 中添加新的页面内容
2. 在侧边栏导航中添加对应的菜单项
3. 在 `app.js` 中添加相应的页面切换逻辑

## 注意事项

- 🔒 这是一个前端演示系统，实际生产环境需要后端API支持
- 🔒 密码验证仅在前端进行，实际应用需要后端安全验证
- 🔒 用户数据存储在浏览器本地，清除浏览器数据会丢失登录状态

## 更新日志

### v1.0.0 (2024-01-15)
- ✅ 完成基础登录系统
- ✅ 添加多个测试账号
- ✅ 实现后台管理界面
- ✅ 添加用户菜单和退出功能
- ✅ 完善响应式设计

---

**提示**: 如果遇到任何问题，请检查浏览器控制台的错误信息，或者尝试清除浏览器缓存后重新访问。
