# 恋雪二游 - 企业管理系统 + NER数据生成器

一个现代化的整合系统，包含企业管理功能和NER（命名实体识别）训练数据生成器。

## 功能特性

### 企业管理系统
- 🔐 用户登录认证系统
- 📊 现代化仪表盘界面
- 👥 用户管理功能
- 📈 数据分析展示
- 🛒 订单管理系统
- ⚙️ 系统设置功能
- 📱 响应式设计，支持移动端

### NER数据生成器
- 🧠 智能NER训练数据生成
- 📝 支持5种实体类型（人名、地名、机构、时间、数字）
- 📊 实时数据统计和可视化
- 💾 多种格式导出（JSON、CoNLL、BIO）
- 🔄 RESTful API接口
- 📈 交互式图表展示

## 测试账号

系统提供了以下固定测试账号，可以直接使用：

| 用户名 | 密码 | 角色 | 说明 |
|--------|------|------|------|
| `admin` | `admin123` | 系统管理员 | 拥有所有权限的管理员账号 |
| `manager` | `manager123` | 部门经理 | 部门管理权限 |
| `user` | `user123` | 普通用户 | 基础用户权限 |
| `test` | `test123` | 测试用户 | 用于功能测试 |
| `demo` | `demo123` | 演示用户 | 用于系统演示 |

## 快速开始

### 方法一：一键启动（推荐）

1. **安装Python依赖**
   ```bash
   pip install -r requirements.txt
   ```

2. **启动整合系统**
   ```bash
   python start_system.py
   ```

3. **访问系统**
   - 系统会自动打开浏览器
   - 或手动访问: http://localhost:8000/login.html
   - API服务: http://localhost:5000

### 方法二：分别启动

1. **启动NER API服务**
   ```bash
   python ner_api.py
   ```

2. **启动Web服务器**
   ```bash
   python -m http.server 8000
   ```

3. **访问登录页面**
   - 打开浏览器访问 http://localhost:8000/login.html

### 使用系统

1. **登录系统**
   - 使用下方测试账号登录
   - 或点击页面提示直接填入

2. **访问功能模块**
   - 登录后自动跳转到管理页面
   - 点击侧边栏访问各个功能
   - 新增的"NER数据管理"模块用于生成训练数据

## 功能说明

### 登录系统
- ✅ 支持用户名/密码登录
- ✅ 记住登录状态功能
- ✅ 登录状态检查
- ✅ 自动跳转功能
- ✅ 错误提示和验证

### 后台管理
- ✅ 侧边栏导航
- ✅ 响应式布局
- ✅ 用户信息显示
- ✅ 退出登录功能
- ✅ 页面权限控制

### 特殊功能
- 🔥 **测试账号提示**: 登录失败后会显示可用的测试账号
- 🔥 **一键填入**: 点击测试账号可自动填入登录表单
- 🔥 **登录状态保持**: 刷新页面不会丢失登录状态
- 🔥 **快捷键支持**: Ctrl+H 显示/隐藏测试账号提示

## 文件结构

```
├── login.html          # 登录页面
├── login.css           # 登录页面样式
├── login.js            # 登录页面逻辑
├── index.html          # 后台管理主页
├── styles.css          # 后台管理样式
├── app.js              # 后台管理逻辑
└── README.md           # 说明文档
```

## 技术栈

- **前端**: HTML5, CSS3, JavaScript (ES6+)
- **样式**: CSS Variables, Flexbox, Grid
- **图标**: Font Awesome 6.4.0
- **字体**: Inter (Google Fonts)
- **存储**: LocalStorage

## 浏览器支持

- Chrome 60+
- Firefox 60+
- Safari 12+
- Edge 79+

## 开发说明

### 添加新的测试账号

在 `login.js` 文件中的 `TEST_ACCOUNTS` 数组中添加新账号：

```javascript
const TEST_ACCOUNTS = [
    // 现有账号...
    {
        username: 'newuser',
        password: 'newpass123',
        role: '新角色',
        name: '新用户'
    }
];
```

### 自定义登录验证

修改 `login.js` 中的 `handleLogin` 函数来实现自定义验证逻辑。

### 添加新的管理页面

1. 在 `index.html` 中添加新的页面内容
2. 在侧边栏导航中添加对应的菜单项
3. 在 `app.js` 中添加相应的页面切换逻辑

## 注意事项

- 🔒 这是一个前端演示系统，实际生产环境需要后端API支持
- 🔒 密码验证仅在前端进行，实际应用需要后端安全验证
- 🔒 用户数据存储在浏览器本地，清除浏览器数据会丢失登录状态

## 更新日志

### v1.0.0 (2024-01-15)
- ✅ 完成基础登录系统
- ✅ 添加多个测试账号
- ✅ 实现后台管理界面
- ✅ 添加用户菜单和退出功能
- ✅ 完善响应式设计

---

**提示**: 如果遇到任何问题，请检查浏览器控制台的错误信息，或者尝试清除浏览器缓存后重新访问。
