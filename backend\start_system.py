#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
现代化企业级管理系统启动脚本
启动HTTP服务器提供前端页面，支持五大核心功能：
- 高级数据分析
- 实时通信系统
- 高级安全中心
- 高级工作流引擎
- 智能助手系统
"""

import os
import sys
import time
import threading
import subprocess
import webbrowser
import json
from http.server import HTTPServer, SimpleHTTPRequestHandler
from socketserver import ThreadingMixIn
from datetime import datetime

class ThreadingHTTPServer(ThreadingMixIn, HTTPServer):
    """支持多线程的HTTP服务器"""
    daemon_threads = True

def create_system_status():
    """创建系统状态文件"""
    status = {
        "system_name": "现代化企业级管理系统",
        "version": "2.0.0",
        "startup_time": datetime.now().isoformat(),
        "status": "running",
        "features": {
            "advanced_analytics": {"status": "active", "description": "高级数据分析"},
            "realtime_communication": {"status": "active", "description": "实时通信系统"},
            "security_center": {"status": "active", "description": "高级安全中心"},
            "workflow_engine": {"status": "active", "description": "高级工作流引擎"},
            "ai_assistant": {"status": "active", "description": "智能助手系统"}
        },
        "endpoints": {
            "frontend": "http://localhost:8000",
            "login": "http://localhost:8000/login.html",
            "dashboard": "http://localhost:8000/index.html"
        }
    }

    with open('system_status.json', 'w', encoding='utf-8') as f:
        json.dump(status, f, ensure_ascii=False, indent=2)

def start_web_server():
    """启动Web服务器"""
    print("🌐 启动现代化企业级管理系统Web服务器...")

    class CustomHandler(SimpleHTTPRequestHandler):
        def end_headers(self):
            # 添加CORS头部和安全头部
            self.send_header('Access-Control-Allow-Origin', '*')
            self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS, PUT, DELETE')
            self.send_header('Access-Control-Allow-Headers', 'Content-Type, Authorization, X-Requested-With')
            self.send_header('X-Content-Type-Options', 'nosniff')
            self.send_header('X-Frame-Options', 'DENY')
            self.send_header('X-XSS-Protection', '1; mode=block')
            super().end_headers()

        def do_OPTIONS(self):
            # 处理预检请求
            self.send_response(200)
            self.end_headers()

        def log_message(self, format, *args):
            # 记录访问日志
            timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            print(f"[{timestamp}] {format % args}")

    try:
        server = ThreadingHTTPServer(('localhost', 8000), CustomHandler)
        print(f"✅ 前端服务器已启动: http://localhost:8000")
        print(f"� 登录页面: http://localhost:8000/login.html")
        print(f"� 管理仪表板: http://localhost:8000/index.html")
        server.serve_forever()
    except KeyboardInterrupt:
        print("\n⏹️  Web服务器已停止")
    except Exception as e:
        print(f"❌ Web服务器启动失败: {e}")

def check_dependencies():
    """检查系统依赖项"""
    print("🔍 检查现代化企业级管理系统依赖...")

    # 核心文件检查
    core_files = [
        'index.html',
        'login.html',
        'modern-theme.css',
        'app.js'
    ]

    # 功能模块文件检查
    feature_files = [
        'advanced-analytics.js',
        'realtime-communication.js',
        'security-center.js',
        'workflow-engine.js',
        'ai-assistant.js'
    ]

    all_files = core_files + feature_files
    missing_files = []

    for file in all_files:
        if not os.path.exists(file):
            missing_files.append(file)

    if missing_files:
        print("❌ 缺少以下关键文件:")
        for file in missing_files:
            file_type = "核心文件" if file in core_files else "功能模块"
            print(f"   - {file} ({file_type})")
        return False

    print("✅ 核心文件检查通过")
    print("✅ 功能模块检查通过")
    print("✅ 所有依赖检查通过")
    return True

def print_system_info():
    """打印系统信息"""
    print("=" * 80)
    print("🚀 现代化企业级管理系统 v2.0.0")
    print("=" * 80)
    print("📊 核心功能模块:")
    print("   ✅ 高级数据分析 - 多维度数据分析和可视化")
    print("   ✅ 实时通信系统 - 即时消息、视频通话、协作功能")
    print("   ✅ 高级安全中心 - 威胁检测、合规管理、安全策略")
    print("   ✅ 高级工作流引擎 - 可视化流程设计、任务管理、系统集成")
    print("   ✅ 智能助手系统 - 对话交互、智能问答、数据洞察、学习能力")
    print()
    print("🔧 技术架构:")
    print("   • 前端: HTML5, CSS3, JavaScript ES6+, WebRTC, Canvas")
    print("   • 样式: 现代化响应式设计, 毛玻璃效果, 动画过渡")
    print("   • 功能: 模块化架构, 实时通信, AI助手, 工作流引擎")
    print()
    print("🌐 访问地址:")
    print("   • 系统首页: http://localhost:8000")
    print("   • 登录页面: http://localhost:8000/login.html")
    print("   • 管理仪表板: http://localhost:8000/index.html")
    print()
    print("👤 测试账号:")
    print("   • admin / admin123 (系统管理员)")
    print("   • manager / manager123 (部门经理)")
    print("   • user / user123 (普通用户)")
    print("=" * 80)

def main():
    """主函数"""
    print_system_info()

    # 检查依赖
    if not check_dependencies():
        print("\n❌ 依赖检查失败，请解决上述问题后重试")
        sys.exit(1)

    # 创建系统状态文件
    create_system_status()

    print("\n🎯 启动现代化企业级管理系统...")
    print()

    try:
        print("🔧 正在初始化系统组件...")
        print("   � 高级数据分析模块")
        print("   💬 实时通信系统")
        print("   🔐 高级安全中心")
        print("   🔄 高级工作流引擎")
        print("   🤖 智能助手系统")
        print()

        # 自动打开浏览器到登录页面
        print("🌐 正在打开浏览器...")
        try:
            webbrowser.open('http://localhost:8000/login.html')
            print("✅ 浏览器已自动打开")
        except:
            print("⚠️  无法自动打开浏览器，请手动访问 http://localhost:8000/login.html")

        print()
        print("💡 使用指南:")
        print("   1. 使用测试账号登录系统")
        print("   2. 探索五大核心功能模块")
        print("   3. 体验现代化的企业级管理功能")
        print("   4. 按 Ctrl+C 停止系统")
        print()
        print("📊 系统运行中... (按 Ctrl+C 停止)")
        print("=" * 80)

        # 启动Web服务器（主线程）
        start_web_server()

    except KeyboardInterrupt:
        print("\n\n⏹️  正在停止系统...")
        print("👋 感谢使用现代化企业级管理系统！")

        # 清理系统状态文件
        if os.path.exists('system_status.json'):
            os.remove('system_status.json')
            print("🧹 系统状态已清理")

    except Exception as e:
        print(f"\n❌ 系统启动失败: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
