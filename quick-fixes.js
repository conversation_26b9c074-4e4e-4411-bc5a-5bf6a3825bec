/**
 * 快速修复脚本
 * 解决项目中的常见问题和错误
 */

(function() {
    'use strict';

    // 等待DOM加载完成
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initQuickFixes);
    } else {
        initQuickFixes();
    }

    function initQuickFixes() {
        console.log('开始应用快速修复...');
        
        // 修复1: 确保Chart.js正确加载
        fixChartJS();
        
        // 修复2: 修复模态框问题
        fixModalIssues();
        
        // 修复3: 修复导航问题
        fixNavigationIssues();
        
        // 修复4: 修复表单验证
        fixFormValidation();
        
        // 修复5: 修复响应式问题
        fixResponsiveIssues();
        
        // 修复6: 添加缺失的事件监听器
        addMissingEventListeners();
        
        // 修复7: 修复CSS问题
        fixCSSIssues();
        
        console.log('快速修复应用完成');
    }

    // 修复Chart.js加载问题
    function fixChartJS() {
        if (typeof Chart === 'undefined') {
            console.warn('Chart.js未加载，尝试动态加载...');
            
            const script = document.createElement('script');
            script.src = 'https://cdn.jsdelivr.net/npm/chart.js';
            script.onload = function() {
                console.log('Chart.js动态加载成功');
                // 重新初始化图表
                if (window.initializeCharts) {
                    window.initializeCharts();
                }
            };
            script.onerror = function() {
                console.error('Chart.js动态加载失败');
            };
            document.head.appendChild(script);
        }
    }

    // 修复模态框问题
    function fixModalIssues() {
        // 确保所有模态框都有正确的关闭功能
        const modals = document.querySelectorAll('[id*="Modal"]');
        modals.forEach(modal => {
            // 添加ESC键关闭功能
            if (!modal.hasAttribute('data-esc-fixed')) {
                document.addEventListener('keydown', function(e) {
                    if (e.key === 'Escape' && modal.style.display === 'block') {
                        modal.style.display = 'none';
                    }
                });
                modal.setAttribute('data-esc-fixed', 'true');
            }

            // 添加点击背景关闭功能
            if (!modal.hasAttribute('data-backdrop-fixed')) {
                modal.addEventListener('click', function(e) {
                    if (e.target === modal) {
                        modal.style.display = 'none';
                    }
                });
                modal.setAttribute('data-backdrop-fixed', 'true');
            }

            // 确保关闭按钮工作
            const closeButtons = modal.querySelectorAll('.close, .btn-close, [data-dismiss="modal"]');
            closeButtons.forEach(btn => {
                if (!btn.hasAttribute('data-close-fixed')) {
                    btn.addEventListener('click', function() {
                        modal.style.display = 'none';
                    });
                    btn.setAttribute('data-close-fixed', 'true');
                }
            });
        });
    }

    // 修复导航问题
    function fixNavigationIssues() {
        const navItems = document.querySelectorAll('.nav-item');
        navItems.forEach(item => {
            if (!item.hasAttribute('data-nav-fixed')) {
                item.addEventListener('click', function(e) {
                    e.preventDefault();
                    
                    // 移除所有活动状态
                    navItems.forEach(nav => nav.classList.remove('active'));
                    
                    // 添加当前活动状态
                    this.classList.add('active');
                    
                    // 显示对应页面
                    const targetPage = this.getAttribute('data-page');
                    if (targetPage) {
                        showPage(targetPage);
                    }
                });
                item.setAttribute('data-nav-fixed', 'true');
            }
        });
    }

    // 显示页面函数
    function showPage(pageId) {
        // 隐藏所有页面
        const pages = document.querySelectorAll('.page-content');
        pages.forEach(page => {
            page.style.display = 'none';
        });

        // 显示目标页面
        const targetPage = document.getElementById(pageId);
        if (targetPage) {
            targetPage.style.display = 'block';
        }

        // 更新面包屑
        updateBreadcrumb(pageId);
    }

    // 更新面包屑
    function updateBreadcrumb(pageId) {
        const breadcrumb = document.querySelector('.breadcrumb-current');
        if (breadcrumb) {
            const pageNames = {
                'dashboard': '仪表盘',
                'users': '用户管理',
                'analytics': '数据分析',
                'orders': '订单管理',
                'inventory': '库存管理',
                'reports': '报表中心',
                'files': '文件管理',
                'permissions': '权限管理',
                'notifications': '通知中心',
                'settings': '系统设置',
                'help': '帮助中心'
            };
            breadcrumb.textContent = pageNames[pageId] || '未知页面';
        }
    }

    // 修复表单验证
    function fixFormValidation() {
        const forms = document.querySelectorAll('form');
        forms.forEach(form => {
            if (!form.hasAttribute('data-validation-fixed')) {
                form.addEventListener('submit', function(e) {
                    const requiredFields = form.querySelectorAll('[required]');
                    let isValid = true;

                    requiredFields.forEach(field => {
                        if (!field.value.trim()) {
                            isValid = false;
                            field.classList.add('error');
                            
                            // 添加错误提示
                            let errorMsg = field.parentNode.querySelector('.error-message');
                            if (!errorMsg) {
                                errorMsg = document.createElement('div');
                                errorMsg.className = 'error-message';
                                errorMsg.style.color = '#ef4444';
                                errorMsg.style.fontSize = '12px';
                                errorMsg.style.marginTop = '4px';
                                field.parentNode.appendChild(errorMsg);
                            }
                            errorMsg.textContent = '此字段为必填项';
                        } else {
                            field.classList.remove('error');
                            const errorMsg = field.parentNode.querySelector('.error-message');
                            if (errorMsg) {
                                errorMsg.remove();
                            }
                        }
                    });

                    if (!isValid) {
                        e.preventDefault();
                        if (window.showNotification) {
                            window.showNotification('请填写所有必填字段', 'error');
                        }
                    }
                });
                form.setAttribute('data-validation-fixed', 'true');
            }
        });
    }

    // 修复响应式问题
    function fixResponsiveIssues() {
        // 添加移动端菜单切换功能
        const menuToggle = document.querySelector('.menu-toggle');
        const sidebar = document.querySelector('.sidebar');
        
        if (menuToggle && sidebar && !menuToggle.hasAttribute('data-toggle-fixed')) {
            menuToggle.addEventListener('click', function() {
                sidebar.classList.toggle('collapsed');
            });
            menuToggle.setAttribute('data-toggle-fixed', 'true');
        }

        // 修复表格在小屏幕上的显示
        const tables = document.querySelectorAll('table');
        tables.forEach(table => {
            if (!table.parentNode.classList.contains('table-responsive')) {
                const wrapper = document.createElement('div');
                wrapper.className = 'table-responsive';
                table.parentNode.insertBefore(wrapper, table);
                wrapper.appendChild(table);
            }
        });
    }

    // 添加缺失的事件监听器
    function addMissingEventListeners() {
        // 用户菜单切换
        const userProfile = document.querySelector('.user-profile');
        const userMenu = document.querySelector('.user-menu');
        
        if (userProfile && userMenu && !userProfile.hasAttribute('data-menu-fixed')) {
            userProfile.addEventListener('click', function(e) {
                e.stopPropagation();
                userMenu.classList.toggle('show');
            });

            // 点击其他地方关闭菜单
            document.addEventListener('click', function() {
                userMenu.classList.remove('show');
            });

            userProfile.setAttribute('data-menu-fixed', 'true');
        }

        // 搜索功能
        const searchInputs = document.querySelectorAll('input[type="search"], .search-input');
        searchInputs.forEach(input => {
            if (!input.hasAttribute('data-search-fixed')) {
                input.addEventListener('input', function() {
                    const searchTerm = this.value.toLowerCase();
                    const targetTable = this.closest('.page-content').querySelector('table tbody');
                    
                    if (targetTable) {
                        const rows = targetTable.querySelectorAll('tr');
                        rows.forEach(row => {
                            const text = row.textContent.toLowerCase();
                            row.style.display = text.includes(searchTerm) ? '' : 'none';
                        });
                    }
                });
                input.setAttribute('data-search-fixed', 'true');
            }
        });
    }

    // 修复CSS问题
    function fixCSSIssues() {
        // 添加缺失的样式
        const style = document.createElement('style');
        style.textContent = `
            /* 修复模态框层级问题 */
            .modal {
                z-index: 1050 !important;
            }
            
            /* 修复表单错误状态 */
            .error {
                border-color: #ef4444 !important;
                box-shadow: 0 0 0 2px rgba(239, 68, 68, 0.1) !important;
            }
            
            /* 修复响应式表格 */
            .table-responsive {
                overflow-x: auto;
                -webkit-overflow-scrolling: touch;
            }
            
            /* 修复用户菜单 */
            .user-menu.show {
                display: block !important;
            }
            
            /* 修复加载状态 */
            .btn.loading {
                opacity: 0.6;
                pointer-events: none;
            }
            
            .btn.loading::after {
                content: '';
                display: inline-block;
                width: 12px;
                height: 12px;
                margin-left: 8px;
                border: 2px solid transparent;
                border-top: 2px solid currentColor;
                border-radius: 50%;
                animation: spin 1s linear infinite;
            }
            
            @keyframes spin {
                to { transform: rotate(360deg); }
            }
        `;
        document.head.appendChild(style);
    }

    // 导出到全局作用域
    window.quickFixes = {
        fixChartJS,
        fixModalIssues,
        fixNavigationIssues,
        fixFormValidation,
        fixResponsiveIssues,
        addMissingEventListeners,
        fixCSSIssues,
        showPage,
        updateBreadcrumb
    };

})();
