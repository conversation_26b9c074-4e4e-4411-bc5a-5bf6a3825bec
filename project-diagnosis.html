<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>项目诊断工具</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .diagnosis-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 16px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .diagnosis-header {
            background: linear-gradient(135deg, #ef4444, #dc2626);
            color: white;
            padding: 24px;
            text-align: center;
        }
        
        .diagnosis-header h1 {
            font-size: 28px;
            margin-bottom: 8px;
        }
        
        .diagnosis-content {
            padding: 24px;
        }
        
        .test-section {
            margin-bottom: 32px;
            border: 1px solid #e2e8f0;
            border-radius: 12px;
            overflow: hidden;
        }
        
        .section-header {
            background: #f8fafc;
            padding: 16px 20px;
            border-bottom: 1px solid #e2e8f0;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
        
        .section-title {
            font-weight: 600;
            color: #1e293b;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .section-content {
            padding: 20px;
        }
        
        .test-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 12px 0;
            border-bottom: 1px solid #f1f5f9;
        }
        
        .test-item:last-child {
            border-bottom: none;
        }
        
        .test-name {
            font-weight: 500;
            color: #374151;
        }
        
        .test-status {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
            text-transform: uppercase;
        }
        
        .status-pass {
            background: #dcfce7;
            color: #166534;
        }
        
        .status-fail {
            background: #fef2f2;
            color: #dc2626;
        }
        
        .status-warning {
            background: #fef3c7;
            color: #d97706;
        }
        
        .status-running {
            background: #dbeafe;
            color: #2563eb;
        }
        
        .run-all-btn {
            background: linear-gradient(135deg, #6366f1, #818cf8);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s;
        }
        
        .run-all-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 16px rgba(99, 102, 241, 0.3);
        }
        
        .error-details {
            background: #fef2f2;
            border: 1px solid #fecaca;
            border-radius: 8px;
            padding: 16px;
            margin-top: 16px;
            display: none;
        }
        
        .error-details.show {
            display: block;
        }
        
        .error-title {
            font-weight: 600;
            color: #dc2626;
            margin-bottom: 8px;
        }
        
        .error-message {
            color: #7f1d1d;
            font-size: 14px;
            line-height: 1.5;
        }
        
        .progress-bar {
            width: 100%;
            height: 4px;
            background: #f1f5f9;
            border-radius: 2px;
            overflow: hidden;
            margin-top: 16px;
        }
        
        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #6366f1, #818cf8);
            width: 0%;
            transition: width 0.3s ease;
        }
        
        .summary-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 16px;
            margin-bottom: 24px;
        }
        
        .stat-card {
            background: #f8fafc;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            padding: 16px;
            text-align: center;
        }
        
        .stat-number {
            font-size: 24px;
            font-weight: 700;
            margin-bottom: 4px;
        }
        
        .stat-label {
            font-size: 12px;
            color: #64748b;
            text-transform: uppercase;
            font-weight: 600;
        }
        
        .stat-card.pass .stat-number { color: #16a34a; }
        .stat-card.fail .stat-number { color: #dc2626; }
        .stat-card.warning .stat-number { color: #d97706; }
        .stat-card.total .stat-number { color: #6366f1; }
    </style>
</head>
<body>
    <div class="diagnosis-container">
        <div class="diagnosis-header">
            <h1><i class="fas fa-stethoscope"></i> 项目诊断工具</h1>
            <p>检测项目中存在的问题和错误</p>
        </div>
        
        <div class="diagnosis-content">
            <!-- 统计概览 -->
            <div class="summary-stats">
                <div class="stat-card total">
                    <div class="stat-number" id="totalTests">0</div>
                    <div class="stat-label">总测试项</div>
                </div>
                <div class="stat-card pass">
                    <div class="stat-number" id="passedTests">0</div>
                    <div class="stat-label">通过</div>
                </div>
                <div class="stat-card fail">
                    <div class="stat-number" id="failedTests">0</div>
                    <div class="stat-label">失败</div>
                </div>
                <div class="stat-card warning">
                    <div class="stat-number" id="warningTests">0</div>
                    <div class="stat-label">警告</div>
                </div>
            </div>
            
            <!-- 进度条 -->
            <div class="progress-bar">
                <div class="progress-fill" id="progressFill"></div>
            </div>
            
            <!-- 文件存在性检查 -->
            <div class="test-section">
                <div class="section-header">
                    <div class="section-title">
                        <i class="fas fa-file-check"></i>
                        文件存在性检查
                    </div>
                    <button class="run-all-btn" onclick="runAllTests()">
                        <i class="fas fa-play"></i> 运行所有测试
                    </button>
                </div>
                <div class="section-content" id="fileTests">
                    <!-- 测试项将通过JavaScript生成 -->
                </div>
            </div>
            
            <!-- JavaScript错误检查 -->
            <div class="test-section">
                <div class="section-header">
                    <div class="section-title">
                        <i class="fas fa-code"></i>
                        JavaScript错误检查
                    </div>
                </div>
                <div class="section-content" id="jsTests">
                    <!-- 测试项将通过JavaScript生成 -->
                </div>
            </div>
            
            <!-- CSS样式检查 -->
            <div class="test-section">
                <div class="section-header">
                    <div class="section-title">
                        <i class="fas fa-palette"></i>
                        CSS样式检查
                    </div>
                </div>
                <div class="section-content" id="cssTests">
                    <!-- 测试项将通过JavaScript生成 -->
                </div>
            </div>
            
            <!-- 功能测试 -->
            <div class="test-section">
                <div class="section-header">
                    <div class="section-title">
                        <i class="fas fa-cogs"></i>
                        功能测试
                    </div>
                </div>
                <div class="section-content" id="functionTests">
                    <!-- 测试项将通过JavaScript生成 -->
                </div>
            </div>
        </div>
    </div>

    <script>
        // 诊断测试配置
        const diagnosticTests = {
            files: [
                { name: 'index.html', path: 'index.html', required: true },
                { name: 'login.html', path: 'login.html', required: true },
                { name: 'styles.css', path: 'styles.css', required: true },
                { name: 'login.css', path: 'login.css', required: true },
                { name: 'app.js', path: 'app.js', required: true },
                { name: 'login.js', path: 'login.js', required: true },
                { name: 'user-management.js', path: 'user-management.js', required: true },
                { name: 'analytics.js', path: 'analytics.js', required: true },
                { name: 'order-management.js', path: 'order-management.js', required: true },
                { name: 'settings.js', path: 'settings.js', required: true },
                { name: 'notifications.js', path: 'notifications.js', required: true },
                { name: 'help-center.js', path: 'help-center.js', required: true },
                { name: 'inventory-management.js', path: 'inventory-management.js', required: true },
                { name: 'reports-management.js', path: 'reports-management.js', required: true },
                { name: 'file-management.js', path: 'file-management.js', required: true },
                { name: 'permission-management.js', path: 'permission-management.js', required: true },
                { name: 'ui-enhancements.js', path: 'ui-enhancements.js', required: true },
                { name: 'data-visualization.js', path: 'data-visualization.js', required: true }
            ],
            javascript: [
                { name: '登录函数存在性', test: () => typeof handleLogin === 'function' },
                { name: '用户管理函数', test: () => typeof showAddUserModal === 'function' },
                { name: '通知系统', test: () => typeof showNotification === 'function' },
                { name: 'Chart.js加载', test: () => typeof Chart !== 'undefined' },
                { name: 'localStorage支持', test: () => typeof Storage !== 'undefined' }
            ],
            css: [
                { name: '主样式表加载', test: () => checkStylesheet('styles.css') },
                { name: '登录样式表加载', test: () => checkStylesheet('login.css') },
                { name: 'Font Awesome图标', test: () => checkFontAwesome() }
            ],
            functions: [
                { name: '页面导航功能', test: () => testNavigation() },
                { name: '用户菜单功能', test: () => testUserMenu() },
                { name: '模态框功能', test: () => testModals() },
                { name: '表单验证', test: () => testFormValidation() }
            ]
        };

        let testResults = {
            total: 0,
            passed: 0,
            failed: 0,
            warnings: 0
        };

        // 运行所有测试
        async function runAllTests() {
            resetResults();
            
            await runFileTests();
            await runJavaScriptTests();
            await runCSSTests();
            await runFunctionTests();
            
            updateSummary();
        }

        // 重置结果
        function resetResults() {
            testResults = { total: 0, passed: 0, failed: 0, warnings: 0 };
            updateSummary();
        }

        // 更新统计
        function updateSummary() {
            document.getElementById('totalTests').textContent = testResults.total;
            document.getElementById('passedTests').textContent = testResults.passed;
            document.getElementById('failedTests').textContent = testResults.failed;
            document.getElementById('warningTests').textContent = testResults.warnings;
            
            const progress = testResults.total > 0 ? (testResults.passed / testResults.total) * 100 : 0;
            document.getElementById('progressFill').style.width = progress + '%';
        }

        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', function() {
            initializeDiagnostics();
        });

        function initializeDiagnostics() {
            generateFileTests();
            generateJavaScriptTests();
            generateCSSTests();
            generateFunctionTests();
        }

        // 生成文件测试项
        function generateFileTests() {
            const container = document.getElementById('fileTests');
            diagnosticTests.files.forEach(file => {
                const testItem = createTestItem(file.name, 'status-running');
                container.appendChild(testItem);
            });
        }

        // 生成JavaScript测试项
        function generateJavaScriptTests() {
            const container = document.getElementById('jsTests');
            diagnosticTests.javascript.forEach(test => {
                const testItem = createTestItem(test.name, 'status-running');
                container.appendChild(testItem);
            });
        }

        // 生成CSS测试项
        function generateCSSTests() {
            const container = document.getElementById('cssTests');
            diagnosticTests.css.forEach(test => {
                const testItem = createTestItem(test.name, 'status-running');
                container.appendChild(testItem);
            });
        }

        // 生成功能测试项
        function generateFunctionTests() {
            const container = document.getElementById('functionTests');
            diagnosticTests.functions.forEach(test => {
                const testItem = createTestItem(test.name, 'status-running');
                container.appendChild(testItem);
            });
        }

        // 创建测试项元素
        function createTestItem(name, status) {
            const div = document.createElement('div');
            div.className = 'test-item';
            div.innerHTML = `
                <div class="test-name">${name}</div>
                <div class="test-status ${status}" id="status-${name.replace(/\s+/g, '-')}">运行中</div>
            `;
            return div;
        }

        // 运行文件测试
        async function runFileTests() {
            for (const file of diagnosticTests.files) {
                await testFileExists(file);
                await sleep(100); // 添加延迟以显示进度
            }
        }

        // 测试文件是否存在
        async function testFileExists(file) {
            try {
                const response = await fetch(file.path, { method: 'HEAD' });
                const statusId = `status-${file.name.replace(/\s+/g, '-')}`;
                const statusElement = document.getElementById(statusId);

                if (response.ok) {
                    updateTestStatus(statusElement, 'pass', '通过');
                    testResults.passed++;
                } else {
                    updateTestStatus(statusElement, 'fail', '失败');
                    testResults.failed++;
                    addErrorDetails(statusElement.parentElement, `文件 ${file.name} 不存在或无法访问`);
                }
            } catch (error) {
                const statusId = `status-${file.name.replace(/\s+/g, '-')}`;
                const statusElement = document.getElementById(statusId);
                updateTestStatus(statusElement, 'fail', '错误');
                testResults.failed++;
                addErrorDetails(statusElement.parentElement, `检查文件 ${file.name} 时出错: ${error.message}`);
            }
            testResults.total++;
        }

        // 运行JavaScript测试
        async function runJavaScriptTests() {
            for (const test of diagnosticTests.javascript) {
                await runJSTest(test);
                await sleep(100);
            }
        }

        // 运行单个JS测试
        async function runJSTest(test) {
            const statusId = `status-${test.name.replace(/\s+/g, '-')}`;
            const statusElement = document.getElementById(statusId);

            try {
                const result = test.test();
                if (result) {
                    updateTestStatus(statusElement, 'pass', '通过');
                    testResults.passed++;
                } else {
                    updateTestStatus(statusElement, 'warning', '警告');
                    testResults.warnings++;
                    addErrorDetails(statusElement.parentElement, `${test.name} 功能可能未正确加载`);
                }
            } catch (error) {
                updateTestStatus(statusElement, 'fail', '失败');
                testResults.failed++;
                addErrorDetails(statusElement.parentElement, `测试 ${test.name} 时出错: ${error.message}`);
            }
            testResults.total++;
        }

        // 运行CSS测试
        async function runCSSTests() {
            for (const test of diagnosticTests.css) {
                await runCSSTest(test);
                await sleep(100);
            }
        }

        // 运行单个CSS测试
        async function runCSSTest(test) {
            const statusId = `status-${test.name.replace(/\s+/g, '-')}`;
            const statusElement = document.getElementById(statusId);

            try {
                const result = test.test();
                if (result) {
                    updateTestStatus(statusElement, 'pass', '通过');
                    testResults.passed++;
                } else {
                    updateTestStatus(statusElement, 'warning', '警告');
                    testResults.warnings++;
                    addErrorDetails(statusElement.parentElement, `${test.name} 可能未正确加载`);
                }
            } catch (error) {
                updateTestStatus(statusElement, 'fail', '失败');
                testResults.failed++;
                addErrorDetails(statusElement.parentElement, `测试 ${test.name} 时出错: ${error.message}`);
            }
            testResults.total++;
        }

        // 运行功能测试
        async function runFunctionTests() {
            for (const test of diagnosticTests.functions) {
                await runFunctionTest(test);
                await sleep(100);
            }
        }

        // 运行单个功能测试
        async function runFunctionTest(test) {
            const statusId = `status-${test.name.replace(/\s+/g, '-')}`;
            const statusElement = document.getElementById(statusId);

            try {
                const result = test.test();
                if (result) {
                    updateTestStatus(statusElement, 'pass', '通过');
                    testResults.passed++;
                } else {
                    updateTestStatus(statusElement, 'warning', '警告');
                    testResults.warnings++;
                    addErrorDetails(statusElement.parentElement, `${test.name} 功能可能存在问题`);
                }
            } catch (error) {
                updateTestStatus(statusElement, 'fail', '失败');
                testResults.failed++;
                addErrorDetails(statusElement.parentElement, `测试 ${test.name} 时出错: ${error.message}`);
            }
            testResults.total++;
        }

        // 更新测试状态
        function updateTestStatus(element, status, text) {
            element.className = `test-status status-${status}`;
            element.textContent = text;
        }

        // 添加错误详情
        function addErrorDetails(parentElement, message) {
            const errorDiv = document.createElement('div');
            errorDiv.className = 'error-details show';
            errorDiv.innerHTML = `
                <div class="error-title">错误详情</div>
                <div class="error-message">${message}</div>
            `;
            parentElement.appendChild(errorDiv);
        }

        // 辅助函数
        function sleep(ms) {
            return new Promise(resolve => setTimeout(resolve, ms));
        }

        function checkStylesheet(filename) {
            const links = document.getElementsByTagName('link');
            for (let i = 0; i < links.length; i++) {
                if (links[i].href && links[i].href.includes(filename)) {
                    return true;
                }
            }
            return false;
        }

        function checkFontAwesome() {
            const testElement = document.createElement('i');
            testElement.className = 'fas fa-home';
            testElement.style.display = 'none';
            document.body.appendChild(testElement);

            const computed = window.getComputedStyle(testElement);
            const fontFamily = computed.getPropertyValue('font-family');

            document.body.removeChild(testElement);
            return fontFamily.includes('Font Awesome');
        }

        function testNavigation() {
            return document.querySelectorAll('.nav-item').length > 0;
        }

        function testUserMenu() {
            return document.querySelector('.user-profile') !== null;
        }

        function testModals() {
            return document.querySelectorAll('[id*="Modal"]').length > 0;
        }

        function testFormValidation() {
            return document.querySelectorAll('form').length > 0;
        }
    </script>
</body>
</html>
