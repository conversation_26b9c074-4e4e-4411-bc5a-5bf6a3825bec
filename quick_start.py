#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速启动脚本
"""

import subprocess
import time
import webbrowser
import sys
import os

def start_servers():
    print("=" * 50)
    print("🎯 恋雪二游 - 整合系统快速启动")
    print("=" * 50)
    print()
    
    try:
        # 启动前端服务器
        print("🌐 启动前端服务器...")
        frontend_process = subprocess.Popen([
            sys.executable, "-m", "http.server", "8000"
        ], cwd=os.getcwd())
        
        time.sleep(2)
        
        # 启动API服务器
        print("🔧 启动API服务器...")
        api_process = subprocess.Popen([
            sys.executable, "simple_ner_api.py"
        ], cwd=os.getcwd())
        
        time.sleep(3)
        
        print()
        print("✅ 系统启动完成！")
        print()
        print("📱 前端地址: http://localhost:8000/login.html")
        print("🔧 API地址: http://localhost:5000")
        print()
        print("👤 测试账号:")
        print("   用户名: admin")
        print("   密码: admin123")
        print()
        
        # 打开浏览器
        print("🌐 正在打开浏览器...")
        webbrowser.open('http://localhost:8000/login.html')
        
        print()
        print("📊 系统运行中... (按 Ctrl+C 停止)")
        
        # 保持运行
        try:
            while True:
                time.sleep(1)
        except KeyboardInterrupt:
            print("\n⏹️  正在停止服务器...")
            frontend_process.terminate()
            api_process.terminate()
            print("👋 系统已停止")
            
    except Exception as e:
        print(f"❌ 启动失败: {e}")

if __name__ == "__main__":
    start_servers()
