#!/usr/bin/env python3
# -*- coding: utf-8 -*-
import json
import random

def main():
    # 实体词库
    person_names = ['张三', '李四', '王五', '赵六', '陈七', '刘八', '杨九', '黄十', '周明', '吴亮', '马云', '马化腾', '李彦宏', '雷军', '任正非']
    locations = ['北京', '上海', '广州', '深圳', '杭州', '南京', '武汉', '成都', '西安', '重庆', '天津', '青岛', '大连', '厦门', '苏州']
    organizations = ['阿里巴巴', '腾讯', '百度', '字节跳动', '美团', '滴滴', '京东', '网易', '华为', '小米', '清华大学', '北京大学', '工商银行', '中国银行']
    times = ['2023年', '2024年', '今天', '明天', '昨天', '上周', '下周', '上个月', '下个月', '去年', '今年', '明年', '上午', '下午', '晚上']
    numbers = ['100', '200', '300', '500', '1000', '50%', '80%', '90%', '10万', '100万', '1亿', '50.5', '99.9']

    # 句子模板
    templates = [
        '{person}在{location}的{organization}工作了{time}。',
        '{person}于{time}从{location}来到{organization}。',
        '{organization}位于{location}，由{person}创立于{time}。',
        '{location}的{organization}宣布，{person}获得了{number}万元奖金。',
        '{person}计划在{time}访问{location}的{organization}。',
        '{organization}的{person}表示，{time}将在{location}举办活动。',
        '{time}，{person}在{location}成立了{organization}。',
        '{person}从{organization}离职后，于{time}在{location}创业。'
    ]

    dataset = []
    print('开始生成10,000条NER训练数据...')

    for i in range(10000):
        if (i + 1) % 1000 == 0:
            print(f'已生成 {i + 1} 条数据...')
        
        template = random.choice(templates)
        person = random.choice(person_names)
        location = random.choice(locations)
        organization = random.choice(organizations)
        time = random.choice(times)
        number = random.choice(numbers)
        
        sentence = template.format(person=person, location=location, organization=organization, time=time, number=number)
        
        # 创建BIO标签
        labels = ['O'] * len(sentence)
        
        # 标注人名
        start = sentence.find(person)
        if start != -1:
            labels[start] = 'B-PERSON'
            for j in range(start + 1, start + len(person)):
                if j < len(labels):
                    labels[j] = 'I-PERSON'
        
        # 标注地名
        start = sentence.find(location)
        if start != -1:
            labels[start] = 'B-LOCATION'
            for j in range(start + 1, start + len(location)):
                if j < len(labels):
                    labels[j] = 'I-LOCATION'
        
        # 标注组织名
        start = sentence.find(organization)
        if start != -1:
            labels[start] = 'B-ORGANIZATION'
            for j in range(start + 1, start + len(organization)):
                if j < len(labels):
                    labels[j] = 'I-ORGANIZATION'
        
        # 标注时间
        start = sentence.find(time)
        if start != -1:
            labels[start] = 'B-TIME'
            for j in range(start + 1, start + len(time)):
                if j < len(labels):
                    labels[j] = 'I-TIME'
        
        # 标注数字
        start = sentence.find(number)
        if start != -1:
            labels[start] = 'B-NUMBER'
            for j in range(start + 1, start + len(number)):
                if j < len(labels):
                    labels[j] = 'I-NUMBER'
        
        dataset.append({
            'id': i + 1,
            'text': sentence,
            'tokens': list(sentence),
            'labels': labels
        })

    # 保存数据
    with open('ner_training_data.json', 'w', encoding='utf-8') as f:
        json.dump(dataset, f, ensure_ascii=False, indent=2)

    print('数据集已保存到 ner_training_data.json')

    # 统计信息
    entity_counts = {'PERSON': 0, 'LOCATION': 0, 'ORGANIZATION': 0, 'TIME': 0, 'NUMBER': 0}
    sentence_lengths = []
    
    for sample in dataset:
        sentence_lengths.append(len(sample['text']))
        for label in sample['labels']:
            if label.startswith('B-'):
                entity_type = label[2:]
                if entity_type in entity_counts:
                    entity_counts[entity_type] += 1

    avg_length = sum(sentence_lengths) / len(sentence_lengths)
    
    print('\n=== 数据集统计信息 ===')
    print(f'总样本数: {len(dataset)}')
    print(f'平均句子长度: {avg_length:.2f} 字符')
    print('实体类型分布:')
    for entity_type, count in entity_counts.items():
        print(f'  {entity_type}: {count}')

    # 保存统计信息
    stats = {
        'total_samples': len(dataset),
        'avg_sentence_length': avg_length,
        'entity_counts': entity_counts
    }
    
    with open('ner_data_statistics.json', 'w', encoding='utf-8') as f:
        json.dump(stats, f, ensure_ascii=False, indent=2)
    
    print('统计信息已保存到 ner_data_statistics.json')

    # 显示样本
    print('\n=== 样本展示 ===')
    for i in range(3):
        sample = dataset[i]
        print(f'\n样本 {i+1}:')
        print(f'文本: {sample["text"]}')
        print('标签: ', end='')
        for char, label in zip(sample['tokens'], sample['labels']):
            if label != 'O':
                print(f'{char}({label}) ', end='')
        print()

if __name__ == "__main__":
    main()