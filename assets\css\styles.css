/* 现代企业管理系统样式 */

/* 重置和基础样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    /* 颜色系统 - 现代化配色方案 */
    --primary-color: #667eea;
    --primary-light: #7c8aed;
    --primary-dark: #5a6fd8;
    --secondary-color: #764ba2;
    --success-color: #10b981;
    --warning-color: #f59e0b;
    --danger-color: #ef4444;
    --info-color: #3b82f6;

    /* 中性色 - 增强对比度 */
    --gray-50: #fafafa;
    --gray-100: #f4f4f5;
    --gray-200: #e4e4e7;
    --gray-300: #d4d4d8;
    --gray-400: #a1a1aa;
    --gray-500: #71717a;
    --gray-600: #52525b;
    --gray-700: #3f3f46;
    --gray-800: #27272a;
    --gray-900: #18181b;

    /* 文字颜色 */
    --text-primary: #18181b;
    --text-secondary: #3f3f46;
    --text-muted: #71717a;
    --text-light: #a1a1aa;
    
    /* 布局 */
    --sidebar-width: 280px;
    --sidebar-collapsed-width: 80px;
    --header-height: 70px;
    --border-radius: 12px;
    --border-radius-sm: 8px;
    --border-radius-lg: 16px;
    
    /* 阴影 */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    
    /* 过渡 */
    --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-fast: all 0.15s cubic-bezier(0.4, 0, 0.2, 1);

    /* 现代化渐变 - 协调配色 */
    --gradient-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --gradient-secondary: linear-gradient(135deg, #764ba2 0%, #667eea 100%);
    --gradient-success: linear-gradient(135deg, #10b981 0%, #34d399 100%);
    --gradient-warning: linear-gradient(135deg, #f59e0b 0%, #fbbf24 100%);
    --gradient-danger: linear-gradient(135deg, #ef4444 0%, #f87171 100%);
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    background-attachment: fixed;
    color: var(--text-primary);
    line-height: 1.6;
    font-size: 15px;
    font-weight: 400;
    overflow-x: hidden;
    min-height: 100vh;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* 应用容器 */
.app-container {
    display: flex;
    min-height: 100vh;
    position: relative;
    overflow: hidden;
}

/* 背景装饰效果 */
.app-container::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background:
        radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.3) 0%, transparent 50%);
    pointer-events: none;
    z-index: -1;
    animation: backgroundFloat 20s ease-in-out infinite;
}

@keyframes backgroundFloat {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    33% { transform: translateY(-30px) rotate(1deg); }
    66% { transform: translateY(30px) rotate(-1deg); }
}

/* 侧边栏样式 - 现代化设计 */
.sidebar {
    width: var(--sidebar-width);
    background: linear-gradient(145deg, #667eea 0%, #764ba2 100%);
    border-right: none;
    display: flex;
    flex-direction: column;
    position: fixed;
    left: 0;
    top: 0;
    height: 100vh;
    z-index: 1000;
    transition: var(--transition);
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
    backdrop-filter: blur(20px);
}

.sidebar.collapsed {
    width: var(--sidebar-collapsed-width);
}

.sidebar-header {
    padding: 32px 24px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    display: flex;
    align-items: center;
    justify-content: space-between;
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(10px);
}

.logo {
    display: flex;
    align-items: center;
    gap: 12px;
    font-weight: 700;
    font-size: 18px;
    color: var(--gray-900);
}

.logo-icon {
    width: 40px;
    height: 40px;
    background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
    border-radius: var(--border-radius);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 18px;
}

.logo-text {
    font-size: 22px;
    font-weight: 700;
    color: white;
    margin-left: 12px;
    transition: var(--transition);
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.sidebar.collapsed .logo-text {
    opacity: 0;
    width: 0;
    overflow: hidden;
}

.sidebar-toggle {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    padding: 10px;
    border-radius: var(--border-radius-sm);
    color: white;
    cursor: pointer;
    transition: var(--transition);
    backdrop-filter: blur(10px);
}

.sidebar-toggle:hover {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    transform: scale(1.05);
}

/* 导航菜单 */
.nav-menu {
    flex: 1;
    padding: 20px 0;
    overflow-y: auto;
}

.nav-section {
    margin-bottom: 32px;
}

.nav-section-title {
    font-size: 11px;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 1px;
    color: rgba(255, 255, 255, 0.6);
    padding: 0 24px 12px;
    display: block;
    transition: var(--transition);
}

.sidebar.collapsed .nav-section-title {
    opacity: 0;
    height: 0;
    padding: 0;
    margin: 0;
    overflow: hidden;
}

.nav-item {
    display: flex;
    align-items: center;
    padding: 16px 24px;
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    margin: 4px 16px;
    border-radius: 12px;
    font-weight: 500;
    font-size: 14px;
    backdrop-filter: blur(10px);
}

.nav-item:hover {
    background: rgba(255, 255, 255, 0.15);
    color: white;
    transform: translateX(4px) scale(1.02);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.nav-item.active {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.25), rgba(255, 255, 255, 0.1));
    color: white;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
    font-weight: 600;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.nav-item i {
    width: 20px;
    font-size: 16px;
    margin-right: 12px;
    text-align: center;
}

.nav-item span {
    transition: var(--transition);
}

.sidebar.collapsed .nav-item span {
    opacity: 0;
    width: 0;
    overflow: hidden;
}

.nav-indicator {
    position: absolute;
    right: 12px;
    width: 6px;
    height: 6px;
    background-color: currentColor;
    border-radius: 50%;
    opacity: 0;
    transition: var(--transition);
}

.nav-item.active .nav-indicator {
    opacity: 1;
}

/* 侧边栏底部 */
.sidebar-footer {
    padding: 24px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(10px);
}

.user-card {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 16px;
    border-radius: 12px;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: var(--transition);
    backdrop-filter: blur(10px);
}

.user-card:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: scale(1.02);
}

.user-avatar {
    position: relative;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    overflow: hidden;
    flex-shrink: 0;
}

.user-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.status-indicator {
    position: absolute;
    bottom: 2px;
    right: 2px;
    width: 10px;
    height: 10px;
    border-radius: 50%;
    border: 2px solid white;
}

.status-indicator.online {
    background-color: var(--success-color);
}

.user-info {
    flex: 1;
    min-width: 0;
    transition: var(--transition);
}

.sidebar.collapsed .user-info {
    opacity: 0;
    width: 0;
    overflow: hidden;
}

.user-name {
    display: block;
    font-weight: 600;
    color: white;
    font-size: 14px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.user-role {
    display: block;
    font-size: 12px;
    color: rgba(255, 255, 255, 0.7);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.user-menu-btn {
    background: none;
    border: none;
    padding: 8px;
    border-radius: var(--border-radius-sm);
    color: var(--gray-500);
    cursor: pointer;
    transition: var(--transition);
}

.user-menu-btn:hover {
    background-color: var(--gray-200);
    color: var(--gray-700);
}

.sidebar.collapsed .user-menu-btn {
    opacity: 0;
    width: 0;
    overflow: hidden;
}

/* 主内容区域 */
.main-content {
    flex: 1;
    margin-left: var(--sidebar-width);
    display: flex;
    flex-direction: column;
    min-height: 100vh;
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    transition: var(--transition);
}

.sidebar.collapsed + .main-content {
    margin-left: var(--sidebar-collapsed-width);
}

/* 顶部导航栏 */
.top-header {
    height: var(--header-height);
    background: rgba(255, 255, 255, 0.95);
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 32px;
    position: sticky;
    top: 0;
    z-index: 100;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(20px);
}

.header-left {
    display: flex;
    align-items: center;
    gap: 20px;
}

.mobile-menu-btn {
    display: none;
    background: none;
    border: none;
    padding: 8px;
    border-radius: var(--border-radius-sm);
    color: var(--gray-600);
    cursor: pointer;
    transition: var(--transition);
}

.mobile-menu-btn:hover {
    background-color: var(--gray-100);
    color: var(--gray-900);
}

.breadcrumb {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 13px;
}

.breadcrumb-item {
    color: var(--gray-600);
}

.breadcrumb-item.current {
    color: var(--gray-900);
    font-weight: 600;
}

.breadcrumb i {
    color: var(--gray-400);
    font-size: 10px;
}

.header-right {
    display: flex;
    align-items: center;
    gap: 20px;
}

.header-actions {
    display: flex;
    align-items: center;
    gap: 8px;
}

.action-btn {
    width: 40px;
    height: 40px;
    border: none;
    background: none;
    border-radius: var(--border-radius);
    color: var(--gray-600);
    cursor: pointer;
    transition: var(--transition);
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
}

.action-btn:hover {
    background-color: var(--gray-100);
    color: var(--gray-900);
}

.notification-badge {
    position: absolute;
    top: 8px;
    right: 8px;
    background-color: var(--danger-color);
    color: white;
    font-size: 10px;
    font-weight: 600;
    padding: 2px 6px;
    border-radius: 10px;
    min-width: 16px;
    text-align: center;
}

.user-profile {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 8px 12px;
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: var(--transition);
    position: relative;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(10px);
}

.user-profile:hover {
    background: rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.3);
}

/* 右上角用户下拉菜单 */
.header-user-dropdown {
    position: absolute;
    top: 100%;
    right: 0;
    width: 280px;
    background: rgba(255, 255, 255, 0.95);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: var(--border-radius-lg);
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    backdrop-filter: blur(20px);
    margin-top: 8px;
}

.header-user-dropdown.show {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.user-profile .user-avatar {
    width: 32px;
    height: 32px;
}

.user-profile .user-name {
    font-weight: 600;
    color: var(--gray-900);
    font-size: 13px;
}

.user-profile i {
    color: var(--gray-500);
    font-size: 12px;
}

/* 页面内容 */
.page-content {
    flex: 1;
    padding: 32px;
    overflow-y: auto;
    background-color: var(--gray-50);
    min-height: calc(100vh - var(--header-height));
}

.page {
    display: none;
}

.page.active {
    display: block;
}

.page-header {
    margin-bottom: 40px;
    padding: 32px;
    background: rgba(255, 255, 255, 0.95);
    border-radius: 20px;
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    border: 1px solid rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(20px);
}

.page-header h1 {
    font-size: 28px;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 8px;
    letter-spacing: -0.025em;
}

.page-header p {
    color: var(--text-muted);
    font-size: 16px;
    font-weight: 400;
}

/* 欢迎横幅 */
.welcome-banner {
    background: var(--gradient-primary);
    border-radius: 20px;
    padding: 32px;
    margin-bottom: 32px;
    color: white;
    position: relative;
    overflow: hidden;
    box-shadow: 0 8px 32px rgba(102, 126, 234, 0.3);
}

.welcome-banner::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    opacity: 0.1;
}

.welcome-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    position: relative;
    z-index: 1;
}

.welcome-title {
    font-size: 28px;
    font-weight: 700;
    margin-bottom: 8px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.welcome-subtitle {
    font-size: 16px;
    opacity: 0.9;
    margin-bottom: 24px;
}

.welcome-stats {
    display: flex;
    gap: 32px;
}

.welcome-stat-item {
    text-align: center;
}

.stat-value {
    display: block;
    font-size: 24px;
    font-weight: 700;
    margin-bottom: 4px;
}

.stat-label {
    font-size: 14px;
    opacity: 0.8;
}

.floating-elements {
    display: flex;
    gap: 16px;
}

.floating-element {
    font-size: 32px;
    animation: float 3s ease-in-out infinite;
    animation-delay: var(--delay);
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}

/* 统计卡片 */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 24px;
    margin-bottom: 32px;
    padding: 0;
}

.stat-card {
    background: rgba(255, 255, 255, 0.98);
    border-radius: 16px;
    padding: 24px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
    border: 1px solid rgba(255, 255, 255, 0.3);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
    backdrop-filter: blur(20px);
    min-height: 140px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

.stat-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 16px 48px rgba(0, 0, 0, 0.18);
    border-color: rgba(255, 255, 255, 0.5);
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: var(--gradient-primary);
    border-radius: 16px 16px 0 0;
}

.stat-card.primary::before {
    background: var(--gradient-primary);
}

.stat-card.success::before {
    background: var(--gradient-success);
}

.stat-card.warning::before {
    background: var(--gradient-warning);
}

.stat-card.danger::before {
    background: var(--gradient-danger);
}

.stat-content {
    display: flex;
    align-items: flex-start;
    gap: 16px;
    margin-bottom: 16px;
}

.stat-icon {
    width: 48px;
    height: 48px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 18px;
    color: white;
    flex-shrink: 0;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    margin-right: 16px;
}

.stat-card.primary .stat-icon {
    background: var(--gradient-primary);
}

.stat-card.success .stat-icon {
    background: var(--gradient-success);
}

.stat-card.warning .stat-icon {
    background: var(--gradient-warning);
}

.stat-card.danger .stat-icon {
    background: var(--gradient-danger);
}

.stat-info {
    flex: 1;
}

.stat-number {
    font-size: 32px;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 4px;
    line-height: 1;
    letter-spacing: -0.025em;
}

.stat-label {
    color: var(--text-muted);
    font-size: 14px;
    margin-bottom: 8px;
    font-weight: 500;
}

.stat-trend {
    display: flex;
    align-items: center;
    gap: 4px;
    font-size: 12px;
    font-weight: 600;
}

.stat-trend.positive {
    color: var(--success-color);
}

.stat-trend.negative {
    color: var(--danger-color);
}

.stat-trend.neutral {
    color: var(--gray-500);
}

.stat-chart {
    height: 40px;
    opacity: 0.7;
}

.mini-chart {
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent 0%, var(--gray-200) 50%, transparent 100%);
    border-radius: var(--border-radius-sm);
}

/* 仪表盘网格 */
.dashboard-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 24px;
    margin-top: 24px;
}

.dashboard-card {
    background: rgba(255, 255, 255, 0.98);
    border-radius: 16px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
    border: 1px solid rgba(255, 255, 255, 0.3);
    overflow: hidden;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    backdrop-filter: blur(20px);
}

.dashboard-card:hover {
    box-shadow: 0 16px 48px rgba(0, 0, 0, 0.18);
    transform: translateY(-4px);
    border-color: rgba(255, 255, 255, 0.5);
}

.card-header {
    padding: 24px 24px 0;
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 20px;
}

.card-header h3 {
    font-size: 18px;
    font-weight: 600;
    color: var(--text-primary);
    letter-spacing: -0.025em;
}

.card-content {
    padding: 0 24px 24px;
}

.card-actions {
    display: flex;
    gap: 12px;
}

.time-filter {
    padding: 8px 12px;
    border: 1px solid var(--gray-300);
    border-radius: var(--border-radius-sm);
    background: white;
    color: var(--gray-700);
    font-size: 13px;
    cursor: pointer;
    transition: var(--transition);
}

.time-filter:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

/* 图表容器 */
.chart-container {
    height: 300px;
    position: relative;
}

.chart-card {
    grid-row: span 2;
}

/* 表格样式 */
.table-container {
    overflow-x: auto;
}

.modern-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 13px;
}

.modern-table th {
    background-color: var(--gray-50);
    color: var(--text-secondary);
    font-weight: 600;
    padding: 14px 16px;
    text-align: left;
    border-bottom: 1px solid var(--gray-200);
    font-size: 13px;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.modern-table td {
    padding: 16px;
    border-bottom: 1px solid var(--gray-100);
    vertical-align: middle;
    color: var(--text-primary);
    font-size: 14px;
}

.modern-table tr:hover {
    background-color: var(--gray-50);
}

.order-id {
    font-family: 'Monaco', 'Menlo', monospace;
    font-weight: 600;
    color: var(--gray-900);
    font-size: 12px;
}

.customer-info {
    display: flex;
    align-items: center;
    gap: 12px;
}

.customer-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    overflow: hidden;
    flex-shrink: 0;
}

.customer-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.customer-details {
    display: flex;
    flex-direction: column;
}

.customer-name {
    font-weight: 600;
    color: var(--gray-900);
    font-size: 13px;
}

.customer-email {
    color: var(--gray-500);
    font-size: 11px;
}

.amount {
    font-weight: 600;
    color: var(--gray-900);
    font-size: 14px;
}

.status-badge {
    display: inline-flex;
    align-items: center;
    gap: 4px;
    padding: 4px 8px;
    border-radius: 20px;
    font-size: 11px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.status-badge.completed {
    background-color: rgba(16, 185, 129, 0.1);
    color: var(--success-color);
}

.status-badge.processing {
    background-color: rgba(59, 130, 246, 0.1);
    color: var(--info-color);
}

.status-badge.pending {
    background-color: rgba(245, 158, 11, 0.1);
    color: var(--warning-color);
}

.date {
    color: var(--gray-600);
    font-size: 12px;
}

.action-buttons {
    display: flex;
    gap: 4px;
}

.btn-icon {
    width: 32px;
    height: 32px;
    border: none;
    background: none;
    border-radius: var(--border-radius-sm);
    color: var(--gray-500);
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
}

.btn-icon:hover {
    background-color: var(--gray-100);
    color: var(--gray-700);
}

.btn-icon.danger:hover {
    background-color: rgba(239, 68, 68, 0.1);
    color: var(--danger-color);
}

/* 按钮样式 */
.btn-primary {
    background: var(--gradient-primary);
    color: white;
    border: none;
    padding: 14px 28px;
    border-radius: 12px;
    font-weight: 600;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    display: inline-flex;
    align-items: center;
    gap: 8px;
    text-decoration: none;
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
    position: relative;
    overflow: hidden;
}

.btn-primary:hover {
    transform: translateY(-3px) scale(1.02);
    box-shadow: 0 15px 35px rgba(102, 126, 234, 0.4);
    background: linear-gradient(135deg, #5a67d8, #667eea);
}

.btn-primary::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.btn-primary:hover::before {
    left: 100%;
}

.btn-primary.btn-sm {
    padding: 8px 16px;
    font-size: 12px;
}

/* 快速操作卡片 */
.quick-actions {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 16px;
}

.quick-action-btn {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 12px;
    padding: 24px 16px;
    background: var(--gray-50);
    border: 1px solid var(--gray-200);
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: var(--transition);
    text-decoration: none;
    color: var(--gray-700);
}

.quick-action-btn:hover {
    background: white;
    border-color: var(--primary-color);
    color: var(--primary-color);
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.quick-action-btn i {
    font-size: 24px;
    color: var(--primary-color);
}

.quick-action-btn span {
    font-weight: 600;
    font-size: 13px;
}

/* 活动日志 */
.activity-list {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.activity-item {
    display: flex;
    align-items: flex-start;
    gap: 12px;
    padding: 16px;
    background: var(--gray-50);
    border-radius: var(--border-radius);
    transition: var(--transition);
}

.activity-item:hover {
    background: white;
    box-shadow: var(--shadow-sm);
}

.activity-icon {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    color: white;
    flex-shrink: 0;
}

.activity-icon.success {
    background-color: var(--success-color);
}

.activity-icon.primary {
    background-color: var(--primary-color);
}

.activity-icon.warning {
    background-color: var(--warning-color);
}

.activity-icon.info {
    background-color: var(--info-color);
}

.activity-content {
    flex: 1;
}

.activity-content p {
    color: var(--gray-900);
    font-size: 13px;
    margin-bottom: 4px;
}

.activity-time {
    color: var(--gray-500);
    font-size: 11px;
}

/* 即将推出页面 */
.coming-soon {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 80px 20px;
    text-align: center;
    background: white;
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow);
    border: 1px solid var(--gray-200);
}

.coming-soon i {
    font-size: 64px;
    color: var(--gray-300);
    margin-bottom: 24px;
}

.coming-soon h3 {
    font-size: 24px;
    font-weight: 600;
    color: var(--gray-900);
    margin-bottom: 12px;
}

.coming-soon p {
    color: var(--gray-600);
    font-size: 16px;
}

/* 移动端遮罩层 */
.mobile-overlay {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 999;
    opacity: 0;
    transition: var(--transition);
}

.mobile-overlay.active {
    display: block;
    opacity: 1;
}

/* 响应式设计 */
@media (max-width: 1024px) {
    .dashboard-grid {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 20px;
    }

    .welcome-content {
        flex-direction: column;
        text-align: center;
        gap: 24px;
    }

    .welcome-stats {
        justify-content: center;
    }
}

@media (max-width: 768px) {
    .sidebar {
        transform: translateX(-100%);
    }

    .sidebar.mobile-open {
        transform: translateX(0);
    }

    .main-content {
        margin-left: 0;
    }

    .mobile-menu-btn {
        display: block;
    }

    .top-header {
        padding: 0 16px;
    }

    .page-content {
        padding: 20px 16px;
    }

    .stats-grid {
        grid-template-columns: 1fr;
        gap: 16px;
    }

    .dashboard-grid {
        gap: 16px;
    }

    .page-header h1 {
        font-size: 24px;
    }

    .breadcrumb {
        display: none;
    }

    .header-actions {
        gap: 4px;
    }

    .user-profile .user-name {
        display: none;
    }

    .quick-actions {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 480px) {
    .top-header {
        padding: 0 12px;
    }

    .page-content {
        padding: 16px 12px;
    }

    .stat-card {
        padding: 20px;
    }

    .stat-number {
        font-size: 24px;
    }

    .card-header {
        padding: 20px 20px 0;
        flex-direction: column;
        align-items: flex-start;
        gap: 12px;
    }

    .card-content {
        padding: 0 20px 20px;
    }

    .modern-table {
        font-size: 12px;
    }

    .modern-table th,
    .modern-table td {
        padding: 8px 12px;
    }

    .customer-info {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
    }

    .action-buttons {
        flex-direction: column;
    }
}

/* 滚动条样式 */
::-webkit-scrollbar {
    width: 6px;
    height: 6px;
}

::-webkit-scrollbar-track {
    background: var(--gray-100);
    border-radius: 3px;
}

::-webkit-scrollbar-thumb {
    background: var(--gray-300);
    border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--gray-400);
}

/* 模态框内容滚动条样式 */
.modal-content::-webkit-scrollbar {
    width: 8px;
}

.modal-content::-webkit-scrollbar-track {
    background: var(--gray-50);
    border-radius: 4px;
}

.modal-content::-webkit-scrollbar-thumb {
    background: var(--gray-300);
    border-radius: 4px;
    border: 2px solid var(--gray-50);
}

.modal-content::-webkit-scrollbar-thumb:hover {
    background: var(--gray-400);
}

/* 模态框动画优化 */
.modal-overlay {
    backdrop-filter: blur(4px);
    -webkit-backdrop-filter: blur(4px);
}

.modal-container {
    animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: translateY(-30px) scale(0.95);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

/* 移动端模态框动画 */
@media (max-width: 768px) {
    .modal-container {
        animation: modalSlideUp 0.3s ease-out;
    }

    @keyframes modalSlideUp {
        from {
            opacity: 0;
            transform: translateY(100%);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }
}

/* 动画 */
@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.stat-card,
.dashboard-card {
    animation: slideIn 0.6s ease-out;
}

.stat-card:nth-child(1) { animation-delay: 0.1s; }
.stat-card:nth-child(2) { animation-delay: 0.2s; }
.stat-card:nth-child(3) { animation-delay: 0.3s; }
.stat-card:nth-child(4) { animation-delay: 0.4s; }

/* 加载状态 */
.loading {
    opacity: 0.6;
    pointer-events: none;
}

.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid var(--gray-300);
    border-top-color: var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

/* 用户下拉菜单样式 */
.user-dropdown-menu {
    background: white;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-xl);
    border: 1px solid var(--gray-200);
    overflow: hidden;
    min-width: 250px;
}

.user-menu-header {
    padding: 16px;
    background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
    color: white;
    display: flex;
    align-items: center;
    gap: 12px;
}

.user-menu-header .user-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    overflow: hidden;
    border: 2px solid rgba(255, 255, 255, 0.3);
}

.user-menu-header .user-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.user-menu-header .user-details {
    flex: 1;
}

.user-menu-header .user-name {
    font-weight: 600;
    font-size: 14px;
    margin-bottom: 2px;
}

.user-menu-header .user-role {
    font-size: 12px;
    opacity: 0.9;
}

.user-menu-divider {
    height: 1px;
    background: var(--gray-200);
    margin: 0;
}

.user-menu-items {
    padding: 8px 0;
}

.user-menu-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px 16px;
    color: var(--gray-700);
    text-decoration: none;
    font-size: 14px;
    font-weight: 500;
    transition: var(--transition);
    cursor: pointer;
}

.user-menu-item:hover {
    background: var(--gray-50);
    color: var(--primary-color);
}

.user-menu-item.logout {
    color: var(--danger-color);
}

.user-menu-item.logout:hover {
    background: rgba(239, 68, 68, 0.1);
    color: var(--danger-color);
}

.user-menu-item i {
    width: 16px;
    text-align: center;
    font-size: 14px;
}

/* 用户菜单动画 */
@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 用户头像点击效果 */
.user-profile {
    cursor: pointer;
    transition: var(--transition);
}

.user-profile:hover {
    background: rgba(255, 255, 255, 0.1);
    border-radius: var(--border-radius-sm);
}

.user-menu-btn {
    cursor: pointer;
    transition: var(--transition);
    padding: 8px;
    border-radius: var(--border-radius-sm);
}

.user-menu-btn:hover {
    background: rgba(255, 255, 255, 0.1);
}

/* 用户管理页面样式 */
.page-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 32px;
    flex-wrap: wrap;
    gap: 16px;
}

.page-actions {
    display: flex;
    gap: 12px;
    flex-wrap: wrap;
}

.btn-secondary {
    background: var(--gray-600);
    color: white;
    border: none;
    padding: 12px 20px;
    border-radius: var(--border-radius-sm);
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    gap: 8px;
}

.btn-secondary:hover {
    background: var(--gray-700);
}

.search-filters {
    display: flex;
    gap: 12px;
    align-items: center;
    flex-wrap: wrap;
}

.search-box {
    position: relative;
    display: flex;
    align-items: center;
}

.search-box i {
    position: absolute;
    left: 12px;
    color: var(--gray-400);
    z-index: 1;
}

.search-box input {
    padding: 10px 12px 10px 40px;
    border: 2px solid var(--gray-200);
    border-radius: var(--border-radius-sm);
    font-size: 14px;
    min-width: 250px;
    transition: var(--transition);
}

.search-box input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

.search-filters select {
    padding: 10px 12px;
    border: 2px solid var(--gray-200);
    border-radius: var(--border-radius-sm);
    font-size: 14px;
    background: white;
    cursor: pointer;
    transition: var(--transition);
}

.search-filters select:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

/* 表格样式增强 */
.modern-table th:first-child,
.modern-table td:first-child {
    width: 40px;
    text-align: center;
}

.user-info-cell {
    display: flex;
    align-items: center;
    gap: 12px;
}

.user-avatar-small {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    overflow: hidden;
    flex-shrink: 0;
}

.user-avatar-small img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.user-details {
    flex: 1;
    min-width: 0;
}

.user-name {
    font-weight: 600;
    color: var(--gray-900);
    margin-bottom: 2px;
}

.user-email {
    font-size: 13px;
    color: var(--gray-500);
}

.role-badge {
    display: inline-flex;
    align-items: center;
    gap: 6px;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
}

.role-badge.admin {
    background: rgba(239, 68, 68, 0.1);
    color: var(--danger-color);
}

.role-badge.manager {
    background: rgba(245, 158, 11, 0.1);
    color: var(--warning-color);
}

.role-badge.user {
    background: rgba(16, 185, 129, 0.1);
    color: var(--success-color);
}

.role-badge.test {
    background: rgba(59, 130, 246, 0.1);
    color: var(--info-color);
}

.role-badge.demo {
    background: rgba(99, 102, 241, 0.1);
    color: var(--primary-color);
}

/* 分页样式 */
.pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 8px;
    margin-top: 24px;
    padding: 16px 0;
}

.pagination-btn {
    padding: 8px 12px;
    border: 1px solid var(--gray-300);
    background: white;
    color: var(--gray-700);
    border-radius: var(--border-radius-sm);
    cursor: pointer;
    transition: var(--transition);
    font-size: 14px;
}

.pagination-btn:hover {
    background: var(--gray-50);
    border-color: var(--gray-400);
}

.pagination-btn.active {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.pagination-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.pagination-info {
    margin: 0 16px;
    font-size: 14px;
    color: var(--gray-600);
}

/* 模态框样式 - 重新设计 */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10000;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
    padding: 20px;
    box-sizing: border-box;
}

.modal-overlay.active {
    opacity: 1;
    visibility: visible;
}

.modal-container {
    background: white;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-xl);
    max-width: 600px;
    width: 100%;
    max-height: calc(100vh - 40px);
    transform: translateY(-20px);
    transition: all 0.3s ease;
    display: flex;
    flex-direction: column;
    position: relative;
    overflow: hidden;
}

.modal-overlay.active .modal-container {
    transform: translateY(0);
}

.modal-container.small {
    max-width: 400px;
}

.modal-header {
    padding: 20px 24px;
    border-bottom: 1px solid var(--gray-200);
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: var(--gray-50);
}

.modal-header h3 {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
    color: var(--gray-900);
}

.modal-close {
    background: none;
    border: none;
    color: var(--gray-400);
    cursor: pointer;
    padding: 8px;
    border-radius: var(--border-radius-sm);
    transition: var(--transition);
}

.modal-close:hover {
    background: var(--gray-200);
    color: var(--gray-600);
}

.modal-content {
    padding: 24px;
    flex: 1;
    overflow-y: auto;
    overflow-x: hidden;
    min-height: 0;
    /* 确保滚动功能正常 */
    scrollbar-width: thin;
    scrollbar-color: var(--gray-300) transparent;
    /* 重要：确保内容可以滚动 */
    height: 0;
}

.modal-footer {
    padding: 16px 24px;
    border-top: 1px solid var(--gray-200);
    display: flex;
    justify-content: flex-end;
    gap: 12px;
    background: var(--gray-50);
    flex-shrink: 0;
}

/* 表单样式 */
.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 16px;
    margin-bottom: 16px;
}

.form-group {
    margin-bottom: 16px;
}

.form-group label {
    display: block;
    margin-bottom: 6px;
    font-weight: 500;
    color: var(--text-secondary);
    font-size: 14px;
}

.form-group input,
.form-group select {
    width: 100%;
    padding: 12px 14px;
    border: 2px solid var(--gray-200);
    border-radius: var(--border-radius-sm);
    font-size: 14px;
    transition: var(--transition);
    box-sizing: border-box;
    background: white;
    color: var(--text-primary);
    font-weight: 400;
}

.form-group input:focus,
.form-group select:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

.form-group small {
    display: block;
    margin-top: 4px;
    font-size: 12px;
    color: var(--gray-500);
}

/* 模态框表单优化 */
.modal-content .form-group {
    margin-bottom: 20px;
}

.modal-content .form-row {
    margin-bottom: 16px;
}

/* 防止表单元素溢出 */
.modal-content .form-group input,
.modal-content .form-group select,
.modal-content .form-group textarea {
    max-width: 100%;
    box-sizing: border-box;
}

/* 确保表单在小屏幕上的可用性 */
@media (max-width: 480px) {
    .modal-content .form-row {
        grid-template-columns: 1fr;
        gap: 12px;
    }

    .modal-content .form-group {
        margin-bottom: 16px;
    }

    .modal-content .form-group input,
    .modal-content .form-group select,
    .modal-content .form-group textarea {
        padding: 12px 16px;
        font-size: 16px; /* 防止iOS缩放 */
    }
}

/* 删除确认样式 */
.delete-warning {
    text-align: center;
    padding: 20px;
}

.delete-warning i {
    font-size: 48px;
    color: var(--warning-color);
    margin-bottom: 16px;
}

.delete-warning p {
    margin-bottom: 8px;
    color: var(--gray-700);
}

.warning-text {
    color: var(--danger-color);
    font-weight: 500;
    font-size: 14px;
}

.btn-danger {
    background: var(--danger-color);
    color: white;
    border: none;
    padding: 10px 16px;
    border-radius: var(--border-radius-sm);
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    gap: 8px;
}

.btn-danger:hover {
    background: #dc2626;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .page-header {
        flex-direction: column;
        align-items: stretch;
    }

    .search-filters {
        flex-direction: column;
        align-items: stretch;
    }

    .search-box input {
        min-width: auto;
        width: 100%;
    }

    .form-row {
        grid-template-columns: 1fr;
    }

    .modal-container {
        width: 95%;
        margin: 20px;
    }

    .modal-content {
        padding: 16px;
    }

    .modal-header,
    .modal-footer {
        padding: 16px;
    }
}

/* 数据分析页面样式 */
.chart-controls {
    display: flex;
    gap: 8px;
}

.chart-btn {
    padding: 6px 12px;
    border: 1px solid var(--gray-300);
    background: white;
    color: var(--gray-600);
    border-radius: var(--border-radius-sm);
    cursor: pointer;
    font-size: 12px;
    font-weight: 500;
    transition: var(--transition);
}

.chart-btn:hover {
    background: var(--gray-50);
    border-color: var(--gray-400);
}

.chart-btn.active {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.chart-container {
    position: relative;
    height: 300px;
    width: 100%;
}

.chart-container canvas {
    max-height: 100%;
    width: 100% !important;
    height: 100% !important;
}

/* 产品排行样式 */
.product-ranking {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.product-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px;
    background: var(--gray-50);
    border-radius: var(--border-radius-sm);
    transition: var(--transition);
}

.product-item:hover {
    background: var(--gray-100);
}

.product-rank {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    font-size: 14px;
    color: white;
    flex-shrink: 0;
}

.product-rank.rank-1 { background: #ffd700; color: #000; }
.product-rank.rank-2 { background: #c0c0c0; color: #000; }
.product-rank.rank-3 { background: #cd7f32; color: #fff; }
.product-rank.rank-other { background: var(--gray-400); }

.product-info {
    flex: 1;
    min-width: 0;
}

.product-name {
    font-weight: 600;
    color: var(--gray-900);
    margin-bottom: 2px;
}

.product-category {
    font-size: 12px;
    color: var(--gray-500);
}

.product-stats {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    gap: 2px;
}

.product-sales {
    font-weight: 600;
    color: var(--gray-900);
}

.product-growth {
    font-size: 12px;
    display: flex;
    align-items: center;
    gap: 4px;
}

.product-growth.positive { color: var(--success-color); }
.product-growth.negative { color: var(--danger-color); }
.product-growth.neutral { color: var(--gray-500); }

/* 数据表格增强 */
.analytics-table-container {
    max-height: 400px;
    overflow-y: auto;
}

.table-summary {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 0;
    border-top: 1px solid var(--gray-200);
    margin-top: 16px;
    font-size: 14px;
    color: var(--gray-600);
}

.summary-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 4px;
}

.summary-label {
    font-size: 12px;
    color: var(--gray-500);
}

.summary-value {
    font-weight: 600;
    color: var(--gray-900);
}

/* 趋势指示器增强 */
.stat-trend {
    display: flex;
    align-items: center;
    gap: 4px;
    font-size: 12px;
    font-weight: 500;
    margin-top: 4px;
}

.stat-trend.positive {
    color: var(--success-color);
}

.stat-trend.negative {
    color: var(--danger-color);
}

.stat-trend.neutral {
    color: var(--gray-500);
}

.stat-trend i {
    font-size: 10px;
}

/* 加载状态 */
.chart-loading {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 300px;
    color: var(--gray-500);
    font-size: 14px;
}

.chart-loading i {
    margin-right: 8px;
    animation: spin 1s linear infinite;
}

/* 响应式图表 */
@media (max-width: 1024px) {
    .dashboard-grid {
        grid-template-columns: 1fr;
    }

    .chart-container {
        height: 250px;
    }
}

@media (max-width: 768px) {
    .chart-controls {
        flex-wrap: wrap;
    }

    .chart-btn {
        flex: 1;
        min-width: 80px;
    }

    .product-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
    }

    .product-stats {
        align-items: flex-start;
        width: 100%;
    }

    .chart-container {
        height: 200px;
    }
}

/* 订单管理样式 */
.order-info {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.order-id {
    font-weight: 600;
    color: var(--gray-900);
    font-size: 14px;
}

.order-date {
    font-size: 12px;
    color: var(--gray-500);
}

.order-items {
    display: flex;
    flex-direction: column;
    gap: 6px;
    max-width: 200px;
}

.item-summary {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 13px;
}

.item-image {
    width: 24px;
    height: 24px;
    border-radius: 4px;
    object-fit: cover;
    flex-shrink: 0;
}

.more-items {
    font-size: 12px;
    color: var(--gray-500);
    font-style: italic;
}

.order-amount {
    font-weight: 600;
    color: var(--gray-900);
    font-size: 15px;
}

/* 订单详情模态框样式 */
.order-detail {
    display: flex;
    flex-direction: column;
    gap: 24px;
}

.detail-section {
    border-bottom: 1px solid var(--gray-200);
    padding-bottom: 16px;
}

.detail-section:last-child {
    border-bottom: none;
    padding-bottom: 0;
}

.detail-section h4 {
    margin-bottom: 12px;
    color: var(--gray-900);
    font-size: 16px;
    font-weight: 600;
}

.detail-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 12px;
}

.detail-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
}

.detail-item label {
    font-weight: 500;
    color: var(--gray-600);
    margin-right: 12px;
}

.customer-detail {
    display: flex;
    align-items: center;
    gap: 16px;
}

.customer-avatar-large {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    object-fit: cover;
}

.customer-info-detail {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.items-detail {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.item-detail {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px;
    background: var(--gray-50);
    border-radius: var(--border-radius-sm);
}

.item-image-large {
    width: 50px;
    height: 50px;
    border-radius: var(--border-radius-sm);
    object-fit: cover;
    flex-shrink: 0;
}

.item-info {
    flex: 1;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4px;
}

.item-name {
    font-weight: 600;
    color: var(--gray-900);
    grid-column: 1 / -1;
}

.item-price,
.item-quantity,
.item-total {
    font-size: 13px;
    color: var(--gray-600);
}

.item-total {
    font-weight: 600;
    color: var(--gray-900);
}

.order-total {
    text-align: right;
    padding: 12px 0;
    border-top: 1px solid var(--gray-200);
    margin-top: 12px;
    font-size: 16px;
}

.shipping-detail {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.shipping-detail div {
    padding: 4px 0;
}

/* 状态样式增强 */
.status-badge.pending {
    background: rgba(245, 158, 11, 0.1);
    color: var(--warning-color);
}

.status-badge.processing {
    background: rgba(59, 130, 246, 0.1);
    color: var(--info-color);
}

.status-badge.shipped {
    background: rgba(99, 102, 241, 0.1);
    color: var(--primary-color);
}

.status-badge.completed {
    background: rgba(16, 185, 129, 0.1);
    color: var(--success-color);
}

.status-badge.cancelled {
    background: rgba(239, 68, 68, 0.1);
    color: var(--danger-color);
}

.status-badge.refunded {
    background: rgba(139, 92, 246, 0.1);
    color: #8b5cf6;
}

/* 表单元素增强 */
.form-group textarea {
    width: 100%;
    padding: 10px 12px;
    border: 2px solid var(--gray-200);
    border-radius: var(--border-radius-sm);
    font-size: 14px;
    font-family: inherit;
    resize: vertical;
    min-height: 80px;
    transition: var(--transition);
}

.form-group textarea:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

/* 响应式设计 */
@media (max-width: 768px) {
    .detail-grid {
        grid-template-columns: 1fr;
    }

    .customer-detail {
        flex-direction: column;
        align-items: flex-start;
    }

    .item-detail {
        flex-direction: column;
        align-items: flex-start;
    }

    .item-info {
        width: 100%;
        grid-template-columns: 1fr;
    }

    .order-items {
        max-width: none;
    }
}

/* 系统设置样式 */
.settings-container {
    display: flex;
    flex-direction: column;
    gap: 24px;
}

.settings-card {
    background: white;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    overflow: hidden;
    border: 1px solid var(--gray-200);
}

.settings-header {
    padding: 20px 24px;
    background: var(--gray-50);
    border-bottom: 1px solid var(--gray-200);
}

.settings-header h3 {
    margin: 0 0 4px 0;
    color: var(--gray-900);
    font-size: 18px;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 8px;
}

.settings-header p {
    margin: 0;
    color: var(--gray-600);
    font-size: 14px;
}

.settings-content {
    padding: 24px;
}

.setting-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 0;
    border-bottom: 1px solid var(--gray-100);
}

.setting-item:last-child {
    border-bottom: none;
    padding-bottom: 0;
}

.setting-item:first-child {
    padding-top: 0;
}

.setting-info {
    flex: 1;
    margin-right: 16px;
}

.setting-info label {
    display: block;
    font-weight: 600;
    color: var(--gray-900);
    margin-bottom: 4px;
    font-size: 14px;
}

.setting-info span {
    color: var(--gray-600);
    font-size: 13px;
    line-height: 1.4;
}

.setting-control {
    flex-shrink: 0;
}

.setting-control select {
    padding: 8px 12px;
    border: 2px solid var(--gray-200);
    border-radius: var(--border-radius-sm);
    font-size: 14px;
    background: white;
    cursor: pointer;
    transition: var(--transition);
    min-width: 120px;
}

.setting-control select:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

/* 开关样式 */
.switch {
    position: relative;
    display: inline-block;
    width: 50px;
    height: 24px;
}

.switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: var(--gray-300);
    transition: var(--transition);
    border-radius: 24px;
}

.slider:before {
    position: absolute;
    content: "";
    height: 18px;
    width: 18px;
    left: 3px;
    bottom: 3px;
    background-color: white;
    transition: var(--transition);
    border-radius: 50%;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

input:checked + .slider {
    background-color: var(--primary-color);
}

input:checked + .slider:before {
    transform: translateX(26px);
}

.slider:hover {
    box-shadow: 0 0 0 8px rgba(99, 102, 241, 0.1);
}

/* 按钮尺寸变体 */
.btn-sm {
    padding: 6px 12px;
    font-size: 12px;
    border-radius: 6px;
}

.btn-primary.btn-sm {
    background: var(--primary-color);
    color: white;
    border: none;
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    gap: 6px;
}

.btn-primary.btn-sm:hover {
    background: var(--primary-dark);
}

.btn-secondary.btn-sm {
    background: var(--gray-600);
    color: white;
    border: none;
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    gap: 6px;
}

.btn-secondary.btn-sm:hover {
    background: var(--gray-700);
}

.btn-danger.btn-sm {
    background: var(--danger-color);
    color: white;
    border: none;
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    gap: 6px;
}

.btn-danger.btn-sm:hover {
    background: #dc2626;
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
    .settings-card {
        background: var(--gray-800);
        border-color: var(--gray-700);
    }

    .settings-header {
        background: var(--gray-700);
        border-color: var(--gray-600);
    }

    .settings-header h3 {
        color: var(--gray-100);
    }

    .settings-header p {
        color: var(--gray-300);
    }

    .setting-info label {
        color: var(--gray-100);
    }

    .setting-info span {
        color: var(--gray-400);
    }

    .setting-item {
        border-color: var(--gray-700);
    }

    .setting-control select {
        background: var(--gray-700);
        border-color: var(--gray-600);
        color: var(--gray-100);
    }
}

/* 响应式设计 */
@media (max-width: 768px) {
    .settings-container {
        gap: 16px;
    }

    .settings-header {
        padding: 16px 20px;
    }

    .settings-content {
        padding: 20px;
    }

    .setting-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 12px;
    }

    .setting-info {
        margin-right: 0;
    }

    .setting-control {
        width: 100%;
    }

    .setting-control select {
        width: 100%;
        min-width: auto;
    }

    .setting-control .btn-sm {
        width: 100%;
        justify-content: center;
    }
}

/* 移动端优化 */
@media (max-width: 480px) {
    /* 基础布局优化 */
    .app-container {
        padding: 0;
    }

    .main-content {
        margin-left: 0;
        padding: 0;
    }

    .page-content {
        padding: 12px;
    }

    /* 页面头部优化 */
    .page-header {
        padding: 16px 0;
        margin-bottom: 20px;
    }

    .page-header h1 {
        font-size: 24px;
        margin-bottom: 4px;
    }

    .page-header p {
        font-size: 14px;
        margin-bottom: 12px;
    }

    .page-actions {
        flex-direction: column;
        gap: 8px;
    }

    .page-actions .btn-primary,
    .page-actions .btn-secondary {
        width: 100%;
        justify-content: center;
        padding: 12px 16px;
    }

    /* 统计卡片优化 */
    .stats-grid {
        grid-template-columns: 1fr 1fr;
        gap: 12px;
    }

    .stat-card {
        padding: 16px;
    }

    .stat-icon {
        width: 40px;
        height: 40px;
        font-size: 18px;
    }

    .stat-number {
        font-size: 20px;
    }

    .stat-label {
        font-size: 12px;
    }

    .stat-trend {
        font-size: 11px;
    }

    /* 仪表盘网格优化 */
    .dashboard-grid {
        grid-template-columns: 1fr;
        gap: 16px;
    }

    .dashboard-card {
        margin-bottom: 0;
    }

    .card-header {
        padding: 16px;
        flex-direction: column;
        align-items: flex-start;
        gap: 12px;
    }

    .card-content {
        padding: 16px;
    }

    /* 表格优化 */
    .table-container {
        overflow-x: auto;
        -webkit-overflow-scrolling: touch;
    }

    .modern-table {
        min-width: 600px;
        font-size: 13px;
    }

    .modern-table th,
    .modern-table td {
        padding: 8px 6px;
        white-space: nowrap;
    }

    .customer-info {
        min-width: 120px;
    }

    .customer-avatar {
        width: 28px;
        height: 28px;
    }

    .customer-name {
        font-size: 13px;
    }

    .customer-email {
        font-size: 11px;
    }

    /* 搜索筛选优化 */
    .search-filters {
        flex-direction: column;
        gap: 8px;
    }

    .search-box {
        width: 100%;
    }

    .search-box input {
        min-width: auto;
        width: 100%;
        padding: 10px 12px 10px 36px;
    }

    .search-filters select {
        width: 100%;
    }

    /* 分页优化 */
    .pagination {
        flex-wrap: wrap;
        gap: 4px;
        justify-content: center;
    }

    .pagination-btn {
        padding: 6px 10px;
        font-size: 12px;
        min-width: 36px;
    }

    .pagination-info {
        width: 100%;
        text-align: center;
        margin: 8px 0 0 0;
        font-size: 12px;
    }

    /* 模态框移动端优化 */
    .modal-overlay {
        padding: 10px;
    }

    .modal-container {
        max-width: none;
        width: 100%;
        max-height: calc(100vh - 20px);
        border-radius: var(--border-radius-sm);
    }

    .modal-header {
        padding: 16px;
        flex-shrink: 0;
    }

    .modal-header h3 {
        font-size: 16px;
    }

    .modal-content {
        padding: 16px;
        /* 确保移动端滚动正常 */
        overflow-y: auto;
        -webkit-overflow-scrolling: touch;
    }

    .modal-footer {
        padding: 12px 16px;
        flex-direction: column;
        gap: 8px;
        flex-shrink: 0;
    }

    .modal-footer .btn-primary,
    .modal-footer .btn-secondary,
    .modal-footer .btn-danger {
        width: 100%;
        justify-content: center;
    }

    /* 用户管理优化 */
    .user-info-cell {
        min-width: 140px;
    }

    .user-avatar-small {
        width: 32px;
        height: 32px;
    }

    .user-name {
        font-size: 13px;
    }

    .user-email {
        font-size: 11px;
    }

    .role-badge {
        font-size: 10px;
        padding: 2px 6px;
    }

    /* 订单管理优化 */
    .order-info {
        min-width: 100px;
    }

    .order-id {
        font-size: 12px;
    }

    .order-date {
        font-size: 10px;
    }

    .order-items {
        max-width: 150px;
    }

    .item-summary {
        font-size: 11px;
    }

    .item-image {
        width: 20px;
        height: 20px;
    }

    .order-amount {
        font-size: 13px;
    }

    /* 数据分析优化 */
    .chart-controls {
        flex-direction: column;
        gap: 6px;
    }

    .chart-btn {
        width: 100%;
        text-align: center;
    }

    .product-ranking {
        gap: 8px;
    }

    .product-item {
        padding: 8px;
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
    }

    .product-rank {
        width: 28px;
        height: 28px;
        font-size: 12px;
    }

    .product-name {
        font-size: 14px;
    }

    .product-category {
        font-size: 11px;
    }

    .product-sales {
        font-size: 14px;
    }

    .product-growth {
        font-size: 11px;
    }

    /* 设置页面优化 */
    .settings-container {
        gap: 16px;
    }

    .settings-header {
        padding: 16px;
    }

    .settings-header h3 {
        font-size: 16px;
    }

    .settings-content {
        padding: 16px;
    }

    .setting-item {
        padding: 12px 0;
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
    }

    .setting-info {
        margin-right: 0;
        width: 100%;
    }

    .setting-control {
        width: 100%;
    }

    .setting-control select {
        width: 100%;
    }

    /* 操作按钮优化 */
    .action-buttons {
        gap: 4px;
    }

    .btn-icon {
        padding: 6px;
        min-width: 32px;
        height: 32px;
    }

    .btn-icon i {
        font-size: 12px;
    }

    /* 快速操作优化 */
    .quick-actions {
        grid-template-columns: 1fr 1fr;
        gap: 8px;
    }

    .quick-action-btn {
        padding: 12px 8px;
        font-size: 12px;
        flex-direction: column;
        gap: 6px;
    }

    .quick-action-btn i {
        font-size: 16px;
    }

    /* 活动列表优化 */
    .activity-list {
        gap: 8px;
    }

    .activity-item {
        padding: 8px;
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
    }

    .activity-icon {
        width: 28px;
        height: 28px;
        font-size: 12px;
    }

    .activity-content p {
        font-size: 13px;
        margin-bottom: 4px;
    }

    .activity-time {
        font-size: 11px;
    }
}

/* 触摸设备优化 */
@media (hover: none) and (pointer: coarse) {
    /* 增大可点击区域 */
    .btn-icon {
        min-width: 44px;
        min-height: 44px;
    }

    .nav-item {
        min-height: 48px;
    }

    .tab-btn {
        min-height: 44px;
    }

    .pagination-btn {
        min-width: 44px;
        min-height: 44px;
    }

    /* 移除悬停效果 */
    .stat-card:hover {
        transform: none;
    }

    .feature-item:hover {
        transform: none;
        background: var(--gray-50);
    }

    .product-item:hover {
        background: var(--gray-100);
    }

    /* 优化触摸反馈 */
    .btn-primary:active,
    .btn-secondary:active,
    .btn-danger:active {
        transform: scale(0.98);
    }

    .nav-item:active {
        background: rgba(99, 102, 241, 0.1);
    }

    .stat-card:active {
        transform: scale(0.98);
    }
}

/* UI增强样式 */

/* 加载动画 */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(4px);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10001;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.loading-overlay.active {
    opacity: 1;
    visibility: visible;
}

.loading-spinner {
    text-align: center;
}

.spinner-ring {
    width: 60px;
    height: 60px;
    border: 4px solid var(--gray-200);
    border-top: 4px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 16px;
}

.loading-text {
    color: var(--gray-700);
    font-size: 16px;
    font-weight: 500;
}

/* 页面加载 */
.page-loading {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 10000;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.page-loading.active {
    opacity: 1;
    visibility: visible;
}

.page-loading-content {
    text-align: center;
    background: white;
    padding: 32px;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-xl);
    border: 1px solid var(--gray-200);
}

.loading-dots {
    display: flex;
    justify-content: center;
    gap: 8px;
    margin-bottom: 16px;
}

.dot {
    width: 12px;
    height: 12px;
    background: var(--primary-color);
    border-radius: 50%;
    animation: loadingDots 1.4s ease-in-out infinite both;
}

.dot:nth-child(1) { animation-delay: -0.32s; }
.dot:nth-child(2) { animation-delay: -0.16s; }

@keyframes loadingDots {
    0%, 80%, 100% {
        transform: scale(0);
        opacity: 0.5;
    }
    40% {
        transform: scale(1);
        opacity: 1;
    }
}

.loading-message {
    color: var(--gray-600);
    font-size: 14px;
}

/* 元素加载 */
.element-loading {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 100;
    border-radius: inherit;
}

.element-loading-content {
    display: flex;
    align-items: center;
    gap: 12px;
    color: var(--gray-600);
    font-size: 14px;
}

.mini-spinner {
    width: 20px;
    height: 20px;
    border: 2px solid var(--gray-200);
    border-top: 2px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

/* 骨架屏 */
.skeleton-item {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: skeleton-loading 1.5s infinite;
    border-radius: 4px;
    height: 16px;
}

.skeleton-title {
    height: 20px;
    width: 60%;
    margin-bottom: 8px;
}

.skeleton-subtitle {
    height: 14px;
    width: 40%;
    margin-bottom: 16px;
}

.skeleton-text {
    height: 14px;
    width: 100%;
    margin-bottom: 8px;
}

.skeleton-text.short {
    width: 70%;
}

.skeleton-number {
    height: 24px;
    width: 80px;
    margin-bottom: 4px;
}

.skeleton-label {
    height: 12px;
    width: 60px;
}

.skeleton-icon {
    width: 40px;
    height: 40px;
    border-radius: 8px;
}

@keyframes skeleton-loading {
    0% {
        background-position: -200% 0;
    }
    100% {
        background-position: 200% 0;
    }
}

.skeleton-card {
    background: white;
    border-radius: var(--border-radius);
    padding: 20px;
    box-shadow: var(--shadow);
    border: 1px solid var(--gray-200);
}

.skeleton-header {
    margin-bottom: 16px;
}

.skeleton-stat-card {
    background: white;
    border-radius: var(--border-radius);
    padding: 20px;
    box-shadow: var(--shadow);
    border: 1px solid var(--gray-200);
}

.skeleton-stat-content {
    display: flex;
    align-items: center;
    gap: 16px;
}

.skeleton-stat-info {
    flex: 1;
}

/* 动画效果 */
.animate-on-scroll {
    opacity: 0;
    transform: translateY(30px);
    transition: all 0.6s ease;
}

.animate-on-scroll.animated {
    opacity: 1;
    transform: translateY(0);
}

.fadeInUp {
    animation: fadeInUp 0.6s ease forwards;
}

.fadeInLeft {
    animation: fadeInLeft 0.6s ease forwards;
}

.fadeInRight {
    animation: fadeInRight 0.6s ease forwards;
}

.scaleIn {
    animation: scaleIn 0.6s ease forwards;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInLeft {
    from {
        opacity: 0;
        transform: translateX(-30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes fadeInRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes scaleIn {
    from {
        opacity: 0;
        transform: scale(0.8);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

/* 通知增强 */
.notification-container {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 10002;
    display: flex;
    flex-direction: column;
    gap: 12px;
    max-width: 400px;
}

.notification {
    background: white;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-xl);
    border-left: 4px solid var(--primary-color);
    overflow: hidden;
    transform: translateX(100%);
    opacity: 0;
    transition: all 0.3s ease;
}

.notification.show {
    transform: translateX(0);
    opacity: 1;
}

.notification.hide {
    transform: translateX(100%);
    opacity: 0;
}

.notification-success {
    border-left-color: var(--success-color);
}

.notification-error {
    border-left-color: var(--danger-color);
}

.notification-warning {
    border-left-color: var(--warning-color);
}

.notification-info {
    border-left-color: var(--info-color);
}

.notification-content {
    display: flex;
    align-items: center;
    padding: 16px;
    gap: 12px;
}

.notification-icon {
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    flex-shrink: 0;
}

.notification-success .notification-icon {
    background: rgba(16, 185, 129, 0.1);
    color: var(--success-color);
}

.notification-error .notification-icon {
    background: rgba(239, 68, 68, 0.1);
    color: var(--danger-color);
}

.notification-warning .notification-icon {
    background: rgba(245, 158, 11, 0.1);
    color: var(--warning-color);
}

.notification-info .notification-icon {
    background: rgba(59, 130, 246, 0.1);
    color: var(--info-color);
}

.notification-message {
    flex: 1;
    color: var(--gray-700);
    font-size: 14px;
    line-height: 1.4;
}

.notification-close {
    background: none;
    border: none;
    color: var(--gray-400);
    cursor: pointer;
    padding: 4px;
    border-radius: 4px;
    transition: var(--transition);
    flex-shrink: 0;
}

.notification-close:hover {
    background: var(--gray-100);
    color: var(--gray-600);
}

.notification-actions {
    padding: 0 16px 16px;
    display: flex;
    gap: 8px;
    justify-content: flex-end;
}

.notification-btn {
    padding: 6px 12px;
    border: 1px solid var(--gray-300);
    background: white;
    color: var(--gray-700);
    border-radius: 4px;
    cursor: pointer;
    font-size: 12px;
    transition: var(--transition);
}

.notification-btn:hover {
    background: var(--gray-50);
    border-color: var(--gray-400);
}

/* 深色主题 */
.theme-dark {
    --gray-50: #1f2937;
    --gray-100: #374151;
    --gray-200: #4b5563;
    --gray-300: #6b7280;
    --gray-400: #9ca3af;
    --gray-500: #d1d5db;
    --gray-600: #e5e7eb;
    --gray-700: #f3f4f6;
    --gray-800: #f9fafb;
    --gray-900: #ffffff;
}

.theme-dark .notification {
    background: var(--gray-100);
    color: var(--gray-700);
}

.theme-dark .notification-message {
    color: var(--gray-700);
}

.theme-dark .skeleton-item {
    background: linear-gradient(90deg, #374151 25%, #4b5563 50%, #374151 75%);
    background-size: 200% 100%;
}

/* 页面加载完成动画 */
body.loaded .stat-card,
body.loaded .dashboard-card {
    animation: slideInUp 0.6s ease forwards;
}

body.loaded .stat-card:nth-child(1) { animation-delay: 0.1s; }
body.loaded .stat-card:nth-child(2) { animation-delay: 0.2s; }
body.loaded .stat-card:nth-child(3) { animation-delay: 0.3s; }
body.loaded .stat-card:nth-child(4) { animation-delay: 0.4s; }

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 禁用动画类 */
.no-animations * {
    animation-duration: 0s !important;
    animation-delay: 0s !important;
    transition-duration: 0s !important;
    transition-delay: 0s !important;
}

/* 通知中心样式 */
.notifications-container {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.notification-item {
    display: flex;
    align-items: flex-start;
    gap: 16px;
    padding: 20px;
    background: white;
    border: 1px solid var(--gray-200);
    border-radius: var(--border-radius);
    transition: var(--transition);
    position: relative;
}

.notification-item:hover {
    box-shadow: var(--shadow-md);
    transform: translateY(-2px);
}

.notification-item.unread {
    border-left: 4px solid var(--primary-color);
    background: linear-gradient(90deg, rgba(99, 102, 241, 0.05) 0%, white 10%);
}

.notification-item.unread::before {
    content: '';
    position: absolute;
    top: 20px;
    right: 20px;
    width: 8px;
    height: 8px;
    background: var(--primary-color);
    border-radius: 50%;
}

.notification-icon {
    width: 48px;
    height: 48px;
    border-radius: var(--border-radius);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 20px;
    color: white;
    flex-shrink: 0;
}

.notification-icon.primary { background: var(--primary-color); }
.notification-icon.success { background: var(--success-color); }
.notification-icon.warning { background: var(--warning-color); }
.notification-icon.danger { background: var(--danger-color); }
.notification-icon.info { background: var(--info-color); }

.notification-content {
    flex: 1;
    min-width: 0;
}

.notification-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 8px;
    gap: 16px;
}

.notification-title {
    font-size: 16px;
    font-weight: 600;
    color: var(--gray-900);
    margin: 0;
    line-height: 1.4;
}

.notification-meta {
    display: flex;
    align-items: center;
    gap: 12px;
    flex-shrink: 0;
}

.notification-type {
    background: var(--gray-100);
    color: var(--gray-600);
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
}

.notification-time {
    color: var(--gray-500);
    font-size: 12px;
}

.notification-priority {
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
}

.notification-priority.priority-high {
    background: rgba(239, 68, 68, 0.1);
    color: var(--danger-color);
}

.notification-priority.priority-medium {
    background: rgba(245, 158, 11, 0.1);
    color: var(--warning-color);
}

.notification-priority.priority-low {
    background: rgba(16, 185, 129, 0.1);
    color: var(--success-color);
}

.notification-message {
    color: var(--gray-600);
    line-height: 1.5;
    margin: 0;
}

.notification-actions {
    display: flex;
    gap: 8px;
    flex-shrink: 0;
}

.notification-filters {
    display: flex;
    gap: 12px;
    align-items: center;
}

.notification-filters select {
    padding: 8px 12px;
    border: 1px solid var(--gray-300);
    border-radius: var(--border-radius-sm);
    background: white;
    font-size: 14px;
    color: var(--gray-700);
    min-width: 120px;
}

.notification-filters select:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

.empty-state {
    text-align: center;
    padding: 60px 20px;
    color: var(--gray-500);
}

.empty-state i {
    font-size: 48px;
    margin-bottom: 16px;
    opacity: 0.5;
}

.empty-state h3 {
    font-size: 18px;
    margin-bottom: 8px;
    color: var(--gray-700);
}

.empty-state p {
    font-size: 14px;
}

/* 分页样式 */
.pagination {
    margin-top: 24px;
}

.pagination-controls {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 8px;
}

.pagination-btn {
    padding: 8px 12px;
    border: 1px solid var(--gray-300);
    background: white;
    color: var(--gray-700);
    border-radius: var(--border-radius-sm);
    cursor: pointer;
    transition: var(--transition);
    font-size: 14px;
    min-width: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.pagination-btn:hover:not(.disabled) {
    background: var(--gray-50);
    border-color: var(--gray-400);
}

.pagination-btn.active {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.pagination-btn.disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.pagination-ellipsis {
    padding: 8px 4px;
    color: var(--gray-400);
    font-size: 14px;
}

/* 帮助中心样式 */
.help-navigation {
    display: flex;
    gap: 16px;
    margin-bottom: 32px;
    padding: 20px;
    background: white;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-sm);
    overflow-x: auto;
}

.help-nav-item {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 12px 20px;
    background: var(--gray-50);
    border: 1px solid var(--gray-200);
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: var(--transition);
    white-space: nowrap;
    min-width: fit-content;
}

.help-nav-item:hover {
    background: var(--gray-100);
    border-color: var(--gray-300);
}

.help-nav-item.active {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.help-nav-item i {
    font-size: 16px;
}

.help-content {
    display: block;
}

.help-section {
    display: none;
}

.help-section.active {
    display: block;
}

.search-box {
    position: relative;
    max-width: 400px;
}

.search-box i {
    position: absolute;
    left: 16px;
    top: 50%;
    transform: translateY(-50%);
    color: var(--gray-400);
}

.search-box input {
    width: 100%;
    padding: 12px 16px 12px 48px;
    border: 1px solid var(--gray-300);
    border-radius: var(--border-radius);
    font-size: 14px;
    background: white;
}

.search-box input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

/* 快速开始样式 */
.getting-started-grid {
    display: grid;
    gap: 24px;
}

.getting-started-item {
    display: flex;
    gap: 20px;
    padding: 24px;
    background: var(--gray-50);
    border-radius: var(--border-radius);
    border-left: 4px solid var(--primary-color);
}

.step-number {
    width: 48px;
    height: 48px;
    background: var(--primary-color);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 20px;
    font-weight: 600;
    flex-shrink: 0;
}

.step-content h4 {
    font-size: 18px;
    font-weight: 600;
    color: var(--gray-900);
    margin-bottom: 8px;
}

.step-content p {
    color: var(--gray-600);
    line-height: 1.6;
    margin-bottom: 12px;
}

.help-link {
    color: var(--primary-color);
    text-decoration: none;
    font-weight: 500;
    display: inline-flex;
    align-items: center;
    gap: 4px;
    transition: var(--transition);
}

.help-link:hover {
    color: var(--primary-dark);
}

/* 视频教程样式 */
.video-tutorials {
    display: grid;
    gap: 16px;
}

.video-item {
    display: flex;
    gap: 16px;
    padding: 16px;
    border: 1px solid var(--gray-200);
    border-radius: var(--border-radius);
    transition: var(--transition);
    cursor: pointer;
}

.video-item:hover {
    box-shadow: var(--shadow-md);
    border-color: var(--primary-color);
}

.video-thumbnail {
    width: 120px;
    height: 80px;
    background: var(--gray-100);
    border-radius: var(--border-radius-sm);
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    flex-shrink: 0;
}

.video-thumbnail i {
    font-size: 32px;
    color: var(--primary-color);
}

.video-duration {
    position: absolute;
    bottom: 8px;
    right: 8px;
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 2px 6px;
    border-radius: 4px;
    font-size: 12px;
}

.video-info h4 {
    font-size: 16px;
    font-weight: 600;
    color: var(--gray-900);
    margin-bottom: 4px;
}

.video-info p {
    color: var(--gray-600);
    font-size: 14px;
    line-height: 1.5;
}

/* 用户指南样式 */
.guide-categories {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 24px;
}

.guide-category {
    padding: 24px;
    background: var(--gray-50);
    border-radius: var(--border-radius);
    border: 1px solid var(--gray-200);
}

.guide-category h4 {
    display: flex;
    align-items: center;
    gap: 12px;
    font-size: 18px;
    font-weight: 600;
    color: var(--gray-900);
    margin-bottom: 16px;
}

.guide-category i {
    color: var(--primary-color);
    font-size: 20px;
}

.guide-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.guide-list li {
    margin-bottom: 8px;
}

.guide-list a {
    color: var(--gray-700);
    text-decoration: none;
    padding: 8px 0;
    display: block;
    border-bottom: 1px solid transparent;
    transition: var(--transition);
}

.guide-list a:hover {
    color: var(--primary-color);
    border-bottom-color: var(--primary-color);
}

/* FAQ样式 */
.faq-container {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.faq-item {
    border: 1px solid var(--gray-200);
    border-radius: var(--border-radius);
    overflow: hidden;
    transition: var(--transition);
}

.faq-item:hover {
    border-color: var(--gray-300);
}

.faq-item.active {
    border-color: var(--primary-color);
}

.faq-question {
    padding: 20px;
    background: var(--gray-50);
    cursor: pointer;
    display: flex;
    justify-content: space-between;
    align-items: center;
    transition: var(--transition);
}

.faq-question:hover {
    background: var(--gray-100);
}

.faq-item.active .faq-question {
    background: var(--primary-color);
    color: white;
}

.faq-question h4 {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
}

.faq-question i {
    transition: transform 0.3s ease;
}

.faq-answer {
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.3s ease;
    background: white;
}

.faq-answer p,
.faq-answer ul,
.faq-answer ol {
    margin: 16px 20px;
    color: var(--gray-700);
    line-height: 1.6;
}

.faq-answer ul,
.faq-answer ol {
    padding-left: 20px;
}

.faq-answer li {
    margin-bottom: 8px;
}

/* API文档样式 */
.api-overview {
    margin-bottom: 32px;
}

.api-info h4 {
    font-size: 20px;
    font-weight: 600;
    color: var(--gray-900);
    margin-bottom: 12px;
}

.api-info p {
    color: var(--gray-600);
    line-height: 1.6;
    margin-bottom: 24px;
}

.api-stats {
    display: flex;
    gap: 32px;
}

.api-stat {
    text-align: center;
}

.api-stat .stat-number {
    display: block;
    font-size: 24px;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: 4px;
}

.api-stat .stat-label {
    font-size: 14px;
    color: var(--gray-600);
}

.api-endpoints {
    margin-bottom: 32px;
}

.api-endpoints h4 {
    font-size: 18px;
    font-weight: 600;
    color: var(--gray-900);
    margin-bottom: 16px;
}

.endpoint-item {
    display: flex;
    align-items: center;
    gap: 16px;
    padding: 12px 16px;
    border: 1px solid var(--gray-200);
    border-radius: var(--border-radius-sm);
    margin-bottom: 8px;
    background: white;
}

.endpoint-method {
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 600;
    min-width: 60px;
    text-align: center;
}

.endpoint-method.get {
    background: rgba(16, 185, 129, 0.1);
    color: var(--success-color);
}

.endpoint-method.post {
    background: rgba(59, 130, 246, 0.1);
    color: var(--info-color);
}

.endpoint-method.put {
    background: rgba(245, 158, 11, 0.1);
    color: var(--warning-color);
}

.endpoint-method.delete {
    background: rgba(239, 68, 68, 0.1);
    color: var(--danger-color);
}

.endpoint-path {
    font-family: 'Courier New', monospace;
    font-weight: 500;
    color: var(--gray-800);
    flex: 1;
}

.endpoint-desc {
    color: var(--gray-600);
    font-size: 14px;
}

.code-block {
    background: var(--gray-900);
    border-radius: var(--border-radius);
    padding: 20px;
    position: relative;
    overflow-x: auto;
}

.code-block pre {
    margin: 0;
    color: #e5e7eb;
    font-family: 'Courier New', monospace;
    font-size: 14px;
    line-height: 1.5;
}

.code-block code {
    color: inherit;
    background: none;
    padding: 0;
}

/* 联系支持样式 */
.contact-options {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 24px;
    margin-bottom: 32px;
}

.contact-option {
    display: flex;
    gap: 16px;
    padding: 24px;
    background: var(--gray-50);
    border-radius: var(--border-radius);
    border: 1px solid var(--gray-200);
    transition: var(--transition);
}

.contact-option:hover {
    box-shadow: var(--shadow-md);
    border-color: var(--primary-color);
}

.contact-icon {
    width: 48px;
    height: 48px;
    background: var(--primary-color);
    color: white;
    border-radius: var(--border-radius);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 20px;
    flex-shrink: 0;
}

.contact-info h4 {
    font-size: 16px;
    font-weight: 600;
    color: var(--gray-900);
    margin-bottom: 4px;
}

.contact-info p {
    color: var(--gray-700);
    margin-bottom: 8px;
}

.response-time {
    font-size: 12px;
    color: var(--gray-500);
    background: var(--gray-100);
    padding: 4px 8px;
    border-radius: 4px;
    display: inline-block;
}

.support-form {
    background: var(--gray-50);
    padding: 24px;
    border-radius: var(--border-radius);
    border: 1px solid var(--gray-200);
}

.support-form h4 {
    font-size: 18px;
    font-weight: 600;
    color: var(--gray-900);
    margin-bottom: 20px;
}

.support-form textarea {
    width: 100%;
    padding: 12px;
    border: 1px solid var(--gray-300);
    border-radius: var(--border-radius-sm);
    font-size: 14px;
    font-family: inherit;
    resize: vertical;
    min-height: 120px;
}

.support-form textarea:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

/* 搜索高亮样式 */
.search-highlight {
    background: yellow;
    padding: 2px 4px;
    border-radius: 2px;
    font-weight: 600;
}

.search-result {
    background: rgba(255, 255, 0, 0.1);
    padding: 8px;
    border-radius: var(--border-radius-sm);
    border-left: 3px solid yellow;
}

/* 响应式设计 - 帮助中心 */
@media (max-width: 768px) {
    .help-navigation {
        flex-direction: column;
        gap: 8px;
    }

    .help-nav-item {
        justify-content: center;
    }

    .getting-started-item {
        flex-direction: column;
        text-align: center;
    }

    .video-item {
        flex-direction: column;
    }

    .video-thumbnail {
        width: 100%;
        height: 120px;
    }

    .guide-categories {
        grid-template-columns: 1fr;
    }

    .api-stats {
        flex-direction: column;
        gap: 16px;
        text-align: center;
    }

    .endpoint-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
    }

    .contact-options {
        grid-template-columns: 1fr;
    }

    .contact-option {
        flex-direction: column;
        text-align: center;
    }
}

/* 库存管理样式 */
.search-filter-group {
    display: flex;
    gap: 12px;
    align-items: center;
}

.search-filter-group select {
    padding: 8px 12px;
    border: 1px solid var(--gray-300);
    border-radius: var(--border-radius-sm);
    background: white;
    font-size: 14px;
    color: var(--gray-700);
    min-width: 120px;
}

.search-filter-group select:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

.product-info {
    display: flex;
    align-items: center;
    gap: 12px;
}

.product-image {
    width: 48px;
    height: 48px;
    border-radius: var(--border-radius-sm);
    object-fit: cover;
    border: 1px solid var(--gray-200);
}

.product-details {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.product-name {
    font-weight: 600;
    color: var(--gray-900);
    font-size: 14px;
}

.product-sku {
    font-size: 12px;
    color: var(--gray-500);
    font-family: 'Courier New', monospace;
}

.category-badge {
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
}

.category-electronics {
    background: rgba(59, 130, 246, 0.1);
    color: var(--info-color);
}

.category-clothing {
    background: rgba(245, 158, 11, 0.1);
    color: var(--warning-color);
}

.category-books {
    background: rgba(16, 185, 129, 0.1);
    color: var(--success-color);
}

.category-home {
    background: rgba(139, 92, 246, 0.1);
    color: #8b5cf6;
}

.stock-quantity {
    font-weight: 600;
    font-size: 16px;
}

.stock-quantity.in-stock {
    color: var(--success-color);
}

.stock-quantity.low-stock {
    color: var(--warning-color);
}

.stock-quantity.out-of-stock {
    color: var(--danger-color);
}

.inventory-actions {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 16px;
}

.inventory-action-btn {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
    padding: 20px;
    background: var(--gray-50);
    border: 1px solid var(--gray-200);
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: var(--transition);
    text-decoration: none;
    color: var(--gray-700);
}

.inventory-action-btn:hover {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.inventory-action-btn i {
    font-size: 24px;
}

.inventory-action-btn span {
    font-weight: 500;
    font-size: 14px;
}

.warning-list {
    display: flex;
    flex-direction: column;
    gap: 12px;
    max-height: 300px;
    overflow-y: auto;
}

.warning-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 16px;
    background: rgba(245, 158, 11, 0.1);
    border: 1px solid rgba(245, 158, 11, 0.2);
    border-radius: var(--border-radius);
}

.warning-icon {
    width: 32px;
    height: 32px;
    background: var(--warning-color);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
    flex-shrink: 0;
}

.warning-content {
    flex: 1;
    min-width: 0;
}

.warning-content h4 {
    font-size: 14px;
    font-weight: 600;
    color: var(--gray-900);
    margin-bottom: 4px;
}

.warning-content p {
    font-size: 12px;
    color: var(--gray-600);
    margin: 0;
}

.warning-badge {
    background: var(--warning-color);
    color: white;
    padding: 4px 8px;
    border-radius: 50%;
    font-size: 12px;
    font-weight: 600;
    min-width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.no-warnings {
    text-align: center;
    padding: 40px 20px;
    color: var(--gray-500);
}

.no-warnings i {
    font-size: 32px;
    color: var(--success-color);
    margin-bottom: 12px;
}

.no-warnings p {
    margin: 0;
    font-size: 14px;
}

/* 报表管理样式 */
.quick-reports {
    display: grid;
    gap: 16px;
}

.quick-report-item {
    display: flex;
    align-items: center;
    gap: 16px;
    padding: 20px;
    background: var(--gray-50);
    border: 1px solid var(--gray-200);
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: var(--transition);
}

.quick-report-item:hover {
    background: white;
    border-color: var(--primary-color);
    box-shadow: var(--shadow-md);
    transform: translateY(-2px);
}

.report-icon {
    width: 48px;
    height: 48px;
    border-radius: var(--border-radius);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 20px;
    color: white;
    flex-shrink: 0;
}

.report-icon.sales { background: var(--primary-color); }
.report-icon.inventory { background: var(--warning-color); }
.report-icon.users { background: var(--success-color); }
.report-icon.financial { background: var(--info-color); }

.report-info {
    flex: 1;
    min-width: 0;
}

.report-info h4 {
    font-size: 16px;
    font-weight: 600;
    color: var(--gray-900);
    margin-bottom: 4px;
}

.report-info p {
    font-size: 14px;
    color: var(--gray-600);
    margin: 0;
}

.reports-list {
    display: flex;
    flex-direction: column;
    gap: 16px;
    max-height: 500px;
    overflow-y: auto;
}

.report-item {
    display: flex;
    align-items: center;
    gap: 16px;
    padding: 20px;
    background: white;
    border: 1px solid var(--gray-200);
    border-radius: var(--border-radius);
    transition: var(--transition);
}

.report-item:hover {
    box-shadow: var(--shadow-md);
    border-color: var(--gray-300);
}

.report-content {
    flex: 1;
    min-width: 0;
}

.report-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 8px;
    gap: 16px;
}

.report-name {
    font-size: 16px;
    font-weight: 600;
    color: var(--gray-900);
    margin: 0;
}

.report-meta {
    display: flex;
    align-items: center;
    gap: 8px;
    flex-shrink: 0;
}

.report-type {
    background: var(--gray-100);
    color: var(--gray-600);
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
}

.report-status {
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
}

.report-status.status-completed {
    background: rgba(16, 185, 129, 0.1);
    color: var(--success-color);
}

.report-status.status-generating {
    background: rgba(245, 158, 11, 0.1);
    color: var(--warning-color);
}

.report-status.status-failed {
    background: rgba(239, 68, 68, 0.1);
    color: var(--danger-color);
}

.report-status.status-scheduled {
    background: rgba(59, 130, 246, 0.1);
    color: var(--info-color);
}

.report-description {
    color: var(--gray-600);
    font-size: 14px;
    margin: 0 0 12px 0;
    line-height: 1.5;
}

.report-details {
    display: flex;
    gap: 16px;
    flex-wrap: wrap;
}

.report-detail {
    display: flex;
    align-items: center;
    gap: 4px;
    font-size: 12px;
    color: var(--gray-500);
}

.report-detail i {
    font-size: 11px;
}

.report-actions {
    display: flex;
    gap: 8px;
    flex-shrink: 0;
}

.report-builder {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.report-builder .form-group {
    margin-bottom: 0;
}

.report-builder select {
    width: 100%;
    padding: 12px;
    border: 1px solid var(--gray-300);
    border-radius: var(--border-radius-sm);
    background: white;
    font-size: 14px;
    color: var(--gray-700);
}

.report-builder select:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

.report-preview {
    min-height: 300px;
    background: var(--gray-50);
    border-radius: var(--border-radius);
    padding: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.report-table {
    width: 100%;
    overflow-x: auto;
}

.chart-placeholder {
    text-align: center;
    color: var(--gray-500);
    padding: 60px 20px;
}

.chart-placeholder i {
    font-size: 48px;
    margin-bottom: 16px;
    color: var(--primary-color);
}

.chart-placeholder h3 {
    font-size: 18px;
    margin-bottom: 8px;
    color: var(--gray-700);
}

.chart-placeholder p {
    font-size: 14px;
    margin: 4px 0;
}

/* 文件管理样式 */
.file-browser-card {
    grid-column: 1 / -1;
}

.file-navigation {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
}

.breadcrumb-nav {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
}

.breadcrumb-item {
    color: var(--primary-color);
    cursor: pointer;
    padding: 4px 8px;
    border-radius: 4px;
    transition: var(--transition);
}

.breadcrumb-item:hover {
    background: rgba(99, 102, 241, 0.1);
}

.file-view-controls {
    display: flex;
    gap: 4px;
}

.view-btn {
    padding: 8px;
    border: 1px solid var(--gray-300);
    background: white;
    color: var(--gray-600);
    border-radius: var(--border-radius-sm);
    cursor: pointer;
    transition: var(--transition);
}

.view-btn:hover,
.view-btn.active {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.file-toolbar {
    display: flex;
    gap: 12px;
    align-items: center;
    margin-bottom: 16px;
}

.file-toolbar select {
    padding: 8px 12px;
    border: 1px solid var(--gray-300);
    border-radius: var(--border-radius-sm);
    background: white;
    font-size: 14px;
    color: var(--gray-700);
    min-width: 120px;
}

.file-container {
    min-height: 400px;
}

.file-container.grid-view {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 16px;
}

.file-container.list-view {
    display: flex;
    flex-direction: column;
}

.file-item {
    display: flex;
    flex-direction: column;
    padding: 16px;
    background: white;
    border: 1px solid var(--gray-200);
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: var(--transition);
    position: relative;
}

.file-item:hover {
    box-shadow: var(--shadow-md);
    border-color: var(--primary-color);
    transform: translateY(-2px);
}

.file-thumbnail {
    position: relative;
    margin-bottom: 12px;
    text-align: center;
}

.file-icon {
    font-size: 48px;
    color: var(--primary-color);
}

.file-icon img {
    width: 64px;
    height: 64px;
    object-fit: cover;
    border-radius: var(--border-radius-sm);
}

.shared-indicator {
    position: absolute;
    top: -8px;
    right: -8px;
    width: 20px;
    height: 20px;
    background: var(--success-color);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 10px;
}

.file-info {
    flex: 1;
    text-align: center;
}

.file-name {
    font-weight: 600;
    color: var(--gray-900);
    margin-bottom: 4px;
    word-break: break-word;
    font-size: 14px;
}

.file-meta {
    display: flex;
    justify-content: space-between;
    font-size: 12px;
    color: var(--gray-500);
}

.file-actions {
    display: flex;
    justify-content: center;
    gap: 8px;
    margin-top: 12px;
    opacity: 0;
    transition: var(--transition);
}

.file-item:hover .file-actions {
    opacity: 1;
}

/* 列表视图样式 */
.file-list-header {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr 1fr 100px;
    gap: 16px;
    padding: 12px 16px;
    background: var(--gray-50);
    border: 1px solid var(--gray-200);
    border-radius: var(--border-radius-sm);
    font-weight: 600;
    color: var(--gray-700);
    font-size: 14px;
}

.file-list-item {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr 1fr 100px;
    gap: 16px;
    padding: 12px 16px;
    border: 1px solid var(--gray-200);
    border-top: none;
    cursor: pointer;
    transition: var(--transition);
    align-items: center;
}

.file-list-item:hover {
    background: var(--gray-50);
}

.file-list-item:last-child {
    border-radius: 0 0 var(--border-radius-sm) var(--border-radius-sm);
}

.file-col-name {
    display: flex;
    align-items: center;
    gap: 12px;
}

.file-icon-small {
    font-size: 20px;
    color: var(--primary-color);
}

.file-icon-small img {
    width: 24px;
    height: 24px;
    object-fit: cover;
    border-radius: 4px;
}

.shared-icon {
    color: var(--success-color);
    font-size: 12px;
    margin-left: 8px;
}

.file-col-size,
.file-col-date,
.file-col-owner {
    font-size: 14px;
    color: var(--gray-600);
}

.file-col-actions {
    display: flex;
    gap: 4px;
    justify-content: center;
}

/* 文件操作面板 */
.file-actions {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 16px;
}

.file-action-btn {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
    padding: 20px;
    background: var(--gray-50);
    border: 1px solid var(--gray-200);
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: var(--transition);
    text-decoration: none;
    color: var(--gray-700);
}

.file-action-btn:hover {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.file-action-btn i {
    font-size: 24px;
}

.file-action-btn span {
    font-weight: 500;
    font-size: 14px;
}

/* 最近文件 */
.recent-files {
    display: flex;
    flex-direction: column;
    gap: 12px;
    max-height: 300px;
    overflow-y: auto;
}

.recent-file-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px;
    background: var(--gray-50);
    border-radius: var(--border-radius-sm);
    cursor: pointer;
    transition: var(--transition);
}

.recent-file-item:hover {
    background: var(--gray-100);
}

.recent-file-icon {
    font-size: 20px;
    color: var(--primary-color);
}

.recent-file-info {
    flex: 1;
    min-width: 0;
}

.recent-file-name {
    font-weight: 500;
    color: var(--gray-900);
    font-size: 14px;
    margin-bottom: 2px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.recent-file-time {
    font-size: 12px;
    color: var(--gray-500);
}

.no-recent-files {
    text-align: center;
    padding: 40px 20px;
    color: var(--gray-500);
}

.no-recent-files i {
    font-size: 32px;
    margin-bottom: 12px;
    opacity: 0.5;
}

.no-recent-files p {
    margin: 0;
    font-size: 14px;
}

/* 文件上传样式 */
.modal-container.large {
    max-width: 800px;
}

.modal-container.extra-large {
    max-width: 90vw;
    max-height: 95vh;
}

/* 桌面端模态框优化 */
@media (min-width: 769px) {
    .modal-overlay {
        padding: 20px;
    }

    .modal-container {
        max-height: calc(100vh - 40px);
        min-height: 300px;
    }

    /* 确保表单内容有足够空间 */
    .modal-container .form-group {
        margin-bottom: 20px;
    }

    .modal-container .form-row {
        margin-bottom: 16px;
    }
}

.upload-zone {
    border: 2px dashed var(--gray-300);
    border-radius: var(--border-radius);
    padding: 60px 20px;
    text-align: center;
    background: var(--gray-50);
    transition: var(--transition);
    cursor: pointer;
}

.upload-zone:hover,
.upload-zone.drag-over {
    border-color: var(--primary-color);
    background: rgba(99, 102, 241, 0.05);
}

.upload-zone i {
    font-size: 48px;
    color: var(--primary-color);
    margin-bottom: 16px;
}

.upload-zone h4 {
    font-size: 18px;
    color: var(--gray-900);
    margin-bottom: 8px;
}

.upload-zone p {
    color: var(--gray-600);
    margin-bottom: 20px;
}

.upload-items {
    display: flex;
    flex-direction: column;
    gap: 12px;
    max-height: 300px;
    overflow-y: auto;
}

.upload-item {
    display: flex;
    align-items: center;
    gap: 16px;
    padding: 16px;
    background: var(--gray-50);
    border: 1px solid var(--gray-200);
    border-radius: var(--border-radius-sm);
}

.upload-file-info {
    display: flex;
    align-items: center;
    gap: 12px;
    flex: 1;
    min-width: 0;
}

.upload-file-icon {
    font-size: 24px;
    color: var(--primary-color);
}

.upload-file-details {
    flex: 1;
    min-width: 0;
}

.upload-file-name {
    font-weight: 500;
    color: var(--gray-900);
    margin-bottom: 2px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.upload-file-size {
    font-size: 12px;
    color: var(--gray-500);
}

.upload-progress {
    display: flex;
    flex-direction: column;
    gap: 4px;
    min-width: 120px;
}

.progress-bar {
    width: 100%;
    height: 6px;
    background: var(--gray-200);
    border-radius: 3px;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background: var(--primary-color);
    transition: width 0.3s ease;
}

.upload-status {
    font-size: 12px;
    color: var(--gray-600);
    text-align: center;
}

/* 文件预览样式 */
.preview-actions {
    display: flex;
    gap: 8px;
    align-items: center;
}

.file-preview {
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 400px;
    background: var(--gray-50);
    border-radius: var(--border-radius);
}

.image-preview img {
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-lg);
}

.video-preview,
.audio-preview {
    width: 100%;
    text-align: center;
}

.audio-info {
    margin-top: 20px;
    text-align: center;
}

.audio-info h4 {
    color: var(--gray-900);
    margin-bottom: 8px;
}

.audio-info p {
    color: var(--gray-600);
    margin: 4px 0;
}

.document-preview {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 20px;
    padding: 40px;
    text-align: center;
}

.document-icon i {
    font-size: 64px;
    color: var(--primary-color);
}

.document-info h4 {
    color: var(--gray-900);
    margin-bottom: 16px;
}

.document-info p {
    color: var(--gray-600);
    margin: 4px 0;
}

.file-preview-default {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 20px;
    padding: 40px;
    text-align: center;
}

.file-icon-large {
    font-size: 64px;
    color: var(--primary-color);
}

.file-details h4 {
    color: var(--gray-900);
    margin-bottom: 16px;
}

.file-details p {
    color: var(--gray-600);
    margin: 4px 0;
}

/* 响应式设计 - 文件管理 */
@media (max-width: 768px) {
    .file-navigation {
        flex-direction: column;
        gap: 12px;
        align-items: stretch;
    }

    .file-toolbar {
        flex-direction: column;
        gap: 8px;
    }

    .file-toolbar select {
        min-width: auto;
    }

    .file-container.grid-view {
        grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
        gap: 12px;
    }

    .file-list-header,
    .file-list-item {
        grid-template-columns: 2fr 80px 60px;
        gap: 8px;
    }

    .file-col-date,
    .file-col-owner {
        display: none;
    }

    .file-actions {
        grid-template-columns: 1fr;
        gap: 12px;
    }

    .file-action-btn {
        flex-direction: row;
        justify-content: center;
        padding: 16px;
    }

    .file-action-btn i {
        font-size: 20px;
    }

    .upload-zone {
        padding: 40px 20px;
    }

    .upload-zone i {
        font-size: 36px;
    }

    .upload-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 12px;
    }

    .upload-progress {
        width: 100%;
        min-width: auto;
    }

    .modal-container.extra-large {
        max-width: 95vw;
        max-height: 95vh;
    }
}

/* 个人资料模态框样式 */
.profile-tabs {
    display: flex;
    border-bottom: 1px solid var(--gray-200);
    margin-bottom: 24px;
}

.profile-tab {
    background: none;
    border: none;
    padding: 12px 20px;
    font-size: 14px;
    font-weight: 500;
    color: var(--gray-600);
    cursor: pointer;
    border-bottom: 2px solid transparent;
    transition: all 0.2s ease;
}

.profile-tab:hover {
    color: var(--primary-color);
    background: var(--gray-50);
}

.profile-tab.active {
    color: var(--primary-color);
    border-bottom-color: var(--primary-color);
    background: var(--gray-50);
}

.profile-tab-content {
    display: none;
}

.profile-tab-content.active {
    display: block;
}

/* 头像上传区域 */
.profile-avatar-section {
    margin-bottom: 32px;
    text-align: center;
}

.avatar-upload-container {
    display: inline-block;
}

.avatar-preview {
    position: relative;
    width: 80px;
    height: 80px;
    margin: 0 auto 16px;
    cursor: pointer;
    border-radius: 50%;
    overflow: hidden;
    border: 3px solid var(--gray-200);
    transition: all 0.3s ease;
}

.avatar-preview:hover {
    border-color: var(--primary-color);
    transform: scale(1.05);
}

.avatar-preview img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.avatar-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.6);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s ease;
    color: white;
    font-size: 12px;
}

.avatar-preview:hover .avatar-overlay {
    opacity: 1;
}

.avatar-overlay i {
    font-size: 16px;
    margin-bottom: 4px;
}

.avatar-info {
    font-size: 12px;
    color: var(--gray-500);
    line-height: 1.4;
}

.avatar-info p {
    margin: 2px 0;
}

/* 表单字段错误提示 */
.field-error {
    color: var(--danger-color);
    font-size: 12px;
    margin-top: 4px;
    display: none;
}

.field-error.show {
    display: block;
}

.form-group.error input,
.form-group.error select,
.form-group.error textarea {
    border-color: var(--danger-color);
    box-shadow: 0 0 0 2px rgba(239, 68, 68, 0.1);
}

/* 个人资料表单特殊样式 */
#profileForm .form-group label {
    font-weight: 500;
    color: var(--gray-700);
}

#profileForm input[readonly] {
    background: var(--gray-50);
    color: var(--gray-500);
    cursor: not-allowed;
}

/* 视频播放器模态框样式 */
.video-player-container {
    position: relative;
    background: #000;
    border-radius: var(--border-radius);
    overflow: hidden;
    margin-bottom: 20px;
}

#mainVideoPlayer {
    width: 100%;
    height: auto;
    max-height: 60vh;
    display: block;
}

.video-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(0, 0, 0, 0.7);
    color: white;
    z-index: 2;
}

.video-loading,
.video-error {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 12px;
    text-align: center;
}

.video-loading i {
    font-size: 24px;
}

.video-custom-controls {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));
    color: white;
    padding: 20px 16px 16px;
    opacity: 0;
    transition: opacity 0.3s ease;
    z-index: 3;
}

.video-player-container:hover .video-custom-controls {
    opacity: 1;
}

.progress-container {
    margin-bottom: 12px;
}

.progress-bar {
    position: relative;
    height: 4px;
    background: rgba(255, 255, 255, 0.3);
    border-radius: 2px;
    cursor: pointer;
    margin-bottom: 8px;
}

.progress-filled {
    height: 100%;
    background: var(--primary-color);
    border-radius: 2px;
    width: 0%;
    transition: width 0.1s ease;
}

.progress-handle {
    position: absolute;
    top: -6px;
    width: 16px;
    height: 16px;
    background: var(--primary-color);
    border-radius: 50%;
    cursor: pointer;
    opacity: 0;
    transition: opacity 0.2s ease;
    transform: translateX(-50%);
}

.progress-bar:hover .progress-handle {
    opacity: 1;
}

.time-display {
    font-size: 12px;
    color: rgba(255, 255, 255, 0.8);
}

.controls-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.controls-left,
.controls-right {
    display: flex;
    align-items: center;
    gap: 12px;
}

.control-btn {
    background: none;
    border: none;
    color: white;
    font-size: 16px;
    cursor: pointer;
    padding: 8px;
    border-radius: 4px;
    transition: all 0.2s ease;
}

.control-btn:hover {
    background: rgba(255, 255, 255, 0.2);
}

.volume-container {
    display: flex;
    align-items: center;
    gap: 8px;
}

.volume-slider {
    width: 80px;
    height: 4px;
    background: rgba(255, 255, 255, 0.3);
    border-radius: 2px;
    outline: none;
    cursor: pointer;
}

.volume-slider::-webkit-slider-thumb {
    appearance: none;
    width: 12px;
    height: 12px;
    background: var(--primary-color);
    border-radius: 50%;
    cursor: pointer;
}

.speed-selector {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.3);
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    cursor: pointer;
}

.speed-selector option {
    background: var(--gray-800);
    color: white;
}

.video-controls-header {
    display: flex;
    gap: 8px;
}

.video-info-panel {
    display: grid;
    grid-template-columns: 1fr auto;
    gap: 20px;
    margin-bottom: 20px;
    padding: 16px;
    background: var(--gray-50);
    border-radius: var(--border-radius);
}

.video-details h4 {
    margin: 0 0 8px 0;
    color: var(--gray-700);
}

.video-meta {
    display: flex;
    gap: 16px;
    margin-bottom: 12px;
    font-size: 12px;
    color: var(--gray-500);
}

.video-description {
    font-size: 14px;
    color: var(--gray-600);
    line-height: 1.5;
}

.video-actions {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.video-playlist {
    border-top: 1px solid var(--gray-200);
    padding-top: 20px;
}

.video-playlist h4 {
    margin: 0 0 16px 0;
    color: var(--gray-700);
}

.playlist-items {
    display: grid;
    gap: 8px;
    max-height: 200px;
    overflow-y: auto;
}

.playlist-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 8px 12px;
    background: white;
    border: 1px solid var(--gray-200);
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: all 0.2s ease;
}

.playlist-item:hover {
    background: var(--gray-50);
    border-color: var(--primary-color);
}

.playlist-item.active {
    background: var(--primary-light);
    border-color: var(--primary-color);
}

.playlist-item .thumbnail {
    width: 60px;
    height: 40px;
    background: var(--gray-200);
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--gray-500);
    font-size: 12px;
}

.playlist-item .info {
    flex: 1;
}

.playlist-item .title {
    font-weight: 500;
    color: var(--gray-700);
    margin-bottom: 4px;
}

.playlist-item .duration {
    font-size: 12px;
    color: var(--gray-500);
}

.video-progress-info {
    font-size: 14px;
    color: var(--gray-600);
}

/* 全屏视频样式 */
.video-fullscreen {
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    width: 100vw !important;
    height: 100vh !important;
    z-index: 9999 !important;
    background: #000 !important;
}

.video-fullscreen #mainVideoPlayer {
    width: 100vw;
    height: 100vh;
    max-height: none;
}

/* 移动端视频播放器优化 */
@media (max-width: 768px) {
    .video-info-panel {
        grid-template-columns: 1fr;
        gap: 16px;
    }
    
    .video-actions {
        flex-direction: row;
        flex-wrap: wrap;
    }
    
    .video-meta {
        flex-direction: column;
        gap: 4px;
    }
    
    .controls-row {
        flex-wrap: wrap;
        gap: 8px;
    }
    
    .volume-container {
        display: none;
    }
    
    .control-btn {
        padding: 12px;
        font-size: 18px;
    }
    
    .playlist-item .thumbnail {
        width: 50px;
        height: 30px;
    }
}

/* 文件操作模态框样式 */
.selected-files-info {
    margin-bottom: 24px;
    padding: 16px;
    background: var(--gray-50);
    border-radius: var(--border-radius);
}

.selected-files-list {
    margin-top: 12px;
}

.selected-file-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 8px 12px;
    background: white;
    border-radius: var(--border-radius);
    margin-bottom: 8px;
    border: 1px solid var(--gray-200);
}

.selected-file-item .file-icon {
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--primary-color);
}

.selected-file-item .file-name {
    flex: 1;
    font-weight: 500;
    color: var(--gray-700);
}

.selected-file-item .file-size {
    font-size: 12px;
    color: var(--gray-500);
}

.destination-selector {
    margin-bottom: 24px;
}

.directory-tree {
    max-height: 300px;
    overflow-y: auto;
    border: 1px solid var(--gray-200);
    border-radius: var(--border-radius);
    padding: 12px;
    background: white;
}

.directory-item {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 12px;
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: all 0.2s ease;
    margin-bottom: 4px;
}

.directory-item:hover {
    background: var(--gray-100);
}

.directory-item.selected {
    background: var(--primary-light);
    color: var(--primary-color);
}

.directory-item .folder-icon {
    color: var(--warning-color);
}

.directory-item .folder-name {
    flex: 1;
    font-weight: 500;
}

.directory-item .folder-path {
    font-size: 12px;
    color: var(--gray-500);
}

.new-folder-section {
    margin-top: 16px;
}

.new-folder-input {
    display: flex;
    gap: 8px;
    align-items: center;
    margin-top: 12px;
    padding: 12px;
    background: var(--gray-50);
    border-radius: var(--border-radius);
}

.new-folder-input input {
    flex: 1;
    padding: 8px 12px;
    border: 1px solid var(--gray-300);
    border-radius: var(--border-radius);
}

.operation-options {
    padding: 16px;
    background: var(--gray-50);
    border-radius: var(--border-radius);
}

.operation-options .checkbox-label {
    display: flex;
    align-items: center;
    margin-bottom: 12px;
    cursor: pointer;
}

.operation-options input[type="radio"] {
    margin-right: 12px;
}

/* 文件重命名模态框样式 */
.current-file-info {
    display: flex;
    align-items: center;
    gap: 16px;
    padding: 16px;
    background: var(--gray-50);
    border-radius: var(--border-radius);
    margin-bottom: 20px;
}

.current-file-info .file-icon {
    width: 48px;
    height: 48px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--primary-light);
    color: var(--primary-color);
    border-radius: var(--border-radius);
    font-size: 20px;
}

.current-file-info .file-details {
    flex: 1;
}

.current-file-info .current-name {
    font-weight: 600;
    color: var(--gray-700);
    margin-bottom: 4px;
}

.current-file-info .file-type {
    font-size: 12px;
    color: var(--gray-500);
    text-transform: uppercase;
}

.rename-tips {
    margin-top: 20px;
    padding: 16px;
    background: var(--info-light);
    border-radius: var(--border-radius);
    border-left: 4px solid var(--info-color);
}

.rename-tips h4 {
    margin: 0 0 12px 0;
    color: var(--info-color);
    font-size: 14px;
}

.rename-tips ul {
    margin: 0;
    padding-left: 20px;
}

.rename-tips li {
    font-size: 13px;
    color: var(--gray-600);
    margin-bottom: 4px;
}

/* 右键菜单样式 */
.context-menu {
    position: fixed;
    background: white;
    border: 1px solid var(--gray-200);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-lg);
    padding: 8px 0;
    min-width: 160px;
    z-index: 10000;
    display: none;
}

.context-menu.show {
    display: block;
}

.context-menu-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 8px 16px;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 14px;
}

.context-menu-item:hover {
    background: var(--gray-100);
}

.context-menu-item.danger {
    color: var(--danger-color);
}

.context-menu-item.danger:hover {
    background: var(--danger-light);
}

.context-menu-item i {
    width: 16px;
    text-align: center;
}

.context-menu-divider {
    height: 1px;
    background: var(--gray-200);
    margin: 4px 0;
}

/* 移动端文件操作优化 */
@media (max-width: 768px) {
    .selected-files-info {
        padding: 12px;
    }
    
    .directory-tree {
        max-height: 200px;
    }
    
    .directory-item {
        padding: 12px 8px;
    }
    
    .new-folder-input {
        flex-direction: column;
        align-items: stretch;
    }
    
    .new-folder-input .btn-sm {
        margin-top: 8px;
    }
    
    .current-file-info {
        flex-direction: column;
        text-align: center;
    }
    
    .context-menu {
        min-width: 140px;
    }
    
    .context-menu-item {
        padding: 12px 16px;
        font-size: 16px;
    }
}

/* 登录历史模态框样式 */
.login-history-header {
    margin-bottom: 24px;
}

.history-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 16px;
    margin-bottom: 20px;
    padding: 20px;
    background: var(--gray-50);
    border-radius: var(--border-radius);
}

.stat-item {
    text-align: center;
}

.stat-item .stat-number {
    display: block;
    font-size: 24px;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: 4px;
}

.stat-item .stat-label {
    font-size: 12px;
    color: var(--gray-600);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.history-filters {
    display: flex;
    gap: 16px;
    align-items: end;
    flex-wrap: wrap;
}

.filter-group {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.filter-group label {
    font-size: 12px;
    font-weight: 500;
    color: var(--gray-700);
}

.filter-group select {
    padding: 8px 12px;
    border: 1px solid var(--gray-300);
    border-radius: var(--border-radius);
    font-size: 14px;
    min-width: 120px;
}

.login-history-table {
    margin: 20px 0;
    max-height: 400px;
    overflow-y: auto;
    border: 1px solid var(--gray-200);
    border-radius: var(--border-radius);
}

.login-history-table .modern-table {
    margin: 0;
}

.login-history-table th {
    position: sticky;
    top: 0;
    background: var(--gray-50);
    z-index: 1;
}

.status-badge.success {
    background: var(--success-light);
    color: var(--success-color);
}

.status-badge.failed {
    background: var(--danger-light);
    color: var(--danger-color);
}

.status-badge.suspicious {
    background: var(--warning-light);
    color: var(--warning-color);
}

.device-info {
    font-size: 12px;
    color: var(--gray-600);
}

.device-info .device-type {
    font-weight: 500;
    color: var(--gray-700);
}

.location-info {
    font-size: 12px;
    color: var(--gray-600);
}

.location-info .country {
    font-weight: 500;
    color: var(--gray-700);
}

.history-pagination {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 20px;
    padding-top: 16px;
    border-top: 1px solid var(--gray-200);
}

.pagination-info {
    font-size: 14px;
    color: var(--gray-600);
}

.pagination-controls {
    display: flex;
    align-items: center;
    gap: 12px;
}

.page-info {
    font-size: 14px;
    color: var(--gray-600);
    margin: 0 8px;
}

.security-alerts {
    margin-top: 20px;
}

.security-alert {
    padding: 12px 16px;
    border-radius: var(--border-radius);
    margin-bottom: 12px;
    display: flex;
    align-items: center;
    gap: 12px;
}

.security-alert.warning {
    background: var(--warning-light);
    border-left: 4px solid var(--warning-color);
}

.security-alert.danger {
    background: var(--danger-light);
    border-left: 4px solid var(--danger-color);
}

.security-alert.info {
    background: var(--info-light);
    border-left: 4px solid var(--info-color);
}

.security-alert-icon {
    font-size: 16px;
    flex-shrink: 0;
}

.security-alert-content {
    flex: 1;
}

.security-alert-title {
    font-weight: 600;
    margin-bottom: 4px;
}

.security-alert-message {
    font-size: 14px;
    color: var(--gray-600);
}

.security-alert-actions {
    display: flex;
    gap: 8px;
    margin-top: 8px;
}

.security-alert-actions .btn-sm {
    padding: 4px 12px;
    font-size: 12px;
}

/* 移动端登录历史优化 */
@media (max-width: 768px) {
    .history-stats {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .history-filters {
        flex-direction: column;
        align-items: stretch;
    }
    
    .filter-group {
        flex-direction: row;
        align-items: center;
        justify-content: space-between;
    }
    
    .login-history-table {
        font-size: 12px;
    }
    
    .login-history-table th,
    .login-history-table td {
        padding: 8px 4px;
    }
    
    .history-pagination {
        flex-direction: column;
        gap: 12px;
        text-align: center;
    }
    
    .pagination-controls {
        justify-content: center;
    }
}

/* 密码修改模态框样式 */
.password-input-group {
    position: relative;
    display: flex;
    align-items: center;
}

.password-input-group input {
    flex: 1;
    padding-right: 45px;
}

.password-toggle {
    position: absolute;
    right: 12px;
    background: none;
    border: none;
    color: var(--gray-500);
    cursor: pointer;
    padding: 4px;
    border-radius: 4px;
    transition: all 0.2s ease;
}

.password-toggle:hover {
    color: var(--primary-color);
    background: var(--gray-100);
}

/* 密码强度指示器 */
.password-strength-meter {
    margin-top: 8px;
}

.strength-bar {
    height: 4px;
    background: var(--gray-200);
    border-radius: 2px;
    overflow: hidden;
    margin-bottom: 4px;
}

.strength-fill {
    height: 100%;
    width: 0%;
    transition: all 0.3s ease;
    border-radius: 2px;
}

.strength-fill.weak {
    width: 25%;
    background: var(--danger-color);
}

.strength-fill.fair {
    width: 50%;
    background: var(--warning-color);
}

.strength-fill.good {
    width: 75%;
    background: var(--info-color);
}

.strength-fill.strong {
    width: 100%;
    background: var(--success-color);
}

.strength-text {
    font-size: 12px;
    color: var(--gray-600);
}

.strength-text.weak {
    color: var(--danger-color);
}

.strength-text.fair {
    color: var(--warning-color);
}

.strength-text.good {
    color: var(--info-color);
}

.strength-text.strong {
    color: var(--success-color);
}

/* 密码要求列表 */
.password-requirements {
    margin: 20px 0;
    padding: 16px;
    background: var(--gray-50);
    border-radius: var(--border-radius);
    border-left: 4px solid var(--info-color);
}

.password-requirements h4 {
    margin: 0 0 12px 0;
    font-size: 14px;
    color: var(--gray-700);
}

.password-requirements ul {
    margin: 0;
    padding: 0;
    list-style: none;
}

.password-requirements li {
    font-size: 13px;
    color: var(--gray-600);
    margin-bottom: 4px;
    padding-left: 20px;
    position: relative;
}

.password-requirements li::before {
    content: '✗';
    position: absolute;
    left: 0;
    color: var(--danger-color);
    font-weight: bold;
}

.password-requirements li.valid::before {
    content: '✓';
    color: var(--success-color);
}

/* 安全选项 */
.security-options {
    margin-top: 20px;
    padding-top: 16px;
    border-top: 1px solid var(--gray-200);
}

.checkbox-label {
    display: flex;
    align-items: center;
    cursor: pointer;
    font-size: 14px;
    color: var(--gray-700);
}

.checkbox-label input[type="checkbox"] {
    display: none;
}

.checkmark {
    width: 18px;
    height: 18px;
    border: 2px solid var(--gray-300);
    border-radius: 3px;
    margin-right: 10px;
    position: relative;
    transition: all 0.2s ease;
}

.checkbox-label:hover .checkmark {
    border-color: var(--primary-color);
}

.checkbox-label input[type="checkbox"]:checked + .checkmark {
    background: var(--primary-color);
    border-color: var(--primary-color);
}

.checkbox-label input[type="checkbox"]:checked + .checkmark::after {
    content: '✓';
    position: absolute;
    top: -2px;
    left: 2px;
    color: white;
    font-size: 12px;
    font-weight: bold;
}

/* 移动端个人资料优化 */
@media (max-width: 768px) {
    .profile-tabs {
        overflow-x: auto;
        -webkit-overflow-scrolling: touch;
    }
    
    .profile-tab {
        white-space: nowrap;
        min-width: 80px;
    }
    
    .avatar-preview {
        width: 60px;
        height: 60px;
    }
    
    .profile-avatar-section {
        margin-bottom: 24px;
    }
}

/* 超小屏幕优化 (小于480px) */
@media (max-width: 480px) {
    .modal-overlay {
        padding: 0;
    }

    /* 超小屏幕全屏模态框 */
    .modal-overlay {
        padding: 0;
    }

    .modal-container {
        width: 100%;
        height: 100vh;
        max-height: 100vh;
        border-radius: 0;
        /* 完全全屏显示 */
    }

    .modal-header {
        padding: 12px 16px;
        flex-shrink: 0;
        background: var(--gray-50);
        border-bottom: 1px solid var(--gray-200);
    }

    .modal-content {
        padding: 16px;
        /* 超小屏幕确保滚动 */
        overflow-y: auto;
        -webkit-overflow-scrolling: touch;
    }

    .modal-footer {
        padding: 12px 16px;
        flex-shrink: 0;
        background: var(--gray-50);
        border-top: 1px solid var(--gray-200);
    }
}

/* 响应式设计 - 库存管理 */
@media (max-width: 768px) {
    .search-filter-group {
        flex-direction: column;
        align-items: stretch;
        gap: 8px;
    }

    .search-filter-group select {
        min-width: auto;
    }

    .product-info {
        flex-direction: column;
        text-align: center;
        gap: 8px;
    }

    .product-image {
        width: 40px;
        height: 40px;
    }

    .inventory-actions {
        grid-template-columns: 1fr;
        gap: 12px;
    }

    .inventory-action-btn {
        flex-direction: row;
        justify-content: center;
        padding: 16px;
    }

    .inventory-action-btn i {
        font-size: 20px;
    }

    .warning-item {
        flex-direction: column;
        text-align: center;
        gap: 8px;
    }

    .warning-content {
        text-align: center;
    }

    .quick-report-item {
        flex-direction: column;
        text-align: center;
        gap: 12px;
    }

    .report-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 12px;
    }

    .report-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
    }

    .report-meta {
        flex-wrap: wrap;
    }

    .report-details {
        flex-direction: column;
        gap: 8px;
    }

    .report-actions {
        width: 100%;
        justify-content: center;
    }
}

/* 响应式通知 */
@media (max-width: 768px) {
    .notification-container {
        top: 10px;
        right: 10px;
        left: 10px;
        max-width: none;
    }

    .notification-content {
        padding: 12px;
    }

    .notification-message {
        font-size: 13px;
    }

    .notification-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
    }

    .notification-meta {
        flex-wrap: wrap;
        gap: 8px;
    }

    .notification-filters {
        flex-direction: column;
        align-items: stretch;
        gap: 8px;
    }

    .notification-filters select {
        min-width: auto;
    }

    .notification-item {
        padding: 16px;
        gap: 12px;
    }

    .notification-icon {
        width: 40px;
        height: 40px;
        font-size: 18px;
    }

    .notification-title {
        font-size: 15px;
    }

    .notification-actions {
        flex-direction: column;
        gap: 4px;
    }
}

/* NER数据管理样式 */
.ner-page {
    padding: 2rem;
}

.ner-samples {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.ner-sample {
    background: white;
    border: 1px solid var(--gray-200);
    border-radius: var(--border-radius);
    padding: 1rem;
    cursor: pointer;
    transition: var(--transition);
}

.ner-sample:hover {
    border-color: var(--primary-color);
    box-shadow: var(--shadow-md);
}

.sample-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.5rem;
}

.sample-id {
    font-weight: 600;
    color: var(--primary-color);
}

.sample-length {
    font-size: 0.875rem;
    color: var(--gray-500);
}

.sample-text {
    margin: 0.5rem 0;
    line-height: 1.6;
    font-size: 0.95rem;
}

.sample-stats {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 0.5rem;
    padding-top: 0.5rem;
    border-top: 1px solid var(--gray-100);
}

.entity-count {
    font-size: 0.875rem;
    color: var(--gray-600);
}

/* 实体高亮样式 */
.entity {
    padding: 2px 4px;
    border-radius: 4px;
    font-weight: 500;
    font-size: 0.9em;
    margin: 0 1px;
}

.entity-person {
    background-color: #fef3c7;
    color: #92400e;
    border: 1px solid #fcd34d;
}

.entity-location {
    background-color: #dbeafe;
    color: #1e40af;
    border: 1px solid #60a5fa;
}

.entity-organization {
    background-color: #d1fae5;
    color: #065f46;
    border: 1px solid #34d399;
}

.entity-time {
    background-color: #fce7f3;
    color: #be185d;
    border: 1px solid #f472b6;
}

.entity-number {
    background-color: #e0e7ff;
    color: #3730a3;
    border: 1px solid #a5b4fc;
}

/* 复选框组样式 */
.checkbox-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.checkbox-item {
    display: flex;
    align-items: center;
    cursor: pointer;
    padding: 0.5rem;
    border-radius: var(--border-radius-sm);
    transition: var(--transition);
}

.checkbox-item:hover {
    background-color: var(--gray-50);
}

.checkbox-item input[type="checkbox"] {
    margin-right: 0.5rem;
    width: 16px;
    height: 16px;
    accent-color: var(--primary-color);
}

.checkmark {
    margin-left: 0.25rem;
}

/* 预览占位符样式 */
.preview-placeholder {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 3rem;
    color: var(--gray-500);
    text-align: center;
}

.preview-placeholder i {
    font-size: 3rem;
    margin-bottom: 1rem;
    opacity: 0.5;
}

.preview-placeholder p {
    font-size: 1.1rem;
}

/* 图表容器样式 */
.chart-container {
    position: relative;
    height: 300px;
    width: 100%;
}

/* 增强用户管理样式 */
.bulk-actions-toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    border: 1px solid var(--gray-200);
    border-radius: var(--border-radius-sm);
    margin-bottom: 16px;
}

.bulk-actions-info {
    font-weight: 600;
    color: var(--text-secondary);
}

.bulk-actions-buttons {
    display: flex;
    gap: 8px;
}

.advanced-filters {
    background: var(--gray-50);
    border: 1px solid var(--gray-200);
    border-radius: var(--border-radius-sm);
    padding: 16px;
    margin: 16px 0;
}

.filter-row {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 16px;
    margin-bottom: 16px;
}

.filter-group {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.filter-group label {
    font-weight: 600;
    color: var(--text-secondary);
    font-size: 13px;
}

.filter-group input,
.filter-group select {
    padding: 8px 12px;
    border: 1px solid var(--gray-300);
    border-radius: var(--border-radius-sm);
    font-size: 14px;
}

.filter-group span {
    align-self: center;
    color: var(--text-muted);
    font-size: 14px;
    margin: 0 8px;
}

.filter-actions {
    display: flex;
    gap: 8px;
    justify-content: flex-end;
}

.role-badge {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 600;
    text-align: center;
}

.role-系统管理员 {
    background: rgba(239, 68, 68, 0.1);
    color: var(--danger-color);
}

.role-部门经理 {
    background: rgba(245, 158, 11, 0.1);
    color: var(--warning-color);
}

.role-普通用户 {
    background: rgba(59, 130, 246, 0.1);
    color: var(--info-color);
}

.role-测试用户 {
    background: rgba(139, 92, 246, 0.1);
    color: #8b5cf6;
}

.role-演示用户 {
    background: rgba(6, 182, 212, 0.1);
    color: #06b6d4;
}

.status-badge {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 600;
    text-align: center;
}

.status-active {
    background: rgba(16, 185, 129, 0.1);
    color: var(--success-color);
}

.status-inactive {
    background: rgba(156, 163, 175, 0.1);
    color: var(--gray-500);
}

.status-suspended {
    background: rgba(239, 68, 68, 0.1);
    color: var(--danger-color);
}

.user-username {
    font-size: 12px;
    color: var(--text-muted);
    font-family: 'Monaco', 'Menlo', monospace;
}

.pagination {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 20px;
    padding: 16px 0;
}

.pagination-info {
    color: var(--text-muted);
    font-size: 14px;
}

.pagination-controls {
    display: flex;
    gap: 4px;
}

.pagination-btn {
    padding: 8px 12px;
    border: 1px solid var(--gray-300);
    background: white;
    color: var(--text-secondary);
    border-radius: var(--border-radius-sm);
    cursor: pointer;
    transition: var(--transition);
    font-size: 14px;
    min-width: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.pagination-btn:hover:not(.disabled) {
    background: var(--gray-50);
    border-color: var(--gray-400);
}

.pagination-btn.active {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.pagination-btn.disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.pagination-ellipsis {
    padding: 8px 4px;
    color: var(--text-muted);
    font-size: 14px;
}

.text-muted {
    color: var(--text-muted);
    font-style: italic;
}

/* 增强数据分析样式 */
.status-indicator {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
    font-weight: 500;
}

.status-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    display: inline-block;
}

.status-dot.active {
    background: var(--success-color);
    animation: pulse 2s infinite;
}

.status-dot.inactive {
    background: var(--gray-400);
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}

.real-time-metrics {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
}

.metric-item {
    background: var(--gray-50);
    padding: 16px;
    border-radius: var(--border-radius);
    text-align: center;
    border: 1px solid var(--gray-200);
}

.metric-label {
    font-size: 14px;
    color: var(--text-muted);
    margin-bottom: 8px;
}

.metric-value {
    font-size: 24px;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 4px;
}

.metric-change {
    font-size: 12px;
    font-weight: 600;
}

.metric-change.positive {
    color: var(--success-color);
}

.metric-change.negative {
    color: var(--danger-color);
}

.comparison-chart-container,
.prediction-chart-container {
    position: relative;
    height: 300px;
    margin-bottom: 16px;
}

.prediction-summary {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 16px;
    padding: 16px;
    background: var(--gray-50);
    border-radius: var(--border-radius);
}

.prediction-item {
    text-align: center;
}

.prediction-label {
    display: block;
    font-size: 12px;
    color: var(--text-muted);
    margin-bottom: 4px;
}

.prediction-value {
    display: block;
    font-size: 16px;
    font-weight: 600;
    color: var(--text-primary);
}

.prediction-confidence {
    display: block;
    font-size: 16px;
    font-weight: 600;
    color: var(--success-color);
}

.geo-analysis-container {
    display: grid;
    grid-template-columns: 1fr 300px;
    gap: 20px;
}

.geo-chart-container {
    position: relative;
    height: 300px;
}

.geo-stats {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.geo-stat-item {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.geo-region {
    font-weight: 600;
    color: var(--text-primary);
    font-size: 14px;
}

.geo-percentage {
    font-size: 18px;
    font-weight: 700;
    color: var(--primary-color);
}

.geo-bar {
    height: 8px;
    background: var(--gray-200);
    border-radius: 4px;
    overflow: hidden;
}

.geo-bar-fill {
    height: 100%;
    background: linear-gradient(90deg, var(--primary-color), var(--primary-light));
    transition: width 0.3s ease;
}

/* 响应式调整 */
@media (max-width: 768px) {
    .real-time-metrics {
        grid-template-columns: repeat(2, 1fr);
    }

    .geo-analysis-container {
        grid-template-columns: 1fr;
    }

    .prediction-summary {
        grid-template-columns: 1fr;
    }

    .filter-row {
        grid-template-columns: 1fr;
    }
}

/* 增强订单管理样式 */
.order-info-cell {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.order-number {
    font-weight: 600;
    color: var(--primary-color);
    font-family: 'Monaco', 'Menlo', monospace;
}

.order-time {
    font-size: 12px;
    color: var(--text-muted);
}

.tracking-number {
    font-size: 11px;
    color: var(--success-color);
    background: rgba(16, 185, 129, 0.1);
    padding: 2px 6px;
    border-radius: 4px;
    font-family: 'Monaco', 'Menlo', monospace;
}

.customer-info {
    display: flex;
    flex-direction: column;
    gap: 2px;
}

.customer-name {
    font-weight: 600;
    color: var(--text-primary);
}

.customer-contact {
    font-size: 12px;
    color: var(--text-muted);
    font-family: 'Monaco', 'Menlo', monospace;
}

.customer-email {
    font-size: 11px;
    color: var(--text-muted);
}

.product-info {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.product-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 4px 8px;
    background: var(--gray-50);
    border-radius: 4px;
}

.product-name {
    font-weight: 500;
    color: var(--text-primary);
    flex: 1;
}

.product-quantity {
    font-size: 12px;
    color: var(--text-muted);
    font-weight: 600;
}

.order-amount {
    font-weight: 700;
    color: var(--text-primary);
    font-size: 16px;
}

.payment-method {
    font-size: 12px;
    color: var(--text-muted);
    margin-top: 2px;
}

/* 订单状态样式 */
.status-pending {
    background: rgba(245, 158, 11, 0.1);
    color: var(--warning-color);
}

.status-processing {
    background: rgba(59, 130, 246, 0.1);
    color: var(--info-color);
}

.status-shipped {
    background: rgba(139, 92, 246, 0.1);
    color: #8b5cf6;
}

.status-completed {
    background: rgba(16, 185, 129, 0.1);
    color: var(--success-color);
}

.status-cancelled {
    background: rgba(239, 68, 68, 0.1);
    color: var(--danger-color);
}

.status-refunded {
    background: rgba(156, 163, 175, 0.1);
    color: var(--gray-500);
}

/* 订单操作按钮样式 */
.action-buttons {
    display: flex;
    gap: 4px;
    justify-content: center;
}

.btn-icon {
    width: 32px;
    height: 32px;
    border: 1px solid var(--gray-300);
    background: white;
    color: var(--text-secondary);
    border-radius: 4px;
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
}

.btn-icon:hover {
    background: var(--gray-50);
    border-color: var(--gray-400);
    color: var(--text-primary);
}

.btn-icon.danger:hover {
    background: var(--danger-color);
    border-color: var(--danger-color);
    color: white;
}

/* 搜索筛选区域样式 */
.search-filters {
    display: flex;
    gap: 12px;
    align-items: center;
    flex-wrap: wrap;
}

.search-filters select {
    min-width: 120px;
    padding: 8px 12px;
    border: 1px solid var(--gray-300);
    border-radius: var(--border-radius-sm);
    font-size: 14px;
    background: white;
}

.search-filters .btn-sm {
    padding: 8px 12px;
    font-size: 13px;
    white-space: nowrap;
}

/* 响应式调整 */
@media (max-width: 768px) {
    .search-filters {
        flex-direction: column;
        align-items: stretch;
    }

    .search-filters > * {
        width: 100%;
    }

    .action-buttons {
        flex-direction: column;
    }

    .product-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 2px;
    }

    .order-info-cell,
    .customer-info,
    .product-info {
        min-width: 120px;
    }
}

/* 用户下拉菜单样式 */
.user-profile {
    position: relative;
}

.user-menu-btn {
    background: none;
    border: none;
    color: var(--text-muted);
    cursor: pointer;
    padding: 8px;
    border-radius: var(--border-radius-sm);
    transition: var(--transition);
}

.user-menu-btn:hover {
    background: var(--gray-100);
    color: var(--text-primary);
}

.user-dropdown-menu {
    position: absolute;
    top: 100%;
    right: 0;
    width: 280px;
    background: white;
    border: 1px solid var(--gray-200);
    border-radius: var(--border-radius-lg);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all 0.2s ease;
}

.user-dropdown-menu.show {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.dropdown-header {
    padding: 20px;
    background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
    color: white;
    border-radius: var(--border-radius-lg) var(--border-radius-lg) 0 0;
    display: flex;
    align-items: center;
    gap: 12px;
}

.user-avatar-large {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    overflow: hidden;
    border: 2px solid rgba(255, 255, 255, 0.3);
}

.user-avatar-large img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.user-details {
    flex: 1;
}

.user-name-large {
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 2px;
}

.user-role-large {
    font-size: 13px;
    opacity: 0.9;
    margin-bottom: 2px;
}

.user-email {
    font-size: 12px;
    opacity: 0.8;
}

.dropdown-divider {
    height: 1px;
    background: var(--gray-200);
    margin: 8px 0;
}

.dropdown-menu-items {
    padding: 8px 0;
}

.dropdown-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px 20px;
    color: var(--text-secondary);
    text-decoration: none;
    transition: var(--transition);
    font-size: 14px;
}

.dropdown-item:hover {
    background: var(--gray-50);
    color: var(--text-primary);
}

.dropdown-item i {
    width: 16px;
    text-align: center;
    opacity: 0.7;
}

.dropdown-item.logout-item {
    color: var(--danger-color);
}

.dropdown-item.logout-item:hover {
    background: rgba(239, 68, 68, 0.1);
    color: var(--danger-color);
}

.dropdown-footer {
    padding: 12px 20px;
    background: var(--gray-50);
    border-radius: 0 0 var(--border-radius-lg) var(--border-radius-lg);
    border-top: 1px solid var(--gray-200);
}

.login-time {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 12px;
    color: var(--text-muted);
}

.login-time i {
    opacity: 0.6;
}

/* 点击外部关闭菜单的遮罩 */
.dropdown-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 999;
    background: transparent;
    display: none;
}

.dropdown-overlay.show {
    display: block;
}

/* 响应式调整 */
@media (max-width: 768px) {
    .user-dropdown-menu {
        width: 260px;
        right: -10px;
    }

    .dropdown-header {
        padding: 16px;
    }

    .user-avatar-large {
        width: 40px;
        height: 40px;
    }

    .user-name-large {
        font-size: 15px;
    }
}

/* 系统设置增强样式 */
.settings-container {
    display: grid;
    grid-template-columns: 1fr;
    gap: 24px;
    max-width: 1200px;
    margin: 0 auto;
}

.settings-card {
    background: white;
    border: 1px solid var(--gray-200);
    border-radius: var(--border-radius-lg);
    overflow: hidden;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    transition: var(--transition);
}

.settings-card:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.settings-header {
    padding: 20px 24px;
    background: linear-gradient(135deg, var(--gray-50) 0%, var(--gray-100) 100%);
    border-bottom: 1px solid var(--gray-200);
}

.settings-header h3 {
    margin: 0 0 8px 0;
    color: var(--text-primary);
    font-size: 18px;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 8px;
}

.settings-header h3 i {
    color: var(--primary-color);
}

.settings-header p {
    margin: 0;
    color: var(--text-muted);
    font-size: 14px;
}

.settings-content {
    padding: 24px;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 16px;
    margin-bottom: 16px;
}

.form-group {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.form-group label {
    font-weight: 600;
    color: var(--text-secondary);
    font-size: 14px;
}

.form-group input,
.form-group textarea,
.form-group select {
    padding: 12px 16px;
    border: 1px solid var(--gray-300);
    border-radius: var(--border-radius);
    font-size: 14px;
    transition: var(--transition);
}

.form-group input:focus,
.form-group textarea:focus,
.form-group select:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

.setting-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 0;
    border-bottom: 1px solid var(--gray-100);
}

.setting-item:last-child {
    border-bottom: none;
}

.setting-info {
    flex: 1;
}

.setting-info label {
    display: block;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 4px;
}

.setting-info span {
    color: var(--text-muted);
    font-size: 13px;
}

.setting-control {
    display: flex;
    align-items: center;
    gap: 8px;
}

/* 开关样式 */
.switch {
    position: relative;
    display: inline-block;
    width: 50px;
    height: 24px;
}

.switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: var(--gray-300);
    transition: 0.3s;
    border-radius: 24px;
}

.slider:before {
    position: absolute;
    content: "";
    height: 18px;
    width: 18px;
    left: 3px;
    bottom: 3px;
    background-color: white;
    transition: 0.3s;
    border-radius: 50%;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

input:checked + .slider {
    background-color: var(--primary-color);
}

input:checked + .slider:before {
    transform: translateX(26px);
}

/* 主题样式 */
.theme-dark {
    --bg-primary: #1a1a1a;
    --bg-secondary: #2d2d2d;
    --text-primary: #ffffff;
    --text-secondary: #e5e5e5;
    --text-muted: #a0a0a0;
    --border-color: #404040;
    --gray-50: #2d2d2d;
    --gray-100: #404040;
    --gray-200: #525252;
    --gray-300: #737373;
}

.theme-dark .settings-card {
    background: var(--bg-secondary);
    border-color: var(--border-color);
}

.theme-dark .settings-header {
    background: linear-gradient(135deg, var(--gray-100) 0%, var(--gray-200) 100%);
    border-color: var(--border-color);
}

.theme-dark .form-group input,
.theme-dark .form-group textarea,
.theme-dark .form-group select {
    background: var(--bg-primary);
    border-color: var(--border-color);
    color: var(--text-primary);
}

.theme-dark .setting-item {
    border-color: var(--border-color);
}

/* 禁用动画样式 */
.no-animations * {
    animation-duration: 0s !important;
    transition-duration: 0s !important;
}

/* 退出消息样式 */
.logout-message {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    color: white;
    text-align: center;
}

.logout-content {
    background: white;
    color: #333;
    padding: 2rem;
    border-radius: 10px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    max-width: 300px;
}

.logout-content i {
    font-size: 3rem;
    color: #6366f1;
    margin-bottom: 1rem;
}

.logout-content h3 {
    margin: 0 0 0.5rem 0;
    font-size: 1.5rem;
    color: #333;
}

.logout-content p {
    margin: 0;
    color: #666;
    font-size: 0.9rem;
}

/* 响应式调整 */
@media (max-width: 768px) {
    .settings-container {
        gap: 16px;
    }

    .settings-header {
        padding: 16px 20px;
    }

    .settings-content {
        padding: 20px;
    }

    .form-row {
        grid-template-columns: 1fr;
        gap: 12px;
    }

    .setting-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 12px;
    }

    .setting-control {
        align-self: flex-end;
    }
}

/* 文件管理样式 */
.file-toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 16px;
}

.breadcrumb {
    display: flex;
    align-items: center;
    gap: 4px;
    font-size: 14px;
}

.breadcrumb-item {
    display: flex;
    align-items: center;
    gap: 4px;
    padding: 4px 8px;
    border-radius: 4px;
    cursor: pointer;
    transition: var(--transition);
    color: var(--text-muted);
}

.breadcrumb-item:hover {
    background: var(--gray-100);
    color: var(--text-primary);
}

.breadcrumb-item.active {
    color: var(--primary-color);
    font-weight: 600;
}

.breadcrumb-separator {
    color: var(--text-muted);
    margin: 0 4px;
}

.view-controls {
    display: flex;
    gap: 4px;
}

.view-btn {
    width: 36px;
    height: 36px;
    border: 1px solid var(--gray-300);
    background: white;
    color: var(--text-muted);
    border-radius: 4px;
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    justify-content: center;
}

.view-btn:hover {
    background: var(--gray-50);
    color: var(--text-primary);
}

.view-btn.active {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.file-filters {
    display: flex;
    gap: 12px;
    margin-bottom: 16px;
    flex-wrap: wrap;
}

.file-filters .search-box {
    flex: 1;
    min-width: 200px;
}

.file-filters select {
    min-width: 120px;
    padding: 8px 12px;
    border: 1px solid var(--gray-300);
    border-radius: var(--border-radius-sm);
    font-size: 14px;
    background: white;
}

.file-container {
    min-height: 400px;
}

.file-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 16px;
    margin-bottom: 20px;
}

.file-list {
    display: flex;
    flex-direction: column;
    gap: 1px;
    margin-bottom: 20px;
}

.file-card {
    background: white;
    border: 1px solid var(--gray-200);
    border-radius: var(--border-radius);
    padding: 16px;
    transition: var(--transition);
    position: relative;
    cursor: pointer;
}

.file-card:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    border-color: var(--primary-color);
}

.file-card.selected {
    border-color: var(--primary-color);
    background: rgba(99, 102, 241, 0.05);
}

.file-checkbox {
    position: absolute;
    top: 8px;
    left: 8px;
    z-index: 2;
}

.file-checkbox input {
    width: 16px;
    height: 16px;
}

.file-thumbnail {
    text-align: center;
    margin-bottom: 12px;
    height: 120px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--gray-50);
    border-radius: 4px;
    overflow: hidden;
}

.file-thumbnail img {
    max-width: 100%;
    max-height: 100%;
    object-fit: cover;
}

.file-icon {
    font-size: 48px;
    color: var(--primary-color);
}

.file-info {
    text-align: center;
}

.file-name {
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 4px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.file-meta {
    font-size: 12px;
    color: var(--text-muted);
    margin-bottom: 2px;
}

.file-date {
    font-size: 11px;
    color: var(--text-muted);
}

.file-actions {
    display: flex;
    justify-content: center;
    gap: 4px;
    margin-top: 8px;
    opacity: 0;
    transition: var(--transition);
}

.file-card:hover .file-actions {
    opacity: 1;
}

.file-action-btn {
    width: 28px;
    height: 28px;
    border: 1px solid var(--gray-300);
    background: white;
    color: var(--text-muted);
    border-radius: 4px;
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
}

.file-action-btn:hover {
    background: var(--gray-50);
    color: var(--text-primary);
}

.file-action-btn.danger:hover {
    background: var(--danger-color);
    border-color: var(--danger-color);
    color: white;
}

/* 列表视图样式 */
.file-row {
    display: grid;
    grid-template-columns: 40px 40px 1fr 100px 80px 120px 120px;
    gap: 12px;
    align-items: center;
    padding: 12px 16px;
    background: white;
    border-bottom: 1px solid var(--gray-100);
    transition: var(--transition);
}

.file-row:hover {
    background: var(--gray-50);
}

.file-row.selected {
    background: rgba(99, 102, 241, 0.05);
}

.file-icon-small {
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--primary-color);
    font-size: 18px;
}

.file-name-col {
    font-weight: 500;
    color: var(--text-primary);
    cursor: pointer;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.file-name-col:hover {
    color: var(--primary-color);
}

.file-size-col,
.file-type-col,
.file-date-col {
    font-size: 13px;
    color: var(--text-muted);
}

.file-actions-col {
    display: flex;
    gap: 4px;
    justify-content: flex-end;
}

/* 文件预览模态框样式 */
.file-preview-content {
    max-width: 800px;
    max-height: 90vh;
}

.file-preview-container {
    text-align: center;
    padding: 20px;
    min-height: 300px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.preview-image {
    max-width: 100%;
    max-height: 500px;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.preview-pdf,
.preview-document,
.preview-video,
.preview-audio,
.preview-default {
    text-align: center;
    padding: 40px;
}

.preview-pdf i,
.preview-document i,
.preview-video i,
.preview-audio i,
.preview-default i {
    font-size: 64px;
    color: var(--primary-color);
    margin-bottom: 16px;
}

.preview-pdf p,
.preview-document p,
.preview-video p,
.preview-audio p,
.preview-default p {
    margin: 8px 0;
    color: var(--text-secondary);
}

/* 文件上传样式 */
.upload-area {
    border: 2px dashed var(--gray-300);
    border-radius: var(--border-radius-lg);
    padding: 40px;
    text-align: center;
    transition: var(--transition);
    cursor: pointer;
}

.upload-area:hover,
.upload-area.dragover {
    border-color: var(--primary-color);
    background: rgba(99, 102, 241, 0.05);
}

.upload-icon {
    font-size: 48px;
    color: var(--primary-color);
    margin-bottom: 16px;
}

.upload-text h4 {
    margin: 0 0 8px 0;
    color: var(--text-primary);
}

.upload-text p {
    margin: 0 0 20px 0;
    color: var(--text-muted);
    font-size: 14px;
}

.upload-progress {
    margin: 20px 0;
}

.progress-bar {
    width: 100%;
    height: 8px;
    background: var(--gray-200);
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: 8px;
}

.progress-fill {
    height: 100%;
    background: var(--primary-color);
    transition: width 0.3s ease;
}

.progress-text {
    text-align: center;
    font-size: 14px;
    color: var(--text-secondary);
}

.upload-list {
    margin-top: 20px;
}

.upload-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px;
    background: var(--gray-50);
    border-radius: 4px;
    margin-bottom: 8px;
}

.upload-item-info {
    display: flex;
    align-items: center;
    gap: 8px;
    flex: 1;
}

.upload-item-name {
    font-weight: 500;
    color: var(--text-primary);
}

.upload-item-size {
    font-size: 12px;
    color: var(--text-muted);
}

.upload-item-status {
    font-size: 12px;
    color: var(--text-muted);
}

/* 响应式调整 */
@media (max-width: 768px) {
    .file-toolbar {
        flex-direction: column;
        align-items: stretch;
        gap: 12px;
    }

    .file-filters {
        flex-direction: column;
    }

    .file-filters > * {
        width: 100%;
    }

    .file-grid {
        grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
        gap: 12px;
    }

    .file-row {
        grid-template-columns: 40px 1fr 80px;
        gap: 8px;
    }

    .file-size-col,
    .file-type-col,
    .file-date-col {
        display: none;
    }

    .file-actions-col {
        grid-column: 1 / -1;
        justify-content: center;
        margin-top: 8px;
    }
}

/* 通知系统样式 */
.notification-filters {
    display: flex;
    gap: 12px;
    align-items: center;
}

.notification-filters select {
    min-width: 120px;
    padding: 8px 12px;
    border: 1px solid var(--gray-300);
    border-radius: var(--border-radius-sm);
    font-size: 14px;
    background: white;
}

.notifications-container {
    display: flex;
    flex-direction: column;
    gap: 1px;
    margin-bottom: 20px;
}

.notification-item {
    display: flex;
    align-items: flex-start;
    gap: 16px;
    padding: 16px;
    background: white;
    border: 1px solid var(--gray-200);
    border-radius: var(--border-radius);
    transition: var(--transition);
    position: relative;
}

.notification-item:hover {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    border-color: var(--primary-color);
}

.notification-item.unread {
    background: rgba(99, 102, 241, 0.02);
    border-left: 4px solid var(--primary-color);
}

.notification-item.read {
    opacity: 0.8;
}

.notification-item.high-priority {
    border-left: 4px solid var(--danger-color);
}

.notification-item.medium-priority {
    border-left: 4px solid var(--warning-color);
}

.notification-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: var(--gray-100);
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
    margin-top: 4px;
}

.notification-icon i {
    font-size: 18px;
    color: var(--primary-color);
}

.notification-content {
    flex: 1;
    cursor: pointer;
}

.notification-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 8px;
}

.notification-title {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
    color: var(--text-primary);
    line-height: 1.3;
}

.notification-time {
    font-size: 12px;
    color: var(--text-muted);
    white-space: nowrap;
    margin-left: 12px;
}

.notification-message {
    margin: 0 0 12px 0;
    color: var(--text-secondary);
    line-height: 1.5;
    font-size: 14px;
}

.notification-meta {
    display: flex;
    align-items: center;
    gap: 8px;
    flex-wrap: wrap;
}

.notification-type {
    font-size: 12px;
    color: var(--text-muted);
    background: var(--gray-100);
    padding: 2px 8px;
    border-radius: 12px;
}

.priority-badge {
    font-size: 11px;
    color: white;
    background: var(--danger-color);
    padding: 2px 6px;
    border-radius: 10px;
    font-weight: 600;
}

.unread-indicator {
    font-size: 11px;
    color: white;
    background: var(--primary-color);
    padding: 2px 6px;
    border-radius: 10px;
    font-weight: 600;
}

.notification-actions {
    display: flex;
    flex-direction: column;
    gap: 4px;
    opacity: 0;
    transition: var(--transition);
}

.notification-item:hover .notification-actions {
    opacity: 1;
}

.notification-action-btn {
    width: 32px;
    height: 32px;
    border: 1px solid var(--gray-300);
    background: white;
    color: var(--text-muted);
    border-radius: 4px;
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
}

.notification-action-btn:hover {
    background: var(--gray-50);
    color: var(--text-primary);
}

.notification-action-btn.danger:hover {
    background: var(--danger-color);
    border-color: var(--danger-color);
    color: white;
}

.empty-state {
    text-align: center;
    padding: 60px 20px;
    color: var(--text-muted);
}

.empty-state i {
    font-size: 64px;
    margin-bottom: 16px;
    opacity: 0.5;
}

.empty-state h3 {
    margin: 0 0 8px 0;
    color: var(--text-secondary);
}

.empty-state p {
    margin: 0;
    font-size: 14px;
}

/* 通知徽章样式 */
.notification-badge {
    position: absolute;
    top: -2px;
    right: 8px;
    background: var(--danger-color);
    color: white;
    font-size: 11px;
    font-weight: 600;
    padding: 2px 6px;
    border-radius: 10px;
    min-width: 18px;
    text-align: center;
    line-height: 1.2;
    display: none;
}

.nav-item {
    position: relative;
}

/* 桌面通知样式覆盖 */
.notification-permission-request {
    position: fixed;
    top: 20px;
    right: 20px;
    background: white;
    border: 1px solid var(--gray-300);
    border-radius: var(--border-radius);
    padding: 16px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    z-index: 1000;
    max-width: 300px;
}

.notification-permission-request h4 {
    margin: 0 0 8px 0;
    color: var(--text-primary);
}

.notification-permission-request p {
    margin: 0 0 12px 0;
    color: var(--text-secondary);
    font-size: 14px;
}

.notification-permission-request .permission-actions {
    display: flex;
    gap: 8px;
    justify-content: flex-end;
}

/* 响应式调整 */
@media (max-width: 768px) {
    .notification-filters {
        flex-direction: column;
        align-items: stretch;
    }

    .notification-filters select {
        width: 100%;
    }

    .notification-item {
        flex-direction: column;
        gap: 12px;
    }

    .notification-header {
        flex-direction: column;
        gap: 4px;
    }

    .notification-time {
        margin-left: 0;
    }

    .notification-actions {
        flex-direction: row;
        justify-content: center;
        opacity: 1;
    }

    .notification-meta {
        justify-content: center;
    }
}
