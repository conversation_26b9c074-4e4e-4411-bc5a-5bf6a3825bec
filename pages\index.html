<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>恋雪二游总导航页</title>
    <link rel="stylesheet" href="../assets/css/styles.css">
    <link rel="stylesheet" href="../assets/css/modern-theme.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        /* 紧急修复CSS - 确保表格正常显示 */
        .modern-table {
            width: 100%;
            border-collapse: collapse;
            font-size: 14px;
        }
        .modern-table th {
            background: #f9fafb;
            padding: 12px;
            text-align: left;
            font-weight: 600;
            color: #374151;
            border-bottom: 1px solid #e5e7eb;
        }
        .modern-table td {
            padding: 12px;
            border-bottom: 1px solid #f3f4f6;
            vertical-align: middle;
        }
        .modern-table tbody tr:hover {
            background-color: #f9fafb;
        }
        /* 确保页面布局正常 */
        .page {
            padding: 20px;
        }
        .page-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 24px;
            padding-bottom: 16px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            flex-wrap: wrap;
            gap: 16px;
        }
        .page-actions {
            display: flex;
            gap: 12px;
            flex-wrap: wrap;
        }
    </style>
</head>
<body>
    <div class="app-container">
        <!-- 侧边栏 -->
        <aside class="sidebar" id="sidebar">
            <div class="sidebar-header">
                <div class="logo">
                    <div class="logo-icon">
                        <i class="fas fa-cube"></i>
                    </div>
                    <span class="logo-text">恋雪二游</span>
                </div>
                <button class="sidebar-toggle" id="sidebarToggle">
                    <i class="fas fa-bars"></i>
                </button>
            </div>

            <nav class="nav-menu">
                <div class="nav-section">
                    <span class="nav-section-title">主要功能</span>
                    <a href="#dashboard" class="nav-item active" data-page="dashboard">
                        <i class="fas fa-chart-pie"></i>
                        <span>仪表盘</span>
                        <div class="nav-indicator"></div>
                    </a>
                    <a href="#users" class="nav-item" data-page="users">
                        <i class="fas fa-users"></i>
                        <span>用户管理</span>
                        <div class="nav-indicator"></div>
                    </a>
                    <a href="#analytics" class="nav-item" data-page="analytics">
                        <i class="fas fa-chart-line"></i>
                        <span>数据分析</span>
                        <div class="nav-indicator"></div>
                    </a>
                    <a href="#orders" class="nav-item" data-page="orders">
                        <i class="fas fa-shopping-bag"></i>
                        <span>订单管理</span>
                        <div class="nav-indicator"></div>
                    </a>
                    <a href="#ner" class="nav-item" data-page="ner">
                        <i class="fas fa-brain"></i>
                        <span>NER数据管理</span>
                        <div class="nav-indicator"></div>
                    </a>
                    <a href="#inventory" class="nav-item" data-page="inventory">
                        <i class="fas fa-boxes"></i>
                        <span>库存管理</span>
                        <div class="nav-indicator"></div>
                    </a>
                    <a href="#reports" class="nav-item" data-page="reports">
                        <i class="fas fa-chart-bar"></i>
                        <span>报表中心</span>
                        <div class="nav-indicator"></div>
                    </a>
                    <a href="#files" class="nav-item" data-page="files">
                        <i class="fas fa-folder"></i>
                        <span>文件管理</span>
                        <div class="nav-indicator"></div>
                    </a>
                    <a href="#permissions" class="nav-item" data-page="permissions">
                        <i class="fas fa-shield-alt"></i>
                        <span>权限管理</span>
                        <div class="nav-indicator"></div>
                    </a>
                </div>

                <div class="nav-section">
                    <span class="nav-section-title">系统</span>
                    <a href="#notifications" class="nav-item" data-page="notifications">
                        <i class="fas fa-bell"></i>
                        <span>通知中心</span>
                        <span class="notification-badge" id="notificationBadge">0</span>
                        <div class="nav-indicator"></div>
                    </a>
                    <a href="#files" class="nav-item" data-page="files">
                        <i class="fas fa-folder"></i>
                        <span>文件管理</span>
                        <div class="nav-indicator"></div>
                    </a>
                    <a href="#settings" class="nav-item" data-page="settings">
                        <i class="fas fa-cog"></i>
                        <span>系统设置</span>
                        <div class="nav-indicator"></div>
                    </a>
                    <a href="#help" class="nav-item" data-page="help">
                        <i class="fas fa-question-circle"></i>
                        <span>帮助中心</span>
                        <div class="nav-indicator"></div>
                    </a>
                </div>
            </nav>

            <div class="sidebar-footer">
                <div class="user-card">
                    <div class="user-avatar">
                        <img src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=40&h=40&fit=crop&crop=face" alt="用户头像">
                        <div class="status-indicator online"></div>
                    </div>
                    <div class="user-info">
                        <span class="user-name">张管理员</span>
                        <span class="user-role">系统管理员</span>
                    </div>
                    <button class="user-menu-btn" onclick="toggleUserMenu()">
                        <i class="fas fa-ellipsis-v"></i>
                    </button>

                    <!-- 用户下拉菜单 -->
                    <div class="user-dropdown-menu" id="userDropdownMenu">
                        <div class="dropdown-header">
                            <div class="user-avatar-large">
                                <img src="https://ui-avatars.com/api/?name=Admin&background=6366f1&color=fff" alt="用户头像">
                            </div>
                            <div class="user-details">
                                <div class="user-name-large" id="currentUserName">张管理员</div>
                                <div class="user-role-large" id="currentUserRole">系统管理员</div>
                                <div class="user-email" id="currentUserEmail"><EMAIL></div>
                            </div>
                        </div>

                        <div class="dropdown-divider"></div>

                        <div class="dropdown-menu-items">
                            <a href="#" class="dropdown-item" onclick="showUserProfile(); closeUserMenu();">
                                <i class="fas fa-user"></i>
                                <span>个人资料</span>
                            </a>
                            <a href="#" class="dropdown-item" onclick="showChangePasswordModal(); closeUserMenu();">
                                <i class="fas fa-key"></i>
                                <span>修改密码</span>
                            </a>
                            <a href="#settings" class="dropdown-item" onclick="showPage('settings'); closeUserMenu();">
                                <i class="fas fa-cog"></i>
                                <span>系统设置</span>
                            </a>
                            <a href="#" class="dropdown-item" onclick="showUserPreferences(); closeUserMenu();">
                                <i class="fas fa-palette"></i>
                                <span>偏好设置</span>
                            </a>
                            <a href="#" class="dropdown-item" onclick="viewLoginHistory(); closeUserMenu();">
                                <i class="fas fa-history"></i>
                                <span>登录历史</span>
                            </a>
                            <a href="#" class="dropdown-item" onclick="showHelp(); closeUserMenu();">
                                <i class="fas fa-question-circle"></i>
                                <span>帮助中心</span>
                            </a>

                            <div class="dropdown-divider"></div>

                            <a href="#" class="dropdown-item" onclick="switchAccount(); closeUserMenu();">
                                <i class="fas fa-user-friends"></i>
                                <span>切换账户</span>
                            </a>
                            <a href="#" class="dropdown-item" onclick="lockScreen(); closeUserMenu();">
                                <i class="fas fa-lock"></i>
                                <span>锁定屏幕</span>
                            </a>

                            <div class="dropdown-divider"></div>

                            <a href="#" class="dropdown-item logout-item" onclick="logout(); closeUserMenu();">
                                <i class="fas fa-sign-out-alt"></i>
                                <span>退出登录</span>
                            </a>
                        </div>

                        <div class="dropdown-footer">
                            <div class="login-time">
                                <i class="fas fa-clock"></i>
                                <span id="loginTime">登录时间: 今天 09:30</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </aside>

        <!-- 主要内容区域 -->
        <main class="main-content">
            <!-- 顶部导航栏 -->
            <header class="top-header">
                <div class="header-left">
                    <button class="mobile-menu-btn" id="mobileMenuBtn">
                        <i class="fas fa-bars"></i>
                    </button>
                    <div class="breadcrumb">
                        <span class="breadcrumb-item">首页</span>
                        <i class="fas fa-chevron-right"></i>
                        <span class="breadcrumb-item current">仪表盘</span>
                    </div>
                </div>
                <div class="header-right">
                    <div class="header-actions">
                        <button class="action-btn" title="搜索">
                            <i class="fas fa-search"></i>
                        </button>
                        <button class="action-btn notification-btn" title="通知">
                            <i class="fas fa-bell"></i>
                            <span class="notification-badge">3</span>
                        </button>
                        <button class="action-btn" onclick="showWorkflowPanel()" title="工作流管理">
                            <i class="fas fa-project-diagram"></i>
                        </button>
                        <button class="action-btn" onclick="showSystemMonitor()" title="系统监控">
                            <i class="fas fa-desktop"></i>
                        </button>
                        <div class="settings-dropdown">
                            <button class="action-btn" onclick="toggleSettingsDropdown()" title="设置">
                                <i class="fas fa-cog"></i>
                            </button>
                            <div class="dropdown-menu" id="settingsDropdown">
                                <div class="dropdown-item" onclick="showThemeCustomizer()">
                                    <i class="fas fa-palette"></i>
                                    <span>主题定制</span>
                                </div>
                                <div class="dropdown-item" onclick="showLanguageSettings()">
                                    <i class="fas fa-globe"></i>
                                    <span>语言设置</span>
                                </div>
                                <div class="dropdown-item" onclick="showAccountSettings()">
                                    <i class="fas fa-user-cog"></i>
                                    <span>账户设置</span>
                                </div>
                                <div class="dropdown-item" onclick="showPermissionPanel()" data-permission="role.manage">
                                    <i class="fas fa-shield-alt"></i>
                                    <span>权限管理</span>
                                </div>
                                <div class="dropdown-item" onclick="showAPIPanel()" data-permission="system.config">
                                    <i class="fas fa-code"></i>
                                    <span>API管理</span>
                                </div>
                                <div class="dropdown-item" onclick="showBackupPanel()" data-permission="system.backup">
                                    <i class="fas fa-database"></i>
                                    <span>数据备份</span>
                                </div>
                                <div class="dropdown-item" onclick="showPerformancePanel()" data-permission="system.monitor">
                                    <i class="fas fa-tachometer-alt"></i>
                                    <span>性能分析</span>
                                </div>
                                <div class="dropdown-item" onclick="showAdvancedAnalytics()" data-permission="data.analyze">
                                    <i class="fas fa-chart-line"></i>
                                    <span>数据分析</span>
                                </div>
                                <div class="dropdown-item" onclick="showCommunicationPanel()" data-permission="communication.access">
                                    <i class="fas fa-comments"></i>
                                    <span>实时通信</span>
                                </div>
                                <div class="dropdown-item" onclick="showSecurityPanel()" data-permission="security.manage">
                                    <i class="fas fa-shield-alt"></i>
                                    <span>安全中心</span>
                                </div>
                                <div class="dropdown-item" onclick="showWorkflowPanel()" data-permission="workflow.manage">
                                    <i class="fas fa-project-diagram"></i>
                                    <span>工作流引擎</span>
                                </div>
                                <div class="dropdown-item" onclick="showAIAssistant()" data-permission="ai.access">
                                    <i class="fas fa-robot"></i>
                                    <span>AI助手</span>
                                </div>
                                <div class="dropdown-item" onclick="showPluginPanel()" data-permission="system.config">
                                    <i class="fas fa-puzzle-piece"></i>
                                    <span>插件管理</span>
                                </div>
                                <div class="dropdown-item" onclick="showSystemSettings()">
                                    <i class="fas fa-cogs"></i>
                                    <span>系统设置</span>
                                </div>
                                <div class="dropdown-divider"></div>
                                <div class="dropdown-item" onclick="showAbout()">
                                    <i class="fas fa-info-circle"></i>
                                    <span>关于系统</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="user-profile" onclick="toggleHeaderUserMenu()">
                        <div class="user-avatar">
                            <img src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=32&h=32&fit=crop&crop=face" alt="用户头像" id="headerUserAvatar">
                        </div>
                        <span class="user-name" id="headerUserName">张管理员</span>
                        <i class="fas fa-chevron-down"></i>

                        <!-- 右上角用户下拉菜单 -->
                        <div class="header-user-dropdown" id="headerUserDropdown">
                            <div class="dropdown-header">
                                <div class="user-avatar-large">
                                    <img src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=50&h=50&fit=crop&crop=face" alt="用户头像" id="dropdownUserAvatar">
                                </div>
                                <div class="user-details">
                                    <div class="user-name-large" id="dropdownUserName">张管理员</div>
                                    <div class="user-role-large" id="dropdownUserRole">系统管理员</div>
                                    <div class="user-email" id="dropdownUserEmail"><EMAIL></div>
                                </div>
                            </div>

                            <div class="dropdown-divider"></div>

                            <div class="dropdown-menu-items">
                                <a href="#" class="dropdown-item" onclick="showUserProfile(); closeHeaderUserMenu();">
                                    <i class="fas fa-user"></i>
                                    <span>个人资料</span>
                                </a>
                                <a href="#" class="dropdown-item" onclick="showChangePasswordModal(); closeHeaderUserMenu();">
                                    <i class="fas fa-key"></i>
                                    <span>修改密码</span>
                                </a>
                                <a href="#settings" class="dropdown-item" onclick="showPage('settings'); closeHeaderUserMenu();">
                                    <i class="fas fa-cog"></i>
                                    <span>系统设置</span>
                                </a>
                                <a href="#" class="dropdown-item" onclick="showUserPreferences(); closeHeaderUserMenu();">
                                    <i class="fas fa-palette"></i>
                                    <span>偏好设置</span>
                                </a>
                                <a href="#" class="dropdown-item" onclick="viewLoginHistory(); closeHeaderUserMenu();">
                                    <i class="fas fa-history"></i>
                                    <span>登录历史</span>
                                </a>

                                <div class="dropdown-divider"></div>

                                <a href="#" class="dropdown-item" onclick="switchAccount(); closeHeaderUserMenu();">
                                    <i class="fas fa-user-friends"></i>
                                    <span>切换账户</span>
                                </a>
                                <a href="#" class="dropdown-item" onclick="lockScreen(); closeHeaderUserMenu();">
                                    <i class="fas fa-lock"></i>
                                    <span>锁定屏幕</span>
                                </a>

                                <div class="dropdown-divider"></div>

                                <a href="#" class="dropdown-item logout-item" onclick="logout(); closeHeaderUserMenu();">
                                    <i class="fas fa-sign-out-alt"></i>
                                    <span>退出登录</span>
                                </a>
                            </div>

                            <div class="dropdown-footer">
                                <div class="login-time">
                                    <i class="fas fa-clock"></i>
                                    <span id="headerLoginTime">登录时间: 今天 09:30</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </header>

            <!-- 页面内容 -->
            <div class="page-content" id="pageContent">
                <!-- 仪表盘页面 -->
                <div class="page dashboard-page active" id="dashboard">
                    <!-- 现代化欢迎横幅 -->
                    <div class="welcome-banner">
                        <div class="welcome-content">
                            <div class="welcome-text">
                                <h1 class="welcome-title">
                                    <span class="greeting" id="greetingText">早上好</span>，
                                    <span class="user-name-display" id="welcomeUserName">张管理员</span>
                                    <span class="wave-emoji">👋</span>
                                </h1>
                                <p class="welcome-subtitle">今天是美好的一天，让我们一起创造更多价值</p>
                                <div class="welcome-stats">
                                    <div class="welcome-stat-item">
                                        <span class="stat-value" id="todayVisits">1,234</span>
                                        <span class="stat-label">今日访问</span>
                                    </div>
                                    <div class="welcome-stat-item">
                                        <span class="stat-value" id="activeUsers">89</span>
                                        <span class="stat-label">在线用户</span>
                                    </div>
                                    <div class="welcome-stat-item">
                                        <span class="stat-value" id="systemStatus">正常</span>
                                        <span class="stat-label">系统状态</span>
                                    </div>
                                </div>
                            </div>
                            <div class="welcome-visual">
                                <div class="floating-elements">
                                    <div class="floating-element" style="--delay: 0s;">📊</div>
                                    <div class="floating-element" style="--delay: 0.5s;">💼</div>
                                    <div class="floating-element" style="--delay: 1s;">🚀</div>
                                    <div class="floating-element" style="--delay: 1.5s;">⭐</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 统计卡片 -->
                    <div class="stats-grid">
                        <div class="stat-card primary">
                            <div class="stat-content">
                                <div class="stat-icon">
                                    <i class="fas fa-users"></i>
                                </div>
                                <div class="stat-info">
                                    <h3 class="stat-number">2,847</h3>
                                    <p class="stat-label">总用户数</p>
                                    <div class="stat-trend positive">
                                        <i class="fas fa-arrow-up"></i>
                                        <span>+12.5%</span>
                                    </div>
                                </div>
                            </div>
                            <div class="stat-chart">
                                <div class="mini-chart" data-chart="users"></div>
                            </div>
                        </div>

                        <div class="stat-card success">
                            <div class="stat-content">
                                <div class="stat-icon">
                                    <i class="fas fa-dollar-sign"></i>
                                </div>
                                <div class="stat-info">
                                    <h3 class="stat-number">¥156,432</h3>
                                    <p class="stat-label">月收入</p>
                                    <div class="stat-trend positive">
                                        <i class="fas fa-arrow-up"></i>
                                        <span>+8.2%</span>
                                    </div>
                                </div>
                            </div>
                            <div class="stat-chart">
                                <div class="mini-chart" data-chart="revenue"></div>
                            </div>
                        </div>

                        <div class="stat-card warning">
                            <div class="stat-content">
                                <div class="stat-icon">
                                    <i class="fas fa-shopping-cart"></i>
                                </div>
                                <div class="stat-info">
                                    <h3 class="stat-number">1,234</h3>
                                    <p class="stat-label">订单数量</p>
                                    <div class="stat-trend negative">
                                        <i class="fas fa-arrow-down"></i>
                                        <span>-3.1%</span>
                                    </div>
                                </div>
                            </div>
                            <div class="stat-chart">
                                <div class="mini-chart" data-chart="orders"></div>
                            </div>
                        </div>

                        <div class="stat-card danger">
                            <div class="stat-content">
                                <div class="stat-icon">
                                    <i class="fas fa-exclamation-triangle"></i>
                                </div>
                                <div class="stat-info">
                                    <h3 class="stat-number">8</h3>
                                    <p class="stat-label">待处理</p>
                                    <div class="stat-trend neutral">
                                        <i class="fas fa-minus"></i>
                                        <span>0%</span>
                                    </div>
                                </div>
                            </div>
                            <div class="stat-chart">
                                <div class="mini-chart" data-chart="pending"></div>
                            </div>
                        </div>
                    </div>

                    <!-- 图表和表格区域 -->
                    <div class="dashboard-grid">
                        <!-- 销售趋势图表 -->
                        <div class="dashboard-card chart-card">
                            <div class="card-header">
                                <h3>销售趋势</h3>
                                <div class="card-actions">
                                    <select class="time-filter">
                                        <option value="7d">最近7天</option>
                                        <option value="30d" selected>最近30天</option>
                                        <option value="90d">最近90天</option>
                                    </select>
                                </div>
                            </div>
                            <div class="card-content">
                                <div class="chart-container">
                                    <canvas id="salesChart"></canvas>
                                </div>
                            </div>
                        </div>

                        <!-- 最近订单 -->
                        <div class="dashboard-card table-card">
                            <div class="card-header">
                                <h3>最近订单</h3>
                                <button class="btn-primary btn-sm">
                                    <i class="fas fa-plus"></i>
                                    新增订单
                                </button>
                            </div>
                            <div class="card-content">
                                <div class="table-container">
                                    <table class="modern-table">
                                        <thead>
                                            <tr>
                                                <th>订单号</th>
                                                <th>客户</th>
                                                <th>金额</th>
                                                <th>状态</th>
                                                <th>日期</th>
                                                <th>操作</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr>
                                                <td>
                                                    <span class="order-id">#ORD-2024-001</span>
                                                </td>
                                                <td>
                                                    <div class="customer-info">
                                                        <div class="customer-avatar">
                                                            <img src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=32&h=32&fit=crop&crop=face" alt="客户头像">
                                                        </div>
                                                        <div class="customer-details">
                                                            <span class="customer-name">张三</span>
                                                            <span class="customer-email"><EMAIL></span>
                                                        </div>
                                                    </div>
                                                </td>
                                                <td>
                                                    <span class="amount">¥2,599</span>
                                                </td>
                                                <td>
                                                    <span class="status-badge completed">
                                                        <i class="fas fa-check"></i>
                                                        已完成
                                                    </span>
                                                </td>
                                                <td>
                                                    <span class="date">2024-01-15</span>
                                                </td>
                                                <td>
                                                    <div class="action-buttons">
                                                        <button class="btn-icon" title="查看详情">
                                                            <i class="fas fa-eye"></i>
                                                        </button>
                                                        <button class="btn-icon" title="编辑">
                                                            <i class="fas fa-edit"></i>
                                                        </button>
                                                        <button class="btn-icon danger" title="删除">
                                                            <i class="fas fa-trash"></i>
                                                        </button>
                                                    </div>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td>
                                                    <span class="order-id">#ORD-2024-002</span>
                                                </td>
                                                <td>
                                                    <div class="customer-info">
                                                        <div class="customer-avatar">
                                                            <img src="https://images.unsplash.com/photo-1494790108755-2616b612b786?w=32&h=32&fit=crop&crop=face" alt="客户头像">
                                                        </div>
                                                        <div class="customer-details">
                                                            <span class="customer-name">李四</span>
                                                            <span class="customer-email"><EMAIL></span>
                                                        </div>
                                                    </div>
                                                </td>
                                                <td>
                                                    <span class="amount">¥1,299</span>
                                                </td>
                                                <td>
                                                    <span class="status-badge processing">
                                                        <i class="fas fa-clock"></i>
                                                        处理中
                                                    </span>
                                                </td>
                                                <td>
                                                    <span class="date">2024-01-14</span>
                                                </td>
                                                <td>
                                                    <div class="action-buttons">
                                                        <button class="btn-icon" title="查看详情">
                                                            <i class="fas fa-eye"></i>
                                                        </button>
                                                        <button class="btn-icon" title="编辑">
                                                            <i class="fas fa-edit"></i>
                                                        </button>
                                                        <button class="btn-icon danger" title="删除">
                                                            <i class="fas fa-trash"></i>
                                                        </button>
                                                    </div>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td>
                                                    <span class="order-id">#ORD-2024-003</span>
                                                </td>
                                                <td>
                                                    <div class="customer-info">
                                                        <div class="customer-avatar">
                                                            <img src="https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=32&h=32&fit=crop&crop=face" alt="客户头像">
                                                        </div>
                                                        <div class="customer-details">
                                                            <span class="customer-name">王五</span>
                                                            <span class="customer-email"><EMAIL></span>
                                                        </div>
                                                    </div>
                                                </td>
                                                <td>
                                                    <span class="amount">¥899</span>
                                                </td>
                                                <td>
                                                    <span class="status-badge pending">
                                                        <i class="fas fa-hourglass-half"></i>
                                                        待处理
                                                    </span>
                                                </td>
                                                <td>
                                                    <span class="date">2024-01-13</span>
                                                </td>
                                                <td>
                                                    <div class="action-buttons">
                                                        <button class="btn-icon" title="查看详情">
                                                            <i class="fas fa-eye"></i>
                                                        </button>
                                                        <button class="btn-icon" title="编辑">
                                                            <i class="fas fa-edit"></i>
                                                        </button>
                                                        <button class="btn-icon danger" title="删除">
                                                            <i class="fas fa-trash"></i>
                                                        </button>
                                                    </div>
                                                </td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>

                        <!-- 快速操作 -->
                        <div class="dashboard-card quick-actions-card">
                            <div class="card-header">
                                <h3>快速操作</h3>
                            </div>
                            <div class="card-content">
                                <div class="quick-actions">
                                    <button class="quick-action-btn">
                                        <i class="fas fa-user-plus"></i>
                                        <span>添加用户</span>
                                    </button>
                                    <button class="quick-action-btn">
                                        <i class="fas fa-file-invoice"></i>
                                        <span>创建订单</span>
                                    </button>
                                    <button class="quick-action-btn">
                                        <i class="fas fa-chart-bar"></i>
                                        <span>生成报告</span>
                                    </button>
                                    <button class="quick-action-btn">
                                        <i class="fas fa-cog"></i>
                                        <span>系统设置</span>
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- 活动日志 -->
                        <div class="dashboard-card activity-card">
                            <div class="card-header">
                                <h3>最近活动</h3>
                            </div>
                            <div class="card-content">
                                <div class="activity-list">
                                    <div class="activity-item">
                                        <div class="activity-icon success">
                                            <i class="fas fa-check"></i>
                                        </div>
                                        <div class="activity-content">
                                            <p><strong>张三</strong> 完成了订单 #ORD-2024-001</p>
                                            <span class="activity-time">2分钟前</span>
                                        </div>
                                    </div>
                                    <div class="activity-item">
                                        <div class="activity-icon primary">
                                            <i class="fas fa-user"></i>
                                        </div>
                                        <div class="activity-content">
                                            <p>新用户 <strong>李四</strong> 注册了账户</p>
                                            <span class="activity-time">15分钟前</span>
                                        </div>
                                    </div>
                                    <div class="activity-item">
                                        <div class="activity-icon warning">
                                            <i class="fas fa-exclamation"></i>
                                        </div>
                                        <div class="activity-content">
                                            <p>订单 #ORD-2024-002 需要处理</p>
                                            <span class="activity-time">1小时前</span>
                                        </div>
                                    </div>
                                    <div class="activity-item">
                                        <div class="activity-icon info">
                                            <i class="fas fa-info"></i>
                                        </div>
                                        <div class="activity-content">
                                            <p>系统备份已完成</p>
                                            <span class="activity-time">2小时前</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 高级图表区域 -->
                    <div class="charts-section">
                        <div class="charts-grid">
                            <!-- 销售趋势图 -->
                            <div class="chart-card">
                                <div class="chart-header">
                                    <h3>销售趋势</h3>
                                    <div class="chart-actions">
                                        <button class="btn-icon" onclick="advancedCharts?.exportChart('sales')" title="导出图表">
                                            <i class="fas fa-download"></i>
                                        </button>
                                        <button class="btn-icon" title="设置">
                                            <i class="fas fa-cog"></i>
                                        </button>
                                    </div>
                                </div>
                                <div class="chart-container">
                                    <canvas id="salesChart"></canvas>
                                </div>
                            </div>

                            <!-- 用户增长图 -->
                            <div class="chart-card">
                                <div class="chart-header">
                                    <h3>用户增长</h3>
                                    <div class="chart-actions">
                                        <button class="btn-icon" onclick="advancedCharts?.exportChart('userGrowth')" title="导出图表">
                                            <i class="fas fa-download"></i>
                                        </button>
                                    </div>
                                </div>
                                <div class="chart-container">
                                    <canvas id="userGrowthChart"></canvas>
                                </div>
                            </div>

                            <!-- 收入分析图 -->
                            <div class="chart-card">
                                <div class="chart-header">
                                    <h3>收入分析</h3>
                                    <div class="chart-actions">
                                        <button class="btn-icon" onclick="advancedCharts?.exportChart('revenue')" title="导出图表">
                                            <i class="fas fa-download"></i>
                                        </button>
                                    </div>
                                </div>
                                <div class="chart-container">
                                    <canvas id="revenueChart"></canvas>
                                </div>
                            </div>

                            <!-- 订单状态图 -->
                            <div class="chart-card">
                                <div class="chart-header">
                                    <h3>订单状态</h3>
                                    <div class="chart-actions">
                                        <button class="btn-icon" onclick="advancedCharts?.exportChart('orderStatus')" title="导出图表">
                                            <i class="fas fa-download"></i>
                                        </button>
                                    </div>
                                </div>
                                <div class="chart-container">
                                    <canvas id="orderStatusChart"></canvas>
                                </div>
                            </div>

                            <!-- 流量来源图 -->
                            <div class="chart-card">
                                <div class="chart-header">
                                    <h3>流量来源</h3>
                                    <div class="chart-actions">
                                        <button class="btn-icon" onclick="advancedCharts?.exportChart('trafficSource')" title="导出图表">
                                            <i class="fas fa-download"></i>
                                        </button>
                                    </div>
                                </div>
                                <div class="chart-container">
                                    <canvas id="trafficSourceChart"></canvas>
                                </div>
                            </div>

                            <!-- 性能监控图 -->
                            <div class="chart-card">
                                <div class="chart-header">
                                    <h3>系统性能</h3>
                                    <div class="chart-actions">
                                        <button class="btn-icon" onclick="advancedCharts?.exportChart('performance')" title="导出图表">
                                            <i class="fas fa-download"></i>
                                        </button>
                                    </div>
                                </div>
                                <div class="chart-container">
                                    <canvas id="performanceChart"></canvas>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 用户管理页面 -->
                <div class="page users-page" id="users">
                    <div class="page-header">
                        <h1>用户管理</h1>
                        <p>管理系统用户和权限</p>
                        <div class="page-actions">
                            <button class="btn-primary add-user-btn" onclick="showAddUserModal()">
                                <i class="fas fa-plus"></i>
                                添加用户
                            </button>
                            <button class="btn-secondary" onclick="showImportExportModal('import')">
                                <i class="fas fa-upload"></i>
                                导入用户
                            </button>
                            <button class="btn-secondary" onclick="showImportExportModal('export')">
                                <i class="fas fa-download"></i>
                                导出用户
                            </button>
                        </div>
                    </div>



                    <!-- 用户统计卡片 -->
                    <div class="stats-grid">
                        <div class="stat-card primary">
                            <div class="stat-content">
                                <div class="stat-icon">
                                    <i class="fas fa-users"></i>
                                </div>
                                <div class="stat-info">
                                    <h3 class="stat-number" id="totalUsers">0</h3>
                                    <p class="stat-label">总用户数</p>
                                </div>
                            </div>
                        </div>
                        <div class="stat-card success">
                            <div class="stat-content">
                                <div class="stat-icon">
                                    <i class="fas fa-user-check"></i>
                                </div>
                                <div class="stat-info">
                                    <h3 class="stat-number" id="activeUsers">0</h3>
                                    <p class="stat-label">活跃用户</p>
                                </div>
                            </div>
                        </div>
                        <div class="stat-card warning">
                            <div class="stat-content">
                                <div class="stat-icon">
                                    <i class="fas fa-user-shield"></i>
                                </div>
                                <div class="stat-info">
                                    <h3 class="stat-number" id="adminUsers">0</h3>
                                    <p class="stat-label">管理员</p>
                                </div>
                            </div>
                        </div>
                        <div class="stat-card info">
                            <div class="stat-content">
                                <div class="stat-icon">
                                    <i class="fas fa-user-plus"></i>
                                </div>
                                <div class="stat-info">
                                    <h3 class="stat-number" id="newUsers">0</h3>
                                    <p class="stat-label">本月新增</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 搜索和筛选 -->
                    <div class="dashboard-card">
                        <div class="card-header">
                            <h3>用户列表</h3>
                            <div class="search-filters">
                                <div class="search-box">
                                    <i class="fas fa-search"></i>
                                    <input type="text" id="userSearch" placeholder="搜索用户名、邮箱或姓名..." onkeyup="filterUsers()">
                                </div>
                                <select id="roleFilter" onchange="filterUsers()">
                                    <option value="">所有角色</option>
                                    <option value="系统管理员">系统管理员</option>
                                    <option value="部门经理">部门经理</option>
                                    <option value="普通用户">普通用户</option>
                                    <option value="测试用户">测试用户</option>
                                    <option value="演示用户">演示用户</option>
                                </select>
                                <select id="statusFilter" onchange="filterUsers()">
                                    <option value="">所有状态</option>
                                    <option value="active">活跃</option>
                                    <option value="inactive">非活跃</option>
                                    <option value="suspended">已暂停</option>
                                </select>
                                <button class="btn-secondary btn-sm" onclick="toggleAdvancedFilters()">
                                    <i class="fas fa-sliders-h"></i>
                                    高级筛选
                                </button>
                                <button class="btn-secondary btn-sm" onclick="refreshUsers()">
                                    <i class="fas fa-sync"></i>
                                    刷新
                                </button>
                            </div>
                        </div>
                        <div class="card-content">
                            <!-- 批量操作工具栏 -->
                            <div class="bulk-actions-toolbar" id="bulkActionsToolbar" style="display: none;">
                                <div class="bulk-actions-info">
                                    <span id="selectedCount">0</span> 个用户已选中
                                </div>
                                <div class="bulk-actions-buttons">
                                    <button class="btn-secondary btn-sm" onclick="bulkActivateUsers()">
                                        <i class="fas fa-check"></i>
                                        批量激活
                                    </button>
                                    <button class="btn-secondary btn-sm" onclick="bulkDeactivateUsers()">
                                        <i class="fas fa-pause"></i>
                                        批量停用
                                    </button>
                                    <button class="btn-secondary btn-sm" onclick="bulkChangeRole()">
                                        <i class="fas fa-user-tag"></i>
                                        批量改角色
                                    </button>
                                    <button class="btn-secondary btn-sm" onclick="bulkExportUsers()">
                                        <i class="fas fa-download"></i>
                                        导出选中
                                    </button>
                                    <button class="btn-danger btn-sm" onclick="bulkDeleteUsers()">
                                        <i class="fas fa-trash"></i>
                                        批量删除
                                    </button>
                                </div>
                            </div>

                            <div class="table-container">
                                <table class="modern-table" id="usersTable">
                                    <thead>
                                        <tr>
                                            <th>
                                                <input type="checkbox" id="selectAll" onchange="toggleSelectAll()">
                                            </th>
                                            <th>用户信息</th>
                                            <th>角色</th>
                                            <th>状态</th>
                                            <th>最后登录</th>
                                            <th>注册时间</th>
                                            <th>操作</th>
                                        </tr>
                                    </thead>
                                    <tbody id="usersTableBody">
                                        <!-- 用户数据将通过JavaScript动态生成 -->
                                    </tbody>
                                </table>
                            </div>

                            <!-- 高级筛选 -->
                            <div class="advanced-filters" id="advancedFilters" style="display: none;">
                                <div class="filter-row">
                                    <div class="filter-group">
                                        <label>注册时间</label>
                                        <input type="date" id="registerDateFrom" placeholder="开始日期">
                                        <span>至</span>
                                        <input type="date" id="registerDateTo" placeholder="结束日期">
                                    </div>
                                    <div class="filter-group">
                                        <label>最后登录</label>
                                        <select id="lastLoginFilter">
                                            <option value="">不限</option>
                                            <option value="today">今天</option>
                                            <option value="week">本周</option>
                                            <option value="month">本月</option>
                                            <option value="never">从未登录</option>
                                        </select>
                                    </div>
                                    <div class="filter-group">
                                        <label>权限级别</label>
                                        <select id="permissionFilter">
                                            <option value="">所有权限</option>
                                            <option value="full">完全权限</option>
                                            <option value="limited">受限权限</option>
                                            <option value="readonly">只读权限</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="filter-actions">
                                    <button class="btn-primary btn-sm" onclick="applyAdvancedFilters()">
                                        <i class="fas fa-filter"></i>
                                        应用筛选
                                    </button>
                                    <button class="btn-secondary btn-sm" onclick="clearAdvancedFilters()">
                                        <i class="fas fa-times"></i>
                                        清除筛选
                                    </button>
                                    <button class="btn-secondary btn-sm" onclick="toggleAdvancedFilters()">
                                        <i class="fas fa-chevron-up"></i>
                                        收起
                                    </button>
                                </div>
                            </div>

                            <!-- 分页 -->
                            <div class="pagination" id="usersPagination">
                                <!-- 分页控件将通过JavaScript生成 -->
                            </div>
                        </div>
                    </div>
                </div>

                <div class="page analytics-page" id="analytics">
                    <div class="page-header">
                        <h1>数据分析</h1>
                        <p>深入了解您的业务数据</p>
                        <div class="page-actions">
                            <select id="timeRangeSelect" onchange="updateAnalytics()">
                                <option value="7d">最近7天</option>
                                <option value="30d" selected>最近30天</option>
                                <option value="90d">最近90天</option>
                                <option value="1y">最近1年</option>
                            </select>
                            <button class="btn-primary" onclick="exportReport()">
                                <i class="fas fa-download"></i>
                                导出报告
                            </button>
                            <button class="btn-secondary" onclick="refreshData()">
                                <i class="fas fa-sync-alt"></i>
                                刷新数据
                            </button>
                        </div>
                    </div>



                    <!-- 关键指标卡片 -->
                    <div class="stats-grid">
                        <div class="stat-card primary">
                            <div class="stat-content">
                                <div class="stat-icon">
                                    <i class="fas fa-chart-line"></i>
                                </div>
                                <div class="stat-info">
                                    <h3 class="stat-number" id="totalRevenue">¥0</h3>
                                    <p class="stat-label">总收入</p>
                                    <div class="stat-trend positive" id="revenueTrend">
                                        <i class="fas fa-arrow-up"></i>
                                        <span>+0%</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="stat-card success">
                            <div class="stat-content">
                                <div class="stat-icon">
                                    <i class="fas fa-shopping-cart"></i>
                                </div>
                                <div class="stat-info">
                                    <h3 class="stat-number" id="totalOrders">0</h3>
                                    <p class="stat-label">总订单</p>
                                    <div class="stat-trend positive" id="ordersTrend">
                                        <i class="fas fa-arrow-up"></i>
                                        <span>+0%</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="stat-card warning">
                            <div class="stat-content">
                                <div class="stat-icon">
                                    <i class="fas fa-users"></i>
                                </div>
                                <div class="stat-info">
                                    <h3 class="stat-number" id="activeCustomers">0</h3>
                                    <p class="stat-label">活跃客户</p>
                                    <div class="stat-trend positive" id="customersTrend">
                                        <i class="fas fa-arrow-up"></i>
                                        <span>+0%</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="stat-card info">
                            <div class="stat-content">
                                <div class="stat-icon">
                                    <i class="fas fa-percentage"></i>
                                </div>
                                <div class="stat-info">
                                    <h3 class="stat-number" id="conversionRate">0%</h3>
                                    <p class="stat-label">转化率</p>
                                    <div class="stat-trend neutral" id="conversionTrend">
                                        <i class="fas fa-minus"></i>
                                        <span>0%</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 图表区域 -->
                    <div class="dashboard-grid">
                        <!-- 收入趋势图 -->
                        <div class="dashboard-card chart-card">
                            <div class="card-header">
                                <h3>收入趋势分析</h3>
                                <div class="chart-controls">
                                    <button class="chart-btn active" data-chart="revenue">收入</button>
                                    <button class="chart-btn" data-chart="profit">利润</button>
                                    <button class="chart-btn" data-chart="cost">成本</button>
                                </div>
                            </div>
                            <div class="card-content">
                                <div class="chart-container">
                                    <canvas id="revenueChart"></canvas>
                                </div>
                            </div>
                        </div>

                        <!-- 订单分析 -->
                        <div class="dashboard-card chart-card">
                            <div class="card-header">
                                <h3>订单状态分布</h3>
                            </div>
                            <div class="card-content">
                                <div class="chart-container">
                                    <canvas id="orderStatusChart"></canvas>
                                </div>
                            </div>
                        </div>

                        <!-- 客户分析 -->
                        <div class="dashboard-card chart-card">
                            <div class="card-header">
                                <h3>客户来源分析</h3>
                            </div>
                            <div class="card-content">
                                <div class="chart-container">
                                    <canvas id="customerSourceChart"></canvas>
                                </div>
                            </div>
                        </div>

                        <!-- 产品销售排行 -->
                        <div class="dashboard-card">
                            <div class="card-header">
                                <h3>热销产品排行</h3>
                            </div>
                            <div class="card-content">
                                <div class="product-ranking" id="productRanking">
                                    <!-- 产品排行数据将通过JavaScript生成 -->
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 实时数据监控 -->
                    <div class="dashboard-card">
                        <div class="card-header">
                            <h3>实时数据监控</h3>
                            <div class="card-actions">
                                <div class="status-indicator" id="realTimeStatus">
                                    <span class="status-dot active"></span>
                                    实时更新
                                </div>
                                <button class="btn-sm" onclick="toggleRealTimeUpdates()">
                                    <i class="fas fa-pause" id="realTimeToggleIcon"></i>
                                    暂停
                                </button>
                            </div>
                        </div>
                        <div class="card-content">
                            <div class="real-time-metrics">
                                <div class="metric-item">
                                    <div class="metric-label">当前在线用户</div>
                                    <div class="metric-value" id="onlineUsers">0</div>
                                    <div class="metric-change positive" id="onlineUsersChange">+0</div>
                                </div>
                                <div class="metric-item">
                                    <div class="metric-label">今日新订单</div>
                                    <div class="metric-value" id="todayOrders">0</div>
                                    <div class="metric-change positive" id="todayOrdersChange">+0</div>
                                </div>
                                <div class="metric-item">
                                    <div class="metric-label">实时收入</div>
                                    <div class="metric-value" id="realTimeRevenue">¥0</div>
                                    <div class="metric-change positive" id="revenueChange">+¥0</div>
                                </div>
                                <div class="metric-item">
                                    <div class="metric-label">转化率</div>
                                    <div class="metric-value" id="conversionRate">0%</div>
                                    <div class="metric-change positive" id="conversionChange">+0%</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 数据对比分析 -->
                    <div class="dashboard-grid">
                        <div class="dashboard-card">
                            <div class="card-header">
                                <h3>同期对比分析</h3>
                                <div class="card-actions">
                                    <select id="comparisonPeriod" onchange="updateComparison()">
                                        <option value="lastWeek">与上周对比</option>
                                        <option value="lastMonth">与上月对比</option>
                                        <option value="lastYear">与去年同期对比</option>
                                    </select>
                                </div>
                            </div>
                            <div class="card-content">
                                <div class="comparison-chart-container">
                                    <canvas id="comparisonChart"></canvas>
                                </div>
                            </div>
                        </div>

                        <div class="dashboard-card">
                            <div class="card-header">
                                <h3>趋势预测</h3>
                                <div class="card-actions">
                                    <select id="predictionMetric" onchange="updatePrediction()">
                                        <option value="revenue">收入预测</option>
                                        <option value="orders">订单预测</option>
                                        <option value="users">用户增长预测</option>
                                    </select>
                                </div>
                            </div>
                            <div class="card-content">
                                <div class="prediction-chart-container">
                                    <canvas id="predictionChart"></canvas>
                                </div>
                                <div class="prediction-summary">
                                    <div class="prediction-item">
                                        <span class="prediction-label">下周预测:</span>
                                        <span class="prediction-value" id="nextWeekPrediction">-</span>
                                    </div>
                                    <div class="prediction-item">
                                        <span class="prediction-label">下月预测:</span>
                                        <span class="prediction-value" id="nextMonthPrediction">-</span>
                                    </div>
                                    <div class="prediction-item">
                                        <span class="prediction-label">置信度:</span>
                                        <span class="prediction-confidence" id="predictionConfidence">-</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 地理分析 -->
                    <div class="dashboard-card">
                        <div class="card-header">
                            <h3>地理分布分析</h3>
                            <div class="card-actions">
                                <button class="btn-sm" onclick="toggleMapView()">
                                    <i class="fas fa-map"></i>
                                    地图视图
                                </button>
                                <button class="btn-sm" onclick="exportGeoData()">
                                    <i class="fas fa-download"></i>
                                    导出数据
                                </button>
                            </div>
                        </div>
                        <div class="card-content">
                            <div class="geo-analysis-container">
                                <div class="geo-chart-container">
                                    <canvas id="geoChart"></canvas>
                                </div>
                                <div class="geo-stats">
                                    <div class="geo-stat-item">
                                        <div class="geo-region">华东地区</div>
                                        <div class="geo-percentage">45.2%</div>
                                        <div class="geo-bar">
                                            <div class="geo-bar-fill" style="width: 45.2%"></div>
                                        </div>
                                    </div>
                                    <div class="geo-stat-item">
                                        <div class="geo-region">华南地区</div>
                                        <div class="geo-percentage">28.7%</div>
                                        <div class="geo-bar">
                                            <div class="geo-bar-fill" style="width: 28.7%"></div>
                                        </div>
                                    </div>
                                    <div class="geo-stat-item">
                                        <div class="geo-region">华北地区</div>
                                        <div class="geo-percentage">15.8%</div>
                                        <div class="geo-bar">
                                            <div class="geo-bar-fill" style="width: 15.8%"></div>
                                        </div>
                                    </div>
                                    <div class="geo-stat-item">
                                        <div class="geo-region">其他地区</div>
                                        <div class="geo-percentage">10.3%</div>
                                        <div class="geo-bar">
                                            <div class="geo-bar-fill" style="width: 10.3%"></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 详细数据表格 -->
                    <div class="dashboard-card">
                        <div class="card-header">
                            <h3>详细数据报表</h3>
                            <div class="card-actions">
                                <button class="btn-sm" onclick="toggleTableView()">
                                    <i class="fas fa-table"></i>
                                    切换视图
                                </button>
                            </div>
                        </div>
                        <div class="card-content">
                            <div class="table-container">
                                <table class="modern-table" id="analyticsTable">
                                    <thead>
                                        <tr>
                                            <th>日期</th>
                                            <th>收入</th>
                                            <th>订单数</th>
                                            <th>客户数</th>
                                            <th>转化率</th>
                                            <th>平均订单价值</th>
                                        </tr>
                                    </thead>
                                    <tbody id="analyticsTableBody">
                                        <!-- 数据将通过JavaScript生成 -->
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="page orders-page" id="orders">
                    <div class="page-header">
                        <h1>订单管理</h1>
                        <p>管理所有订单和交易</p>
                        <div class="page-actions">
                            <button class="btn-primary" onclick="showAddOrderModal()">
                                <i class="fas fa-plus"></i>
                                新建订单
                            </button>
                            <button class="btn-secondary" onclick="exportOrders()">
                                <i class="fas fa-download"></i>
                                导出订单
                            </button>
                            <button class="btn-secondary" onclick="batchProcess()">
                                <i class="fas fa-tasks"></i>
                                批量处理
                            </button>
                        </div>
                    </div>



                    <!-- 订单统计 -->
                    <div class="stats-grid">
                        <div class="stat-card primary">
                            <div class="stat-content">
                                <div class="stat-icon">
                                    <i class="fas fa-shopping-cart"></i>
                                </div>
                                <div class="stat-info">
                                    <h3 class="stat-number" id="totalOrdersCount">0</h3>
                                    <p class="stat-label">总订单数</p>
                                    <div class="stat-trend positive">
                                        <i class="fas fa-arrow-up"></i>
                                        <span>+12.5%</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="stat-card success">
                            <div class="stat-content">
                                <div class="stat-icon">
                                    <i class="fas fa-check-circle"></i>
                                </div>
                                <div class="stat-info">
                                    <h3 class="stat-number" id="completedOrders">0</h3>
                                    <p class="stat-label">已完成</p>
                                    <div class="stat-trend positive">
                                        <i class="fas fa-arrow-up"></i>
                                        <span>+8.2%</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="stat-card warning">
                            <div class="stat-content">
                                <div class="stat-icon">
                                    <i class="fas fa-clock"></i>
                                </div>
                                <div class="stat-info">
                                    <h3 class="stat-number" id="pendingOrders">0</h3>
                                    <p class="stat-label">待处理</p>
                                    <div class="stat-trend negative">
                                        <i class="fas fa-arrow-down"></i>
                                        <span>-3.1%</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="stat-card info">
                            <div class="stat-content">
                                <div class="stat-icon">
                                    <i class="fas fa-dollar-sign"></i>
                                </div>
                                <div class="stat-info">
                                    <h3 class="stat-number" id="totalOrderValue">¥0</h3>
                                    <p class="stat-label">总金额</p>
                                    <div class="stat-trend positive">
                                        <i class="fas fa-arrow-up"></i>
                                        <span>+15.8%</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 订单筛选和搜索 -->
                    <div class="dashboard-card">
                        <div class="card-header">
                            <h3>订单列表</h3>
                            <div class="search-filters">
                                <div class="search-box">
                                    <i class="fas fa-search"></i>
                                    <input type="text" id="orderSearch" placeholder="搜索订单号、客户名称..." onkeyup="filterOrders()">
                                </div>
                                <select id="orderStatusFilter" onchange="filterOrders()">
                                    <option value="">所有状态</option>
                                    <option value="pending">待付款</option>
                                    <option value="processing">处理中</option>
                                    <option value="shipped">已发货</option>
                                    <option value="completed">已完成</option>
                                    <option value="cancelled">已取消</option>
                                    <option value="refunded">已退款</option>
                                </select>
                                <select id="orderDateFilter" onchange="filterOrders()">
                                    <option value="">所有时间</option>
                                    <option value="today">今天</option>
                                    <option value="week">本周</option>
                                    <option value="month">本月</option>
                                    <option value="quarter">本季度</option>
                                </select>
                                <select id="orderAmountFilter" onchange="filterOrders()">
                                    <option value="">所有金额</option>
                                    <option value="0-100">¥0-100</option>
                                    <option value="100-500">¥100-500</option>
                                    <option value="500-1000">¥500-1000</option>
                                    <option value="1000+">¥1000+</option>
                                </select>
                                <button class="btn-secondary btn-sm" onclick="toggleOrderAdvancedFilters()">
                                    <i class="fas fa-sliders-h"></i>
                                    高级筛选
                                </button>
                                <button class="btn-secondary btn-sm" onclick="refreshOrders()">
                                    <i class="fas fa-sync"></i>
                                    刷新
                                </button>
                            </div>
                        </div>
                        <div class="card-content">
                            <!-- 批量操作工具栏 -->
                            <div class="bulk-actions-toolbar" id="orderBulkActionsToolbar" style="display: none;">
                                <div class="bulk-actions-info">
                                    <span id="selectedOrdersCount">0</span> 个订单已选中
                                </div>
                                <div class="bulk-actions-buttons">
                                    <button class="btn-secondary btn-sm" onclick="bulkUpdateOrderStatus('processing')">
                                        <i class="fas fa-play"></i>
                                        批量处理
                                    </button>
                                    <button class="btn-secondary btn-sm" onclick="bulkUpdateOrderStatus('shipped')">
                                        <i class="fas fa-shipping-fast"></i>
                                        批量发货
                                    </button>
                                    <button class="btn-secondary btn-sm" onclick="bulkUpdateOrderStatus('completed')">
                                        <i class="fas fa-check"></i>
                                        批量完成
                                    </button>
                                    <button class="btn-secondary btn-sm" onclick="bulkExportOrders()">
                                        <i class="fas fa-download"></i>
                                        导出选中
                                    </button>
                                    <button class="btn-danger btn-sm" onclick="bulkCancelOrders()">
                                        <i class="fas fa-times"></i>
                                        批量取消
                                    </button>
                                </div>
                            </div>

                            <!-- 高级筛选面板 -->
                            <div class="advanced-filters" id="orderAdvancedFilters" style="display: none;">
                                <div class="filter-row">
                                    <div class="filter-group">
                                        <label>订单金额范围</label>
                                        <input type="number" id="minAmount" placeholder="最小金额" min="0">
                                        <span>至</span>
                                        <input type="number" id="maxAmount" placeholder="最大金额" min="0">
                                    </div>
                                    <div class="filter-group">
                                        <label>创建时间范围</label>
                                        <input type="date" id="orderDateFrom">
                                        <span>至</span>
                                        <input type="date" id="orderDateTo">
                                    </div>
                                    <div class="filter-group">
                                        <label>支付方式</label>
                                        <select id="paymentMethodFilter">
                                            <option value="">所有方式</option>
                                            <option value="alipay">支付宝</option>
                                            <option value="wechat">微信支付</option>
                                            <option value="bank">银行卡</option>
                                            <option value="cash">现金</option>
                                        </select>
                                    </div>
                                    <div class="filter-group">
                                        <label>配送方式</label>
                                        <select id="shippingMethodFilter">
                                            <option value="">所有方式</option>
                                            <option value="express">快递</option>
                                            <option value="pickup">自提</option>
                                            <option value="same-day">当日达</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="filter-actions">
                                    <button class="btn-primary btn-sm" onclick="applyOrderAdvancedFilters()">
                                        <i class="fas fa-filter"></i>
                                        应用筛选
                                    </button>
                                    <button class="btn-secondary btn-sm" onclick="clearOrderAdvancedFilters()">
                                        <i class="fas fa-times"></i>
                                        清除筛选
                                    </button>
                                    <button class="btn-secondary btn-sm" onclick="toggleOrderAdvancedFilters()">
                                        <i class="fas fa-chevron-up"></i>
                                        收起
                                    </button>
                                </div>
                            </div>
                            <div class="table-container">
                                <table class="modern-table" id="ordersTable">
                                    <thead>
                                        <tr>
                                            <th>
                                                <input type="checkbox" id="selectAllOrders" onchange="toggleSelectAllOrders()">
                                            </th>
                                            <th>订单信息</th>
                                            <th>客户</th>
                                            <th>商品</th>
                                            <th>金额</th>
                                            <th>状态</th>
                                            <th>创建时间</th>
                                            <th>操作</th>
                                        </tr>
                                    </thead>
                                    <tbody id="ordersTableBody">
                                        <!-- 订单数据将通过JavaScript生成 -->
                                    </tbody>
                                </table>
                            </div>

                            <!-- 分页 -->
                            <div class="pagination" id="ordersPagination">
                                <!-- 分页控件将通过JavaScript生成 -->
                            </div>
                        </div>
                    </div>
                </div>

                <!-- NER数据管理页面 -->
                <div class="page ner-page" id="ner">
                    <div class="page-header">
                        <h1>NER数据管理</h1>
                        <p>命名实体识别训练数据生成和管理</p>
                        <div class="page-actions">
                            <button class="btn-primary" onclick="generateNERData()">
                                <i class="fas fa-magic"></i>
                                生成数据
                            </button>
                            <button class="btn-secondary" onclick="downloadNERData()">
                                <i class="fas fa-download"></i>
                                下载数据
                            </button>
                            <button class="btn-secondary" onclick="clearNERData()">
                                <i class="fas fa-trash"></i>
                                清空数据
                            </button>
                        </div>
                    </div>

                    <!-- NER统计 -->
                    <div class="stats-grid">
                        <div class="stat-card primary">
                            <div class="stat-content">
                                <div class="stat-icon">
                                    <i class="fas fa-database"></i>
                                </div>
                                <div class="stat-info">
                                    <h3 class="stat-number" id="totalNERSamples">0</h3>
                                    <p class="stat-label">总样本数</p>
                                    <div class="stat-trend positive">
                                        <i class="fas fa-arrow-up"></i>
                                        <span id="samplesTrend">+0%</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="stat-card success">
                            <div class="stat-content">
                                <div class="stat-icon">
                                    <i class="fas fa-user-tag"></i>
                                </div>
                                <div class="stat-info">
                                    <h3 class="stat-number" id="totalPersonEntities">0</h3>
                                    <p class="stat-label">人名实体</p>
                                    <div class="stat-trend positive">
                                        <i class="fas fa-arrow-up"></i>
                                        <span>+5.2%</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="stat-card warning">
                            <div class="stat-content">
                                <div class="stat-icon">
                                    <i class="fas fa-map-marker-alt"></i>
                                </div>
                                <div class="stat-info">
                                    <h3 class="stat-number" id="totalLocationEntities">0</h3>
                                    <p class="stat-label">地名实体</p>
                                    <div class="stat-trend positive">
                                        <i class="fas fa-arrow-up"></i>
                                        <span>+3.8%</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="stat-card info">
                            <div class="stat-content">
                                <div class="stat-icon">
                                    <i class="fas fa-building"></i>
                                </div>
                                <div class="stat-info">
                                    <h3 class="stat-number" id="totalOrgEntities">0</h3>
                                    <p class="stat-label">机构实体</p>
                                    <div class="stat-trend positive">
                                        <i class="fas fa-arrow-up"></i>
                                        <span>+7.1%</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 数据生成配置 -->
                    <div class="dashboard-card">
                        <div class="card-header">
                            <h3>数据生成配置</h3>
                            <div class="card-actions">
                                <button class="btn-secondary btn-sm" onclick="resetNERConfig()">
                                    <i class="fas fa-undo"></i>
                                    重置配置
                                </button>
                            </div>
                        </div>
                        <div class="card-content">
                            <div class="form-grid">
                                <div class="form-group">
                                    <label for="nerSampleCount">样本数量</label>
                                    <input type="number" id="nerSampleCount" value="1000" min="100" max="50000" step="100">
                                    <small>建议生成1000-10000条样本</small>
                                </div>
                                <div class="form-group">
                                    <label for="nerEntityTypes">实体类型</label>
                                    <div class="checkbox-group">
                                        <label class="checkbox-item">
                                            <input type="checkbox" id="enablePerson" checked>
                                            <span class="checkmark"></span>
                                            人名 (PERSON)
                                        </label>
                                        <label class="checkbox-item">
                                            <input type="checkbox" id="enableLocation" checked>
                                            <span class="checkmark"></span>
                                            地名 (LOCATION)
                                        </label>
                                        <label class="checkbox-item">
                                            <input type="checkbox" id="enableOrganization" checked>
                                            <span class="checkmark"></span>
                                            机构 (ORGANIZATION)
                                        </label>
                                        <label class="checkbox-item">
                                            <input type="checkbox" id="enableTime" checked>
                                            <span class="checkmark"></span>
                                            时间 (TIME)
                                        </label>
                                        <label class="checkbox-item">
                                            <input type="checkbox" id="enableNumber" checked>
                                            <span class="checkmark"></span>
                                            数字 (NUMBER)
                                        </label>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label for="nerOutputFormat">输出格式</label>
                                    <select id="nerOutputFormat">
                                        <option value="json">JSON格式</option>
                                        <option value="conll">CoNLL格式</option>
                                        <option value="bio">BIO标签格式</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 数据预览和管理 -->
                    <div class="dashboard-card">
                        <div class="card-header">
                            <h3>数据预览</h3>
                            <div class="card-actions">
                                <button class="btn-secondary btn-sm" onclick="refreshNERPreview()">
                                    <i class="fas fa-sync"></i>
                                    刷新
                                </button>
                                <button class="btn-secondary btn-sm" onclick="exportNERSample()">
                                    <i class="fas fa-file-export"></i>
                                    导出样本
                                </button>
                            </div>
                        </div>
                        <div class="card-content">
                            <div id="nerDataPreview" class="ner-preview">
                                <div class="preview-placeholder">
                                    <i class="fas fa-database"></i>
                                    <p>暂无数据，请先生成NER训练数据</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 实体统计图表 -->
                    <div class="dashboard-card">
                        <div class="card-header">
                            <h3>实体分布统计</h3>
                            <div class="card-actions">
                                <select id="nerChartType" onchange="updateNERChart()">
                                    <option value="pie">饼图</option>
                                    <option value="bar">柱状图</option>
                                    <option value="doughnut">环形图</option>
                                </select>
                            </div>
                        </div>
                        <div class="card-content">
                            <div class="chart-container">
                                <canvas id="nerEntityChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 文件管理页面 -->
                <div class="page files-page" id="files">
                    <div class="page-header">
                        <h1>文件管理</h1>
                        <p>管理和组织您的文件</p>
                        <div class="page-actions">
                            <button class="btn-primary" onclick="showUploadModal()">
                                <i class="fas fa-upload"></i>
                                上传文件
                            </button>
                            <button class="btn-secondary" onclick="createNewFolder()">
                                <i class="fas fa-folder-plus"></i>
                                新建文件夹
                            </button>
                            <button class="btn-secondary" onclick="refreshFiles()">
                                <i class="fas fa-sync"></i>
                                刷新
                            </button>
                        </div>
                    </div>

                    <!-- 文件统计 -->
                    <div class="stats-grid">
                        <div class="stat-card primary">
                            <div class="stat-content">
                                <div class="stat-icon">
                                    <i class="fas fa-file"></i>
                                </div>
                                <div class="stat-info">
                                    <h3 class="stat-number" id="totalFiles">0</h3>
                                    <p class="stat-label">总文件数</p>
                                </div>
                            </div>
                        </div>
                        <div class="stat-card success">
                            <div class="stat-content">
                                <div class="stat-icon">
                                    <i class="fas fa-folder"></i>
                                </div>
                                <div class="stat-info">
                                    <h3 class="stat-number" id="totalFolders">0</h3>
                                    <p class="stat-label">文件夹数</p>
                                </div>
                            </div>
                        </div>
                        <div class="stat-card warning">
                            <div class="stat-content">
                                <div class="stat-icon">
                                    <i class="fas fa-hdd"></i>
                                </div>
                                <div class="stat-info">
                                    <h3 class="stat-number" id="totalSize">0 MB</h3>
                                    <p class="stat-label">总大小</p>
                                </div>
                            </div>
                        </div>
                        <div class="stat-card info">
                            <div class="stat-content">
                                <div class="stat-icon">
                                    <i class="fas fa-clock"></i>
                                </div>
                                <div class="stat-info">
                                    <h3 class="stat-number" id="recentFiles">0</h3>
                                    <p class="stat-label">最近文件</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 文件操作工具栏 -->
                    <div class="dashboard-card">
                        <div class="card-header">
                            <h3>文件浏览器</h3>
                            <div class="file-toolbar">
                                <div class="breadcrumb" id="fileBreadcrumb">
                                    <span class="breadcrumb-item active" onclick="navigateToPath('/')">
                                        <i class="fas fa-home"></i>
                                        根目录
                                    </span>
                                </div>
                                <div class="view-controls">
                                    <button class="view-btn active" onclick="setViewMode('grid')" title="网格视图">
                                        <i class="fas fa-th"></i>
                                    </button>
                                    <button class="view-btn" onclick="setViewMode('list')" title="列表视图">
                                        <i class="fas fa-list"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div class="card-content">
                            <!-- 文件筛选和搜索 -->
                            <div class="file-filters">
                                <div class="search-box">
                                    <i class="fas fa-search"></i>
                                    <input type="text" id="fileSearch" placeholder="搜索文件和文件夹..." onkeyup="filterFiles()">
                                </div>
                                <select id="fileTypeFilter" onchange="filterFiles()">
                                    <option value="">所有类型</option>
                                    <option value="image">图片</option>
                                    <option value="document">文档</option>
                                    <option value="video">视频</option>
                                    <option value="audio">音频</option>
                                    <option value="archive">压缩包</option>
                                    <option value="other">其他</option>
                                </select>
                                <select id="fileSizeFilter" onchange="filterFiles()">
                                    <option value="">所有大小</option>
                                    <option value="small">小于1MB</option>
                                    <option value="medium">1MB-10MB</option>
                                    <option value="large">大于10MB</option>
                                </select>
                                <select id="fileDateFilter" onchange="filterFiles()">
                                    <option value="">所有时间</option>
                                    <option value="today">今天</option>
                                    <option value="week">本周</option>
                                    <option value="month">本月</option>
                                </select>
                            </div>

                            <!-- 批量操作工具栏 -->
                            <div class="bulk-actions-toolbar" id="fileBulkActionsToolbar" style="display: none;">
                                <div class="bulk-actions-info">
                                    <span id="selectedFilesCount">0</span> 个项目已选中
                                </div>
                                <div class="bulk-actions-buttons">
                                    <button class="btn-secondary btn-sm" onclick="bulkDownloadFiles()">
                                        <i class="fas fa-download"></i>
                                        批量下载
                                    </button>
                                    <button class="btn-secondary btn-sm" onclick="bulkMoveFiles()">
                                        <i class="fas fa-arrows-alt"></i>
                                        批量移动
                                    </button>
                                    <button class="btn-secondary btn-sm" onclick="bulkCopyFiles()">
                                        <i class="fas fa-copy"></i>
                                        批量复制
                                    </button>
                                    <button class="btn-danger btn-sm" onclick="bulkDeleteFiles()">
                                        <i class="fas fa-trash"></i>
                                        批量删除
                                    </button>
                                </div>
                            </div>

                            <!-- 文件列表容器 -->
                            <div class="file-container" id="fileContainer">
                                <div class="file-grid" id="fileGrid">
                                    <!-- 文件和文件夹将通过JavaScript动态生成 -->
                                </div>
                            </div>

                            <!-- 分页 -->
                            <div class="pagination" id="filesPagination">
                                <!-- 分页控件将通过JavaScript生成 -->
                            </div>
                        </div>
                    </div>

                    <!-- 文件预览模态框 -->
                    <div class="modal" id="filePreviewModal">
                        <div class="modal-content file-preview-content">
                            <div class="modal-header">
                                <h3 id="previewFileName">文件预览</h3>
                                <button class="modal-close" onclick="closeFilePreview()">
                                    <i class="fas fa-times"></i>
                                </button>
                            </div>
                            <div class="modal-body">
                                <div class="file-preview-container" id="filePreviewContainer">
                                    <!-- 预览内容将动态加载 -->
                                </div>
                            </div>
                            <div class="modal-footer">
                                <button class="btn-secondary" onclick="downloadFile()">
                                    <i class="fas fa-download"></i>
                                    下载
                                </button>
                                <button class="btn-secondary" onclick="shareFile()">
                                    <i class="fas fa-share"></i>
                                    分享
                                </button>
                                <button class="btn-danger" onclick="deleteFile()">
                                    <i class="fas fa-trash"></i>
                                    删除
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- 文件上传模态框 -->
                    <div class="modal" id="fileUploadModal">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h3>上传文件</h3>
                                <button class="modal-close" onclick="closeUploadModal()">
                                    <i class="fas fa-times"></i>
                                </button>
                            </div>
                            <div class="modal-body">
                                <div class="upload-area" id="uploadArea">
                                    <div class="upload-icon">
                                        <i class="fas fa-cloud-upload-alt"></i>
                                    </div>
                                    <div class="upload-text">
                                        <h4>拖拽文件到此处或点击选择</h4>
                                        <p>支持多种文件格式，单个文件最大100MB</p>
                                    </div>
                                    <input type="file" id="fileInput" multiple hidden>
                                    <button class="btn-primary" onclick="selectFiles()">
                                        <i class="fas fa-folder-open"></i>
                                        选择文件
                                    </button>
                                </div>
                                <div class="upload-progress" id="uploadProgress" style="display: none;">
                                    <div class="progress-bar">
                                        <div class="progress-fill" id="progressFill"></div>
                                    </div>
                                    <div class="progress-text" id="progressText">0%</div>
                                </div>
                                <div class="upload-list" id="uploadList">
                                    <!-- 上传文件列表 -->
                                </div>
                            </div>
                            <div class="modal-footer">
                                <button class="btn-secondary" onclick="closeUploadModal()">取消</button>
                                <button class="btn-primary" onclick="startUpload()" id="uploadBtn" disabled>
                                    <i class="fas fa-upload"></i>
                                    开始上传
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="page settings-page" id="settings">
                    <div class="page-header">
                        <h1>系统设置</h1>
                        <p>配置系统参数和选项</p>
                        <div class="page-actions">
                            <button class="btn-primary" onclick="saveAllSettings()">
                                <i class="fas fa-save"></i>
                                保存所有设置
                            </button>
                            <button class="btn-secondary" onclick="resetSettings()">
                                <i class="fas fa-undo"></i>
                                重置设置
                            </button>
                        </div>
                    </div>

                    <div class="settings-container">
                        <!-- 个人资料设置 -->
                        <div class="settings-card">
                            <div class="settings-header">
                                <h3><i class="fas fa-user"></i> 个人资料</h3>
                                <p>管理您的个人信息和偏好设置</p>
                            </div>
                            <div class="settings-content">
                                <div class="form-row">
                                    <div class="form-group">
                                        <label for="profileName">姓名</label>
                                        <input type="text" id="profileName" placeholder="请输入您的姓名">
                                    </div>
                                    <div class="form-group">
                                        <label for="profileEmail">邮箱</label>
                                        <input type="email" id="profileEmail" placeholder="请输入邮箱地址">
                                    </div>
                                </div>
                                <div class="form-row">
                                    <div class="form-group">
                                        <label for="profilePhone">电话</label>
                                        <input type="tel" id="profilePhone" placeholder="请输入电话号码">
                                    </div>
                                    <div class="form-group">
                                        <label for="profileDepartment">部门</label>
                                        <input type="text" id="profileDepartment" placeholder="请输入所属部门">
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label for="profileBio">个人简介</label>
                                    <textarea id="profileBio" rows="3" placeholder="请输入个人简介..."></textarea>
                                </div>
                            </div>
                        </div>

                        <!-- 界面设置 -->
                        <div class="settings-card">
                            <div class="settings-header">
                                <h3><i class="fas fa-palette"></i> 界面设置</h3>
                                <p>自定义系统界面外观和行为</p>
                            </div>
                            <div class="settings-content">
                                <div class="setting-item">
                                    <div class="setting-info">
                                        <label>主题模式</label>
                                        <span>选择您喜欢的界面主题</span>
                                    </div>
                                    <div class="setting-control">
                                        <select id="themeMode">
                                            <option value="light">浅色模式</option>
                                            <option value="dark">深色模式</option>
                                            <option value="auto">跟随系统</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="setting-item">
                                    <div class="setting-info">
                                        <label>侧边栏状态</label>
                                        <span>设置侧边栏默认展开状态</span>
                                    </div>
                                    <div class="setting-control">
                                        <label class="switch">
                                            <input type="checkbox" id="sidebarExpanded">
                                            <span class="slider"></span>
                                        </label>
                                    </div>
                                </div>
                                <div class="setting-item">
                                    <div class="setting-info">
                                        <label>动画效果</label>
                                        <span>启用或禁用界面动画效果</span>
                                    </div>
                                    <div class="setting-control">
                                        <label class="switch">
                                            <input type="checkbox" id="animationsEnabled" checked>
                                            <span class="slider"></span>
                                        </label>
                                    </div>
                                </div>
                                <div class="setting-item">
                                    <div class="setting-info">
                                        <label>语言设置</label>
                                        <span>选择系统显示语言</span>
                                    </div>
                                    <div class="setting-control">
                                        <select id="languageSelect">
                                            <option value="zh-CN">简体中文</option>
                                            <option value="zh-TW">繁体中文</option>
                                            <option value="en-US">English</option>
                                            <option value="ja-JP">日本語</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 通知设置 -->
                        <div class="settings-card">
                            <div class="settings-header">
                                <h3><i class="fas fa-bell"></i> 通知设置</h3>
                                <p>管理系统通知和提醒偏好</p>
                            </div>
                            <div class="settings-content">
                                <div class="setting-item">
                                    <div class="setting-info">
                                        <label>桌面通知</label>
                                        <span>允许系统发送桌面通知</span>
                                    </div>
                                    <div class="setting-control">
                                        <label class="switch">
                                            <input type="checkbox" id="desktopNotifications" checked>
                                            <span class="slider"></span>
                                        </label>
                                    </div>
                                </div>
                                <div class="setting-item">
                                    <div class="setting-info">
                                        <label>邮件通知</label>
                                        <span>接收重要事件的邮件通知</span>
                                    </div>
                                    <div class="setting-control">
                                        <label class="switch">
                                            <input type="checkbox" id="emailNotifications" checked>
                                            <span class="slider"></span>
                                        </label>
                                    </div>
                                </div>
                                <div class="setting-item">
                                    <div class="setting-info">
                                        <label>声音提示</label>
                                        <span>播放通知声音</span>
                                    </div>
                                    <div class="setting-control">
                                        <label class="switch">
                                            <input type="checkbox" id="soundNotifications">
                                            <span class="slider"></span>
                                        </label>
                                    </div>
                                </div>
                                <div class="setting-item">
                                    <div class="setting-info">
                                        <label>通知频率</label>
                                        <span>设置接收通知的频率</span>
                                    </div>
                                    <div class="setting-control">
                                        <select id="notificationFrequency">
                                            <option value="realtime">实时</option>
                                            <option value="hourly">每小时</option>
                                            <option value="daily">每日</option>
                                            <option value="weekly">每周</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 安全设置 -->
                        <div class="settings-card">
                            <div class="settings-header">
                                <h3><i class="fas fa-shield-alt"></i> 安全设置</h3>
                                <p>管理账户安全和隐私设置</p>
                            </div>
                            <div class="settings-content">
                                <div class="setting-item">
                                    <div class="setting-info">
                                        <label>自动登出</label>
                                        <span>设置无操作自动登出时间</span>
                                    </div>
                                    <div class="setting-control">
                                        <select id="autoLogout">
                                            <option value="never">从不</option>
                                            <option value="15">15分钟</option>
                                            <option value="30">30分钟</option>
                                            <option value="60">1小时</option>
                                            <option value="120">2小时</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="setting-item">
                                    <div class="setting-info">
                                        <label>双因素认证</label>
                                        <span>启用双因素身份验证</span>
                                    </div>
                                    <div class="setting-control">
                                        <label class="switch">
                                            <input type="checkbox" id="twoFactorAuth">
                                            <span class="slider"></span>
                                        </label>
                                    </div>
                                </div>
                                <div class="setting-item">
                                    <div class="setting-info">
                                        <label>登录历史</label>
                                        <span>记录登录活动历史</span>
                                    </div>
                                    <div class="setting-control">
                                        <button class="btn-secondary btn-sm" onclick="viewLoginHistory()">
                                            <i class="fas fa-history"></i>
                                            查看历史
                                        </button>
                                    </div>
                                </div>
                                <div class="setting-item">
                                    <div class="setting-info">
                                        <label>修改密码</label>
                                        <span>更改您的登录密码</span>
                                    </div>
                                    <div class="setting-control">
                                        <button class="btn-primary btn-sm" onclick="showChangePasswordModal()">
                                            <i class="fas fa-key"></i>
                                            修改密码
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 数据设置 -->
                        <div class="settings-card">
                            <div class="settings-header">
                                <h3><i class="fas fa-database"></i> 数据设置</h3>
                                <p>管理数据备份和导出设置</p>
                            </div>
                            <div class="settings-content">
                                <div class="setting-item">
                                    <div class="setting-info">
                                        <label>自动备份</label>
                                        <span>定期自动备份重要数据</span>
                                    </div>
                                    <div class="setting-control">
                                        <label class="switch">
                                            <input type="checkbox" id="autoBackup" checked>
                                            <span class="slider"></span>
                                        </label>
                                    </div>
                                </div>
                                <div class="setting-item">
                                    <div class="setting-info">
                                        <label>备份频率</label>
                                        <span>设置数据备份的频率</span>
                                    </div>
                                    <div class="setting-control">
                                        <select id="backupFrequency">
                                            <option value="daily">每日</option>
                                            <option value="weekly">每周</option>
                                            <option value="monthly">每月</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="setting-item">
                                    <div class="setting-info">
                                        <label>导出数据</label>
                                        <span>导出您的个人数据</span>
                                    </div>
                                    <div class="setting-control">
                                        <button class="btn-secondary btn-sm" onclick="exportUserData()">
                                            <i class="fas fa-download"></i>
                                            导出数据
                                        </button>
                                    </div>
                                </div>
                                <div class="setting-item">
                                    <div class="setting-info">
                                        <label>清除缓存</label>
                                        <span>清除浏览器缓存数据</span>
                                    </div>
                                    <div class="setting-control">
                                        <button class="btn-danger btn-sm" onclick="clearCache()">
                                            <i class="fas fa-trash"></i>
                                            清除缓存
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 通知中心页面 -->
                <div class="page notifications-page" id="notifications">
                    <div class="page-header">
                        <h1>通知中心</h1>
                        <p>管理您的消息和通知</p>
                        <div class="page-actions">
                            <button class="btn-primary" onclick="markAllAsRead()">
                                <i class="fas fa-check-double"></i>
                                全部标记已读
                            </button>
                            <button class="btn-secondary" onclick="clearAllNotifications()">
                                <i class="fas fa-trash"></i>
                                清空通知
                            </button>
                        </div>
                    </div>

                    <!-- 通知统计 -->
                    <div class="stats-grid">
                        <div class="stat-card primary">
                            <div class="stat-content">
                                <div class="stat-icon">
                                    <i class="fas fa-bell"></i>
                                </div>
                                <div class="stat-info">
                                    <h3 class="stat-number" id="totalNotifications">0</h3>
                                    <p class="stat-label">总通知数</p>
                                </div>
                            </div>
                        </div>
                        <div class="stat-card warning">
                            <div class="stat-content">
                                <div class="stat-icon">
                                    <i class="fas fa-exclamation-circle"></i>
                                </div>
                                <div class="stat-info">
                                    <h3 class="stat-number" id="unreadNotifications">0</h3>
                                    <p class="stat-label">未读通知</p>
                                </div>
                            </div>
                        </div>
                        <div class="stat-card success">
                            <div class="stat-content">
                                <div class="stat-icon">
                                    <i class="fas fa-check-circle"></i>
                                </div>
                                <div class="stat-info">
                                    <h3 class="stat-number" id="readNotifications">0</h3>
                                    <p class="stat-label">已读通知</p>
                                </div>
                            </div>
                        </div>
                        <div class="stat-card info">
                            <div class="stat-content">
                                <div class="stat-icon">
                                    <i class="fas fa-calendar-day"></i>
                                </div>
                                <div class="stat-info">
                                    <h3 class="stat-number" id="todayNotifications">0</h3>
                                    <p class="stat-label">今日通知</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 通知筛选 -->
                    <div class="dashboard-card">
                        <div class="card-header">
                            <h3>通知列表</h3>
                            <div class="notification-filters">
                                <select id="notificationTypeFilter" onchange="filterNotifications()">
                                    <option value="">所有类型</option>
                                    <option value="system">系统通知</option>
                                    <option value="order">订单通知</option>
                                    <option value="user">用户通知</option>
                                    <option value="security">安全通知</option>
                                    <option value="reminder">提醒通知</option>
                                </select>
                                <select id="notificationStatusFilter" onchange="filterNotifications()">
                                    <option value="">所有状态</option>
                                    <option value="unread">未读</option>
                                    <option value="read">已读</option>
                                </select>
                                <select id="notificationTimeFilter" onchange="filterNotifications()">
                                    <option value="">所有时间</option>
                                    <option value="today">今天</option>
                                    <option value="week">本周</option>
                                    <option value="month">本月</option>
                                </select>
                            </div>
                        </div>
                        <div class="card-content">
                            <div class="notifications-container" id="notificationsContainer">
                                <!-- 通知列表将通过JavaScript生成 -->
                            </div>

                            <!-- 分页 -->
                            <div class="pagination" id="notificationsPagination">
                                <!-- 分页控件将通过JavaScript生成 -->
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 库存管理页面 -->
                <div class="page inventory-page" id="inventory">
                    <div class="page-header">
                        <h1>库存管理</h1>
                        <p>管理商品库存和仓储信息</p>
                        <div class="page-actions">
                            <button class="btn-secondary" onclick="exportInventory()">
                                <i class="fas fa-download"></i>
                                导出库存
                            </button>
                            <button class="btn-primary" onclick="showAddProductModal()">
                                <i class="fas fa-plus"></i>
                                添加商品
                            </button>
                        </div>
                    </div>

                    <!-- 库存统计 -->
                    <div class="stats-grid">
                        <div class="stat-card primary">
                            <div class="stat-content">
                                <div class="stat-icon">
                                    <i class="fas fa-boxes"></i>
                                </div>
                                <div class="stat-info">
                                    <h3 class="stat-number" id="totalProducts">0</h3>
                                    <p class="stat-label">商品总数</p>
                                    <div class="stat-trend positive">
                                        <i class="fas fa-arrow-up"></i>
                                        <span>+5.2%</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="stat-card warning">
                            <div class="stat-content">
                                <div class="stat-icon">
                                    <i class="fas fa-exclamation-triangle"></i>
                                </div>
                                <div class="stat-info">
                                    <h3 class="stat-number" id="lowStockProducts">0</h3>
                                    <p class="stat-label">库存不足</p>
                                    <div class="stat-trend negative">
                                        <i class="fas fa-arrow-down"></i>
                                        <span>需补货</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="stat-card success">
                            <div class="stat-content">
                                <div class="stat-icon">
                                    <i class="fas fa-dollar-sign"></i>
                                </div>
                                <div class="stat-info">
                                    <h3 class="stat-number" id="totalInventoryValue">¥0</h3>
                                    <p class="stat-label">库存总值</p>
                                    <div class="stat-trend positive">
                                        <i class="fas fa-arrow-up"></i>
                                        <span>+12.8%</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="stat-card info">
                            <div class="stat-content">
                                <div class="stat-icon">
                                    <i class="fas fa-warehouse"></i>
                                </div>
                                <div class="stat-info">
                                    <h3 class="stat-number" id="warehouseCount">3</h3>
                                    <p class="stat-label">仓库数量</p>
                                    <div class="stat-trend neutral">
                                        <i class="fas fa-minus"></i>
                                        <span>稳定</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 库存管理主要内容 -->
                    <div class="dashboard-grid">
                        <!-- 商品列表 -->
                        <div class="dashboard-card table-card">
                            <div class="card-header">
                                <h3>商品库存</h3>
                                <div class="card-actions">
                                    <div class="search-filter-group">
                                        <div class="search-box">
                                            <i class="fas fa-search"></i>
                                            <input type="text" id="productSearch" placeholder="搜索商品..." onkeyup="filterProducts()">
                                        </div>
                                        <select id="categoryFilter" onchange="filterProducts()">
                                            <option value="">所有分类</option>
                                            <option value="electronics">电子产品</option>
                                            <option value="clothing">服装</option>
                                            <option value="books">图书</option>
                                            <option value="home">家居用品</option>
                                        </select>
                                        <select id="stockFilter" onchange="filterProducts()">
                                            <option value="">所有库存</option>
                                            <option value="in-stock">有库存</option>
                                            <option value="low-stock">库存不足</option>
                                            <option value="out-of-stock">缺货</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <div class="card-content">
                                <div class="table-container">
                                    <table class="modern-table">
                                        <thead>
                                            <tr>
                                                <th>
                                                    <input type="checkbox" id="selectAllProducts" onchange="toggleSelectAllProducts()">
                                                </th>
                                                <th>商品信息</th>
                                                <th>分类</th>
                                                <th>库存数量</th>
                                                <th>单价</th>
                                                <th>库存价值</th>
                                                <th>状态</th>
                                                <th>操作</th>
                                            </tr>
                                        </thead>
                                        <tbody id="productsTableBody">
                                            <!-- 商品数据将通过JavaScript生成 -->
                                        </tbody>
                                    </table>
                                </div>

                                <!-- 分页 -->
                                <div class="pagination" id="productsPagination">
                                    <!-- 分页控件将通过JavaScript生成 -->
                                </div>
                            </div>
                        </div>

                        <!-- 库存操作 -->
                        <div class="dashboard-card">
                            <div class="card-header">
                                <h3>快速操作</h3>
                            </div>
                            <div class="card-content">
                                <div class="inventory-actions">
                                    <button class="inventory-action-btn" onclick="showStockInModal()">
                                        <i class="fas fa-plus-circle"></i>
                                        <span>入库</span>
                                    </button>
                                    <button class="inventory-action-btn" onclick="showStockOutModal()">
                                        <i class="fas fa-minus-circle"></i>
                                        <span>出库</span>
                                    </button>
                                    <button class="inventory-action-btn" onclick="showStockTransferModal()">
                                        <i class="fas fa-exchange-alt"></i>
                                        <span>调拨</span>
                                    </button>
                                    <button class="inventory-action-btn" onclick="showStockCheckModal()">
                                        <i class="fas fa-clipboard-check"></i>
                                        <span>盘点</span>
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- 库存预警 -->
                        <div class="dashboard-card">
                            <div class="card-header">
                                <h3>库存预警</h3>
                                <span class="warning-badge" id="warningCount">0</span>
                            </div>
                            <div class="card-content">
                                <div class="warning-list" id="warningList">
                                    <!-- 预警信息将通过JavaScript生成 -->
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 报表中心页面 -->
                <div class="page reports-page" id="reports">
                    <div class="page-header">
                        <h1>报表中心</h1>
                        <p>生成和管理各类业务报表</p>
                        <div class="page-actions">
                            <button class="btn-secondary" onclick="scheduleReport()">
                                <i class="fas fa-clock"></i>
                                定时报表
                            </button>
                            <button class="btn-primary" onclick="createCustomReport()">
                                <i class="fas fa-plus"></i>
                                自定义报表
                            </button>
                        </div>
                    </div>

                    <!-- 报表统计 -->
                    <div class="stats-grid">
                        <div class="stat-card primary">
                            <div class="stat-content">
                                <div class="stat-icon">
                                    <i class="fas fa-file-alt"></i>
                                </div>
                                <div class="stat-info">
                                    <h3 class="stat-number" id="totalReports">12</h3>
                                    <p class="stat-label">报表总数</p>
                                    <div class="stat-trend positive">
                                        <i class="fas fa-arrow-up"></i>
                                        <span>+3</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="stat-card success">
                            <div class="stat-content">
                                <div class="stat-icon">
                                    <i class="fas fa-download"></i>
                                </div>
                                <div class="stat-info">
                                    <h3 class="stat-number" id="monthlyDownloads">156</h3>
                                    <p class="stat-label">本月下载</p>
                                    <div class="stat-trend positive">
                                        <i class="fas fa-arrow-up"></i>
                                        <span>+23%</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="stat-card warning">
                            <div class="stat-content">
                                <div class="stat-icon">
                                    <i class="fas fa-clock"></i>
                                </div>
                                <div class="stat-info">
                                    <h3 class="stat-number" id="scheduledReports">5</h3>
                                    <p class="stat-label">定时报表</p>
                                    <div class="stat-trend neutral">
                                        <i class="fas fa-minus"></i>
                                        <span>稳定</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="stat-card info">
                            <div class="stat-content">
                                <div class="stat-icon">
                                    <i class="fas fa-users"></i>
                                </div>
                                <div class="stat-info">
                                    <h3 class="stat-number" id="reportUsers">8</h3>
                                    <p class="stat-label">使用用户</p>
                                    <div class="stat-trend positive">
                                        <i class="fas fa-arrow-up"></i>
                                        <span>+2</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 报表内容 -->
                    <div class="dashboard-grid">
                        <!-- 快速报表 -->
                        <div class="dashboard-card">
                            <div class="card-header">
                                <h3>快速报表</h3>
                                <p>一键生成常用报表</p>
                            </div>
                            <div class="card-content">
                                <div class="quick-reports">
                                    <div class="quick-report-item" onclick="generateQuickReport('sales')">
                                        <div class="report-icon sales">
                                            <i class="fas fa-chart-line"></i>
                                        </div>
                                        <div class="report-info">
                                            <h4>销售报表</h4>
                                            <p>销售数据统计分析</p>
                                        </div>
                                        <button class="btn-sm btn-primary">生成</button>
                                    </div>
                                    <div class="quick-report-item" onclick="generateQuickReport('inventory')">
                                        <div class="report-icon inventory">
                                            <i class="fas fa-boxes"></i>
                                        </div>
                                        <div class="report-info">
                                            <h4>库存报表</h4>
                                            <p>库存状态和流水</p>
                                        </div>
                                        <button class="btn-sm btn-primary">生成</button>
                                    </div>
                                    <div class="quick-report-item" onclick="generateQuickReport('users')">
                                        <div class="report-icon users">
                                            <i class="fas fa-users"></i>
                                        </div>
                                        <div class="report-info">
                                            <h4>用户报表</h4>
                                            <p>用户活跃度分析</p>
                                        </div>
                                        <button class="btn-sm btn-primary">生成</button>
                                    </div>
                                    <div class="quick-report-item" onclick="generateQuickReport('financial')">
                                        <div class="report-icon financial">
                                            <i class="fas fa-dollar-sign"></i>
                                        </div>
                                        <div class="report-info">
                                            <h4>财务报表</h4>
                                            <p>收支和利润分析</p>
                                        </div>
                                        <button class="btn-sm btn-primary">生成</button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 报表列表 -->
                        <div class="dashboard-card table-card">
                            <div class="card-header">
                                <h3>我的报表</h3>
                                <div class="card-actions">
                                    <select id="reportTypeFilter" onchange="filterReports()">
                                        <option value="">所有类型</option>
                                        <option value="sales">销售报表</option>
                                        <option value="inventory">库存报表</option>
                                        <option value="users">用户报表</option>
                                        <option value="financial">财务报表</option>
                                        <option value="custom">自定义报表</option>
                                    </select>
                                </div>
                            </div>
                            <div class="card-content">
                                <div class="reports-list" id="reportsList">
                                    <!-- 报表列表将通过JavaScript生成 -->
                                </div>
                            </div>
                        </div>

                        <!-- 报表生成器 -->
                        <div class="dashboard-card">
                            <div class="card-header">
                                <h3>报表生成器</h3>
                                <p>自定义数据源和图表类型</p>
                            </div>
                            <div class="card-content">
                                <div class="report-builder">
                                    <div class="form-group">
                                        <label for="reportDataSource">数据源</label>
                                        <select id="reportDataSource">
                                            <option value="orders">订单数据</option>
                                            <option value="users">用户数据</option>
                                            <option value="products">商品数据</option>
                                            <option value="inventory">库存数据</option>
                                        </select>
                                    </div>
                                    <div class="form-group">
                                        <label for="reportChartType">图表类型</label>
                                        <select id="reportChartType">
                                            <option value="line">折线图</option>
                                            <option value="bar">柱状图</option>
                                            <option value="pie">饼图</option>
                                            <option value="table">表格</option>
                                        </select>
                                    </div>
                                    <div class="form-group">
                                        <label for="reportDateRange">时间范围</label>
                                        <select id="reportDateRange">
                                            <option value="7d">最近7天</option>
                                            <option value="30d">最近30天</option>
                                            <option value="90d">最近90天</option>
                                            <option value="1y">最近1年</option>
                                            <option value="custom">自定义</option>
                                        </select>
                                    </div>
                                    <button class="btn-primary" onclick="buildCustomReport()">
                                        <i class="fas fa-magic"></i>
                                        生成报表
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 报表预览区域 -->
                    <div class="dashboard-card" id="reportPreviewCard" style="display: none;">
                        <div class="card-header">
                            <h3>报表预览</h3>
                            <div class="card-actions">
                                <button class="btn-secondary" onclick="exportReport('pdf')">
                                    <i class="fas fa-file-pdf"></i>
                                    导出PDF
                                </button>
                                <button class="btn-secondary" onclick="exportReport('excel')">
                                    <i class="fas fa-file-excel"></i>
                                    导出Excel
                                </button>
                                <button class="btn-primary" onclick="saveReport()">
                                    <i class="fas fa-save"></i>
                                    保存报表
                                </button>
                            </div>
                        </div>
                        <div class="card-content">
                            <div class="report-preview" id="reportPreview">
                                <!-- 报表预览内容将通过JavaScript生成 -->
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 文件管理页面 -->
                <div class="page files-page" id="files">
                    <div class="page-header">
                        <h1>文件管理</h1>
                        <p>管理和组织您的文件资源</p>
                        <div class="page-actions">
                            <button class="btn-secondary" onclick="createFolder()">
                                <i class="fas fa-folder-plus"></i>
                                新建文件夹
                            </button>
                            <button class="btn-primary" onclick="showUploadModal()">
                                <i class="fas fa-upload"></i>
                                上传文件
                            </button>
                        </div>
                    </div>

                    <!-- 文件统计 -->
                    <div class="stats-grid">
                        <div class="stat-card primary">
                            <div class="stat-content">
                                <div class="stat-icon">
                                    <i class="fas fa-file"></i>
                                </div>
                                <div class="stat-info">
                                    <h3 class="stat-number" id="totalFiles">0</h3>
                                    <p class="stat-label">文件总数</p>
                                    <div class="stat-trend positive">
                                        <i class="fas fa-arrow-up"></i>
                                        <span>+12</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="stat-card success">
                            <div class="stat-content">
                                <div class="stat-icon">
                                    <i class="fas fa-hdd"></i>
                                </div>
                                <div class="stat-info">
                                    <h3 class="stat-number" id="totalSize">0 MB</h3>
                                    <p class="stat-label">总大小</p>
                                    <div class="stat-trend positive">
                                        <i class="fas fa-arrow-up"></i>
                                        <span>+2.3MB</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="stat-card warning">
                            <div class="stat-content">
                                <div class="stat-icon">
                                    <i class="fas fa-folder"></i>
                                </div>
                                <div class="stat-info">
                                    <h3 class="stat-number" id="totalFolders">0</h3>
                                    <p class="stat-label">文件夹数</p>
                                    <div class="stat-trend neutral">
                                        <i class="fas fa-minus"></i>
                                        <span>稳定</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="stat-card info">
                            <div class="stat-content">
                                <div class="stat-icon">
                                    <i class="fas fa-share-alt"></i>
                                </div>
                                <div class="stat-info">
                                    <h3 class="stat-number" id="sharedFiles">0</h3>
                                    <p class="stat-label">共享文件</p>
                                    <div class="stat-trend positive">
                                        <i class="fas fa-arrow-up"></i>
                                        <span>+3</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 文件管理主要内容 -->
                    <div class="dashboard-grid">
                        <!-- 文件浏览器 -->
                        <div class="dashboard-card file-browser-card">
                            <div class="card-header">
                                <div class="file-navigation">
                                    <div class="breadcrumb-nav">
                                        <span class="breadcrumb-item" onclick="navigateToFolder('')">
                                            <i class="fas fa-home"></i>
                                            根目录
                                        </span>
                                        <span id="currentPath"></span>
                                    </div>
                                    <div class="file-view-controls">
                                        <button class="view-btn active" data-view="grid" onclick="changeFileView('grid')" title="网格视图">
                                            <i class="fas fa-th"></i>
                                        </button>
                                        <button class="view-btn" data-view="list" onclick="changeFileView('list')" title="列表视图">
                                            <i class="fas fa-list"></i>
                                        </button>
                                    </div>
                                </div>
                                <div class="file-toolbar">
                                    <div class="search-box">
                                        <i class="fas fa-search"></i>
                                        <input type="text" id="fileSearch" placeholder="搜索文件..." onkeyup="searchFiles()">
                                    </div>
                                    <select id="fileTypeFilter" onchange="filterFiles()">
                                        <option value="">所有类型</option>
                                        <option value="image">图片</option>
                                        <option value="document">文档</option>
                                        <option value="video">视频</option>
                                        <option value="audio">音频</option>
                                        <option value="archive">压缩包</option>
                                    </select>
                                    <select id="fileSortBy" onchange="sortFiles()">
                                        <option value="name">按名称</option>
                                        <option value="date">按日期</option>
                                        <option value="size">按大小</option>
                                        <option value="type">按类型</option>
                                    </select>
                                </div>
                            </div>
                            <div class="card-content">
                                <div class="file-container" id="fileContainer">
                                    <!-- 文件列表将通过JavaScript生成 -->
                                </div>

                                <!-- 文件分页 -->
                                <div class="pagination" id="filesPagination">
                                    <!-- 分页控件将通过JavaScript生成 -->
                                </div>
                            </div>
                        </div>

                        <!-- 文件操作面板 -->
                        <div class="dashboard-card">
                            <div class="card-header">
                                <h3>快速操作</h3>
                            </div>
                            <div class="card-content">
                                <div class="file-actions">
                                    <button class="file-action-btn" onclick="showUploadModal()">
                                        <i class="fas fa-upload"></i>
                                        <span>上传文件</span>
                                    </button>
                                    <button class="file-action-btn" onclick="createFolder()">
                                        <i class="fas fa-folder-plus"></i>
                                        <span>新建文件夹</span>
                                    </button>
                                    <button class="file-action-btn" onclick="showShareModal()">
                                        <i class="fas fa-share-alt"></i>
                                        <span>分享文件</span>
                                    </button>
                                    <button class="file-action-btn" onclick="showMoveModal()">
                                        <i class="fas fa-cut"></i>
                                        <span>移动文件</span>
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- 最近文件 -->
                        <div class="dashboard-card">
                            <div class="card-header">
                                <h3>最近文件</h3>
                                <button class="btn-sm btn-secondary" onclick="clearRecentFiles()">清空</button>
                            </div>
                            <div class="card-content">
                                <div class="recent-files" id="recentFilesList">
                                    <!-- 最近文件列表将通过JavaScript生成 -->
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 权限管理页面 -->
                <div class="page permissions-page" id="permissions">
                    <div class="page-header">
                        <h1>权限管理</h1>
                        <p>管理用户角色和权限设置</p>
                        <div class="page-actions">
                            <button class="btn-secondary" onclick="exportPermissions()">
                                <i class="fas fa-download"></i>
                                导出权限
                            </button>
                            <button class="btn-primary" onclick="showCreateRoleModal()">
                                <i class="fas fa-plus"></i>
                                创建角色
                            </button>
                        </div>
                    </div>

                    <!-- 权限统计 -->
                    <div class="stats-grid">
                        <div class="stat-card primary">
                            <div class="stat-content">
                                <div class="stat-icon">
                                    <i class="fas fa-users-cog"></i>
                                </div>
                                <div class="stat-info">
                                    <h3 class="stat-number" id="totalRoles">0</h3>
                                    <p class="stat-label">角色总数</p>
                                    <div class="stat-trend positive">
                                        <i class="fas fa-arrow-up"></i>
                                        <span>+2</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="stat-card success">
                            <div class="stat-content">
                                <div class="stat-icon">
                                    <i class="fas fa-key"></i>
                                </div>
                                <div class="stat-info">
                                    <h3 class="stat-number" id="totalPermissions">0</h3>
                                    <p class="stat-label">权限总数</p>
                                    <div class="stat-trend neutral">
                                        <i class="fas fa-minus"></i>
                                        <span>稳定</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="stat-card warning">
                            <div class="stat-content">
                                <div class="stat-icon">
                                    <i class="fas fa-user-shield"></i>
                                </div>
                                <div class="stat-info">
                                    <h3 class="stat-number" id="activeUsers">0</h3>
                                    <p class="stat-label">活跃用户</p>
                                    <div class="stat-trend positive">
                                        <i class="fas fa-arrow-up"></i>
                                        <span>+5</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="stat-card info">
                            <div class="stat-content">
                                <div class="stat-icon">
                                    <i class="fas fa-exclamation-triangle"></i>
                                </div>
                                <div class="stat-info">
                                    <h3 class="stat-number" id="securityAlerts">0</h3>
                                    <p class="stat-label">安全警告</p>
                                    <div class="stat-trend negative">
                                        <i class="fas fa-arrow-down"></i>
                                        <span>-3</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 权限管理主要内容 -->
                    <div class="dashboard-grid">
                        <!-- 角色管理 -->
                        <div class="dashboard-card">
                            <div class="card-header">
                                <h3>角色管理</h3>
                                <div class="card-actions">
                                    <select id="roleFilter" onchange="filterRoles()">
                                        <option value="">所有角色</option>
                                        <option value="system">系统角色</option>
                                        <option value="custom">自定义角色</option>
                                    </select>
                                </div>
                            </div>
                            <div class="card-content">
                                <div class="roles-list" id="rolesList">
                                    <!-- 角色列表将通过JavaScript生成 -->
                                </div>
                            </div>
                        </div>

                        <!-- 权限矩阵 -->
                        <div class="dashboard-card permissions-matrix-card">
                            <div class="card-header">
                                <h3>权限矩阵</h3>
                                <div class="card-actions">
                                    <select id="selectedRole" onchange="updatePermissionMatrix()">
                                        <option value="">选择角色</option>
                                        <!-- 角色选项将通过JavaScript生成 -->
                                    </select>
                                </div>
                            </div>
                            <div class="card-content">
                                <div class="permissions-matrix" id="permissionsMatrix">
                                    <!-- 权限矩阵将通过JavaScript生成 -->
                                </div>
                            </div>
                        </div>

                        <!-- 用户权限分配 -->
                        <div class="dashboard-card">
                            <div class="card-header">
                                <h3>用户权限分配</h3>
                                <button class="btn-sm btn-primary" onclick="showAssignPermissionModal()">分配权限</button>
                            </div>
                            <div class="card-content">
                                <div class="user-permissions" id="userPermissions">
                                    <!-- 用户权限列表将通过JavaScript生成 -->
                                </div>
                            </div>
                        </div>

                        <!-- 权限日志 -->
                        <div class="dashboard-card">
                            <div class="card-header">
                                <h3>权限日志</h3>
                                <div class="card-actions">
                                    <select id="logFilter" onchange="filterPermissionLogs()">
                                        <option value="">所有操作</option>
                                        <option value="grant">授权</option>
                                        <option value="revoke">撤销</option>
                                        <option value="modify">修改</option>
                                    </select>
                                </div>
                            </div>
                            <div class="card-content">
                                <div class="permission-logs" id="permissionLogs">
                                    <!-- 权限日志将通过JavaScript生成 -->
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="page help-page" id="help">
                    <div class="page-header">
                        <h1>帮助中心</h1>
                        <p>获取帮助和支持</p>
                        <div class="page-actions">
                            <div class="search-box">
                                <i class="fas fa-search"></i>
                                <input type="text" id="helpSearch" placeholder="搜索帮助内容..." onkeyup="searchHelp()">
                            </div>
                        </div>
                    </div>

                    <!-- 快速导航 -->
                    <div class="help-navigation">
                        <div class="help-nav-item active" data-section="getting-started">
                            <i class="fas fa-play-circle"></i>
                            <span>快速开始</span>
                        </div>
                        <div class="help-nav-item" data-section="user-guide">
                            <i class="fas fa-book"></i>
                            <span>用户指南</span>
                        </div>
                        <div class="help-nav-item" data-section="faq">
                            <i class="fas fa-question-circle"></i>
                            <span>常见问题</span>
                        </div>
                        <div class="help-nav-item" data-section="api-docs">
                            <i class="fas fa-code"></i>
                            <span>API文档</span>
                        </div>
                        <div class="help-nav-item" data-section="contact">
                            <i class="fas fa-envelope"></i>
                            <span>联系支持</span>
                        </div>
                    </div>

                    <!-- 帮助内容区域 -->
                    <div class="help-content">
                        <!-- 快速开始 -->
                        <div class="help-section active" id="getting-started">
                            <div class="dashboard-card">
                                <div class="card-header">
                                    <h3>快速开始</h3>
                                    <p>欢迎使用现代企业管理系统，让我们帮助您快速上手</p>
                                </div>
                                <div class="card-content">
                                    <div class="getting-started-grid">
                                        <div class="getting-started-item">
                                            <div class="step-number">1</div>
                                            <div class="step-content">
                                                <h4>登录系统</h4>
                                                <p>使用您的用户名和密码登录系统，如果忘记密码可以联系管理员重置。</p>
                                                <a href="#" class="help-link">查看登录指南 <i class="fas fa-arrow-right"></i></a>
                                            </div>
                                        </div>
                                        <div class="getting-started-item">
                                            <div class="step-number">2</div>
                                            <div class="step-content">
                                                <h4>熟悉界面</h4>
                                                <p>了解系统的主要功能模块，包括仪表盘、用户管理、数据分析等。</p>
                                                <a href="#" class="help-link">界面介绍 <i class="fas fa-arrow-right"></i></a>
                                            </div>
                                        </div>
                                        <div class="getting-started-item">
                                            <div class="step-number">3</div>
                                            <div class="step-content">
                                                <h4>开始使用</h4>
                                                <p>根据您的角色权限，开始使用相应的功能模块进行日常工作。</p>
                                                <a href="#" class="help-link">功能指南 <i class="fas fa-arrow-right"></i></a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 视频教程 -->
                            <div class="dashboard-card">
                                <div class="card-header">
                                    <h3>视频教程</h3>
                                    <p>通过视频快速学习系统使用方法</p>
                                </div>
                                <div class="card-content">
                                    <div class="video-tutorials">
                                        <div class="video-item">
                                            <div class="video-thumbnail">
                                                <i class="fas fa-play-circle"></i>
                                                <span class="video-duration">5:30</span>
                                            </div>
                                            <div class="video-info">
                                                <h4>系统概览介绍</h4>
                                                <p>了解系统的整体架构和主要功能模块</p>
                                            </div>
                                        </div>
                                        <div class="video-item">
                                            <div class="video-thumbnail">
                                                <i class="fas fa-play-circle"></i>
                                                <span class="video-duration">8:15</span>
                                            </div>
                                            <div class="video-info">
                                                <h4>用户管理操作</h4>
                                                <p>学习如何添加、编辑和管理用户账户</p>
                                            </div>
                                        </div>
                                        <div class="video-item">
                                            <div class="video-thumbnail">
                                                <i class="fas fa-play-circle"></i>
                                                <span class="video-duration">6:45</span>
                                            </div>
                                            <div class="video-info">
                                                <h4>数据分析功能</h4>
                                                <p>掌握数据分析和报表生成的使用方法</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 用户指南 -->
                        <div class="help-section" id="user-guide">
                            <div class="dashboard-card">
                                <div class="card-header">
                                    <h3>用户指南</h3>
                                    <p>详细的功能使用说明</p>
                                </div>
                                <div class="card-content">
                                    <div class="guide-categories">
                                        <div class="guide-category">
                                            <h4><i class="fas fa-chart-pie"></i> 仪表盘</h4>
                                            <ul class="guide-list">
                                                <li><a href="#">查看业务概览</a></li>
                                                <li><a href="#">理解统计数据</a></li>
                                                <li><a href="#">使用图表功能</a></li>
                                                <li><a href="#">自定义仪表盘</a></li>
                                            </ul>
                                        </div>
                                        <div class="guide-category">
                                            <h4><i class="fas fa-users"></i> 用户管理</h4>
                                            <ul class="guide-list">
                                                <li><a href="#">添加新用户</a></li>
                                                <li><a href="#">编辑用户信息</a></li>
                                                <li><a href="#">设置用户权限</a></li>
                                                <li><a href="#">批量操作用户</a></li>
                                            </ul>
                                        </div>
                                        <div class="guide-category">
                                            <h4><i class="fas fa-chart-line"></i> 数据分析</h4>
                                            <ul class="guide-list">
                                                <li><a href="#">生成分析报表</a></li>
                                                <li><a href="#">导出数据</a></li>
                                                <li><a href="#">设置时间范围</a></li>
                                                <li><a href="#">创建自定义图表</a></li>
                                            </ul>
                                        </div>
                                        <div class="guide-category">
                                            <h4><i class="fas fa-shopping-bag"></i> 订单管理</h4>
                                            <ul class="guide-list">
                                                <li><a href="#">创建新订单</a></li>
                                                <li><a href="#">处理订单状态</a></li>
                                                <li><a href="#">订单搜索筛选</a></li>
                                                <li><a href="#">订单数据导出</a></li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 常见问题 -->
                        <div class="help-section" id="faq">
                            <div class="dashboard-card">
                                <div class="card-header">
                                    <h3>常见问题</h3>
                                    <p>快速找到常见问题的解答</p>
                                </div>
                                <div class="card-content">
                                    <div class="faq-container">
                                        <div class="faq-item">
                                            <div class="faq-question" onclick="toggleFaq(this)">
                                                <h4>如何重置密码？</h4>
                                                <i class="fas fa-chevron-down"></i>
                                            </div>
                                            <div class="faq-answer">
                                                <p>您可以通过以下方式重置密码：</p>
                                                <ol>
                                                    <li>在登录页面点击"忘记密码"链接</li>
                                                    <li>输入您的邮箱地址</li>
                                                    <li>查收邮件并点击重置链接</li>
                                                    <li>设置新密码并确认</li>
                                                </ol>
                                                <p>如果仍有问题，请联系系统管理员。</p>
                                            </div>
                                        </div>
                                        <div class="faq-item">
                                            <div class="faq-question" onclick="toggleFaq(this)">
                                                <h4>如何导出数据？</h4>
                                                <i class="fas fa-chevron-down"></i>
                                            </div>
                                            <div class="faq-answer">
                                                <p>系统支持多种数据导出格式：</p>
                                                <ul>
                                                    <li><strong>Excel格式：</strong>适合数据分析和报表制作</li>
                                                    <li><strong>CSV格式：</strong>适合数据迁移和批量处理</li>
                                                    <li><strong>PDF格式：</strong>适合打印和存档</li>
                                                </ul>
                                                <p>在相应的数据页面点击"导出"按钮即可选择格式下载。</p>
                                            </div>
                                        </div>
                                        <div class="faq-item">
                                            <div class="faq-question" onclick="toggleFaq(this)">
                                                <h4>系统支持哪些浏览器？</h4>
                                                <i class="fas fa-chevron-down"></i>
                                            </div>
                                            <div class="faq-answer">
                                                <p>系统支持以下现代浏览器：</p>
                                                <ul>
                                                    <li>Chrome 80+</li>
                                                    <li>Firefox 75+</li>
                                                    <li>Safari 13+</li>
                                                    <li>Edge 80+</li>
                                                </ul>
                                                <p>建议使用最新版本的浏览器以获得最佳体验。</p>
                                            </div>
                                        </div>
                                        <div class="faq-item">
                                            <div class="faq-question" onclick="toggleFaq(this)">
                                                <h4>如何设置通知偏好？</h4>
                                                <i class="fas fa-chevron-down"></i>
                                            </div>
                                            <div class="faq-answer">
                                                <p>您可以在系统设置中自定义通知偏好：</p>
                                                <ol>
                                                    <li>进入"系统设置"页面</li>
                                                    <li>选择"通知设置"选项卡</li>
                                                    <li>根据需要开启或关闭不同类型的通知</li>
                                                    <li>设置通知频率和接收方式</li>
                                                    <li>保存设置</li>
                                                </ol>
                                            </div>
                                        </div>
                                        <div class="faq-item">
                                            <div class="faq-question" onclick="toggleFaq(this)">
                                                <h4>数据多久备份一次？</h4>
                                                <i class="fas fa-chevron-down"></i>
                                            </div>
                                            <div class="faq-answer">
                                                <p>系统采用多层备份策略确保数据安全：</p>
                                                <ul>
                                                    <li><strong>实时备份：</strong>关键操作实时同步</li>
                                                    <li><strong>每日备份：</strong>每天凌晨自动全量备份</li>
                                                    <li><strong>周备份：</strong>每周进行完整性检查</li>
                                                    <li><strong>月备份：</strong>长期存档备份</li>
                                                </ul>
                                                <p>所有备份数据都经过加密处理并存储在安全的云端。</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- API文档 -->
                        <div class="help-section" id="api-docs">
                            <div class="dashboard-card">
                                <div class="card-header">
                                    <h3>API文档</h3>
                                    <p>开发者接口文档和示例</p>
                                </div>
                                <div class="card-content">
                                    <div class="api-overview">
                                        <div class="api-info">
                                            <h4>API概览</h4>
                                            <p>我们的RESTful API提供了完整的数据访问和操作接口，支持JSON格式的数据交换。</p>
                                            <div class="api-stats">
                                                <div class="api-stat">
                                                    <span class="stat-number">50+</span>
                                                    <span class="stat-label">API端点</span>
                                                </div>
                                                <div class="api-stat">
                                                    <span class="stat-number">99.9%</span>
                                                    <span class="stat-label">可用性</span>
                                                </div>
                                                <div class="api-stat">
                                                    <span class="stat-number">< 100ms</span>
                                                    <span class="stat-label">响应时间</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="api-endpoints">
                                        <h4>主要端点</h4>
                                        <div class="endpoint-item">
                                            <div class="endpoint-method get">GET</div>
                                            <div class="endpoint-path">/api/users</div>
                                            <div class="endpoint-desc">获取用户列表</div>
                                        </div>
                                        <div class="endpoint-item">
                                            <div class="endpoint-method post">POST</div>
                                            <div class="endpoint-path">/api/users</div>
                                            <div class="endpoint-desc">创建新用户</div>
                                        </div>
                                        <div class="endpoint-item">
                                            <div class="endpoint-method get">GET</div>
                                            <div class="endpoint-path">/api/orders</div>
                                            <div class="endpoint-desc">获取订单列表</div>
                                        </div>
                                        <div class="endpoint-item">
                                            <div class="endpoint-method put">PUT</div>
                                            <div class="endpoint-path">/api/orders/{id}</div>
                                            <div class="endpoint-desc">更新订单状态</div>
                                        </div>
                                    </div>

                                    <div class="api-example">
                                        <h4>请求示例</h4>
                                        <div class="code-block">
                                            <pre><code>curl -X GET "https://api.example.com/api/users" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json"</code></pre>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 联系支持 -->
                        <div class="help-section" id="contact">
                            <div class="dashboard-card">
                                <div class="card-header">
                                    <h3>联系支持</h3>
                                    <p>需要帮助？我们随时为您提供支持</p>
                                </div>
                                <div class="card-content">
                                    <div class="contact-options">
                                        <div class="contact-option">
                                            <div class="contact-icon">
                                                <i class="fas fa-envelope"></i>
                                            </div>
                                            <div class="contact-info">
                                                <h4>邮件支持</h4>
                                                <p>发送邮件至 <EMAIL></p>
                                                <span class="response-time">24小时内回复</span>
                                            </div>
                                        </div>
                                        <div class="contact-option">
                                            <div class="contact-icon">
                                                <i class="fas fa-phone"></i>
                                            </div>
                                            <div class="contact-info">
                                                <h4>电话支持</h4>
                                                <p>拨打 ************</p>
                                                <span class="response-time">工作日 9:00-18:00</span>
                                            </div>
                                        </div>
                                        <div class="contact-option">
                                            <div class="contact-icon">
                                                <i class="fas fa-comments"></i>
                                            </div>
                                            <div class="contact-info">
                                                <h4>在线客服</h4>
                                                <p>点击右下角聊天图标</p>
                                                <span class="response-time">实时响应</span>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="support-form">
                                        <h4>提交支持请求</h4>
                                        <form id="supportForm">
                                            <div class="form-row">
                                                <div class="form-group">
                                                    <label for="supportSubject">主题 *</label>
                                                    <input type="text" id="supportSubject" required>
                                                </div>
                                                <div class="form-group">
                                                    <label for="supportPriority">优先级</label>
                                                    <select id="supportPriority">
                                                        <option value="low">低</option>
                                                        <option value="medium" selected>中</option>
                                                        <option value="high">高</option>
                                                        <option value="urgent">紧急</option>
                                                    </select>
                                                </div>
                                            </div>
                                            <div class="form-group">
                                                <label for="supportMessage">详细描述 *</label>
                                                <textarea id="supportMessage" rows="5" placeholder="请详细描述您遇到的问题..." required></textarea>
                                            </div>
                                            <button type="submit" class="btn-primary">
                                                <i class="fas fa-paper-plane"></i>
                                                提交请求
                                            </button>
                                        </form>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- 移动端遮罩层 -->
    <div class="mobile-overlay" id="mobileOverlay"></div>

    <!-- 添加/编辑用户模态框 -->
    <div class="modal-overlay" id="userModalOverlay">
        <div class="modal-container">
            <div class="modal-header">
                <h3 id="userModalTitle">添加用户</h3>
                <button class="modal-close" onclick="closeUserModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-content">
                <form id="userForm">
                    <div class="form-row">
                        <div class="form-group">
                            <label for="userName">姓名 *</label>
                            <input type="text" id="userName" name="name" required>
                        </div>
                        <div class="form-group">
                            <label for="userUsername">用户名 *</label>
                            <input type="text" id="userUsername" name="username" required>
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-group">
                            <label for="userEmail">邮箱 *</label>
                            <input type="email" id="userEmail" name="email" required>
                        </div>
                        <div class="form-group">
                            <label for="userPhone">电话</label>
                            <input type="tel" id="userPhone" name="phone">
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-group">
                            <label for="userRole">角色 *</label>
                            <select id="userRole" name="role" required>
                                <option value="">请选择角色</option>
                                <option value="系统管理员">系统管理员</option>
                                <option value="部门经理">部门经理</option>
                                <option value="普通用户">普通用户</option>
                                <option value="测试用户">测试用户</option>
                                <option value="演示用户">演示用户</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="userStatus">状态</label>
                            <select id="userStatus" name="status">
                                <option value="active">活跃</option>
                                <option value="inactive">非活跃</option>
                                <option value="suspended">已暂停</option>
                            </select>
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="userDepartment">部门</label>
                        <input type="text" id="userDepartment" name="department" placeholder="如：技术部、市场部等">
                    </div>
                    <div class="form-group" id="passwordGroup">
                        <label for="userPassword">密码 *</label>
                        <input type="password" id="userPassword" name="password" required>
                        <small>密码至少8位，包含字母和数字</small>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn-secondary" onclick="closeUserModal()">取消</button>
                <button type="button" class="btn-primary" onclick="saveUser()">
                    <i class="fas fa-save"></i>
                    保存
                </button>
            </div>
        </div>
    </div>

    <!-- 确认删除模态框 -->
    <div class="modal-overlay" id="deleteModalOverlay">
        <div class="modal-container small">
            <div class="modal-header">
                <h3>确认删除</h3>
                <button class="modal-close" onclick="closeDeleteModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-content">
                <div class="delete-warning">
                    <i class="fas fa-exclamation-triangle"></i>
                    <p>您确定要删除用户 <strong id="deleteUserName"></strong> 吗？</p>
                    <p class="warning-text">此操作不可撤销！</p>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn-secondary" onclick="closeDeleteModal()">取消</button>
                <button type="button" class="btn-danger" onclick="confirmDeleteUser()">
                    <i class="fas fa-trash"></i>
                    确认删除
                </button>
            </div>
        </div>
    </div>

    <!-- 订单详情模态框 -->
    <div class="modal-overlay" id="orderDetailModal">
        <div class="modal-container">
            <div class="modal-header">
                <h3>订单详情</h3>
                <button class="modal-close" onclick="closeOrderDetailModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-content">
                <div id="orderDetailContent">
                    <!-- 订单详情内容将通过JavaScript生成 -->
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn-secondary" onclick="closeOrderDetailModal()">关闭</button>
                <button type="button" class="btn-primary" onclick="printOrder()">
                    <i class="fas fa-print"></i>
                    打印订单
                </button>
            </div>
        </div>
    </div>

    <!-- 订单状态更新模态框 -->
    <div class="modal-overlay" id="orderStatusModal">
        <div class="modal-container small">
            <div class="modal-header">
                <h3>更新订单状态</h3>
                <button class="modal-close" onclick="closeOrderStatusModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-content">
                <div class="form-group">
                    <label for="newOrderStatus">新状态</label>
                    <select id="newOrderStatus">
                        <option value="pending">待付款</option>
                        <option value="processing">处理中</option>
                        <option value="shipped">已发货</option>
                        <option value="completed">已完成</option>
                        <option value="cancelled">已取消</option>
                        <option value="refunded">已退款</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="statusNote">备注</label>
                    <textarea id="statusNote" rows="3" placeholder="请输入状态更新备注..."></textarea>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn-secondary" onclick="closeOrderStatusModal()">取消</button>
                <button type="button" class="btn-primary" onclick="updateOrderStatus()">
                    <i class="fas fa-save"></i>
                    更新状态
                </button>
            </div>
        </div>
    </div>

    <!-- 添加/编辑商品模态框 -->
    <div class="modal-overlay" id="productModalOverlay">
        <div class="modal-container">
            <div class="modal-header">
                <h3 id="productModalTitle">添加商品</h3>
                <button class="modal-close" onclick="closeProductModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-content">
                <form id="productForm">
                    <div class="form-row">
                        <div class="form-group">
                            <label for="productName">商品名称 *</label>
                            <input type="text" id="productName" name="name" required>
                        </div>
                        <div class="form-group">
                            <label for="productSku">商品编码 *</label>
                            <input type="text" id="productSku" name="sku" required>
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-group">
                            <label for="productCategory">分类 *</label>
                            <select id="productCategory" name="category" required>
                                <option value="">请选择分类</option>
                                <option value="electronics">电子产品</option>
                                <option value="clothing">服装</option>
                                <option value="books">图书</option>
                                <option value="home">家居用品</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="productPrice">单价 *</label>
                            <input type="number" id="productPrice" name="price" step="0.01" min="0" required>
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-group">
                            <label for="productStock">初始库存</label>
                            <input type="number" id="productStock" name="stock" min="0" value="0">
                        </div>
                        <div class="form-group">
                            <label for="productMinStock">最低库存</label>
                            <input type="number" id="productMinStock" name="minStock" min="0" value="10">
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="productDescription">商品描述</label>
                        <textarea id="productDescription" name="description" rows="3"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn-secondary" onclick="closeProductModal()">取消</button>
                <button type="button" class="btn-primary" onclick="saveProduct()">
                    <i class="fas fa-save"></i>
                    保存
                </button>
            </div>
        </div>
    </div>

    <!-- 入库模态框 -->
    <div class="modal-overlay" id="stockInModalOverlay">
        <div class="modal-container">
            <div class="modal-header">
                <h3>商品入库</h3>
                <button class="modal-close" onclick="closeStockInModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-content">
                <form id="stockInForm">
                    <div class="form-group">
                        <label for="stockInProduct">选择商品 *</label>
                        <select id="stockInProduct" name="productId" required>
                            <option value="">请选择商品</option>
                            <!-- 商品选项将通过JavaScript生成 -->
                        </select>
                    </div>
                    <div class="form-row">
                        <div class="form-group">
                            <label for="stockInQuantity">入库数量 *</label>
                            <input type="number" id="stockInQuantity" name="quantity" min="1" required>
                        </div>
                        <div class="form-group">
                            <label for="stockInPrice">入库单价</label>
                            <input type="number" id="stockInPrice" name="price" step="0.01" min="0">
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="stockInReason">入库原因</label>
                        <select id="stockInReason" name="reason">
                            <option value="purchase">采购入库</option>
                            <option value="return">退货入库</option>
                            <option value="adjustment">库存调整</option>
                            <option value="other">其他</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="stockInNote">备注</label>
                        <textarea id="stockInNote" name="note" rows="3"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn-secondary" onclick="closeStockInModal()">取消</button>
                <button type="button" class="btn-primary" onclick="processStockIn()">
                    <i class="fas fa-plus-circle"></i>
                    确认入库
                </button>
            </div>
        </div>
    </div>

    <!-- 出库模态框 -->
    <div class="modal-overlay" id="stockOutModalOverlay">
        <div class="modal-container">
            <div class="modal-header">
                <h3>商品出库</h3>
                <button class="modal-close" onclick="closeStockOutModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-content">
                <form id="stockOutForm">
                    <div class="form-group">
                        <label for="stockOutProduct">选择商品 *</label>
                        <select id="stockOutProduct" name="productId" required>
                            <option value="">请选择商品</option>
                            <!-- 商品选项将通过JavaScript生成 -->
                        </select>
                    </div>
                    <div class="form-row">
                        <div class="form-group">
                            <label for="stockOutQuantity">出库数量 *</label>
                            <input type="number" id="stockOutQuantity" name="quantity" min="1" required>
                        </div>
                        <div class="form-group">
                            <label for="currentStock">当前库存</label>
                            <input type="number" id="currentStock" readonly>
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="stockOutReason">出库原因</label>
                        <select id="stockOutReason" name="reason">
                            <option value="sale">销售出库</option>
                            <option value="damage">损坏出库</option>
                            <option value="transfer">调拨出库</option>
                            <option value="other">其他</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="stockOutNote">备注</label>
                        <textarea id="stockOutNote" name="note" rows="3"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn-secondary" onclick="closeStockOutModal()">取消</button>
                <button type="button" class="btn-primary" onclick="processStockOut()">
                    <i class="fas fa-minus-circle"></i>
                    确认出库
                </button>
            </div>
        </div>
    </div>

    <!-- 文件上传模态框 -->
    <div class="modal-overlay" id="uploadModalOverlay">
        <div class="modal-container large">
            <div class="modal-header">
                <h3>上传文件</h3>
                <button class="modal-close" onclick="closeUploadModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-content">
                <div class="upload-area" id="uploadArea">
                    <div class="upload-zone" ondrop="handleFileDrop(event)" ondragover="handleDragOver(event)" ondragleave="handleDragLeave(event)">
                        <i class="fas fa-cloud-upload-alt"></i>
                        <h4>拖拽文件到此处或点击选择</h4>
                        <p>支持多文件上传，单个文件最大 100MB</p>
                        <input type="file" id="fileInput" multiple onchange="handleFileSelect(event)" style="display: none;">
                        <button class="btn-primary" onclick="document.getElementById('fileInput').click()">
                            选择文件
                        </button>
                    </div>
                </div>
                <div class="upload-list" id="uploadList" style="display: none;">
                    <!-- 上传文件列表 -->
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn-secondary" onclick="closeUploadModal()">取消</button>
                <button type="button" class="btn-primary" id="startUploadBtn" onclick="startUpload()" style="display: none;">
                    <i class="fas fa-upload"></i>
                    开始上传
                </button>
            </div>
        </div>
    </div>

    <!-- 文件预览模态框 -->
    <div class="modal-overlay" id="previewModalOverlay">
        <div class="modal-container extra-large">
            <div class="modal-header">
                <h3 id="previewFileName">文件预览</h3>
                <div class="preview-actions">
                    <button class="btn-icon" onclick="downloadPreviewFile()" title="下载">
                        <i class="fas fa-download"></i>
                    </button>
                    <button class="btn-icon" onclick="sharePreviewFile()" title="分享">
                        <i class="fas fa-share-alt"></i>
                    </button>
                    <button class="modal-close" onclick="closePreviewModal()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            </div>
            <div class="modal-content">
                <div class="file-preview" id="filePreview">
                    <!-- 文件预览内容 -->
                </div>
            </div>
        </div>
    </div>

    <!-- 文件分享模态框 -->
    <div class="modal-overlay" id="shareModalOverlay">
        <div class="modal-container">
            <div class="modal-header">
                <h3>分享文件</h3>
                <button class="modal-close" onclick="closeShareModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-content">
                <form id="shareForm">
                    <div class="form-group">
                        <label for="shareUsers">分享给用户</label>
                        <select id="shareUsers" multiple>
                            <option value="user1">张三</option>
                            <option value="user2">李四</option>
                            <option value="user3">王五</option>
                            <option value="user4">赵六</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="sharePermission">权限设置</label>
                        <select id="sharePermission">
                            <option value="view">仅查看</option>
                            <option value="download">查看和下载</option>
                            <option value="edit">编辑权限</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="shareExpiry">有效期</label>
                        <select id="shareExpiry">
                            <option value="1d">1天</option>
                            <option value="7d">7天</option>
                            <option value="30d">30天</option>
                            <option value="never">永久</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="shareMessage">分享消息</label>
                        <textarea id="shareMessage" rows="3" placeholder="可选的分享消息..."></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn-secondary" onclick="closeShareModal()">取消</button>
                <button type="button" class="btn-primary" onclick="processShare()">
                    <i class="fas fa-share-alt"></i>
                    分享
                </button>
            </div>
        </div>
    </div>

    <!-- 创建角色模态框 -->
    <div class="modal-overlay" id="createRoleModalOverlay">
        <div class="modal-container">
            <div class="modal-header">
                <h3 id="roleModalTitle">创建角色</h3>
                <button class="modal-close" onclick="closeCreateRoleModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-content">
                <form id="roleForm">
                    <div class="form-group">
                        <label for="roleName">角色名称 *</label>
                        <input type="text" id="roleName" name="name" required>
                    </div>
                    <div class="form-group">
                        <label for="roleDescription">角色描述</label>
                        <textarea id="roleDescription" name="description" rows="3"></textarea>
                    </div>
                    <div class="form-group">
                        <label for="roleType">角色类型</label>
                        <select id="roleType" name="type">
                            <option value="custom">自定义角色</option>
                            <option value="system">系统角色</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>基础权限</label>
                        <div class="permission-checkboxes" id="basicPermissions">
                            <!-- 基础权限复选框将通过JavaScript生成 -->
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn-secondary" onclick="closeCreateRoleModal()">取消</button>
                <button type="button" class="btn-primary" onclick="saveRole()">
                    <i class="fas fa-save"></i>
                    保存
                </button>
            </div>
        </div>
    </div>

    <!-- 分配权限模态框 -->
    <div class="modal-overlay" id="assignPermissionModalOverlay">
        <div class="modal-container">
            <div class="modal-header">
                <h3>分配用户权限</h3>
                <button class="modal-close" onclick="closeAssignPermissionModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-content">
                <form id="assignPermissionForm">
                    <div class="form-group">
                        <label for="assignUser">选择用户 *</label>
                        <select id="assignUser" name="userId" required>
                            <option value="">请选择用户</option>
                            <!-- 用户选项将通过JavaScript生成 -->
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="assignRole">分配角色</label>
                        <select id="assignRole" name="roleId">
                            <option value="">请选择角色</option>
                            <!-- 角色选项将通过JavaScript生成 -->
                        </select>
                    </div>
                    <div class="form-group">
                        <label>或选择具体权限</label>
                        <div class="permission-tree" id="permissionTree">
                            <!-- 权限树将通过JavaScript生成 -->
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="assignExpiry">权限有效期</label>
                        <select id="assignExpiry" name="expiry">
                            <option value="never">永久</option>
                            <option value="30d">30天</option>
                            <option value="90d">90天</option>
                            <option value="1y">1年</option>
                        </select>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn-secondary" onclick="closeAssignPermissionModal()">取消</button>
                <button type="button" class="btn-primary" onclick="processAssignPermission()">
                    <i class="fas fa-user-shield"></i>
                    分配权限
                </button>
            </div>
        </div>
    </div>

    <!-- 主要功能模块 -->
    <script src="../js/core/app.js"></script>
    <script src="../js/modules/user-management.js"></script>
    <script src="../js/modules/data-visualization.js"></script>
    <script src="../js/modules/analytics.js"></script>
    <script src="../js/modules/order-management.js"></script>
    <script src="../js/modules/settings.js"></script>
    <script src="../js/modules/notifications.js"></script>
    <script src="../js/modules/inventory-management.js"></script>
    <script src="../js/modules/reports-management.js"></script>
    <script src="../js/utils/ner-frontend-only.js"></script>
    <script src="../js/extensions/enhanced-user-management.js"></script>
    <script src="../js/extensions/enhanced-analytics.js"></script>
    <script src="../js/extensions/enhanced-order-management.js"></script>
    <script src="../js/extensions/user-menu.js"></script>
    <script src="../js/extensions/system-settings.js"></script>
    <script src="../js/modules/file-management.js"></script>
    <script src="../js/extensions/notification-system.js"></script>
    <script src="../js/extensions/welcome-banner.js"></script>
    <script src="../js/extensions/user-account.js"></script>
    <script src="../js/extensions/role-based-access.js"></script>
    <script src="../js/extensions/advanced-charts.js"></script>
    <script src="../js/extensions/realtime-notifications.js"></script>
    <script src="../js/extensions/advanced-search.js"></script>
    <script src="../js/extensions/system-monitor.js"></script>
    <script src="../js/extensions/data-import-export.js"></script>
    <script src="../js/extensions/workflow-management.js"></script>
    <script src="../js/extensions/internationalization.js"></script>
    <script src="../js/extensions/theme-customization.js"></script>
    <script src="../js/extensions/intelligent-assistant.js"></script>
    <script src="../js/modules/permission-management.js"></script>
    <script src="../js/extensions/api-management.js"></script>
    <script src="../js/extensions/backup-recovery.js"></script>
    <script src="../js/extensions/performance-analyzer.js"></script>
    <script src="../js/extensions/plugin-system.js"></script>
    <script src="../js/extensions/advanced-analytics.js"></script>
    <script src="../js/extensions/realtime-communication.js"></script>
    <script src="../js/extensions/security-center.js"></script>
    <script src="../js/extensions/workflow-engine.js"></script>
    <script src="../js/extensions/ai-assistant.js"></script>
    <script src="../js/utils/debug.js"></script>
    <script src="../js/utils/chart-initializer.js"></script>
    <script src="../js/utils/ui-enhancements.js"></script>

    <!-- 修改密码模态框 -->
    <div class="modal" id="changePasswordModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>修改密码</h3>
                <button class="modal-close" onclick="closeChangePasswordModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <form id="changePasswordForm" onsubmit="handlePasswordChange(event)">
                    <div class="form-group">
                        <label for="currentPassword">当前密码</label>
                        <div class="password-input-group">
                            <input type="password" id="currentPassword" name="currentPassword" required>
                            <button type="button" class="password-toggle" onclick="togglePasswordVisibility('currentPassword')">
                                <i class="fas fa-eye"></i>
                            </button>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="newPassword">新密码</label>
                        <div class="password-input-group">
                            <input type="password" id="newPassword" name="newPassword" required minlength="6">
                            <button type="button" class="password-toggle" onclick="togglePasswordVisibility('newPassword')">
                                <i class="fas fa-eye"></i>
                            </button>
                        </div>
                        <div class="password-strength" id="passwordStrength">
                            <div class="strength-bar">
                                <div class="strength-fill" id="strengthFill"></div>
                            </div>
                            <div class="strength-text" id="strengthText">密码强度</div>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="confirmPassword">确认新密码</label>
                        <div class="password-input-group">
                            <input type="password" id="confirmPassword" name="confirmPassword" required>
                            <button type="button" class="password-toggle" onclick="togglePasswordVisibility('confirmPassword')">
                                <i class="fas fa-eye"></i>
                            </button>
                        </div>
                        <div class="password-match" id="passwordMatch"></div>
                    </div>

                    <div class="password-requirements">
                        <h4>密码要求：</h4>
                        <ul>
                            <li id="req-length">至少6个字符</li>
                            <li id="req-uppercase">包含大写字母</li>
                            <li id="req-lowercase">包含小写字母</li>
                            <li id="req-number">包含数字</li>
                            <li id="req-special">包含特殊字符</li>
                        </ul>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn-secondary" onclick="closeChangePasswordModal()">取消</button>
                <button type="submit" form="changePasswordForm" class="btn-primary" id="changePasswordBtn">
                    <i class="fas fa-key"></i>
                    修改密码
                </button>
            </div>
        </div>
    </div>

    <!-- 个人资料模态框 -->
    <div class="modal" id="userProfileModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>个人资料</h3>
                <button class="modal-close" onclick="closeUserProfileModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <form id="userProfileForm" onsubmit="handleProfileUpdate(event)">
                    <div class="profile-avatar-section">
                        <div class="profile-avatar-container">
                            <img src="" alt="用户头像" id="profileAvatarImg">
                            <button type="button" class="avatar-upload-btn" onclick="selectAvatarFile()">
                                <i class="fas fa-camera"></i>
                            </button>
                            <input type="file" id="avatarFileInput" accept="image/*" style="display: none;" onchange="handleAvatarUpload(event)">
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="profileUsername">用户名</label>
                            <input type="text" id="profileUsername" name="username" readonly>
                        </div>
                        <div class="form-group">
                            <label for="profileName">姓名</label>
                            <input type="text" id="profileName" name="name" required>
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="profileEmail">邮箱</label>
                            <input type="email" id="profileEmail" name="email" required>
                        </div>
                        <div class="form-group">
                            <label for="profilePhone">电话</label>
                            <input type="tel" id="profilePhone" name="phone">
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="profileDepartment">部门</label>
                        <select id="profileDepartment" name="department">
                            <option value="">请选择部门</option>
                            <option value="技术部">技术部</option>
                            <option value="市场部">市场部</option>
                            <option value="销售部">销售部</option>
                            <option value="人事部">人事部</option>
                            <option value="财务部">财务部</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label for="profileBio">个人简介</label>
                        <textarea id="profileBio" name="bio" rows="3" placeholder="介绍一下自己..."></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn-secondary" onclick="closeUserProfileModal()">取消</button>
                <button type="submit" form="userProfileForm" class="btn-primary">
                    <i class="fas fa-save"></i>
                    保存更改
                </button>
            </div>
        </div>
    </div>
</body>
</html>
