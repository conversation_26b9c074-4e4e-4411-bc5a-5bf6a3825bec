// 通知中心管理系统

// 通知数据模拟
const notifications = [
    {
        id: 1,
        type: 'system',
        title: '系统维护通知',
        message: '系统将于今晚23:00-01:00进行维护，期间可能影响部分功能使用。',
        timestamp: new Date('2024-01-15 14:30:00'),
        isRead: false,
        priority: 'high',
        icon: 'fas fa-tools',
        color: 'warning'
    },
    {
        id: 2,
        type: 'order',
        title: '新订单提醒',
        message: '您有一个新的订单 #ORD-2024-001 需要处理，订单金额：¥2,599',
        timestamp: new Date('2024-01-15 13:45:00'),
        isRead: false,
        priority: 'medium',
        icon: 'fas fa-shopping-cart',
        color: 'primary'
    },
    {
        id: 3,
        type: 'user',
        title: '用户注册通知',
        message: '新用户"张三"已成功注册，等待管理员审核。',
        timestamp: new Date('2024-01-15 12:20:00'),
        isRead: true,
        priority: 'low',
        icon: 'fas fa-user-plus',
        color: 'success'
    },
    {
        id: 4,
        type: 'security',
        title: '安全警告',
        message: '检测到异常登录尝试，IP地址：*************，请注意账户安全。',
        timestamp: new Date('2024-01-15 11:15:00'),
        isRead: false,
        priority: 'high',
        icon: 'fas fa-shield-alt',
        color: 'danger'
    },
    {
        id: 5,
        type: 'reminder',
        title: '数据备份提醒',
        message: '系统数据已自动备份完成，备份文件已保存到云端。',
        timestamp: new Date('2024-01-15 10:00:00'),
        isRead: true,
        priority: 'low',
        icon: 'fas fa-cloud-upload-alt',
        color: 'info'
    },
    {
        id: 6,
        type: 'order',
        title: '订单状态更新',
        message: '订单 #ORD-2024-002 状态已更新为"已发货"，预计3-5个工作日送达。',
        timestamp: new Date('2024-01-14 16:30:00'),
        isRead: true,
        priority: 'medium',
        icon: 'fas fa-truck',
        color: 'primary'
    },
    {
        id: 7,
        type: 'system',
        title: '功能更新通知',
        message: '系统新增了数据导出功能，您现在可以导出用户和订单数据。',
        timestamp: new Date('2024-01-14 09:00:00'),
        isRead: false,
        priority: 'medium',
        icon: 'fas fa-download',
        color: 'info'
    },
    {
        id: 8,
        type: 'user',
        title: '用户权限变更',
        message: '用户"李四"的权限已从"普通用户"升级为"管理员"。',
        timestamp: new Date('2024-01-13 15:20:00'),
        isRead: true,
        priority: 'medium',
        icon: 'fas fa-user-shield',
        color: 'warning'
    }
];

// 分页配置
let currentNotificationPage = 1;
const notificationsPerPage = 5;
let filteredNotifications = [...notifications];

// 初始化通知中心
function initializeNotifications() {
    updateNotificationStats();
    renderNotifications();
    renderNotificationsPagination();
    updateNotificationBadge();
}

// 更新通知统计
function updateNotificationStats() {
    const total = notifications.length;
    const unread = notifications.filter(n => !n.isRead).length;
    const read = notifications.filter(n => n.isRead).length;
    const today = notifications.filter(n => isToday(n.timestamp)).length;

    document.getElementById('totalNotifications').textContent = total;
    document.getElementById('unreadNotifications').textContent = unread;
    document.getElementById('readNotifications').textContent = read;
    document.getElementById('todayNotifications').textContent = today;
}

// 检查是否是今天
function isToday(date) {
    const today = new Date();
    return date.toDateString() === today.toDateString();
}

// 渲染通知列表
function renderNotifications() {
    const container = document.getElementById('notificationsContainer');
    if (!container) return;

    const startIndex = (currentNotificationPage - 1) * notificationsPerPage;
    const endIndex = startIndex + notificationsPerPage;
    const pageNotifications = filteredNotifications.slice(startIndex, endIndex);

    if (pageNotifications.length === 0) {
        container.innerHTML = `
            <div class="empty-state">
                <i class="fas fa-bell-slash"></i>
                <h3>暂无通知</h3>
                <p>当前没有符合条件的通知</p>
            </div>
        `;
        return;
    }

    container.innerHTML = pageNotifications.map(notification => `
        <div class="notification-item ${notification.isRead ? 'read' : 'unread'}" data-id="${notification.id}">
            <div class="notification-icon ${notification.color}">
                <i class="${notification.icon}"></i>
            </div>
            <div class="notification-content">
                <div class="notification-header">
                    <h4 class="notification-title">${notification.title}</h4>
                    <div class="notification-meta">
                        <span class="notification-type">${getTypeLabel(notification.type)}</span>
                        <span class="notification-time">${formatTime(notification.timestamp)}</span>
                        <span class="notification-priority priority-${notification.priority}">${getPriorityLabel(notification.priority)}</span>
                    </div>
                </div>
                <p class="notification-message">${notification.message}</p>
            </div>
            <div class="notification-actions">
                ${!notification.isRead ? `
                    <button class="btn-icon" onclick="markAsRead(${notification.id})" title="标记已读">
                        <i class="fas fa-check"></i>
                    </button>
                ` : ''}
                <button class="btn-icon danger" onclick="deleteNotification(${notification.id})" title="删除">
                    <i class="fas fa-trash"></i>
                </button>
            </div>
        </div>
    `).join('');
}

// 获取类型标签
function getTypeLabel(type) {
    const labels = {
        'system': '系统',
        'order': '订单',
        'user': '用户',
        'security': '安全',
        'reminder': '提醒'
    };
    return labels[type] || type;
}

// 获取优先级标签
function getPriorityLabel(priority) {
    const labels = {
        'high': '高',
        'medium': '中',
        'low': '低'
    };
    return labels[priority] || priority;
}

// 格式化时间
function formatTime(timestamp) {
    const now = new Date();
    const diff = now - timestamp;
    const minutes = Math.floor(diff / 60000);
    const hours = Math.floor(diff / 3600000);
    const days = Math.floor(diff / 86400000);

    if (minutes < 1) return '刚刚';
    if (minutes < 60) return `${minutes}分钟前`;
    if (hours < 24) return `${hours}小时前`;
    if (days < 7) return `${days}天前`;
    
    return timestamp.toLocaleDateString('zh-CN');
}

// 渲染分页
function renderNotificationsPagination() {
    const container = document.getElementById('notificationsPagination');
    if (!container) return;

    const totalPages = Math.ceil(filteredNotifications.length / notificationsPerPage);
    
    if (totalPages <= 1) {
        container.innerHTML = '';
        return;
    }

    let paginationHTML = '<div class="pagination-controls">';
    
    // 上一页
    paginationHTML += `
        <button class="pagination-btn ${currentNotificationPage === 1 ? 'disabled' : ''}" 
                onclick="changeNotificationPage(${currentNotificationPage - 1})" 
                ${currentNotificationPage === 1 ? 'disabled' : ''}>
            <i class="fas fa-chevron-left"></i>
        </button>
    `;
    
    // 页码
    for (let i = 1; i <= totalPages; i++) {
        if (i === 1 || i === totalPages || (i >= currentNotificationPage - 1 && i <= currentNotificationPage + 1)) {
            paginationHTML += `
                <button class="pagination-btn ${i === currentNotificationPage ? 'active' : ''}" 
                        onclick="changeNotificationPage(${i})">
                    ${i}
                </button>
            `;
        } else if (i === currentNotificationPage - 2 || i === currentNotificationPage + 2) {
            paginationHTML += '<span class="pagination-ellipsis">...</span>';
        }
    }
    
    // 下一页
    paginationHTML += `
        <button class="pagination-btn ${currentNotificationPage === totalPages ? 'disabled' : ''}" 
                onclick="changeNotificationPage(${currentNotificationPage + 1})" 
                ${currentNotificationPage === totalPages ? 'disabled' : ''}>
            <i class="fas fa-chevron-right"></i>
        </button>
    `;
    
    paginationHTML += '</div>';
    container.innerHTML = paginationHTML;
}

// 切换页面
function changeNotificationPage(page) {
    const totalPages = Math.ceil(filteredNotifications.length / notificationsPerPage);
    if (page < 1 || page > totalPages) return;

    currentNotificationPage = page;
    renderNotifications();
    renderNotificationsPagination();
}

// 筛选通知
function filterNotifications() {
    const typeFilter = document.getElementById('notificationTypeFilter').value;
    const statusFilter = document.getElementById('notificationStatusFilter').value;
    const timeFilter = document.getElementById('notificationTimeFilter').value;

    filteredNotifications = notifications.filter(notification => {
        // 类型筛选
        if (typeFilter && notification.type !== typeFilter) return false;

        // 状态筛选
        if (statusFilter === 'read' && !notification.isRead) return false;
        if (statusFilter === 'unread' && notification.isRead) return false;

        // 时间筛选
        if (timeFilter) {
            const now = new Date();
            const notificationDate = notification.timestamp;

            switch (timeFilter) {
                case 'today':
                    if (!isToday(notificationDate)) return false;
                    break;
                case 'week':
                    const weekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
                    if (notificationDate < weekAgo) return false;
                    break;
                case 'month':
                    const monthAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
                    if (notificationDate < monthAgo) return false;
                    break;
            }
        }

        return true;
    });

    currentNotificationPage = 1;
    renderNotifications();
    renderNotificationsPagination();
}

// 标记单个通知为已读
function markAsRead(notificationId) {
    const notification = notifications.find(n => n.id === notificationId);
    if (notification) {
        notification.isRead = true;
        updateNotificationStats();
        renderNotifications();
        updateNotificationBadge();
        showNotification('通知已标记为已读', 'success');
    }
}

// 标记所有通知为已读
function markAllAsRead() {
    const unreadCount = notifications.filter(n => !n.isRead).length;
    if (unreadCount === 0) {
        showNotification('没有未读通知', 'info');
        return;
    }

    notifications.forEach(notification => {
        notification.isRead = true;
    });

    updateNotificationStats();
    renderNotifications();
    updateNotificationBadge();
    showNotification(`已标记 ${unreadCount} 条通知为已读`, 'success');
}

// 删除单个通知
function deleteNotification(notificationId) {
    if (confirm('确定要删除这条通知吗？')) {
        const index = notifications.findIndex(n => n.id === notificationId);
        if (index !== -1) {
            notifications.splice(index, 1);

            // 重新筛选
            filterNotifications();
            updateNotificationStats();
            updateNotificationBadge();
            showNotification('通知已删除', 'success');
        }
    }
}

// 清空所有通知
function clearAllNotifications() {
    if (confirm('确定要清空所有通知吗？此操作不可撤销！')) {
        notifications.length = 0;
        filteredNotifications.length = 0;

        updateNotificationStats();
        renderNotifications();
        renderNotificationsPagination();
        updateNotificationBadge();
        showNotification('所有通知已清空', 'success');
    }
}

// 更新通知徽章
function updateNotificationBadge() {
    const unreadCount = notifications.filter(n => !n.isRead).length;
    const badges = document.querySelectorAll('.notification-badge');

    badges.forEach(badge => {
        badge.textContent = unreadCount;
        badge.style.display = unreadCount > 0 ? 'inline-block' : 'none';
    });
}

// 添加新通知
function addNotification(type, title, message, priority = 'medium') {
    const newNotification = {
        id: Math.max(...notifications.map(n => n.id), 0) + 1,
        type: type,
        title: title,
        message: message,
        timestamp: new Date(),
        isRead: false,
        priority: priority,
        icon: getTypeIcon(type),
        color: getTypeColor(type)
    };

    notifications.unshift(newNotification);
    filterNotifications();
    updateNotificationStats();
    updateNotificationBadge();

    // 如果用户在通知页面，显示新通知提示
    const currentPage = document.querySelector('.page.active');
    if (currentPage && currentPage.id === 'notifications') {
        showNotification('收到新通知', 'info');
    }
}

// 获取类型图标
function getTypeIcon(type) {
    const icons = {
        'system': 'fas fa-cog',
        'order': 'fas fa-shopping-cart',
        'user': 'fas fa-user',
        'security': 'fas fa-shield-alt',
        'reminder': 'fas fa-bell'
    };
    return icons[type] || 'fas fa-info-circle';
}

// 获取类型颜色
function getTypeColor(type) {
    const colors = {
        'system': 'info',
        'order': 'primary',
        'user': 'success',
        'security': 'danger',
        'reminder': 'warning'
    };
    return colors[type] || 'info';
}

// 模拟实时通知
function simulateRealTimeNotifications() {
    const notificationTypes = ['system', 'order', 'user', 'security', 'reminder'];
    const sampleNotifications = [
        { type: 'order', title: '新订单提醒', message: '您有新的订单需要处理' },
        { type: 'user', title: '用户注册', message: '新用户已注册并等待审核' },
        { type: 'system', title: '系统更新', message: '系统已完成自动更新' },
        { type: 'security', title: '安全提醒', message: '检测到异常登录尝试' },
        { type: 'reminder', title: '定时提醒', message: '您有待处理的任务' }
    ];

    setInterval(() => {
        // 随机决定是否生成新通知（30%概率）
        if (Math.random() < 0.3) {
            const sample = sampleNotifications[Math.floor(Math.random() * sampleNotifications.length)];
            const priorities = ['low', 'medium', 'high'];
            const priority = priorities[Math.floor(Math.random() * priorities.length)];

            addNotification(sample.type, sample.title, sample.message, priority);
        }
    }, 30000); // 每30秒检查一次
}
