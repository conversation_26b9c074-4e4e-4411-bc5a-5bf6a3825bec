@echo off
echo ========================================
echo 恋雪二游 - 整合系统快速启动
echo ========================================
echo.

echo 启动前端服务器...
start "前端服务器" cmd /k "python -m http.server 8000"
timeout /t 2 /nobreak >nul

echo 启动API服务器...
start "API服务器" cmd /k "python simple_ner_api.py"
timeout /t 3 /nobreak >nul

echo.
echo ✅ 系统启动完成！
echo.
echo 📱 前端地址: http://localhost:8000/login.html
echo 🔧 API地址: http://localhost:5000
echo.
echo 👤 测试账号:
echo    用户名: admin
echo    密码: admin123
echo.
echo 正在打开浏览器...
start http://localhost:8000/login.html

echo.
echo 按任意键退出...
pause >nul
