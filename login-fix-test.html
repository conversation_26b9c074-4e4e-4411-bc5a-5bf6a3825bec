<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>登录页面修复测试</title>
    <link rel="stylesheet" href="login.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        .test-info {
            position: fixed;
            top: 10px;
            left: 10px;
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 10px;
            border-radius: 8px;
            font-size: 12px;
            z-index: 10000;
            max-width: 300px;
        }
        .test-info h4 {
            margin: 0 0 8px 0;
            color: #10b981;
        }
        .test-info ul {
            margin: 0;
            padding-left: 16px;
        }
        .test-info li {
            margin-bottom: 4px;
        }
    </style>
</head>
<body>
    <!-- 测试信息面板 -->
    <div class="test-info">
        <h4>修复测试</h4>
        <ul>
            <li>✅ 移除body overflow:hidden</li>
            <li>✅ 移动端允许滚动</li>
            <li>✅ 注册表单完整显示</li>
            <li>✅ 响应式布局优化</li>
        </ul>
        <p><strong>测试方法：</strong>在不同设备尺寸下测试注册功能和页面滚动</p>
    </div>

    <!-- 背景动画 -->
    <div class="login-background">
        <div class="background-shapes">
            <div class="shape shape-1"></div>
            <div class="shape shape-2"></div>
            <div class="shape shape-3"></div>
        </div>
    </div>

    <div class="login-container">
        <!-- 登录卡片 -->
        <div class="login-card">
            <div class="login-header">
                <div class="logo">
                    <div class="logo-icon">
                        <i class="fas fa-shield-alt"></i>
                    </div>
                    <div class="logo-text">
                        <h1>SecureAdmin</h1>
                        <span>企业级管理系统</span>
                    </div>
                </div>
                <p>安全、高效、智能的企业管理解决方案</p>
            </div>

            <div class="login-form">
                <!-- 标签页 -->
                <div class="form-tabs">
                    <button class="tab-btn active" data-tab="login">
                        <i class="fas fa-sign-in-alt"></i>
                        登录
                    </button>
                    <button class="tab-btn" data-tab="register">
                        <i class="fas fa-user-plus"></i>
                        注册
                    </button>
                </div>

                <form id="loginForm">
                    <!-- 登录表单 -->
                    <div class="tab-content active" id="login">
                        <div class="form-group">
                            <div class="input-wrapper">
                                <i class="fas fa-user"></i>
                                <input type="text" id="loginUsername" placeholder="用户名或邮箱" required>
                            </div>
                            <div class="error-message" id="loginUsernameError"></div>
                        </div>

                        <div class="form-group">
                            <div class="input-wrapper">
                                <i class="fas fa-lock"></i>
                                <input type="password" id="loginPassword" placeholder="密码" required>
                                <button type="button" class="toggle-password">
                                    <i class="fas fa-eye"></i>
                                </button>
                            </div>
                            <div class="error-message" id="loginPasswordError"></div>
                        </div>

                        <div class="form-options">
                            <label class="checkbox-wrapper">
                                <input type="checkbox" name="remember">
                                <span class="checkmark"></span>
                                记住我
                            </label>
                            <a href="#" class="forgot-password">忘记密码？</a>
                        </div>

                        <button type="submit" class="btn-primary">
                            <i class="fas fa-sign-in-alt"></i>
                            <span>登录</span>
                        </button>

                        <div class="divider">
                            <span>或</span>
                        </div>

                        <div class="social-login">
                            <button type="button" class="social-btn google">
                                <i class="fab fa-google"></i>
                                Google
                            </button>
                            <button type="button" class="social-btn github">
                                <i class="fab fa-github"></i>
                                GitHub
                            </button>
                        </div>
                    </div>

                    <!-- 注册表单 -->
                    <div class="tab-content" id="register">
                        <div class="form-row">
                            <div class="form-group">
                                <div class="input-wrapper">
                                    <i class="fas fa-user"></i>
                                    <input type="text" id="firstName" placeholder="名字" required>
                                </div>
                                <div class="error-message" id="firstNameError"></div>
                            </div>
                            <div class="form-group">
                                <div class="input-wrapper">
                                    <i class="fas fa-user"></i>
                                    <input type="text" id="lastName" placeholder="姓氏" required>
                                </div>
                                <div class="error-message" id="lastNameError"></div>
                            </div>
                        </div>

                        <div class="form-group">
                            <div class="input-wrapper">
                                <i class="fas fa-user-circle"></i>
                                <input type="text" id="regUsername" placeholder="用户名" required>
                            </div>
                            <div class="error-message" id="usernameError"></div>
                        </div>

                        <div class="form-group">
                            <div class="input-wrapper">
                                <i class="fas fa-envelope"></i>
                                <input type="email" id="email" placeholder="邮箱地址" required>
                            </div>
                            <div class="error-message" id="emailError"></div>
                        </div>

                        <div class="form-group">
                            <div class="input-wrapper">
                                <i class="fas fa-lock"></i>
                                <input type="password" id="regPassword" placeholder="密码" required>
                                <button type="button" class="toggle-password">
                                    <i class="fas fa-eye"></i>
                                </button>
                            </div>
                            <div class="password-strength">
                                <div class="strength-bar">
                                    <div class="strength-fill"></div>
                                </div>
                                <span class="strength-text">密码强度</span>
                            </div>
                            <div class="error-message" id="passwordError"></div>
                        </div>

                        <div class="form-group">
                            <div class="input-wrapper">
                                <i class="fas fa-lock"></i>
                                <input type="password" id="confirmPassword" placeholder="确认密码" required>
                                <button type="button" class="toggle-password">
                                    <i class="fas fa-eye"></i>
                                </button>
                            </div>
                            <div class="error-message" id="confirmError"></div>
                        </div>

                        <div class="form-group">
                            <label class="checkbox-wrapper">
                                <input type="checkbox" name="terms" required>
                                <span class="checkmark"></span>
                                我同意 <a href="#" style="color: var(--primary-color);">服务条款</a> 和 <a href="#" style="color: var(--primary-color);">隐私政策</a>
                            </label>
                        </div>

                        <button type="submit" class="btn-primary">
                            <i class="fas fa-user-plus"></i>
                            <span>创建账户</span>
                        </button>

                        <div class="divider">
                            <span>或</span>
                        </div>

                        <div class="social-login">
                            <button type="button" class="social-btn google">
                                <i class="fab fa-google"></i>
                                Google
                            </button>
                            <button type="button" class="social-btn github">
                                <i class="fab fa-github"></i>
                                GitHub
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>

        <!-- 功能特性 -->
        <div class="features-section">
            <h3>为什么选择我们？</h3>
            <div class="features-grid">
                <div class="feature-item">
                    <i class="fas fa-shield-alt"></i>
                    <div class="feature-content">
                        <h4>安全可靠</h4>
                        <p>企业级安全防护，保障您的数据安全</p>
                    </div>
                </div>
                <div class="feature-item">
                    <i class="fas fa-rocket"></i>
                    <div class="feature-content">
                        <h4>高效便捷</h4>
                        <p>简洁直观的界面，提升工作效率</p>
                    </div>
                </div>
                <div class="feature-item">
                    <i class="fas fa-cogs"></i>
                    <div class="feature-content">
                        <h4>智能管理</h4>
                        <p>AI驱动的智能分析和决策支持</p>
                    </div>
                </div>
                <div class="feature-item">
                    <i class="fas fa-headset"></i>
                    <div class="feature-content">
                        <h4>专业支持</h4>
                        <p>7×24小时专业技术支持服务</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="login.js"></script>
    <script>
        // 添加测试功能
        document.addEventListener('DOMContentLoaded', function() {
            // 显示当前屏幕尺寸信息
            function updateScreenInfo() {
                const testInfo = document.querySelector('.test-info');
                const width = window.innerWidth;
                const height = window.innerHeight;
                
                let deviceType = '';
                if (width < 480) {
                    deviceType = '超小屏幕 (< 480px)';
                } else if (width < 768) {
                    deviceType = '小屏幕 (480px - 768px)';
                } else if (width < 1024) {
                    deviceType = '中等屏幕 (768px - 1024px)';
                } else {
                    deviceType = '大屏幕 (> 1024px)';
                }
                
                const screenInfo = testInfo.querySelector('.screen-info') || document.createElement('p');
                screenInfo.className = 'screen-info';
                screenInfo.innerHTML = `<strong>当前:</strong> ${deviceType}<br><small>${width}×${height}px</small>`;
                
                if (!testInfo.querySelector('.screen-info')) {
                    testInfo.appendChild(screenInfo);
                }
            }
            
            updateScreenInfo();
            window.addEventListener('resize', updateScreenInfo);
            
            // 测试注册功能
            const registerForm = document.getElementById('register');
            if (registerForm) {
                console.log('注册表单已加载，功能正常');
            }
        });
    </script>
</body>
</html>
