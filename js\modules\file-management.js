// 文件管理功能
class FileManager {
    constructor() {
        this.files = [];
        this.filteredFiles = [];
        this.selectedFiles = new Set();
        this.currentPath = '/';
        this.viewMode = 'grid';
        this.currentPage = 1;
        this.pageSize = 20;
        this.filters = {
            search: '',
            type: '',
            size: '',
            date: ''
        };
        
        this.initializeFiles();
        this.setupEventListeners();
    }
    
    initializeFiles() {
        // 生成示例文件数据
        this.files = this.generateSampleFiles();
        this.updateStats();
        this.applyFilters();
    }
    
    generateSampleFiles() {
        const files = [];
        const fileTypes = {
            image: ['jpg', 'png', 'gif', 'svg', 'webp'],
            document: ['pdf', 'doc', 'docx', 'txt', 'xlsx'],
            video: ['mp4', 'avi', 'mov', 'mkv'],
            audio: ['mp3', 'wav', 'flac', 'aac'],
            archive: ['zip', 'rar', '7z', 'tar'],
            other: ['exe', 'dmg', 'iso', 'bin']
        };
        
        const fileNames = [
            '项目文档', '会议记录', '产品图片', '演示视频', '音乐文件',
            '系统备份', '用户手册', '设计稿', '代码文件', '数据报告',
            '培训材料', '合同文件', '财务报表', '营销素材', '技术文档'
        ];
        
        // 添加一些文件夹
        files.push({
            id: 1,
            name: '文档',
            type: 'folder',
            path: '/文档',
            size: 0,
            createTime: new Date('2024-01-01'),
            modifyTime: new Date('2024-01-15'),
            isFolder: true
        });
        
        files.push({
            id: 2,
            name: '图片',
            type: 'folder',
            path: '/图片',
            size: 0,
            createTime: new Date('2024-01-02'),
            modifyTime: new Date('2024-01-16'),
            isFolder: true
        });
        
        files.push({
            id: 3,
            name: '视频',
            type: 'folder',
            path: '/视频',
            size: 0,
            createTime: new Date('2024-01-03'),
            modifyTime: new Date('2024-01-17'),
            isFolder: true
        });
        
        // 生成文件
        let fileId = 4;
        Object.entries(fileTypes).forEach(([category, extensions]) => {
            extensions.forEach((ext, index) => {
                const fileName = fileNames[Math.floor(Math.random() * fileNames.length)];
                const size = Math.floor(Math.random() * 50000000) + 1000; // 1KB - 50MB
                const createTime = new Date(Date.now() - Math.random() * 90 * 24 * 60 * 60 * 1000);
                
                files.push({
                    id: fileId++,
                    name: `${fileName}.${ext}`,
                    type: category,
                    extension: ext,
                    path: `/${fileName}.${ext}`,
                    size: size,
                    createTime: createTime,
                    modifyTime: new Date(createTime.getTime() + Math.random() * 30 * 24 * 60 * 60 * 1000),
                    isFolder: false,
                    thumbnail: this.generateThumbnail(category, ext)
                });
            });
        });
        
        return files.sort((a, b) => {
            // 文件夹排在前面
            if (a.isFolder && !b.isFolder) return -1;
            if (!a.isFolder && b.isFolder) return 1;
            return a.name.localeCompare(b.name);
        });
    }
    
    generateThumbnail(category, extension) {
        const thumbnails = {
            image: 'https://via.placeholder.com/150x150/6366f1/ffffff?text=IMG',
            document: 'https://via.placeholder.com/150x150/10b981/ffffff?text=DOC',
            video: 'https://via.placeholder.com/150x150/f59e0b/ffffff?text=VID',
            audio: 'https://via.placeholder.com/150x150/ef4444/ffffff?text=AUD',
            archive: 'https://via.placeholder.com/150x150/8b5cf6/ffffff?text=ZIP',
            other: 'https://via.placeholder.com/150x150/6b7280/ffffff?text=FILE'
        };
        
        return thumbnails[category] || thumbnails.other;
    }
    
    updateStats() {
        const totalFiles = this.files.filter(f => !f.isFolder).length;
        const totalFolders = this.files.filter(f => f.isFolder).length;
        const totalSize = this.files.reduce((sum, file) => sum + (file.size || 0), 0);
        const recentFiles = this.files.filter(f => {
            const weekAgo = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);
            return f.createTime > weekAgo && !f.isFolder;
        }).length;
        
        document.getElementById('totalFiles').textContent = totalFiles;
        document.getElementById('totalFolders').textContent = totalFolders;
        document.getElementById('totalSize').textContent = this.formatFileSize(totalSize);
        document.getElementById('recentFiles').textContent = recentFiles;
    }
    
    formatFileSize(bytes) {
        if (bytes === 0) return '0 B';
        const k = 1024;
        const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }
    
    applyFilters() {
        this.filteredFiles = this.files.filter(file => {
            // 路径筛选
            if (!file.path.startsWith(this.currentPath)) {
                return false;
            }
            
            // 搜索筛选
            if (this.filters.search) {
                const searchTerm = this.filters.search.toLowerCase();
                if (!file.name.toLowerCase().includes(searchTerm)) {
                    return false;
                }
            }
            
            // 类型筛选
            if (this.filters.type && !file.isFolder) {
                if (file.type !== this.filters.type) {
                    return false;
                }
            }
            
            // 大小筛选
            if (this.filters.size && !file.isFolder) {
                const size = file.size;
                switch (this.filters.size) {
                    case 'small':
                        if (size >= 1024 * 1024) return false;
                        break;
                    case 'medium':
                        if (size < 1024 * 1024 || size > 10 * 1024 * 1024) return false;
                        break;
                    case 'large':
                        if (size <= 10 * 1024 * 1024) return false;
                        break;
                }
            }
            
            // 日期筛选
            if (this.filters.date) {
                const now = new Date();
                let startDate;
                
                switch (this.filters.date) {
                    case 'today':
                        startDate = new Date(now.getFullYear(), now.getMonth(), now.getDate());
                        break;
                    case 'week':
                        startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
                        break;
                    case 'month':
                        startDate = new Date(now.getFullYear(), now.getMonth(), 1);
                        break;
                }
                
                if (startDate && file.createTime < startDate) {
                    return false;
                }
            }
            
            return true;
        });
        
        this.currentPage = 1;
        this.renderFiles();
        this.renderPagination();
    }
    
    renderFiles() {
        const container = document.getElementById('fileGrid');
        const startIndex = (this.currentPage - 1) * this.pageSize;
        const endIndex = startIndex + this.pageSize;
        const pageFiles = this.filteredFiles.slice(startIndex, endIndex);
        
        if (this.viewMode === 'grid') {
            container.className = 'file-grid';
            container.innerHTML = pageFiles.map(file => this.renderFileCard(file)).join('');
        } else {
            container.className = 'file-list';
            container.innerHTML = pageFiles.map(file => this.renderFileRow(file)).join('');
        }
        
        this.updateBulkActionsToolbar();
    }
    
    renderFileCard(file) {
        const isSelected = this.selectedFiles.has(file.id);
        
        return `
            <div class="file-card ${isSelected ? 'selected' : ''}" data-file-id="${file.id}">
                <div class="file-checkbox">
                    <input type="checkbox" ${isSelected ? 'checked' : ''} 
                           onchange="fileManager.toggleFileSelection(${file.id})">
                </div>
                <div class="file-thumbnail" onclick="fileManager.${file.isFolder ? 'openFolder' : 'previewFile'}(${file.id})">
                    ${file.isFolder ? 
                        '<i class="fas fa-folder file-icon"></i>' : 
                        `<img src="${file.thumbnail}" alt="${file.name}" onerror="this.style.display='none'; this.nextElementSibling.style.display='block';">
                         <i class="fas fa-file file-icon" style="display: none;"></i>`
                    }
                </div>
                <div class="file-info">
                    <div class="file-name" title="${file.name}">${file.name}</div>
                    <div class="file-meta">
                        ${file.isFolder ? '文件夹' : `${this.formatFileSize(file.size)} • ${file.extension.toUpperCase()}`}
                    </div>
                    <div class="file-date">${this.formatDate(file.modifyTime)}</div>
                </div>
                <div class="file-actions">
                    <button class="file-action-btn" onclick="fileManager.${file.isFolder ? 'openFolder' : 'previewFile'}(${file.id})" 
                            title="${file.isFolder ? '打开' : '预览'}">
                        <i class="fas fa-${file.isFolder ? 'folder-open' : 'eye'}"></i>
                    </button>
                    <button class="file-action-btn" onclick="fileManager.downloadFile(${file.id})" title="下载">
                        <i class="fas fa-download"></i>
                    </button>
                    <button class="file-action-btn" onclick="fileManager.shareFile(${file.id})" title="分享">
                        <i class="fas fa-share"></i>
                    </button>
                    <button class="file-action-btn danger" onclick="fileManager.deleteFile(${file.id})" title="删除">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </div>
        `;
    }
    
    renderFileRow(file) {
        const isSelected = this.selectedFiles.has(file.id);
        
        return `
            <div class="file-row ${isSelected ? 'selected' : ''}" data-file-id="${file.id}">
                <div class="file-checkbox">
                    <input type="checkbox" ${isSelected ? 'checked' : ''} 
                           onchange="fileManager.toggleFileSelection(${file.id})">
                </div>
                <div class="file-icon-small">
                    <i class="fas fa-${file.isFolder ? 'folder' : 'file'}"></i>
                </div>
                <div class="file-name-col" onclick="fileManager.${file.isFolder ? 'openFolder' : 'previewFile'}(${file.id})">
                    ${file.name}
                </div>
                <div class="file-size-col">
                    ${file.isFolder ? '-' : this.formatFileSize(file.size)}
                </div>
                <div class="file-type-col">
                    ${file.isFolder ? '文件夹' : file.extension.toUpperCase()}
                </div>
                <div class="file-date-col">
                    ${this.formatDate(file.modifyTime)}
                </div>
                <div class="file-actions-col">
                    <button class="file-action-btn" onclick="fileManager.${file.isFolder ? 'openFolder' : 'previewFile'}(${file.id})">
                        <i class="fas fa-${file.isFolder ? 'folder-open' : 'eye'}"></i>
                    </button>
                    <button class="file-action-btn" onclick="fileManager.downloadFile(${file.id})">
                        <i class="fas fa-download"></i>
                    </button>
                    <button class="file-action-btn danger" onclick="fileManager.deleteFile(${file.id})">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </div>
        `;
    }
    
    renderPagination() {
        const pagination = document.getElementById('filesPagination');
        const totalPages = Math.ceil(this.filteredFiles.length / this.pageSize);
        
        if (totalPages <= 1) {
            pagination.innerHTML = '';
            return;
        }
        
        let paginationHTML = `
            <div class="pagination-info">
                显示 ${(this.currentPage - 1) * this.pageSize + 1}-${Math.min(this.currentPage * this.pageSize, this.filteredFiles.length)} 
                共 ${this.filteredFiles.length} 个项目
            </div>
            <div class="pagination-controls">
        `;
        
        // 上一页
        paginationHTML += `
            <button class="pagination-btn ${this.currentPage === 1 ? 'disabled' : ''}" 
                    onclick="fileManager.goToPage(${this.currentPage - 1})" 
                    ${this.currentPage === 1 ? 'disabled' : ''}>
                <i class="fas fa-chevron-left"></i>
            </button>
        `;
        
        // 页码
        for (let i = 1; i <= totalPages; i++) {
            if (i === 1 || i === totalPages || (i >= this.currentPage - 2 && i <= this.currentPage + 2)) {
                paginationHTML += `
                    <button class="pagination-btn ${i === this.currentPage ? 'active' : ''}" 
                            onclick="fileManager.goToPage(${i})">
                        ${i}
                    </button>
                `;
            } else if (i === this.currentPage - 3 || i === this.currentPage + 3) {
                paginationHTML += '<span class="pagination-ellipsis">...</span>';
            }
        }
        
        // 下一页
        paginationHTML += `
            <button class="pagination-btn ${this.currentPage === totalPages ? 'disabled' : ''}" 
                    onclick="fileManager.goToPage(${this.currentPage + 1})" 
                    ${this.currentPage === totalPages ? 'disabled' : ''}>
                <i class="fas fa-chevron-right"></i>
            </button>
        `;
        
        paginationHTML += '</div>';
        pagination.innerHTML = paginationHTML;
    }
    
    formatDate(date) {
        if (!date) return '';
        return new Intl.DateTimeFormat('zh-CN', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit'
        }).format(date);
    }
    
    setupEventListeners() {
        // 拖拽上传
        const uploadArea = document.getElementById('uploadArea');
        if (uploadArea) {
            uploadArea.addEventListener('dragover', (e) => {
                e.preventDefault();
                uploadArea.classList.add('dragover');
            });
            
            uploadArea.addEventListener('dragleave', (e) => {
                e.preventDefault();
                uploadArea.classList.remove('dragover');
            });
            
            uploadArea.addEventListener('drop', (e) => {
                e.preventDefault();
                uploadArea.classList.remove('dragover');
                const files = Array.from(e.dataTransfer.files);
                this.handleFileSelection(files);
            });
        }
        
        // 文件选择
        const fileInput = document.getElementById('fileInput');
        if (fileInput) {
            fileInput.addEventListener('change', (e) => {
                const files = Array.from(e.target.files);
                this.handleFileSelection(files);
            });
        }
    }
    
    handleFileSelection(files) {
        const uploadList = document.getElementById('uploadList');
        const uploadBtn = document.getElementById('uploadBtn');
        
        uploadList.innerHTML = files.map(file => `
            <div class="upload-item">
                <div class="upload-item-info">
                    <i class="fas fa-file"></i>
                    <span class="upload-item-name">${file.name}</span>
                    <span class="upload-item-size">${this.formatFileSize(file.size)}</span>
                </div>
                <div class="upload-item-status">等待上传</div>
            </div>
        `).join('');
        
        uploadBtn.disabled = files.length === 0;
        
        if (files.length > 0) {
            showNotification(`已选择 ${files.length} 个文件`, 'info');
        }
    }
    
    // 文件选择相关方法
    toggleFileSelection(fileId) {
        if (this.selectedFiles.has(fileId)) {
            this.selectedFiles.delete(fileId);
        } else {
            this.selectedFiles.add(fileId);
        }
        this.updateBulkActionsToolbar();
        this.renderFiles();
    }
    
    updateBulkActionsToolbar() {
        const toolbar = document.getElementById('fileBulkActionsToolbar');
        const selectedCount = document.getElementById('selectedFilesCount');
        
        if (this.selectedFiles.size > 0) {
            toolbar.style.display = 'flex';
            selectedCount.textContent = this.selectedFiles.size;
        } else {
            toolbar.style.display = 'none';
        }
    }
    
    // 分页方法
    goToPage(page) {
        const totalPages = Math.ceil(this.filteredFiles.length / this.pageSize);
        if (page >= 1 && page <= totalPages) {
            this.currentPage = page;
            this.renderFiles();
            this.renderPagination();
        }
    }
    
    // 筛选方法
    setFilter(key, value) {
        this.filters[key] = value;
        this.applyFilters();
    }
    
    // 视图模式切换
    setViewMode(mode) {
        this.viewMode = mode;
        
        // 更新按钮状态
        document.querySelectorAll('.view-btn').forEach(btn => btn.classList.remove('active'));
        document.querySelector(`[onclick="setViewMode('${mode}')"]`).classList.add('active');
        
        this.renderFiles();
    }
}

// 全局文件管理器实例
let fileManager = null;

// 文件操作方法
function filterFiles() {
    if (!fileManager) return;

    const search = document.getElementById('fileSearch').value;
    const type = document.getElementById('fileTypeFilter').value;
    const size = document.getElementById('fileSizeFilter').value;
    const date = document.getElementById('fileDateFilter').value;

    fileManager.setFilter('search', search);
    fileManager.setFilter('type', type);
    fileManager.setFilter('size', size);
    fileManager.setFilter('date', date);
}

function setViewMode(mode) {
    if (fileManager) {
        fileManager.setViewMode(mode);
    }
}

function navigateToPath(path) {
    if (fileManager) {
        fileManager.currentPath = path;
        fileManager.applyFilters();
        updateBreadcrumb(path);
    }
}

function updateBreadcrumb(path) {
    const breadcrumb = document.getElementById('fileBreadcrumb');
    const parts = path.split('/').filter(part => part);

    let html = `<span class="breadcrumb-item ${path === '/' ? 'active' : ''}" onclick="navigateToPath('/')">
        <i class="fas fa-home"></i>
        根目录
    </span>`;

    let currentPath = '';
    parts.forEach((part, index) => {
        currentPath += '/' + part;
        const isLast = index === parts.length - 1;
        html += `<span class="breadcrumb-separator">/</span>
                 <span class="breadcrumb-item ${isLast ? 'active' : ''}"
                       onclick="navigateToPath('${currentPath}')">${part}</span>`;
    });

    breadcrumb.innerHTML = html;
}

function refreshFiles() {
    if (fileManager) {
        fileManager.initializeFiles();
        showNotification('文件列表已刷新', 'success');
    }
}

// 文件操作方法
function openFolder(fileId) {
    const file = fileManager.files.find(f => f.id === fileId);
    if (file && file.isFolder) {
        navigateToPath(file.path);
        showNotification(`已打开文件夹: ${file.name}`, 'info');
    }
}

function previewFile(fileId) {
    const file = fileManager.files.find(f => f.id === fileId);
    if (!file || file.isFolder) return;

    const modal = document.getElementById('filePreviewModal');
    const fileName = document.getElementById('previewFileName');
    const container = document.getElementById('filePreviewContainer');

    fileName.textContent = file.name;

    // 根据文件类型生成预览内容
    let previewContent = '';

    switch (file.type) {
        case 'image':
            previewContent = `<img src="${file.thumbnail}" alt="${file.name}" class="preview-image">`;
            break;
        case 'document':
            if (file.extension === 'pdf') {
                previewContent = `<div class="preview-pdf">
                    <i class="fas fa-file-pdf"></i>
                    <p>PDF文件预览</p>
                    <p>${file.name}</p>
                </div>`;
            } else {
                previewContent = `<div class="preview-document">
                    <i class="fas fa-file-alt"></i>
                    <p>文档文件</p>
                    <p>${file.name}</p>
                    <p>大小: ${fileManager.formatFileSize(file.size)}</p>
                </div>`;
            }
            break;
        case 'video':
            previewContent = `<div class="preview-video">
                <i class="fas fa-play-circle"></i>
                <p>视频文件</p>
                <p>${file.name}</p>
                <p>大小: ${fileManager.formatFileSize(file.size)}</p>
            </div>`;
            break;
        case 'audio':
            previewContent = `<div class="preview-audio">
                <i class="fas fa-music"></i>
                <p>音频文件</p>
                <p>${file.name}</p>
                <p>大小: ${fileManager.formatFileSize(file.size)}</p>
            </div>`;
            break;
        default:
            previewContent = `<div class="preview-default">
                <i class="fas fa-file"></i>
                <p>文件预览</p>
                <p>${file.name}</p>
                <p>类型: ${file.extension.toUpperCase()}</p>
                <p>大小: ${fileManager.formatFileSize(file.size)}</p>
                <p>创建时间: ${fileManager.formatDate(file.createTime)}</p>
                <p>修改时间: ${fileManager.formatDate(file.modifyTime)}</p>
            </div>`;
    }

    container.innerHTML = previewContent;
    modal.style.display = 'flex';

    // 存储当前预览的文件ID
    modal.dataset.fileId = fileId;
}

function closeFilePreview() {
    const modal = document.getElementById('filePreviewModal');
    modal.style.display = 'none';
}

function downloadFile(fileId) {
    const file = fileManager.files.find(f => f.id === fileId);
    if (!file) return;

    // 模拟下载
    showNotification(`正在下载: ${file.name}`, 'info');

    // 实际项目中这里会触发真实的文件下载
    setTimeout(() => {
        showNotification(`下载完成: ${file.name}`, 'success');
    }, 2000);
}

function shareFile(fileId) {
    const file = fileManager.files.find(f => f.id === fileId);
    if (!file) return;

    // 生成分享链接
    const shareUrl = `${window.location.origin}/share/${file.id}`;

    // 复制到剪贴板
    navigator.clipboard.writeText(shareUrl).then(() => {
        showNotification(`分享链接已复制到剪贴板: ${file.name}`, 'success');
    }).catch(() => {
        showNotification(`分享链接: ${shareUrl}`, 'info');
    });
}

function deleteFile(fileId) {
    const file = fileManager.files.find(f => f.id === fileId);
    if (!file) return;

    if (confirm(`确定要删除 ${file.isFolder ? '文件夹' : '文件'} "${file.name}" 吗？`)) {
        fileManager.files = fileManager.files.filter(f => f.id !== fileId);
        fileManager.updateStats();
        fileManager.applyFilters();

        showNotification(`已删除: ${file.name}`, 'warning');
    }
}

// 批量操作方法
function bulkDownloadFiles() {
    if (!fileManager || fileManager.selectedFiles.size === 0) return;

    const selectedFiles = Array.from(fileManager.selectedFiles).map(id =>
        fileManager.files.find(f => f.id === id)
    ).filter(f => f && !f.isFolder);

    if (selectedFiles.length === 0) {
        showNotification('请选择要下载的文件', 'warning');
        return;
    }

    showNotification(`正在打包下载 ${selectedFiles.length} 个文件...`, 'info');

    setTimeout(() => {
        showNotification(`批量下载完成`, 'success');
        fileManager.selectedFiles.clear();
        fileManager.renderFiles();
    }, 3000);
}

function bulkMoveFiles() {
    if (!fileManager || fileManager.selectedFiles.size === 0) return;

    const targetPath = prompt('请输入目标路径:', '/');
    if (targetPath) {
        fileManager.selectedFiles.forEach(fileId => {
            const file = fileManager.files.find(f => f.id === fileId);
            if (file) {
                file.path = targetPath + '/' + file.name;
            }
        });

        fileManager.selectedFiles.clear();
        fileManager.applyFilters();
        showNotification(`已移动到: ${targetPath}`, 'success');
    }
}

function bulkCopyFiles() {
    if (!fileManager || fileManager.selectedFiles.size === 0) return;

    const targetPath = prompt('请输入目标路径:', '/');
    if (targetPath) {
        const selectedFiles = Array.from(fileManager.selectedFiles).map(id =>
            fileManager.files.find(f => f.id === id)
        );

        selectedFiles.forEach(file => {
            if (file) {
                const newFile = {
                    ...file,
                    id: Date.now() + Math.random(),
                    name: `${file.name}_副本`,
                    path: targetPath + '/' + file.name + '_副本',
                    createTime: new Date()
                };
                fileManager.files.push(newFile);
            }
        });

        fileManager.selectedFiles.clear();
        fileManager.updateStats();
        fileManager.applyFilters();
        showNotification(`已复制到: ${targetPath}`, 'success');
    }
}

function bulkDeleteFiles() {
    if (!fileManager || fileManager.selectedFiles.size === 0) return;

    if (confirm(`确定要删除选中的 ${fileManager.selectedFiles.size} 个项目吗？`)) {
        fileManager.files = fileManager.files.filter(f => !fileManager.selectedFiles.has(f.id));
        fileManager.selectedFiles.clear();
        fileManager.updateStats();
        fileManager.applyFilters();

        showNotification('批量删除完成', 'warning');
    }
}

// 文件上传相关方法
function showUploadModal() {
    const modal = document.getElementById('fileUploadModal');
    modal.style.display = 'flex';
}

function closeUploadModal() {
    const modal = document.getElementById('fileUploadModal');
    modal.style.display = 'none';

    // 重置上传状态
    document.getElementById('uploadList').innerHTML = '';
    document.getElementById('uploadBtn').disabled = true;
    document.getElementById('uploadProgress').style.display = 'none';
}

function selectFiles() {
    document.getElementById('fileInput').click();
}

function startUpload() {
    const uploadProgress = document.getElementById('uploadProgress');
    const progressFill = document.getElementById('progressFill');
    const progressText = document.getElementById('progressText');

    uploadProgress.style.display = 'block';

    // 模拟上传进度
    let progress = 0;
    const interval = setInterval(() => {
        progress += Math.random() * 15;
        if (progress >= 100) {
            progress = 100;
            clearInterval(interval);

            setTimeout(() => {
                showNotification('文件上传完成', 'success');
                closeUploadModal();
                refreshFiles();
            }, 500);
        }

        progressFill.style.width = progress + '%';
        progressText.textContent = Math.round(progress) + '%';
    }, 200);
}

function createNewFolder() {
    const folderName = prompt('请输入文件夹名称:', '新建文件夹');
    if (folderName) {
        const newFolder = {
            id: Date.now(),
            name: folderName,
            type: 'folder',
            path: fileManager.currentPath + '/' + folderName,
            size: 0,
            createTime: new Date(),
            modifyTime: new Date(),
            isFolder: true
        };

        fileManager.files.push(newFolder);
        fileManager.updateStats();
        fileManager.applyFilters();

        showNotification(`已创建文件夹: ${folderName}`, 'success');
    }
}

// 初始化文件管理
function initializeFileManagement() {
    fileManager = new FileManager();
    console.log('✅ 文件管理器已初始化');
}
