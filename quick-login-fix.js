// 快速登录修复脚本
(function() {
    console.log('🔧 开始快速登录修复...');
    
    // 设置登录状态
    localStorage.setItem('isLoggedIn', 'true');
    
    // 设置用户信息
    const userInfo = {
        username: 'admin',
        name: '张管理员',
        role: '系统管理员',
        email: '<EMAIL>',
        phone: '13800138001',
        department: '技术部',
        avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=40&h=40&fit=crop&crop=face'
    };
    
    localStorage.setItem('currentUser', JSON.stringify(userInfo));
    
    console.log('✅ 登录状态已修复');
    console.log('✅ 用户信息已设置:', userInfo);
    
    // 检查关键函数是否存在
    const criticalFunctions = [
        'initializeApp',
        'initializeNavigation', 
        'initializeUserManagement',
        'showNotification'
    ];
    
    criticalFunctions.forEach(funcName => {
        if (typeof window[funcName] === 'function') {
            console.log(`✅ ${funcName} 函数可用`);
        } else {
            console.error(`❌ ${funcName} 函数不可用`);
        }
    });
    
    // 尝试重新初始化应用
    if (typeof initializeApp === 'function') {
        try {
            initializeApp();
            console.log('✅ 应用重新初始化成功');
        } catch (error) {
            console.error('❌ 应用初始化失败:', error);
        }
    }
    
    console.log('🎉 快速修复完成！');
})();