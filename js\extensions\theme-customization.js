// 主题定制系统
class ThemeCustomization {
    constructor() {
        this.currentTheme = 'default';
        this.customThemes = {};
        this.themePresets = {};
        this.colorSchemes = {};
        this.fontOptions = {};
        this.animationSettings = {};
        
        this.initializeThemeSystem();
        this.loadThemePresets();
        this.loadStoredTheme();
    }

    initializeThemeSystem() {
        this.createThemeCustomizer();
        this.bindThemeEvents();
        this.setupColorSchemes();
        this.setupFontOptions();
        this.setupAnimationSettings();
    }

    createThemeCustomizer() {
        const customizer = document.createElement('div');
        customizer.id = 'themeCustomizer';
        customizer.className = 'theme-customizer';
        customizer.innerHTML = `
            <div class="customizer-header">
                <h3>
                    <i class="fas fa-palette"></i>
                    主题定制
                </h3>
                <div class="customizer-actions">
                    <button class="btn-secondary" onclick="themeCustomizer.resetTheme()">
                        <i class="fas fa-undo"></i>
                        重置
                    </button>
                    <button class="btn-primary" onclick="themeCustomizer.saveTheme()">
                        <i class="fas fa-save"></i>
                        保存
                    </button>
                    <button class="btn-icon" onclick="themeCustomizer.closeCustomizer()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            </div>
            
            <div class="customizer-body">
                <div class="customizer-tabs">
                    <button class="tab-btn active" data-tab="presets">
                        <i class="fas fa-swatchbook"></i>
                        预设主题
                    </button>
                    <button class="tab-btn" data-tab="colors">
                        <i class="fas fa-palette"></i>
                        颜色配置
                    </button>
                    <button class="tab-btn" data-tab="typography">
                        <i class="fas fa-font"></i>
                        字体设置
                    </button>
                    <button class="tab-btn" data-tab="layout">
                        <i class="fas fa-th-large"></i>
                        布局设置
                    </button>
                    <button class="tab-btn" data-tab="effects">
                        <i class="fas fa-magic"></i>
                        视觉效果
                    </button>
                </div>
                
                <div class="customizer-content">
                    <!-- 预设主题 -->
                    <div class="tab-content active" id="presetsTab">
                        <div class="theme-presets">
                            <h4>选择预设主题</h4>
                            <div class="preset-grid" id="presetGrid">
                                <!-- 预设主题网格 -->
                            </div>
                        </div>
                        
                        <div class="custom-themes">
                            <h4>我的主题</h4>
                            <div class="custom-theme-list" id="customThemeList">
                                <!-- 自定义主题列表 -->
                            </div>
                        </div>
                    </div>
                    
                    <!-- 颜色配置 -->
                    <div class="tab-content" id="colorsTab">
                        <div class="color-section">
                            <h4>主色调</h4>
                            <div class="color-group">
                                <div class="color-item">
                                    <label>主要颜色</label>
                                    <div class="color-input-group">
                                        <input type="color" id="primaryColor" value="#6366f1">
                                        <input type="text" id="primaryColorText" value="#6366f1">
                                    </div>
                                </div>
                                <div class="color-item">
                                    <label>次要颜色</label>
                                    <div class="color-input-group">
                                        <input type="color" id="secondaryColor" value="#8b5cf6">
                                        <input type="text" id="secondaryColorText" value="#8b5cf6">
                                    </div>
                                </div>
                                <div class="color-item">
                                    <label>强调颜色</label>
                                    <div class="color-input-group">
                                        <input type="color" id="accentColor" value="#10b981">
                                        <input type="text" id="accentColorText" value="#10b981">
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="color-section">
                            <h4>背景颜色</h4>
                            <div class="color-group">
                                <div class="color-item">
                                    <label>主背景</label>
                                    <div class="color-input-group">
                                        <input type="color" id="backgroundColor" value="#f8fafc">
                                        <input type="text" id="backgroundColorText" value="#f8fafc">
                                    </div>
                                </div>
                                <div class="color-item">
                                    <label>卡片背景</label>
                                    <div class="color-input-group">
                                        <input type="color" id="cardBackground" value="#ffffff">
                                        <input type="text" id="cardBackgroundText" value="#ffffff">
                                    </div>
                                </div>
                                <div class="color-item">
                                    <label>侧边栏背景</label>
                                    <div class="color-input-group">
                                        <input type="color" id="sidebarBackground" value="#1e293b">
                                        <input type="text" id="sidebarBackgroundText" value="#1e293b">
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="color-section">
                            <h4>文本颜色</h4>
                            <div class="color-group">
                                <div class="color-item">
                                    <label>主要文本</label>
                                    <div class="color-input-group">
                                        <input type="color" id="textPrimary" value="#1e293b">
                                        <input type="text" id="textPrimaryText" value="#1e293b">
                                    </div>
                                </div>
                                <div class="color-item">
                                    <label>次要文本</label>
                                    <div class="color-input-group">
                                        <input type="color" id="textSecondary" value="#64748b">
                                        <input type="text" id="textSecondaryText" value="#64748b">
                                    </div>
                                </div>
                                <div class="color-item">
                                    <label>辅助文本</label>
                                    <div class="color-input-group">
                                        <input type="color" id="textMuted" value="#94a3b8">
                                        <input type="text" id="textMutedText" value="#94a3b8">
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 字体设置 -->
                    <div class="tab-content" id="typographyTab">
                        <div class="typography-section">
                            <h4>字体族</h4>
                            <div class="font-group">
                                <div class="font-item">
                                    <label>主要字体</label>
                                    <select id="primaryFont">
                                        <option value="Inter">Inter</option>
                                        <option value="Roboto">Roboto</option>
                                        <option value="Noto Sans SC">Noto Sans SC</option>
                                        <option value="PingFang SC">PingFang SC</option>
                                        <option value="Microsoft YaHei">Microsoft YaHei</option>
                                        <option value="Arial">Arial</option>
                                        <option value="Helvetica">Helvetica</option>
                                    </select>
                                </div>
                                <div class="font-item">
                                    <label>代码字体</label>
                                    <select id="codeFont">
                                        <option value="Fira Code">Fira Code</option>
                                        <option value="Source Code Pro">Source Code Pro</option>
                                        <option value="Monaco">Monaco</option>
                                        <option value="Consolas">Consolas</option>
                                        <option value="Courier New">Courier New</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        
                        <div class="typography-section">
                            <h4>字体大小</h4>
                            <div class="size-group">
                                <div class="size-item">
                                    <label>基础字体大小</label>
                                    <div class="size-input">
                                        <input type="range" id="baseFontSize" min="12" max="18" value="14" step="1">
                                        <span id="baseFontSizeValue">14px</span>
                                    </div>
                                </div>
                                <div class="size-item">
                                    <label>标题缩放比例</label>
                                    <div class="size-input">
                                        <input type="range" id="headingScale" min="1.1" max="1.5" value="1.25" step="0.05">
                                        <span id="headingScaleValue">1.25</span>
                                    </div>
                                </div>
                                <div class="size-item">
                                    <label>行高</label>
                                    <div class="size-input">
                                        <input type="range" id="lineHeight" min="1.2" max="2.0" value="1.6" step="0.1">
                                        <span id="lineHeightValue">1.6</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 布局设置 -->
                    <div class="tab-content" id="layoutTab">
                        <div class="layout-section">
                            <h4>侧边栏</h4>
                            <div class="layout-group">
                                <div class="layout-item">
                                    <label>侧边栏宽度</label>
                                    <div class="size-input">
                                        <input type="range" id="sidebarWidth" min="200" max="350" value="280" step="10">
                                        <span id="sidebarWidthValue">280px</span>
                                    </div>
                                </div>
                                <div class="layout-item">
                                    <label class="checkbox-label">
                                        <input type="checkbox" id="sidebarCollapsible" checked>
                                        <span class="checkmark"></span>
                                        可折叠侧边栏
                                    </label>
                                </div>
                                <div class="layout-item">
                                    <label class="checkbox-label">
                                        <input type="checkbox" id="sidebarAutoHide">
                                        <span class="checkmark"></span>
                                        自动隐藏
                                    </label>
                                </div>
                            </div>
                        </div>
                        
                        <div class="layout-section">
                            <h4>内容区域</h4>
                            <div class="layout-group">
                                <div class="layout-item">
                                    <label>内容最大宽度</label>
                                    <div class="size-input">
                                        <input type="range" id="contentMaxWidth" min="1200" max="1800" value="1400" step="50">
                                        <span id="contentMaxWidthValue">1400px</span>
                                    </div>
                                </div>
                                <div class="layout-item">
                                    <label>卡片间距</label>
                                    <div class="size-input">
                                        <input type="range" id="cardSpacing" min="16" max="32" value="24" step="4">
                                        <span id="cardSpacingValue">24px</span>
                                    </div>
                                </div>
                                <div class="layout-item">
                                    <label>页面内边距</label>
                                    <div class="size-input">
                                        <input type="range" id="pagePadding" min="16" max="48" value="32" step="4">
                                        <span id="pagePaddingValue">32px</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 视觉效果 -->
                    <div class="tab-content" id="effectsTab">
                        <div class="effects-section">
                            <h4>动画效果</h4>
                            <div class="effects-group">
                                <div class="effect-item">
                                    <label class="checkbox-label">
                                        <input type="checkbox" id="enableAnimations" checked>
                                        <span class="checkmark"></span>
                                        启用动画
                                    </label>
                                </div>
                                <div class="effect-item">
                                    <label>动画速度</label>
                                    <div class="size-input">
                                        <input type="range" id="animationSpeed" min="0.1" max="1.0" value="0.3" step="0.1">
                                        <span id="animationSpeedValue">0.3s</span>
                                    </div>
                                </div>
                                <div class="effect-item">
                                    <label class="checkbox-label">
                                        <input type="checkbox" id="enableHoverEffects" checked>
                                        <span class="checkmark"></span>
                                        悬停效果
                                    </label>
                                </div>
                            </div>
                        </div>
                        
                        <div class="effects-section">
                            <h4>视觉效果</h4>
                            <div class="effects-group">
                                <div class="effect-item">
                                    <label>圆角大小</label>
                                    <div class="size-input">
                                        <input type="range" id="borderRadius" min="4" max="20" value="12" step="2">
                                        <span id="borderRadiusValue">12px</span>
                                    </div>
                                </div>
                                <div class="effect-item">
                                    <label>阴影强度</label>
                                    <div class="size-input">
                                        <input type="range" id="shadowIntensity" min="0" max="100" value="50" step="10">
                                        <span id="shadowIntensityValue">50%</span>
                                    </div>
                                </div>
                                <div class="effect-item">
                                    <label class="checkbox-label">
                                        <input type="checkbox" id="enableBlur" checked>
                                        <span class="checkmark"></span>
                                        毛玻璃效果
                                    </label>
                                </div>
                                <div class="effect-item">
                                    <label class="checkbox-label">
                                        <input type="checkbox" id="enableGradients" checked>
                                        <span class="checkmark"></span>
                                        渐变效果
                                    </label>
                                </div>
                            </div>
                        </div>
                        
                        <div class="effects-section">
                            <h4>深色模式</h4>
                            <div class="effects-group">
                                <div class="effect-item">
                                    <label class="checkbox-label">
                                        <input type="checkbox" id="enableDarkMode">
                                        <span class="checkmark"></span>
                                        启用深色模式
                                    </label>
                                </div>
                                <div class="effect-item">
                                    <label class="checkbox-label">
                                        <input type="checkbox" id="autoSwitchTheme">
                                        <span class="checkmark"></span>
                                        跟随系统主题
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="customizer-footer">
                <div class="theme-preview">
                    <h4>实时预览</h4>
                    <div class="preview-container">
                        <div class="preview-card">
                            <div class="preview-header">
                                <h5>示例卡片</h5>
                                <button class="preview-btn">按钮</button>
                            </div>
                            <p>这是一个示例文本，用于预览当前主题效果。</p>
                            <div class="preview-stats">
                                <div class="preview-stat">
                                    <span class="stat-number">123</span>
                                    <span class="stat-label">数据</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
        
        document.body.appendChild(customizer);
    }

    loadThemePresets() {
        this.themePresets = {
            default: {
                name: '默认主题',
                description: '清新简洁的默认主题',
                colors: {
                    primary: '#6366f1',
                    secondary: '#8b5cf6',
                    accent: '#10b981',
                    background: '#f8fafc',
                    cardBackground: '#ffffff',
                    sidebarBackground: '#1e293b',
                    textPrimary: '#1e293b',
                    textSecondary: '#64748b',
                    textMuted: '#94a3b8'
                },
                preview: 'linear-gradient(135deg, #6366f1, #8b5cf6)'
            },
            ocean: {
                name: '海洋主题',
                description: '深邃的海洋蓝色主题',
                colors: {
                    primary: '#0ea5e9',
                    secondary: '#06b6d4',
                    accent: '#10b981',
                    background: '#f0f9ff',
                    cardBackground: '#ffffff',
                    sidebarBackground: '#0c4a6e',
                    textPrimary: '#0c4a6e',
                    textSecondary: '#0369a1',
                    textMuted: '#0284c7'
                },
                preview: 'linear-gradient(135deg, #0ea5e9, #06b6d4)'
            },
            sunset: {
                name: '日落主题',
                description: '温暖的日落橙色主题',
                colors: {
                    primary: '#f97316',
                    secondary: '#ea580c',
                    accent: '#eab308',
                    background: '#fffbeb',
                    cardBackground: '#ffffff',
                    sidebarBackground: '#9a3412',
                    textPrimary: '#9a3412',
                    textSecondary: '#c2410c',
                    textMuted: '#ea580c'
                },
                preview: 'linear-gradient(135deg, #f97316, #ea580c)'
            },
            forest: {
                name: '森林主题',
                description: '自然的绿色森林主题',
                colors: {
                    primary: '#059669',
                    secondary: '#047857',
                    accent: '#65a30d',
                    background: '#f0fdf4',
                    cardBackground: '#ffffff',
                    sidebarBackground: '#14532d',
                    textPrimary: '#14532d',
                    textSecondary: '#166534',
                    textMuted: '#22c55e'
                },
                preview: 'linear-gradient(135deg, #059669, #047857)'
            },
            purple: {
                name: '紫色主题',
                description: '优雅的紫色主题',
                colors: {
                    primary: '#9333ea',
                    secondary: '#7c3aed',
                    accent: '#c084fc',
                    background: '#faf5ff',
                    cardBackground: '#ffffff',
                    sidebarBackground: '#581c87',
                    textPrimary: '#581c87',
                    textSecondary: '#7c2d92',
                    textMuted: '#a855f7'
                },
                preview: 'linear-gradient(135deg, #9333ea, #7c3aed)'
            },
            dark: {
                name: '深色主题',
                description: '护眼的深色主题',
                colors: {
                    primary: '#818cf8',
                    secondary: '#a78bfa',
                    accent: '#34d399',
                    background: '#0f172a',
                    cardBackground: '#1e293b',
                    sidebarBackground: '#020617',
                    textPrimary: '#f1f5f9',
                    textSecondary: '#cbd5e1',
                    textMuted: '#94a3b8'
                },
                preview: 'linear-gradient(135deg, #0f172a, #1e293b)'
            }
        };
        
        this.renderThemePresets();
    }

    renderThemePresets() {
        const presetGrid = document.getElementById('presetGrid');
        if (!presetGrid) return;
        
        presetGrid.innerHTML = Object.entries(this.themePresets).map(([key, theme]) => `
            <div class="preset-card ${key === this.currentTheme ? 'active' : ''}" 
                 onclick="themeCustomizer.applyPreset('${key}')">
                <div class="preset-preview" style="background: ${theme.preview}"></div>
                <div class="preset-info">
                    <h5>${theme.name}</h5>
                    <p>${theme.description}</p>
                </div>
                ${key === this.currentTheme ? '<i class="fas fa-check preset-check"></i>' : ''}
            </div>
        `).join('');
    }

    bindThemeEvents() {
        // 标签切换
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('tab-btn') && e.target.closest('.theme-customizer')) {
                this.switchCustomizerTab(e.target);
            }
        });
        
        // 颜色输入同步
        this.bindColorInputs();
        
        // 范围输入更新
        this.bindRangeInputs();
        
        // 复选框变更
        this.bindCheckboxInputs();
        
        // 选择框变更
        this.bindSelectInputs();
    }

    bindColorInputs() {
        const colorInputs = [
            'primaryColor', 'secondaryColor', 'accentColor',
            'backgroundColor', 'cardBackground', 'sidebarBackground',
            'textPrimary', 'textSecondary', 'textMuted'
        ];
        
        colorInputs.forEach(inputId => {
            const colorInput = document.getElementById(inputId);
            const textInput = document.getElementById(inputId + 'Text');
            
            if (colorInput && textInput) {
                colorInput.addEventListener('input', (e) => {
                    textInput.value = e.target.value;
                    this.updateThemeColor(inputId, e.target.value);
                });
                
                textInput.addEventListener('input', (e) => {
                    if (this.isValidColor(e.target.value)) {
                        colorInput.value = e.target.value;
                        this.updateThemeColor(inputId, e.target.value);
                    }
                });
            }
        });
    }

    bindRangeInputs() {
        const rangeInputs = [
            'baseFontSize', 'headingScale', 'lineHeight',
            'sidebarWidth', 'contentMaxWidth', 'cardSpacing', 'pagePadding',
            'animationSpeed', 'borderRadius', 'shadowIntensity'
        ];
        
        rangeInputs.forEach(inputId => {
            const rangeInput = document.getElementById(inputId);
            const valueDisplay = document.getElementById(inputId + 'Value');
            
            if (rangeInput && valueDisplay) {
                rangeInput.addEventListener('input', (e) => {
                    const value = e.target.value;
                    const unit = this.getRangeInputUnit(inputId);
                    valueDisplay.textContent = value + unit;
                    this.updateThemeProperty(inputId, value);
                });
            }
        });
    }

    bindCheckboxInputs() {
        const checkboxInputs = [
            'sidebarCollapsible', 'sidebarAutoHide', 'enableAnimations',
            'enableHoverEffects', 'enableBlur', 'enableGradients',
            'enableDarkMode', 'autoSwitchTheme'
        ];
        
        checkboxInputs.forEach(inputId => {
            const checkbox = document.getElementById(inputId);
            if (checkbox) {
                checkbox.addEventListener('change', (e) => {
                    this.updateThemeProperty(inputId, e.target.checked);
                });
            }
        });
    }

    bindSelectInputs() {
        const selectInputs = ['primaryFont', 'codeFont'];
        
        selectInputs.forEach(inputId => {
            const select = document.getElementById(inputId);
            if (select) {
                select.addEventListener('change', (e) => {
                    this.updateThemeProperty(inputId, e.target.value);
                });
            }
        });
    }

    switchCustomizerTab(tabBtn) {
        const customizer = tabBtn.closest('.theme-customizer');
        const tabName = tabBtn.dataset.tab;
        
        // 更新标签状态
        customizer.querySelectorAll('.tab-btn').forEach(btn => btn.classList.remove('active'));
        customizer.querySelectorAll('.tab-content').forEach(content => content.classList.remove('active'));
        
        tabBtn.classList.add('active');
        customizer.querySelector(`#${tabName}Tab`).classList.add('active');
    }

    applyPreset(presetKey) {
        const preset = this.themePresets[presetKey];
        if (!preset) return;
        
        this.currentTheme = presetKey;
        
        // 应用预设颜色
        Object.entries(preset.colors).forEach(([key, value]) => {
            this.updateThemeColor(this.getColorInputId(key), value);
            this.updateColorInputs(this.getColorInputId(key), value);
        });
        
        // 更新预设卡片状态
        this.renderThemePresets();
        
        if (window.showNotification) {
            showNotification(`已应用${preset.name}`, 'success');
        }
    }

    getColorInputId(colorKey) {
        const mapping = {
            primary: 'primaryColor',
            secondary: 'secondaryColor',
            accent: 'accentColor',
            background: 'backgroundColor',
            cardBackground: 'cardBackground',
            sidebarBackground: 'sidebarBackground',
            textPrimary: 'textPrimary',
            textSecondary: 'textSecondary',
            textMuted: 'textMuted'
        };
        return mapping[colorKey] || colorKey;
    }

    updateColorInputs(inputId, value) {
        const colorInput = document.getElementById(inputId);
        const textInput = document.getElementById(inputId + 'Text');
        
        if (colorInput) colorInput.value = value;
        if (textInput) textInput.value = value;
    }

    updateThemeColor(property, value) {
        const cssProperty = this.getCSSProperty(property);
        document.documentElement.style.setProperty(cssProperty, value);
        this.updatePreview();
    }

    updateThemeProperty(property, value) {
        const cssProperty = this.getCSSProperty(property);
        const cssValue = this.getCSSValue(property, value);
        
        if (cssProperty && cssValue !== null) {
            document.documentElement.style.setProperty(cssProperty, cssValue);
        }
        
        this.updatePreview();
    }

    getCSSProperty(property) {
        const mapping = {
            primaryColor: '--primary-color',
            secondaryColor: '--secondary-color',
            accentColor: '--accent-color',
            backgroundColor: '--background-color',
            cardBackground: '--card-background',
            sidebarBackground: '--sidebar-background',
            textPrimary: '--text-primary',
            textSecondary: '--text-secondary',
            textMuted: '--text-muted',
            baseFontSize: '--base-font-size',
            headingScale: '--heading-scale',
            lineHeight: '--line-height',
            sidebarWidth: '--sidebar-width',
            contentMaxWidth: '--content-max-width',
            cardSpacing: '--card-spacing',
            pagePadding: '--page-padding',
            animationSpeed: '--animation-speed',
            borderRadius: '--border-radius',
            shadowIntensity: '--shadow-intensity',
            primaryFont: '--primary-font',
            codeFont: '--code-font'
        };
        return mapping[property];
    }

    getCSSValue(property, value) {
        const unitMapping = {
            baseFontSize: 'px',
            sidebarWidth: 'px',
            contentMaxWidth: 'px',
            cardSpacing: 'px',
            pagePadding: 'px',
            animationSpeed: 's',
            borderRadius: 'px'
        };
        
        if (unitMapping[property]) {
            return value + unitMapping[property];
        }
        
        if (property === 'shadowIntensity') {
            return (value / 100).toString();
        }
        
        if (property === 'primaryFont' || property === 'codeFont') {
            return `"${value}", sans-serif`;
        }
        
        return value;
    }

    getRangeInputUnit(inputId) {
        const units = {
            baseFontSize: 'px',
            sidebarWidth: 'px',
            contentMaxWidth: 'px',
            cardSpacing: 'px',
            pagePadding: 'px',
            animationSpeed: 's',
            borderRadius: 'px',
            shadowIntensity: '%',
            headingScale: '',
            lineHeight: ''
        };
        return units[inputId] || '';
    }

    updatePreview() {
        // 更新实时预览
        const previewCard = document.querySelector('.preview-card');
        if (previewCard) {
            // 应用当前主题样式到预览卡片
            const computedStyle = getComputedStyle(document.documentElement);
            previewCard.style.background = computedStyle.getPropertyValue('--card-background');
            previewCard.style.color = computedStyle.getPropertyValue('--text-primary');
            previewCard.style.borderRadius = computedStyle.getPropertyValue('--border-radius');
        }
    }

    isValidColor(color) {
        const style = new Option().style;
        style.color = color;
        return style.color !== '';
    }

    showCustomizer() {
        const customizer = document.getElementById('themeCustomizer');
        if (customizer) {
            customizer.classList.add('show');
        }
    }

    closeCustomizer() {
        const customizer = document.getElementById('themeCustomizer');
        if (customizer) {
            customizer.classList.remove('show');
        }
    }

    saveTheme() {
        const themeData = this.collectCurrentTheme();
        const themeName = prompt('请输入主题名称:', '我的自定义主题');
        
        if (themeName) {
            themeData.name = themeName;
            themeData.id = 'custom-' + Date.now();
            
            this.customThemes[themeData.id] = themeData;
            this.saveCustomThemes();
            
            if (window.showNotification) {
                showNotification('主题已保存', 'success');
            }
        }
    }

    resetTheme() {
        if (confirm('确定要重置为默认主题吗？')) {
            this.applyPreset('default');
        }
    }

    collectCurrentTheme() {
        const style = getComputedStyle(document.documentElement);
        
        return {
            colors: {
                primary: style.getPropertyValue('--primary-color').trim(),
                secondary: style.getPropertyValue('--secondary-color').trim(),
                accent: style.getPropertyValue('--accent-color').trim(),
                background: style.getPropertyValue('--background-color').trim(),
                cardBackground: style.getPropertyValue('--card-background').trim(),
                sidebarBackground: style.getPropertyValue('--sidebar-background').trim(),
                textPrimary: style.getPropertyValue('--text-primary').trim(),
                textSecondary: style.getPropertyValue('--text-secondary').trim(),
                textMuted: style.getPropertyValue('--text-muted').trim()
            },
            typography: {
                baseFontSize: style.getPropertyValue('--base-font-size').trim(),
                primaryFont: style.getPropertyValue('--primary-font').trim(),
                lineHeight: style.getPropertyValue('--line-height').trim()
            },
            layout: {
                sidebarWidth: style.getPropertyValue('--sidebar-width').trim(),
                borderRadius: style.getPropertyValue('--border-radius').trim()
            },
            createdAt: new Date()
        };
    }

    saveCustomThemes() {
        try {
            localStorage.setItem('customThemes', JSON.stringify(this.customThemes));
        } catch (error) {
            console.error('保存自定义主题失败:', error);
        }
    }

    loadStoredTheme() {
        try {
            const stored = localStorage.getItem('currentTheme');
            if (stored) {
                const themeData = JSON.parse(stored);
                this.applyThemeData(themeData);
            }
            
            const customThemes = localStorage.getItem('customThemes');
            if (customThemes) {
                this.customThemes = JSON.parse(customThemes);
            }
        } catch (error) {
            console.error('加载主题失败:', error);
        }
    }

    applyThemeData(themeData) {
        if (themeData.colors) {
            Object.entries(themeData.colors).forEach(([key, value]) => {
                this.updateThemeColor(this.getColorInputId(key), value);
            });
        }
        
        if (themeData.typography) {
            Object.entries(themeData.typography).forEach(([key, value]) => {
                this.updateThemeProperty(key, value);
            });
        }
        
        if (themeData.layout) {
            Object.entries(themeData.layout).forEach(([key, value]) => {
                this.updateThemeProperty(key, value);
            });
        }
    }
}

// 全局主题定制器实例
let themeCustomizer = null;

// 初始化主题定制系统
function initializeThemeCustomization() {
    themeCustomizer = new ThemeCustomization();
    console.log('✅ 主题定制系统已初始化');
}

// 显示主题定制器
function showThemeCustomizer() {
    if (themeCustomizer) {
        themeCustomizer.showCustomizer();
    }
}

// 页面加载时初始化
document.addEventListener('DOMContentLoaded', function() {
    setTimeout(initializeThemeCustomization, 900);
});
