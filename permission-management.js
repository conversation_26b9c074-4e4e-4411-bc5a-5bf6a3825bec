// 权限管理系统

// 权限定义
const permissions = {
    'dashboard': {
        name: '仪表盘',
        actions: ['view']
    },
    'users': {
        name: '用户管理',
        actions: ['view', 'create', 'edit', 'delete', 'export']
    },
    'analytics': {
        name: '数据分析',
        actions: ['view', 'export']
    },
    'orders': {
        name: '订单管理',
        actions: ['view', 'create', 'edit', 'delete', 'export']
    },
    'inventory': {
        name: '库存管理',
        actions: ['view', 'create', 'edit', 'delete', 'stock_in', 'stock_out']
    },
    'reports': {
        name: '报表中心',
        actions: ['view', 'create', 'edit', 'delete', 'export']
    },
    'files': {
        name: '文件管理',
        actions: ['view', 'upload', 'download', 'delete', 'share']
    },
    'permissions': {
        name: '权限管理',
        actions: ['view', 'create', 'edit', 'delete']
    },
    'settings': {
        name: '系统设置',
        actions: ['view', 'edit']
    }
};

// 角色数据模拟
const roles = [
    {
        id: 1,
        name: '超级管理员',
        description: '拥有系统所有权限',
        type: 'system',
        permissions: getAllPermissions(),
        userCount: 2,
        createdAt: '2024-01-01',
        createdBy: '系统',
        isActive: true
    },
    {
        id: 2,
        name: '管理员',
        description: '拥有大部分管理权限',
        type: 'system',
        permissions: {
            'dashboard': ['view'],
            'users': ['view', 'create', 'edit'],
            'analytics': ['view', 'export'],
            'orders': ['view', 'create', 'edit', 'delete'],
            'inventory': ['view', 'create', 'edit', 'stock_in', 'stock_out'],
            'reports': ['view', 'create', 'export'],
            'files': ['view', 'upload', 'download', 'share'],
            'settings': ['view', 'edit']
        },
        userCount: 5,
        createdAt: '2024-01-01',
        createdBy: '系统',
        isActive: true
    },
    {
        id: 3,
        name: '普通用户',
        description: '基础查看权限',
        type: 'system',
        permissions: {
            'dashboard': ['view'],
            'analytics': ['view'],
            'orders': ['view'],
            'inventory': ['view'],
            'reports': ['view'],
            'files': ['view', 'download']
        },
        userCount: 15,
        createdAt: '2024-01-01',
        createdBy: '系统',
        isActive: true
    },
    {
        id: 4,
        name: '销售员',
        description: '销售相关权限',
        type: 'custom',
        permissions: {
            'dashboard': ['view'],
            'orders': ['view', 'create', 'edit'],
            'inventory': ['view'],
            'reports': ['view'],
            'files': ['view', 'download']
        },
        userCount: 8,
        createdAt: '2024-01-10',
        createdBy: '张管理员',
        isActive: true
    }
];

// 用户权限分配
const userPermissions = [
    {
        id: 1,
        userId: 1,
        userName: '张管理员',
        roleId: 1,
        roleName: '超级管理员',
        assignedAt: '2024-01-01',
        assignedBy: '系统',
        expiry: null,
        isActive: true
    },
    {
        id: 2,
        userId: 2,
        userName: '李经理',
        roleId: 2,
        roleName: '管理员',
        assignedAt: '2024-01-05',
        assignedBy: '张管理员',
        expiry: null,
        isActive: true
    },
    {
        id: 3,
        userId: 3,
        userName: '王分析师',
        roleId: 3,
        roleName: '普通用户',
        assignedAt: '2024-01-08',
        assignedBy: '张管理员',
        expiry: '2024-12-31',
        isActive: true
    }
];

// 权限日志
const permissionLogs = [
    {
        id: 1,
        action: 'grant',
        description: '为用户"李经理"分配了"管理员"角色',
        userId: 2,
        userName: '李经理',
        operatorId: 1,
        operatorName: '张管理员',
        timestamp: '2024-01-15 14:30:00',
        details: {
            roleId: 2,
            roleName: '管理员'
        }
    },
    {
        id: 2,
        action: 'modify',
        description: '修改了"销售员"角色的权限设置',
        roleId: 4,
        roleName: '销售员',
        operatorId: 1,
        operatorName: '张管理员',
        timestamp: '2024-01-14 16:45:00',
        details: {
            changes: ['添加了订单编辑权限']
        }
    },
    {
        id: 3,
        action: 'revoke',
        description: '撤销了用户"赵员工"的文件上传权限',
        userId: 5,
        userName: '赵员工',
        operatorId: 2,
        operatorName: '李经理',
        timestamp: '2024-01-13 10:20:00',
        details: {
            permission: 'files.upload'
        }
    }
];

let filteredRoles = [...roles];
let filteredLogs = [...permissionLogs];
let editingRoleId = null;

// 获取所有权限
function getAllPermissions() {
    const allPerms = {};
    Object.keys(permissions).forEach(module => {
        allPerms[module] = [...permissions[module].actions];
    });
    return allPerms;
}

// 初始化权限管理
function initializePermissionManagement() {
    updatePermissionStats();
    renderRolesList();
    renderUserPermissions();
    renderPermissionLogs();
    populateRoleSelects();
    populateUserSelects();
}

// 更新权限统计
function updatePermissionStats() {
    const totalRoles = roles.length;
    const totalPermissions = Object.keys(permissions).reduce((sum, module) => 
        sum + permissions[module].actions.length, 0);
    const activeUsers = userPermissions.filter(up => up.isActive).length;
    const securityAlerts = permissionLogs.filter(log => 
        log.action === 'revoke' && isRecent(log.timestamp)).length;

    document.getElementById('totalRoles').textContent = totalRoles;
    document.getElementById('totalPermissions').textContent = totalPermissions;
    document.getElementById('activeUsers').textContent = activeUsers;
    document.getElementById('securityAlerts').textContent = securityAlerts;
}

// 检查是否是最近的记录
function isRecent(timestamp) {
    const logDate = new Date(timestamp);
    const now = new Date();
    const diffDays = (now - logDate) / (1000 * 60 * 60 * 24);
    return diffDays <= 7; // 7天内
}

// 渲染角色列表
function renderRolesList() {
    const container = document.getElementById('rolesList');
    if (!container) return;

    if (filteredRoles.length === 0) {
        container.innerHTML = `
            <div class="empty-state">
                <i class="fas fa-users-cog"></i>
                <h3>暂无角色</h3>
                <p>还没有创建任何角色</p>
            </div>
        `;
        return;
    }

    container.innerHTML = filteredRoles.map(role => `
        <div class="role-item">
            <div class="role-header">
                <div class="role-info">
                    <h4 class="role-name">${role.name}</h4>
                    <span class="role-type ${role.type}">${role.type === 'system' ? '系统角色' : '自定义角色'}</span>
                </div>
                <div class="role-status">
                    <span class="status-badge ${role.isActive ? 'success' : 'danger'}">
                        ${role.isActive ? '启用' : '禁用'}
                    </span>
                </div>
            </div>
            <p class="role-description">${role.description}</p>
            <div class="role-meta">
                <span class="role-users">
                    <i class="fas fa-users"></i>
                    ${role.userCount} 个用户
                </span>
                <span class="role-permissions">
                    <i class="fas fa-key"></i>
                    ${getPermissionCount(role.permissions)} 个权限
                </span>
                <span class="role-created">
                    <i class="fas fa-calendar"></i>
                    ${role.createdAt}
                </span>
            </div>
            <div class="role-actions">
                <button class="btn-icon" onclick="editRole(${role.id})" title="编辑">
                    <i class="fas fa-edit"></i>
                </button>
                <button class="btn-icon" onclick="viewRolePermissions(${role.id})" title="查看权限">
                    <i class="fas fa-eye"></i>
                </button>
                <button class="btn-icon" onclick="duplicateRole(${role.id})" title="复制">
                    <i class="fas fa-copy"></i>
                </button>
                ${role.type === 'custom' ? `
                    <button class="btn-icon danger" onclick="deleteRole(${role.id})" title="删除">
                        <i class="fas fa-trash"></i>
                    </button>
                ` : ''}
            </div>
        </div>
    `).join('');
}

// 获取权限数量
function getPermissionCount(rolePermissions) {
    return Object.values(rolePermissions).reduce((sum, actions) => sum + actions.length, 0);
}

// 渲染用户权限
function renderUserPermissions() {
    const container = document.getElementById('userPermissions');
    if (!container) return;

    if (userPermissions.length === 0) {
        container.innerHTML = `
            <div class="empty-state">
                <i class="fas fa-user-shield"></i>
                <h3>暂无用户权限</h3>
                <p>还没有为用户分配权限</p>
            </div>
        `;
        return;
    }

    container.innerHTML = userPermissions.map(up => `
        <div class="user-permission-item">
            <div class="user-info">
                <div class="user-avatar">
                    <i class="fas fa-user"></i>
                </div>
                <div class="user-details">
                    <h4 class="user-name">${up.userName}</h4>
                    <span class="user-role">${up.roleName}</span>
                </div>
            </div>
            <div class="permission-info">
                <span class="permission-assigned">
                    <i class="fas fa-calendar-plus"></i>
                    ${up.assignedAt}
                </span>
                ${up.expiry ? `
                    <span class="permission-expiry">
                        <i class="fas fa-calendar-times"></i>
                        ${up.expiry}
                    </span>
                ` : ''}
                <span class="permission-status ${up.isActive ? 'active' : 'inactive'}">
                    ${up.isActive ? '有效' : '无效'}
                </span>
            </div>
            <div class="permission-actions">
                <button class="btn-icon" onclick="editUserPermission(${up.id})" title="编辑">
                    <i class="fas fa-edit"></i>
                </button>
                <button class="btn-icon danger" onclick="revokeUserPermission(${up.id})" title="撤销">
                    <i class="fas fa-user-times"></i>
                </button>
            </div>
        </div>
    `).join('');
}

// 渲染权限日志
function renderPermissionLogs() {
    const container = document.getElementById('permissionLogs');
    if (!container) return;

    if (filteredLogs.length === 0) {
        container.innerHTML = `
            <div class="empty-state">
                <i class="fas fa-history"></i>
                <h3>暂无日志</h3>
                <p>还没有权限操作记录</p>
            </div>
        `;
        return;
    }

    container.innerHTML = filteredLogs.slice(0, 10).map(log => `
        <div class="permission-log-item">
            <div class="log-icon ${log.action}">
                <i class="fas fa-${getLogIcon(log.action)}"></i>
            </div>
            <div class="log-content">
                <div class="log-description">${log.description}</div>
                <div class="log-meta">
                    <span class="log-operator">操作者: ${log.operatorName}</span>
                    <span class="log-time">${log.timestamp}</span>
                </div>
            </div>
        </div>
    `).join('');
}

// 获取日志图标
function getLogIcon(action) {
    const icons = {
        'grant': 'user-plus',
        'revoke': 'user-times',
        'modify': 'edit'
    };
    return icons[action] || 'info-circle';
}

// 筛选角色
function filterRoles() {
    const typeFilter = document.getElementById('roleFilter').value;

    if (!typeFilter) {
        filteredRoles = [...roles];
    } else {
        filteredRoles = roles.filter(role => role.type === typeFilter);
    }

    renderRolesList();
}

// 筛选权限日志
function filterPermissionLogs() {
    const actionFilter = document.getElementById('logFilter').value;

    if (!actionFilter) {
        filteredLogs = [...permissionLogs];
    } else {
        filteredLogs = permissionLogs.filter(log => log.action === actionFilter);
    }

    renderPermissionLogs();
}

// 更新权限矩阵
function updatePermissionMatrix() {
    const roleId = parseInt(document.getElementById('selectedRole').value);
    const container = document.getElementById('permissionsMatrix');

    if (!roleId || !container) {
        container.innerHTML = '<p class="text-center">请选择一个角色查看权限矩阵</p>';
        return;
    }

    const role = roles.find(r => r.id === roleId);
    if (!role) return;

    container.innerHTML = `
        <div class="matrix-header">
            <h4>${role.name} - 权限矩阵</h4>
            <p>${role.description}</p>
        </div>
        <div class="matrix-table">
            <table class="permissions-table">
                <thead>
                    <tr>
                        <th>模块</th>
                        <th>查看</th>
                        <th>创建</th>
                        <th>编辑</th>
                        <th>删除</th>
                        <th>其他</th>
                    </tr>
                </thead>
                <tbody>
                    ${Object.keys(permissions).map(module => {
                        const modulePerms = role.permissions[module] || [];
                        const availableActions = permissions[module].actions;

                        return `
                            <tr>
                                <td class="module-name">${permissions[module].name}</td>
                                <td class="permission-cell">
                                    ${availableActions.includes('view') ?
                                        `<i class="fas fa-${modulePerms.includes('view') ? 'check text-success' : 'times text-danger'}"></i>` :
                                        '<span class="text-muted">-</span>'}
                                </td>
                                <td class="permission-cell">
                                    ${availableActions.includes('create') ?
                                        `<i class="fas fa-${modulePerms.includes('create') ? 'check text-success' : 'times text-danger'}"></i>` :
                                        '<span class="text-muted">-</span>'}
                                </td>
                                <td class="permission-cell">
                                    ${availableActions.includes('edit') ?
                                        `<i class="fas fa-${modulePerms.includes('edit') ? 'check text-success' : 'times text-danger'}"></i>` :
                                        '<span class="text-muted">-</span>'}
                                </td>
                                <td class="permission-cell">
                                    ${availableActions.includes('delete') ?
                                        `<i class="fas fa-${modulePerms.includes('delete') ? 'check text-success' : 'times text-danger'}"></i>` :
                                        '<span class="text-muted">-</span>'}
                                </td>
                                <td class="permission-cell">
                                    ${getOtherPermissions(availableActions, modulePerms)}
                                </td>
                            </tr>
                        `;
                    }).join('')}
                </tbody>
            </table>
        </div>
    `;
}

// 获取其他权限
function getOtherPermissions(availableActions, modulePerms) {
    const basicActions = ['view', 'create', 'edit', 'delete'];
    const otherActions = availableActions.filter(action => !basicActions.includes(action));

    if (otherActions.length === 0) {
        return '<span class="text-muted">-</span>';
    }

    return otherActions.map(action => {
        const hasPermission = modulePerms.includes(action);
        return `<span class="permission-tag ${hasPermission ? 'granted' : 'denied'}">${action}</span>`;
    }).join(' ');
}

// 填充角色选择框
function populateRoleSelects() {
    const selects = ['selectedRole', 'assignRole'];

    selects.forEach(selectId => {
        const select = document.getElementById(selectId);
        if (select) {
            const options = roles.map(role =>
                `<option value="${role.id}">${role.name}</option>`
            ).join('');

            const currentOptions = select.innerHTML;
            const firstOption = currentOptions.split('</option>')[0] + '</option>';
            select.innerHTML = firstOption + options;
        }
    });
}

// 填充用户选择框
function populateUserSelects() {
    // 模拟用户数据
    const users = [
        { id: 1, name: '张管理员' },
        { id: 2, name: '李经理' },
        { id: 3, name: '王分析师' },
        { id: 4, name: '赵员工' },
        { id: 5, name: '钱助理' }
    ];

    const select = document.getElementById('assignUser');
    if (select) {
        const options = users.map(user =>
            `<option value="${user.id}">${user.name}</option>`
        ).join('');

        select.innerHTML = '<option value="">请选择用户</option>' + options;
    }
}

// 显示创建角色模态框
function showCreateRoleModal() {
    editingRoleId = null;
    document.getElementById('roleModalTitle').textContent = '创建角色';
    document.getElementById('roleForm').reset();
    renderBasicPermissions();
    document.getElementById('createRoleModalOverlay').classList.add('active');
}

// 关闭创建角色模态框
function closeCreateRoleModal() {
    document.getElementById('createRoleModalOverlay').classList.remove('active');
    editingRoleId = null;
}

// 渲染基础权限复选框
function renderBasicPermissions() {
    const container = document.getElementById('basicPermissions');
    if (!container) return;

    container.innerHTML = Object.keys(permissions).map(module => `
        <div class="permission-group">
            <h5>${permissions[module].name}</h5>
            <div class="permission-actions">
                ${permissions[module].actions.map(action => `
                    <label class="permission-checkbox">
                        <input type="checkbox" name="permissions" value="${module}.${action}">
                        <span>${getActionLabel(action)}</span>
                    </label>
                `).join('')}
            </div>
        </div>
    `).join('');
}

// 获取操作标签
function getActionLabel(action) {
    const labels = {
        'view': '查看',
        'create': '创建',
        'edit': '编辑',
        'delete': '删除',
        'export': '导出',
        'upload': '上传',
        'download': '下载',
        'share': '分享',
        'stock_in': '入库',
        'stock_out': '出库'
    };
    return labels[action] || action;
}

// 编辑角色
function editRole(roleId) {
    const role = roles.find(r => r.id === roleId);
    if (!role) return;

    editingRoleId = roleId;
    document.getElementById('roleModalTitle').textContent = '编辑角色';
    document.getElementById('roleName').value = role.name;
    document.getElementById('roleDescription').value = role.description;
    document.getElementById('roleType').value = role.type;

    renderBasicPermissions();

    // 设置已有权限
    setTimeout(() => {
        Object.keys(role.permissions).forEach(module => {
            role.permissions[module].forEach(action => {
                const checkbox = document.querySelector(`input[value="${module}.${action}"]`);
                if (checkbox) {
                    checkbox.checked = true;
                }
            });
        });
    }, 100);

    document.getElementById('createRoleModalOverlay').classList.add('active');
}

// 保存角色
function saveRole() {
    const form = document.getElementById('roleForm');
    const formData = new FormData(form);

    const roleData = {
        name: formData.get('name').trim(),
        description: formData.get('description').trim(),
        type: formData.get('type')
    };

    if (!roleData.name) {
        showNotification('请输入角色名称', 'error');
        return;
    }

    // 收集权限
    const selectedPermissions = Array.from(document.querySelectorAll('input[name="permissions"]:checked'))
        .map(cb => cb.value);

    const permissions = {};
    selectedPermissions.forEach(perm => {
        const [module, action] = perm.split('.');
        if (!permissions[module]) {
            permissions[module] = [];
        }
        permissions[module].push(action);
    });

    if (editingRoleId) {
        // 编辑现有角色
        const roleIndex = roles.findIndex(r => r.id === editingRoleId);
        if (roleIndex !== -1) {
            roles[roleIndex] = {
                ...roles[roleIndex],
                ...roleData,
                permissions: permissions
            };
            showNotification('角色更新成功', 'success');

            // 添加日志
            addPermissionLog('modify', `修改了角色"${roleData.name}"的设置`, null, editingRoleId, roleData.name);
        }
    } else {
        // 创建新角色
        const newRole = {
            id: Math.max(...roles.map(r => r.id), 0) + 1,
            ...roleData,
            permissions: permissions,
            userCount: 0,
            createdAt: new Date().toISOString().split('T')[0],
            createdBy: '当前用户',
            isActive: true
        };

        roles.push(newRole);
        showNotification('角色创建成功', 'success');

        // 添加日志
        addPermissionLog('grant', `创建了新角色"${roleData.name}"`, null, newRole.id, roleData.name);
    }

    closeCreateRoleModal();
    renderRolesList();
    populateRoleSelects();
    updatePermissionStats();
}

// 添加权限日志
function addPermissionLog(action, description, userId, roleId, roleName, userName) {
    const newLog = {
        id: Math.max(...permissionLogs.map(l => l.id), 0) + 1,
        action: action,
        description: description,
        userId: userId,
        userName: userName,
        roleId: roleId,
        roleName: roleName,
        operatorId: 1,
        operatorName: '当前用户',
        timestamp: new Date().toLocaleString('zh-CN'),
        details: {}
    };

    permissionLogs.unshift(newLog);
    filteredLogs = [...permissionLogs];
    renderPermissionLogs();
}

// 查看角色权限
function viewRolePermissions(roleId) {
    const role = roles.find(r => r.id === roleId);
    if (!role) return;

    document.getElementById('selectedRole').value = roleId;
    updatePermissionMatrix();
    showNotification(`正在查看"${role.name}"的权限矩阵`, 'info');
}

// 复制角色
function duplicateRole(roleId) {
    const role = roles.find(r => r.id === roleId);
    if (!role) return;

    const newRoleName = prompt('请输入新角色名称:', `${role.name} - 副本`);
    if (!newRoleName) return;

    const newRole = {
        id: Math.max(...roles.map(r => r.id), 0) + 1,
        name: newRoleName,
        description: `复制自 ${role.name}`,
        type: 'custom',
        permissions: JSON.parse(JSON.stringify(role.permissions)),
        userCount: 0,
        createdAt: new Date().toISOString().split('T')[0],
        createdBy: '当前用户',
        isActive: true
    };

    roles.push(newRole);
    renderRolesList();
    populateRoleSelects();
    updatePermissionStats();
    showNotification('角色复制成功', 'success');

    addPermissionLog('grant', `复制角色"${role.name}"创建了"${newRoleName}"`, null, newRole.id, newRoleName);
}

// 删除角色
function deleteRole(roleId) {
    const role = roles.find(r => r.id === roleId);
    if (!role) return;

    if (role.type === 'system') {
        showNotification('系统角色不能删除', 'error');
        return;
    }

    if (role.userCount > 0) {
        showNotification('该角色还有用户使用，无法删除', 'error');
        return;
    }

    if (confirm(`确定要删除角色"${role.name}"吗？此操作不可撤销！`)) {
        const index = roles.findIndex(r => r.id === roleId);
        if (index !== -1) {
            roles.splice(index, 1);
            renderRolesList();
            populateRoleSelects();
            updatePermissionStats();
            showNotification('角色删除成功', 'success');

            addPermissionLog('revoke', `删除了角色"${role.name}"`, null, roleId, role.name);
        }
    }
}

// 显示分配权限模态框
function showAssignPermissionModal() {
    document.getElementById('assignPermissionForm').reset();
    populateUserSelects();
    populateRoleSelects();
    renderPermissionTree();
    document.getElementById('assignPermissionModalOverlay').classList.add('active');
}

// 关闭分配权限模态框
function closeAssignPermissionModal() {
    document.getElementById('assignPermissionModalOverlay').classList.remove('active');
}

// 渲染权限树
function renderPermissionTree() {
    const container = document.getElementById('permissionTree');
    if (!container) return;

    container.innerHTML = Object.keys(permissions).map(module => `
        <div class="permission-tree-node">
            <div class="tree-node-header">
                <label class="tree-checkbox">
                    <input type="checkbox" class="module-checkbox" data-module="${module}" onchange="toggleModulePermissions('${module}')">
                    <span class="tree-label">${permissions[module].name}</span>
                </label>
            </div>
            <div class="tree-node-children">
                ${permissions[module].actions.map(action => `
                    <label class="tree-checkbox child">
                        <input type="checkbox" class="action-checkbox" data-module="${module}" data-action="${action}">
                        <span class="tree-label">${getActionLabel(action)}</span>
                    </label>
                `).join('')}
            </div>
        </div>
    `).join('');
}

// 切换模块权限
function toggleModulePermissions(module) {
    const moduleCheckbox = document.querySelector(`.module-checkbox[data-module="${module}"]`);
    const actionCheckboxes = document.querySelectorAll(`.action-checkbox[data-module="${module}"]`);

    actionCheckboxes.forEach(checkbox => {
        checkbox.checked = moduleCheckbox.checked;
    });
}

// 处理分配权限
function processAssignPermission() {
    const form = document.getElementById('assignPermissionForm');
    const formData = new FormData(form);

    const userId = parseInt(formData.get('userId'));
    const roleId = parseInt(formData.get('roleId'));
    const expiry = formData.get('expiry');

    if (!userId) {
        showNotification('请选择用户', 'error');
        return;
    }

    // 获取用户名
    const userSelect = document.getElementById('assignUser');
    const userName = userSelect.options[userSelect.selectedIndex].text;

    if (roleId) {
        // 分配角色
        const role = roles.find(r => r.id === roleId);
        if (role) {
            const newAssignment = {
                id: Math.max(...userPermissions.map(up => up.id), 0) + 1,
                userId: userId,
                userName: userName,
                roleId: roleId,
                roleName: role.name,
                assignedAt: new Date().toISOString().split('T')[0],
                assignedBy: '当前用户',
                expiry: expiry === 'never' ? null : calculateExpiryDate(expiry),
                isActive: true
            };

            // 移除现有分配
            const existingIndex = userPermissions.findIndex(up => up.userId === userId);
            if (existingIndex !== -1) {
                userPermissions.splice(existingIndex, 1);
            }

            userPermissions.push(newAssignment);
            role.userCount++;

            showNotification(`已为"${userName}"分配"${role.name}"角色`, 'success');
            addPermissionLog('grant', `为用户"${userName}"分配了"${role.name}"角色`, userId, roleId, role.name, userName);
        }
    } else {
        // 分配具体权限
        const selectedPermissions = Array.from(document.querySelectorAll('.action-checkbox:checked'))
            .map(cb => `${cb.dataset.module}.${cb.dataset.action}`);

        if (selectedPermissions.length === 0) {
            showNotification('请选择要分配的权限', 'error');
            return;
        }

        showNotification(`已为"${userName}"分配 ${selectedPermissions.length} 个权限`, 'success');
        addPermissionLog('grant', `为用户"${userName}"分配了自定义权限`, userId, null, null, userName);
    }

    closeAssignPermissionModal();
    renderUserPermissions();
    updatePermissionStats();
}

// 计算过期日期
function calculateExpiryDate(expiry) {
    const now = new Date();
    switch (expiry) {
        case '30d':
            return new Date(now.getTime() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0];
        case '90d':
            return new Date(now.getTime() + 90 * 24 * 60 * 60 * 1000).toISOString().split('T')[0];
        case '1y':
            return new Date(now.getTime() + 365 * 24 * 60 * 60 * 1000).toISOString().split('T')[0];
        default:
            return null;
    }
}

// 编辑用户权限
function editUserPermission(permissionId) {
    showNotification('编辑用户权限功能开发中...', 'info');
}

// 撤销用户权限
function revokeUserPermission(permissionId) {
    const permission = userPermissions.find(up => up.id === permissionId);
    if (!permission) return;

    if (confirm(`确定要撤销"${permission.userName}"的"${permission.roleName}"权限吗？`)) {
        permission.isActive = false;

        // 更新角色用户数
        const role = roles.find(r => r.id === permission.roleId);
        if (role) {
            role.userCount = Math.max(0, role.userCount - 1);
        }

        renderUserPermissions();
        renderRolesList();
        updatePermissionStats();
        showNotification('权限撤销成功', 'success');

        addPermissionLog('revoke', `撤销了用户"${permission.userName}"的"${permission.roleName}"权限`,
            permission.userId, permission.roleId, permission.roleName, permission.userName);
    }
}

// 导出权限
function exportPermissions() {
    showNotification('正在导出权限数据...', 'info');

    setTimeout(() => {
        const data = {
            roles: roles,
            userPermissions: userPermissions,
            permissionLogs: permissionLogs,
            exportTime: new Date().toISOString()
        };

        const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
        const link = document.createElement('a');
        const url = URL.createObjectURL(blob);
        link.setAttribute('href', url);
        link.setAttribute('download', `权限数据_${new Date().toISOString().split('T')[0]}.json`);
        link.style.visibility = 'hidden';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        showNotification('权限数据导出完成', 'success');
    }, 2000);
}

// 全局函数导出
window.initializePermissionManagement = initializePermissionManagement;
window.filterRoles = filterRoles;
window.filterPermissionLogs = filterPermissionLogs;
window.updatePermissionMatrix = updatePermissionMatrix;
window.showCreateRoleModal = showCreateRoleModal;
window.closeCreateRoleModal = closeCreateRoleModal;
window.editRole = editRole;
window.saveRole = saveRole;
window.viewRolePermissions = viewRolePermissions;
window.duplicateRole = duplicateRole;
window.deleteRole = deleteRole;
window.showAssignPermissionModal = showAssignPermissionModal;
window.closeAssignPermissionModal = closeAssignPermissionModal;
window.toggleModulePermissions = toggleModulePermissions;
window.processAssignPermission = processAssignPermission;
window.editUserPermission = editUserPermission;
window.revokeUserPermission = revokeUserPermission;
window.exportPermissions = exportPermissions;
