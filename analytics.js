// 数据分析功能

// 模拟数据
const analyticsData = {
    '7d': {
        revenue: [12000, 15000, 13000, 18000, 16000, 20000, 22000],
        orders: [45, 52, 48, 61, 55, 68, 72],
        customers: [32, 38, 35, 42, 39, 45, 48],
        conversion: [2.1, 2.3, 2.0, 2.5, 2.2, 2.7, 2.8],
        labels: ['周一', '周二', '周三', '周四', '周五', '周六', '周日']
    },
    '30d': {
        revenue: [156000, 142000, 168000, 175000, 162000, 189000, 195000, 178000, 
                 203000, 187000, 215000, 198000, 225000, 208000, 235000, 218000,
                 245000, 228000, 255000, 238000, 265000, 248000, 275000, 258000,
                 285000, 268000, 295000, 278000, 305000, 288000],
        orders: [520, 485, 562, 598, 545, 635, 658, 612, 695, 648, 725, 678,
                758, 698, 785, 728, 815, 758, 845, 788, 875, 818, 905, 848,
                935, 878, 965, 908, 995, 938],
        customers: [385, 352, 418, 445, 402, 472, 488, 455, 515, 482, 538, 505,
                   562, 518, 582, 538, 605, 562, 628, 585, 648, 605, 672, 628,
                   695, 652, 718, 675, 742, 698],
        conversion: [2.8, 2.5, 3.1, 3.2, 2.9, 3.4, 3.5, 3.2, 3.7, 3.4, 3.8, 3.5,
                    3.9, 3.6, 4.0, 3.7, 4.1, 3.8, 4.2, 3.9, 4.3, 4.0, 4.4, 4.1,
                    4.5, 4.2, 4.6, 4.3, 4.7, 4.4],
        labels: Array.from({length: 30}, (_, i) => `${i + 1}日`)
    }
};

// 产品排行数据
const productRankingData = [
    { name: 'iPhone 15 Pro', category: '智能手机', sales: 1250000, growth: 15.2 },
    { name: 'MacBook Air M2', category: '笔记本电脑', sales: 980000, growth: 8.7 },
    { name: 'AirPods Pro', category: '音频设备', sales: 750000, growth: 22.1 },
    { name: 'iPad Air', category: '平板电脑', sales: 650000, growth: -3.2 },
    { name: 'Apple Watch', category: '智能手表', sales: 580000, growth: 12.8 },
    { name: 'Magic Keyboard', category: '配件', sales: 320000, growth: 5.4 },
    { name: 'Studio Display', category: '显示器', sales: 280000, growth: -1.8 },
    { name: 'Mac Studio', category: '台式机', sales: 180000, growth: 18.9 }
];

// 订单状态数据
const orderStatusData = {
    labels: ['已完成', '处理中', '待付款', '已取消', '退款中'],
    data: [65, 20, 8, 5, 2],
    colors: ['#10b981', '#f59e0b', '#3b82f6', '#ef4444', '#8b5cf6']
};

// 客户来源数据
const customerSourceData = {
    labels: ['直接访问', '搜索引擎', '社交媒体', '邮件营销', '推荐链接', '广告投放'],
    data: [35, 25, 15, 12, 8, 5],
    colors: ['#6366f1', '#10b981', '#f59e0b', '#ef4444', '#8b5cf6', '#06b6d4']
};

let currentTimeRange = '30d';
let charts = {};

// 初始化数据分析
function initializeAnalytics() {
    updateAnalyticsStats();
    createCharts();
    renderProductRanking();
    renderAnalyticsTable();
}

// 更新统计数据
function updateAnalyticsStats() {
    const data = analyticsData[currentTimeRange];
    
    // 计算总值
    const totalRevenue = data.revenue.reduce((sum, val) => sum + val, 0);
    const totalOrders = data.orders.reduce((sum, val) => sum + val, 0);
    const totalCustomers = data.customers.reduce((sum, val) => sum + val, 0);
    const avgConversion = data.conversion.reduce((sum, val) => sum + val, 0) / data.conversion.length;
    
    // 计算增长率（与前一周期比较）
    const revenueGrowth = calculateGrowth(data.revenue);
    const ordersGrowth = calculateGrowth(data.orders);
    const customersGrowth = calculateGrowth(data.customers);
    const conversionGrowth = calculateGrowth(data.conversion);
    
    // 更新显示
    document.getElementById('totalRevenue').textContent = formatCurrency(totalRevenue);
    document.getElementById('totalOrders').textContent = totalOrders.toLocaleString();
    document.getElementById('activeCustomers').textContent = totalCustomers.toLocaleString();
    document.getElementById('conversionRate').textContent = avgConversion.toFixed(1) + '%';
    
    // 更新趋势
    updateTrendIndicator('revenueTrend', revenueGrowth);
    updateTrendIndicator('ordersTrend', ordersGrowth);
    updateTrendIndicator('customersTrend', customersGrowth);
    updateTrendIndicator('conversionTrend', conversionGrowth);
}

// 计算增长率
function calculateGrowth(data) {
    if (data.length < 2) return 0;
    
    const recent = data.slice(-7).reduce((sum, val) => sum + val, 0) / 7;
    const previous = data.slice(-14, -7).reduce((sum, val) => sum + val, 0) / 7;
    
    if (previous === 0) return 0;
    return ((recent - previous) / previous * 100);
}

// 更新趋势指示器
function updateTrendIndicator(elementId, growth) {
    const element = document.getElementById(elementId);
    const icon = element.querySelector('i');
    const span = element.querySelector('span');
    
    element.className = 'stat-trend';
    
    if (growth > 0) {
        element.classList.add('positive');
        icon.className = 'fas fa-arrow-up';
        span.textContent = `+${growth.toFixed(1)}%`;
    } else if (growth < 0) {
        element.classList.add('negative');
        icon.className = 'fas fa-arrow-down';
        span.textContent = `${growth.toFixed(1)}%`;
    } else {
        element.classList.add('neutral');
        icon.className = 'fas fa-minus';
        span.textContent = '0%';
    }
}

// 格式化货币
function formatCurrency(amount) {
    return '¥' + amount.toLocaleString();
}

// 创建图表
function createCharts() {
    createRevenueChart();
    createOrderStatusChart();
    createCustomerSourceChart();
}

// 创建收入趋势图
function createRevenueChart() {
    const data = analyticsData[currentTimeRange];

    const chartData = {
        labels: data.labels,
        datasets: [{
            label: '收入',
            data: data.revenue,
            borderColor: '#6366f1',
            backgroundColor: 'rgba(99, 102, 241, 0.1)',
            fill: true
        }]
    };

    createLineChart('revenueChart', chartData, {
        formatY: 'currency',
        plugins: {
            title: {
                display: true,
                text: '收入趋势分析',
                font: {
                    family: 'Inter',
                    size: 16,
                    weight: '600'
                }
            }
        }
    });
}

// 创建订单状态饼图
function createOrderStatusChart() {
    createPieChart('orderStatusChart', orderStatusData, {
        plugins: {
            title: {
                display: true,
                text: '订单状态分布',
                font: {
                    family: 'Inter',
                    size: 16,
                    weight: '600'
                }
            }
        }
    });
}

// 创建客户来源图表
function createCustomerSourceChart() {
    const chartData = {
        labels: customerSourceData.labels,
        datasets: [{
            label: '客户数量',
            data: customerSourceData.data,
            backgroundColor: customerSourceData.colors,
            borderRadius: 6,
            borderWidth: 0
        }]
    };

    createBarChart('customerSourceChart', chartData, {
        formatY: 'percentage',
        plugins: {
            title: {
                display: true,
                text: '客户来源分析',
                font: {
                    family: 'Inter',
                    size: 16,
                    weight: '600'
                }
            },
            legend: {
                display: false
            }
        }
    });
}

// 简单的折线图绘制
function drawLineChart(ctx, config) {
    const canvas = ctx.canvas;
    const width = canvas.width = canvas.offsetWidth;
    const height = canvas.height = canvas.offsetHeight;
    
    ctx.clearRect(0, 0, width, height);
    
    const padding = 40;
    const chartWidth = width - padding * 2;
    const chartHeight = height - padding * 2;
    
    const max = Math.max(...config.data);
    const min = Math.min(...config.data);
    const range = max - min || 1;
    
    // 绘制网格线
    ctx.strokeStyle = '#e2e8f0';
    ctx.lineWidth = 1;
    
    for (let i = 0; i <= 5; i++) {
        const y = padding + (chartHeight / 5) * i;
        ctx.beginPath();
        ctx.moveTo(padding, y);
        ctx.lineTo(width - padding, y);
        ctx.stroke();
    }
    
    // 绘制数据线
    ctx.strokeStyle = config.color;
    ctx.lineWidth = 3;
    ctx.beginPath();
    
    config.data.forEach((value, index) => {
        const x = padding + (chartWidth / (config.data.length - 1)) * index;
        const y = padding + chartHeight - ((value - min) / range) * chartHeight;
        
        if (index === 0) {
            ctx.moveTo(x, y);
        } else {
            ctx.lineTo(x, y);
        }
    });
    
    ctx.stroke();
    
    // 绘制数据点
    ctx.fillStyle = config.color;
    config.data.forEach((value, index) => {
        const x = padding + (chartWidth / (config.data.length - 1)) * index;
        const y = padding + chartHeight - ((value - min) / range) * chartHeight;
        
        ctx.beginPath();
        ctx.arc(x, y, 4, 0, Math.PI * 2);
        ctx.fill();
    });
}

// 简单的饼图绘制
function drawPieChart(ctx, config) {
    const canvas = ctx.canvas;
    const width = canvas.width = canvas.offsetWidth;
    const height = canvas.height = canvas.offsetHeight;
    
    ctx.clearRect(0, 0, width, height);
    
    const centerX = width / 2;
    const centerY = height / 2;
    const radius = Math.min(width, height) / 2 - 20;
    
    const total = config.data.reduce((sum, val) => sum + val, 0);
    let currentAngle = -Math.PI / 2;
    
    config.data.forEach((value, index) => {
        const sliceAngle = (value / total) * Math.PI * 2;
        
        ctx.fillStyle = config.colors[index];
        ctx.beginPath();
        ctx.moveTo(centerX, centerY);
        ctx.arc(centerX, centerY, radius, currentAngle, currentAngle + sliceAngle);
        ctx.closePath();
        ctx.fill();
        
        // 绘制标签
        const labelAngle = currentAngle + sliceAngle / 2;
        const labelX = centerX + Math.cos(labelAngle) * (radius * 0.7);
        const labelY = centerY + Math.sin(labelAngle) * (radius * 0.7);
        
        ctx.fillStyle = 'white';
        ctx.font = '12px Inter';
        ctx.textAlign = 'center';
        ctx.fillText(`${value}%`, labelX, labelY);
        
        currentAngle += sliceAngle;
    });
}

// 简单的柱状图绘制
function drawBarChart(ctx, config) {
    const canvas = ctx.canvas;
    const width = canvas.width = canvas.offsetWidth;
    const height = canvas.height = canvas.offsetHeight;
    
    ctx.clearRect(0, 0, width, height);
    
    const padding = 40;
    const chartWidth = width - padding * 2;
    const chartHeight = height - padding * 2;
    
    const max = Math.max(...config.data);
    const barWidth = chartWidth / config.data.length * 0.8;
    const barSpacing = chartWidth / config.data.length * 0.2;
    
    config.data.forEach((value, index) => {
        const barHeight = (value / max) * chartHeight;
        const x = padding + index * (barWidth + barSpacing) + barSpacing / 2;
        const y = padding + chartHeight - barHeight;
        
        ctx.fillStyle = config.colors[index];
        ctx.fillRect(x, y, barWidth, barHeight);
        
        // 绘制数值
        ctx.fillStyle = '#374151';
        ctx.font = '12px Inter';
        ctx.textAlign = 'center';
        ctx.fillText(`${value}%`, x + barWidth / 2, y - 5);
    });
}

// 渲染产品排行
function renderProductRanking() {
    const container = document.getElementById('productRanking');
    if (!container) return;
    
    container.innerHTML = productRankingData.map((product, index) => `
        <div class="product-item">
            <div class="product-rank rank-${index < 3 ? index + 1 : 'other'}">
                ${index + 1}
            </div>
            <div class="product-info">
                <div class="product-name">${product.name}</div>
                <div class="product-category">${product.category}</div>
            </div>
            <div class="product-stats">
                <div class="product-sales">${formatCurrency(product.sales)}</div>
                <div class="product-growth ${product.growth >= 0 ? 'positive' : 'negative'}">
                    <i class="fas fa-arrow-${product.growth >= 0 ? 'up' : 'down'}"></i>
                    ${Math.abs(product.growth).toFixed(1)}%
                </div>
            </div>
        </div>
    `).join('');
}

// 渲染分析表格
function renderAnalyticsTable() {
    const tbody = document.getElementById('analyticsTableBody');
    if (!tbody) return;
    
    const data = analyticsData[currentTimeRange];
    
    tbody.innerHTML = data.labels.map((label, index) => `
        <tr>
            <td>${label}</td>
            <td>${formatCurrency(data.revenue[index])}</td>
            <td>${data.orders[index]}</td>
            <td>${data.customers[index]}</td>
            <td>${data.conversion[index].toFixed(1)}%</td>
            <td>${formatCurrency(Math.round(data.revenue[index] / data.orders[index]))}</td>
        </tr>
    `).join('');
}

// 更新分析数据
function updateAnalytics() {
    const timeRange = document.getElementById('timeRangeSelect').value;
    currentTimeRange = timeRange;
    
    showNotification('正在更新数据...', 'info');
    
    // 模拟加载延迟
    setTimeout(() => {
        updateAnalyticsStats();
        createCharts();
        renderAnalyticsTable();
        showNotification('数据更新完成', 'success');
    }, 1000);
}

// 刷新数据
function refreshData() {
    showNotification('正在刷新数据...', 'info');
    
    // 模拟数据刷新
    setTimeout(() => {
        updateAnalytics();
        showNotification('数据刷新完成', 'success');
    }, 1500);
}

// 导出报告
function exportReport() {
    const data = analyticsData[currentTimeRange];
    
    let csvContent = "data:text/csv;charset=utf-8,";
    csvContent += "日期,收入,订单数,客户数,转化率,平均订单价值\n";
    
    data.labels.forEach((label, index) => {
        const avgOrderValue = Math.round(data.revenue[index] / data.orders[index]);
        csvContent += `${label},${data.revenue[index]},${data.orders[index]},${data.customers[index]},${data.conversion[index].toFixed(1)}%,${avgOrderValue}\n`;
    });
    
    const encodedUri = encodeURI(csvContent);
    const link = document.createElement("a");
    link.setAttribute("href", encodedUri);
    link.setAttribute("download", `analytics_report_${currentTimeRange}.csv`);
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    
    showNotification('报告导出成功', 'success');
}

// 切换表格视图
function toggleTableView() {
    const table = document.getElementById('analyticsTable');
    const isHidden = table.style.display === 'none';
    
    table.style.display = isHidden ? 'table' : 'none';
    showNotification(isHidden ? '显示详细表格' : '隐藏详细表格', 'info');
}

// 页面加载时初始化
document.addEventListener('DOMContentLoaded', function() {
    // 检查是否在数据分析页面
    if (document.getElementById('revenueChart')) {
        setTimeout(() => {
            initializeAnalytics();
        }, 500);
    }
});
