// 工作流管理系统
class WorkflowManagement {
    constructor() {
        this.workflows = [];
        this.workflowTemplates = [];
        this.activeWorkflows = [];
        this.workflowHistory = [];
        this.currentWorkflow = null;
        this.workflowSteps = [];
        
        this.initializeWorkflowSystem();
        this.loadWorkflowTemplates();
    }

    initializeWorkflowSystem() {
        this.createWorkflowInterface();
        this.bindWorkflowEvents();
        this.loadStoredWorkflows();
    }

    createWorkflowInterface() {
        // 创建工作流管理界面
        const workflowPanel = document.createElement('div');
        workflowPanel.id = 'workflowPanel';
        workflowPanel.className = 'workflow-panel';
        workflowPanel.innerHTML = `
            <div class="workflow-header">
                <h3>
                    <i class="fas fa-project-diagram"></i>
                    工作流管理
                </h3>
                <div class="workflow-controls">
                    <button class="btn-primary" onclick="workflowManager.showCreateWorkflow()">
                        <i class="fas fa-plus"></i>
                        创建工作流
                    </button>
                    <button class="btn-secondary" onclick="workflowManager.showWorkflowTemplates()">
                        <i class="fas fa-layer-group"></i>
                        模板库
                    </button>
                    <button class="btn-icon" onclick="workflowManager.closeWorkflowPanel()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            </div>
            
            <div class="workflow-body">
                <div class="workflow-tabs">
                    <button class="tab-btn active" data-tab="active">
                        <i class="fas fa-play"></i>
                        进行中 (<span id="activeWorkflowCount">0</span>)
                    </button>
                    <button class="tab-btn" data-tab="completed">
                        <i class="fas fa-check"></i>
                        已完成
                    </button>
                    <button class="tab-btn" data-tab="templates">
                        <i class="fas fa-layer-group"></i>
                        模板
                    </button>
                    <button class="tab-btn" data-tab="analytics">
                        <i class="fas fa-chart-line"></i>
                        分析
                    </button>
                </div>
                
                <div class="workflow-content">
                    <!-- 进行中的工作流 -->
                    <div class="tab-content active" id="activeWorkflowsTab">
                        <div class="workflow-filters">
                            <select id="workflowStatusFilter">
                                <option value="all">所有状态</option>
                                <option value="pending">待开始</option>
                                <option value="running">进行中</option>
                                <option value="paused">已暂停</option>
                                <option value="waiting">等待中</option>
                            </select>
                            <select id="workflowPriorityFilter">
                                <option value="all">所有优先级</option>
                                <option value="high">高优先级</option>
                                <option value="medium">中优先级</option>
                                <option value="low">低优先级</option>
                            </select>
                            <input type="text" id="workflowSearchInput" placeholder="搜索工作流...">
                        </div>
                        <div class="workflow-list" id="activeWorkflowList">
                            <!-- 活跃工作流列表 -->
                        </div>
                    </div>
                    
                    <!-- 已完成的工作流 -->
                    <div class="tab-content" id="completedWorkflowsTab">
                        <div class="workflow-list" id="completedWorkflowList">
                            <!-- 已完成工作流列表 -->
                        </div>
                    </div>
                    
                    <!-- 工作流模板 -->
                    <div class="tab-content" id="templatesTab">
                        <div class="template-categories">
                            <button class="category-btn active" data-category="all">全部</button>
                            <button class="category-btn" data-category="approval">审批流程</button>
                            <button class="category-btn" data-category="automation">自动化</button>
                            <button class="category-btn" data-category="notification">通知流程</button>
                            <button class="category-btn" data-category="custom">自定义</button>
                        </div>
                        <div class="template-grid" id="templateGrid">
                            <!-- 工作流模板网格 -->
                        </div>
                    </div>
                    
                    <!-- 工作流分析 -->
                    <div class="tab-content" id="analyticsTab">
                        <div class="analytics-dashboard">
                            <div class="analytics-stats">
                                <div class="stat-card">
                                    <h4>总工作流数</h4>
                                    <span class="stat-number" id="totalWorkflows">0</span>
                                </div>
                                <div class="stat-card">
                                    <h4>平均完成时间</h4>
                                    <span class="stat-number" id="avgCompletionTime">0小时</span>
                                </div>
                                <div class="stat-card">
                                    <h4>成功率</h4>
                                    <span class="stat-number" id="successRate">0%</span>
                                </div>
                                <div class="stat-card">
                                    <h4>活跃用户</h4>
                                    <span class="stat-number" id="activeUsers">0</span>
                                </div>
                            </div>
                            <div class="analytics-charts">
                                <div class="chart-container">
                                    <canvas id="workflowTrendChart"></canvas>
                                </div>
                                <div class="chart-container">
                                    <canvas id="workflowStatusChart"></canvas>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
        
        document.body.appendChild(workflowPanel);
        
        // 创建工作流创建器
        this.createWorkflowBuilder();
        
        // 创建工作流详情面板
        this.createWorkflowDetailPanel();
    }

    createWorkflowBuilder() {
        const builder = document.createElement('div');
        builder.id = 'workflowBuilder';
        builder.className = 'workflow-builder-modal';
        builder.innerHTML = `
            <div class="builder-content">
                <div class="builder-header">
                    <h3>工作流设计器</h3>
                    <div class="builder-actions">
                        <button class="btn-secondary" onclick="workflowManager.saveWorkflowDraft()">
                            <i class="fas fa-save"></i>
                            保存草稿
                        </button>
                        <button class="btn-primary" onclick="workflowManager.publishWorkflow()">
                            <i class="fas fa-rocket"></i>
                            发布工作流
                        </button>
                        <button class="btn-icon" onclick="workflowManager.closeWorkflowBuilder()">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                </div>
                
                <div class="builder-body">
                    <div class="builder-sidebar">
                        <div class="workflow-info">
                            <div class="form-group">
                                <label>工作流名称</label>
                                <input type="text" id="workflowName" placeholder="输入工作流名称">
                            </div>
                            <div class="form-group">
                                <label>描述</label>
                                <textarea id="workflowDescription" placeholder="描述工作流用途"></textarea>
                            </div>
                            <div class="form-group">
                                <label>优先级</label>
                                <select id="workflowPriority">
                                    <option value="low">低</option>
                                    <option value="medium" selected>中</option>
                                    <option value="high">高</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label>触发条件</label>
                                <select id="workflowTrigger">
                                    <option value="manual">手动触发</option>
                                    <option value="schedule">定时触发</option>
                                    <option value="event">事件触发</option>
                                    <option value="webhook">Webhook触发</option>
                                </select>
                            </div>
                        </div>
                        
                        <div class="step-palette">
                            <h4>步骤组件</h4>
                            <div class="palette-items">
                                <div class="palette-item" draggable="true" data-type="start">
                                    <i class="fas fa-play"></i>
                                    <span>开始</span>
                                </div>
                                <div class="palette-item" draggable="true" data-type="task">
                                    <i class="fas fa-tasks"></i>
                                    <span>任务</span>
                                </div>
                                <div class="palette-item" draggable="true" data-type="approval">
                                    <i class="fas fa-check-circle"></i>
                                    <span>审批</span>
                                </div>
                                <div class="palette-item" draggable="true" data-type="condition">
                                    <i class="fas fa-code-branch"></i>
                                    <span>条件</span>
                                </div>
                                <div class="palette-item" draggable="true" data-type="notification">
                                    <i class="fas fa-bell"></i>
                                    <span>通知</span>
                                </div>
                                <div class="palette-item" draggable="true" data-type="delay">
                                    <i class="fas fa-clock"></i>
                                    <span>延时</span>
                                </div>
                                <div class="palette-item" draggable="true" data-type="end">
                                    <i class="fas fa-stop"></i>
                                    <span>结束</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="builder-canvas">
                        <div class="canvas-toolbar">
                            <button class="tool-btn" onclick="workflowManager.zoomIn()">
                                <i class="fas fa-search-plus"></i>
                            </button>
                            <button class="tool-btn" onclick="workflowManager.zoomOut()">
                                <i class="fas fa-search-minus"></i>
                            </button>
                            <button class="tool-btn" onclick="workflowManager.resetZoom()">
                                <i class="fas fa-expand-arrows-alt"></i>
                            </button>
                            <button class="tool-btn" onclick="workflowManager.autoLayout()">
                                <i class="fas fa-magic"></i>
                                自动布局
                            </button>
                        </div>
                        <div class="workflow-canvas" id="workflowCanvas">
                            <!-- 工作流画布 -->
                        </div>
                    </div>
                    
                    <div class="builder-properties">
                        <h4>属性面板</h4>
                        <div class="properties-content" id="propertiesContent">
                            <p>选择一个步骤来编辑属性</p>
                        </div>
                    </div>
                </div>
            </div>
        `;
        
        document.body.appendChild(builder);
    }

    createWorkflowDetailPanel() {
        const detailPanel = document.createElement('div');
        detailPanel.id = 'workflowDetailPanel';
        detailPanel.className = 'workflow-detail-panel';
        detailPanel.innerHTML = `
            <div class="detail-header">
                <div class="workflow-title">
                    <h3 id="detailWorkflowName">工作流详情</h3>
                    <span class="workflow-status" id="detailWorkflowStatus">运行中</span>
                </div>
                <div class="detail-actions">
                    <button class="btn-secondary" onclick="workflowManager.pauseWorkflow()">
                        <i class="fas fa-pause"></i>
                        暂停
                    </button>
                    <button class="btn-primary" onclick="workflowManager.resumeWorkflow()">
                        <i class="fas fa-play"></i>
                        继续
                    </button>
                    <button class="btn-danger" onclick="workflowManager.stopWorkflow()">
                        <i class="fas fa-stop"></i>
                        停止
                    </button>
                    <button class="btn-icon" onclick="workflowManager.closeDetailPanel()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            </div>
            
            <div class="detail-body">
                <div class="detail-tabs">
                    <button class="tab-btn active" data-tab="progress">进度</button>
                    <button class="tab-btn" data-tab="history">历史</button>
                    <button class="tab-btn" data-tab="participants">参与者</button>
                    <button class="tab-btn" data-tab="logs">日志</button>
                </div>
                
                <div class="detail-content">
                    <!-- 进度标签页 -->
                    <div class="tab-content active" id="progressTab">
                        <div class="workflow-progress">
                            <div class="progress-overview">
                                <div class="progress-stats">
                                    <div class="stat-item">
                                        <span class="stat-label">总步骤</span>
                                        <span class="stat-value" id="totalSteps">0</span>
                                    </div>
                                    <div class="stat-item">
                                        <span class="stat-label">已完成</span>
                                        <span class="stat-value" id="completedSteps">0</span>
                                    </div>
                                    <div class="stat-item">
                                        <span class="stat-label">进度</span>
                                        <span class="stat-value" id="progressPercentage">0%</span>
                                    </div>
                                </div>
                                <div class="progress-bar">
                                    <div class="progress-fill" id="progressFill"></div>
                                </div>
                            </div>
                            <div class="step-timeline" id="stepTimeline">
                                <!-- 步骤时间线 -->
                            </div>
                        </div>
                    </div>
                    
                    <!-- 历史标签页 -->
                    <div class="tab-content" id="historyTab">
                        <div class="workflow-history" id="workflowHistoryList">
                            <!-- 工作流历史记录 -->
                        </div>
                    </div>
                    
                    <!-- 参与者标签页 -->
                    <div class="tab-content" id="participantsTab">
                        <div class="participants-list" id="participantsList">
                            <!-- 参与者列表 -->
                        </div>
                    </div>
                    
                    <!-- 日志标签页 -->
                    <div class="tab-content" id="logsTab">
                        <div class="workflow-logs" id="workflowLogs">
                            <!-- 工作流日志 -->
                        </div>
                    </div>
                </div>
            </div>
        `;
        
        document.body.appendChild(detailPanel);
    }

    bindWorkflowEvents() {
        // 标签切换事件
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('tab-btn') && e.target.closest('.workflow-panel, .workflow-detail-panel')) {
                this.switchTab(e.target);
            }
        });

        // 工作流筛选事件
        document.getElementById('workflowStatusFilter')?.addEventListener('change', () => {
            this.filterWorkflows();
        });

        document.getElementById('workflowPriorityFilter')?.addEventListener('change', () => {
            this.filterWorkflows();
        });

        document.getElementById('workflowSearchInput')?.addEventListener('input', () => {
            this.filterWorkflows();
        });

        // 拖拽事件
        this.bindDragDropEvents();
    }

    bindDragDropEvents() {
        // 绑定拖拽事件到画布
        const canvas = document.getElementById('workflowCanvas');
        if (canvas) {
            canvas.addEventListener('dragover', this.handleCanvasDragOver.bind(this));
            canvas.addEventListener('drop', this.handleCanvasDrop.bind(this));
        }

        // 绑定拖拽事件到调色板项目
        document.querySelectorAll('.palette-item').forEach(item => {
            item.addEventListener('dragstart', this.handlePaletteDragStart.bind(this));
        });
    }

    loadWorkflowTemplates() {
        this.workflowTemplates = [
            {
                id: 'approval-basic',
                name: '基础审批流程',
                category: 'approval',
                description: '简单的审批工作流，包含申请、审批、通知步骤',
                steps: [
                    { type: 'start', name: '开始' },
                    { type: 'task', name: '提交申请' },
                    { type: 'approval', name: '主管审批' },
                    { type: 'condition', name: '审批结果' },
                    { type: 'notification', name: '通知申请人' },
                    { type: 'end', name: '结束' }
                ],
                icon: 'fas fa-check-circle',
                color: '#10b981'
            },
            {
                id: 'user-onboarding',
                name: '用户入职流程',
                category: 'automation',
                description: '新员工入职自动化流程',
                steps: [
                    { type: 'start', name: '开始' },
                    { type: 'task', name: '创建账户' },
                    { type: 'task', name: '分配权限' },
                    { type: 'notification', name: '发送欢迎邮件' },
                    { type: 'task', name: '安排培训' },
                    { type: 'end', name: '结束' }
                ],
                icon: 'fas fa-user-plus',
                color: '#3b82f6'
            },
            {
                id: 'order-processing',
                name: '订单处理流程',
                category: 'automation',
                description: '自动化订单处理和发货流程',
                steps: [
                    { type: 'start', name: '开始' },
                    { type: 'task', name: '验证订单' },
                    { type: 'task', name: '库存检查' },
                    { type: 'condition', name: '库存充足？' },
                    { type: 'task', name: '生成发货单' },
                    { type: 'notification', name: '通知客户' },
                    { type: 'end', name: '结束' }
                ],
                icon: 'fas fa-shopping-cart',
                color: '#f59e0b'
            },
            {
                id: 'incident-response',
                name: '事件响应流程',
                category: 'notification',
                description: '系统事件自动响应和通知流程',
                steps: [
                    { type: 'start', name: '事件触发' },
                    { type: 'task', name: '事件分析' },
                    { type: 'condition', name: '严重程度' },
                    { type: 'notification', name: '通知相关人员' },
                    { type: 'task', name: '记录事件' },
                    { type: 'end', name: '结束' }
                ],
                icon: 'fas fa-exclamation-triangle',
                color: '#ef4444'
            }
        ];
        
        this.renderWorkflowTemplates();
    }

    renderWorkflowTemplates() {
        const templateGrid = document.getElementById('templateGrid');
        if (!templateGrid) return;

        templateGrid.innerHTML = this.workflowTemplates.map(template => `
            <div class="template-card" data-category="${template.category}">
                <div class="template-icon" style="background: ${template.color}">
                    <i class="${template.icon}"></i>
                </div>
                <div class="template-content">
                    <h4>${template.name}</h4>
                    <p>${template.description}</p>
                    <div class="template-meta">
                        <span class="step-count">${template.steps.length} 步骤</span>
                        <span class="template-category">${this.getCategoryLabel(template.category)}</span>
                    </div>
                </div>
                <div class="template-actions">
                    <button class="btn-secondary" onclick="workflowManager.previewTemplate('${template.id}')">
                        <i class="fas fa-eye"></i>
                        预览
                    </button>
                    <button class="btn-primary" onclick="workflowManager.useTemplate('${template.id}')">
                        <i class="fas fa-plus"></i>
                        使用
                    </button>
                </div>
            </div>
        `).join('');
    }

    getCategoryLabel(category) {
        const labels = {
            approval: '审批流程',
            automation: '自动化',
            notification: '通知流程',
            custom: '自定义'
        };
        return labels[category] || category;
    }

    switchTab(tabBtn) {
        const container = tabBtn.closest('.workflow-panel, .workflow-detail-panel');
        const tabName = tabBtn.dataset.tab;

        // 更新标签状态
        container.querySelectorAll('.tab-btn').forEach(btn => btn.classList.remove('active'));
        container.querySelectorAll('.tab-content').forEach(content => content.classList.remove('active'));

        tabBtn.classList.add('active');
        container.querySelector(`#${tabName}Tab, #${tabName}WorkflowsTab`).classList.add('active');

        // 加载对应内容
        this.loadTabContent(tabName);
    }

    loadTabContent(tabName) {
        switch (tabName) {
            case 'active':
                this.loadActiveWorkflows();
                break;
            case 'completed':
                this.loadCompletedWorkflows();
                break;
            case 'templates':
                this.renderWorkflowTemplates();
                break;
            case 'analytics':
                this.loadWorkflowAnalytics();
                break;
        }
    }

    loadActiveWorkflows() {
        // 模拟活跃工作流数据
        const activeWorkflows = [
            {
                id: 'wf-001',
                name: '用户注册审批',
                status: 'running',
                priority: 'medium',
                progress: 60,
                currentStep: '主管审批',
                assignee: '李经理',
                startTime: new Date(Date.now() - 2 * 60 * 60 * 1000),
                estimatedCompletion: new Date(Date.now() + 1 * 60 * 60 * 1000)
            },
            {
                id: 'wf-002',
                name: '订单处理流程',
                status: 'waiting',
                priority: 'high',
                progress: 80,
                currentStep: '等待发货',
                assignee: '王仓管',
                startTime: new Date(Date.now() - 4 * 60 * 60 * 1000),
                estimatedCompletion: new Date(Date.now() + 30 * 60 * 1000)
            }
        ];

        this.renderWorkflowList(activeWorkflows, 'activeWorkflowList');
        document.getElementById('activeWorkflowCount').textContent = activeWorkflows.length;
    }

    loadCompletedWorkflows() {
        // 模拟已完成工作流数据
        const completedWorkflows = [
            {
                id: 'wf-003',
                name: '员工入职流程',
                status: 'completed',
                priority: 'medium',
                progress: 100,
                completedTime: new Date(Date.now() - 24 * 60 * 60 * 1000),
                duration: '2小时30分钟',
                result: 'success'
            }
        ];

        this.renderWorkflowList(completedWorkflows, 'completedWorkflowList');
    }

    renderWorkflowList(workflows, containerId) {
        const container = document.getElementById(containerId);
        if (!container) return;

        if (workflows.length === 0) {
            container.innerHTML = `
                <div class="empty-state">
                    <i class="fas fa-project-diagram"></i>
                    <p>暂无工作流</p>
                </div>
            `;
            return;
        }

        container.innerHTML = workflows.map(workflow => `
            <div class="workflow-item" data-id="${workflow.id}">
                <div class="workflow-info">
                    <div class="workflow-header">
                        <h4>${workflow.name}</h4>
                        <span class="workflow-status ${workflow.status}">${this.getStatusLabel(workflow.status)}</span>
                    </div>
                    <div class="workflow-meta">
                        <span class="priority ${workflow.priority}">
                            <i class="fas fa-flag"></i>
                            ${this.getPriorityLabel(workflow.priority)}
                        </span>
                        ${workflow.currentStep ? `<span class="current-step">${workflow.currentStep}</span>` : ''}
                        ${workflow.assignee ? `<span class="assignee">负责人: ${workflow.assignee}</span>` : ''}
                    </div>
                    ${workflow.progress !== undefined ? `
                        <div class="workflow-progress">
                            <div class="progress-bar">
                                <div class="progress-fill" style="width: ${workflow.progress}%"></div>
                            </div>
                            <span class="progress-text">${workflow.progress}%</span>
                        </div>
                    ` : ''}
                    <div class="workflow-time">
                        ${workflow.startTime ? `<span>开始: ${this.formatTime(workflow.startTime)}</span>` : ''}
                        ${workflow.estimatedCompletion ? `<span>预计完成: ${this.formatTime(workflow.estimatedCompletion)}</span>` : ''}
                        ${workflow.completedTime ? `<span>完成: ${this.formatTime(workflow.completedTime)}</span>` : ''}
                        ${workflow.duration ? `<span>耗时: ${workflow.duration}</span>` : ''}
                    </div>
                </div>
                <div class="workflow-actions">
                    <button class="btn-icon" onclick="workflowManager.viewWorkflowDetail('${workflow.id}')" title="查看详情">
                        <i class="fas fa-eye"></i>
                    </button>
                    ${workflow.status === 'running' ? `
                        <button class="btn-icon" onclick="workflowManager.pauseWorkflow('${workflow.id}')" title="暂停">
                            <i class="fas fa-pause"></i>
                        </button>
                    ` : ''}
                    ${workflow.status === 'paused' ? `
                        <button class="btn-icon" onclick="workflowManager.resumeWorkflow('${workflow.id}')" title="继续">
                            <i class="fas fa-play"></i>
                        </button>
                    ` : ''}
                    <button class="btn-icon" onclick="workflowManager.deleteWorkflow('${workflow.id}')" title="删除">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </div>
        `).join('');
    }

    getStatusLabel(status) {
        const labels = {
            pending: '待开始',
            running: '进行中',
            paused: '已暂停',
            waiting: '等待中',
            completed: '已完成',
            failed: '已失败',
            cancelled: '已取消'
        };
        return labels[status] || status;
    }

    getPriorityLabel(priority) {
        const labels = {
            low: '低',
            medium: '中',
            high: '高'
        };
        return labels[priority] || priority;
    }

    formatTime(date) {
        return date.toLocaleString('zh-CN', {
            month: 'short',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        });
    }

    showWorkflowPanel() {
        const panel = document.getElementById('workflowPanel');
        if (panel) {
            panel.classList.add('show');
            this.loadActiveWorkflows();
        }
    }

    closeWorkflowPanel() {
        const panel = document.getElementById('workflowPanel');
        if (panel) {
            panel.classList.remove('show');
        }
    }

    showCreateWorkflow() {
        const builder = document.getElementById('workflowBuilder');
        if (builder) {
            builder.classList.add('show');
            this.initializeCanvas();
        }
    }

    closeWorkflowBuilder() {
        const builder = document.getElementById('workflowBuilder');
        if (builder) {
            builder.classList.remove('show');
        }
    }

    initializeCanvas() {
        const canvas = document.getElementById('workflowCanvas');
        if (canvas) {
            canvas.innerHTML = `
                <div class="canvas-grid"></div>
                <div class="canvas-content">
                    <div class="start-hint">
                        <i class="fas fa-hand-pointer"></i>
                        <p>从左侧拖拽组件到此处开始设计工作流</p>
                    </div>
                </div>
            `;
        }
    }

    handleCanvasDragOver(e) {
        e.preventDefault();
        e.dataTransfer.dropEffect = 'copy';
    }

    handleCanvasDrop(e) {
        e.preventDefault();
        const stepType = e.dataTransfer.getData('text/plain');
        const rect = e.currentTarget.getBoundingClientRect();
        const x = e.clientX - rect.left;
        const y = e.clientY - rect.top;
        
        this.addStepToCanvas(stepType, x, y);
    }

    handlePaletteDragStart(e) {
        const stepType = e.currentTarget.dataset.type;
        e.dataTransfer.setData('text/plain', stepType);
        e.dataTransfer.effectAllowed = 'copy';
    }

    addStepToCanvas(stepType, x, y) {
        const canvas = document.getElementById('workflowCanvas');
        const canvasContent = canvas.querySelector('.canvas-content');
        
        // 移除提示
        const hint = canvasContent.querySelector('.start-hint');
        if (hint) hint.remove();
        
        // 创建步骤元素
        const stepElement = document.createElement('div');
        stepElement.className = 'workflow-step';
        stepElement.dataset.type = stepType;
        stepElement.style.left = x + 'px';
        stepElement.style.top = y + 'px';
        
        const stepConfig = this.getStepConfig(stepType);
        stepElement.innerHTML = `
            <div class="step-icon" style="background: ${stepConfig.color}">
                <i class="${stepConfig.icon}"></i>
            </div>
            <div class="step-label">${stepConfig.label}</div>
            <div class="step-connectors">
                <div class="connector input"></div>
                <div class="connector output"></div>
            </div>
        `;
        
        canvasContent.appendChild(stepElement);
        
        // 绑定点击事件
        stepElement.addEventListener('click', () => {
            this.selectStep(stepElement);
        });
        
        // 使步骤可拖拽
        this.makeStepDraggable(stepElement);
    }

    getStepConfig(type) {
        const configs = {
            start: { icon: 'fas fa-play', label: '开始', color: '#10b981' },
            task: { icon: 'fas fa-tasks', label: '任务', color: '#3b82f6' },
            approval: { icon: 'fas fa-check-circle', label: '审批', color: '#f59e0b' },
            condition: { icon: 'fas fa-code-branch', label: '条件', color: '#8b5cf6' },
            notification: { icon: 'fas fa-bell', label: '通知', color: '#ec4899' },
            delay: { icon: 'fas fa-clock', label: '延时', color: '#6b7280' },
            end: { icon: 'fas fa-stop', label: '结束', color: '#ef4444' }
        };
        return configs[type] || configs.task;
    }

    makeStepDraggable(element) {
        let isDragging = false;
        let startX, startY, initialX, initialY;
        
        element.addEventListener('mousedown', (e) => {
            if (e.target.closest('.connector')) return;
            
            isDragging = true;
            startX = e.clientX;
            startY = e.clientY;
            initialX = parseInt(element.style.left) || 0;
            initialY = parseInt(element.style.top) || 0;
            
            element.classList.add('dragging');
        });
        
        document.addEventListener('mousemove', (e) => {
            if (!isDragging) return;
            
            const deltaX = e.clientX - startX;
            const deltaY = e.clientY - startY;
            
            element.style.left = (initialX + deltaX) + 'px';
            element.style.top = (initialY + deltaY) + 'px';
        });
        
        document.addEventListener('mouseup', () => {
            if (isDragging) {
                isDragging = false;
                element.classList.remove('dragging');
            }
        });
    }

    selectStep(stepElement) {
        // 清除之前的选择
        document.querySelectorAll('.workflow-step.selected').forEach(step => {
            step.classList.remove('selected');
        });
        
        // 选择当前步骤
        stepElement.classList.add('selected');
        
        // 显示属性面板
        this.showStepProperties(stepElement);
    }

    showStepProperties(stepElement) {
        const propertiesContent = document.getElementById('propertiesContent');
        const stepType = stepElement.dataset.type;
        const stepConfig = this.getStepConfig(stepType);
        
        propertiesContent.innerHTML = `
            <div class="property-group">
                <label>步骤名称</label>
                <input type="text" value="${stepConfig.label}" onchange="workflowManager.updateStepProperty('name', this.value)">
            </div>
            <div class="property-group">
                <label>描述</label>
                <textarea placeholder="输入步骤描述" onchange="workflowManager.updateStepProperty('description', this.value)"></textarea>
            </div>
            ${this.getStepSpecificProperties(stepType)}
            <div class="property-actions">
                <button class="btn-danger" onclick="workflowManager.deleteSelectedStep()">
                    <i class="fas fa-trash"></i>
                    删除步骤
                </button>
            </div>
        `;
    }

    getStepSpecificProperties(stepType) {
        switch (stepType) {
            case 'task':
                return `
                    <div class="property-group">
                        <label>负责人</label>
                        <select>
                            <option>选择负责人</option>
                            <option>张管理员</option>
                            <option>李经理</option>
                            <option>王用户</option>
                        </select>
                    </div>
                    <div class="property-group">
                        <label>预计时间</label>
                        <input type="number" placeholder="小时" min="0" step="0.5">
                    </div>
                `;
            case 'approval':
                return `
                    <div class="property-group">
                        <label>审批人</label>
                        <select>
                            <option>选择审批人</option>
                            <option>张管理员</option>
                            <option>李经理</option>
                        </select>
                    </div>
                    <div class="property-group">
                        <label>审批类型</label>
                        <select>
                            <option>单人审批</option>
                            <option>多人审批</option>
                            <option>会签</option>
                        </select>
                    </div>
                `;
            case 'condition':
                return `
                    <div class="property-group">
                        <label>条件表达式</label>
                        <textarea placeholder="输入条件表达式"></textarea>
                    </div>
                `;
            case 'notification':
                return `
                    <div class="property-group">
                        <label>通知方式</label>
                        <div class="checkbox-group">
                            <label><input type="checkbox" checked> 系统通知</label>
                            <label><input type="checkbox"> 邮件通知</label>
                            <label><input type="checkbox"> 短信通知</label>
                        </div>
                    </div>
                    <div class="property-group">
                        <label>通知内容</label>
                        <textarea placeholder="输入通知内容"></textarea>
                    </div>
                `;
            case 'delay':
                return `
                    <div class="property-group">
                        <label>延时时间</label>
                        <div class="time-input">
                            <input type="number" placeholder="数值" min="1">
                            <select>
                                <option>分钟</option>
                                <option>小时</option>
                                <option>天</option>
                            </select>
                        </div>
                    </div>
                `;
            default:
                return '';
        }
    }

    updateStepProperty(property, value) {
        const selectedStep = document.querySelector('.workflow-step.selected');
        if (selectedStep) {
            selectedStep.dataset[property] = value;
            if (property === 'name') {
                const label = selectedStep.querySelector('.step-label');
                if (label) label.textContent = value;
            }
        }
    }

    deleteSelectedStep() {
        const selectedStep = document.querySelector('.workflow-step.selected');
        if (selectedStep && confirm('确定要删除这个步骤吗？')) {
            selectedStep.remove();
            document.getElementById('propertiesContent').innerHTML = '<p>选择一个步骤来编辑属性</p>';
        }
    }

    saveWorkflowDraft() {
        const workflowData = this.collectWorkflowData();
        workflowData.status = 'draft';
        
        // 保存到本地存储
        const drafts = JSON.parse(localStorage.getItem('workflowDrafts') || '[]');
        drafts.push(workflowData);
        localStorage.setItem('workflowDrafts', JSON.stringify(drafts));
        
        if (window.showNotification) {
            showNotification('工作流草稿已保存', 'success');
        }
    }

    publishWorkflow() {
        const workflowData = this.collectWorkflowData();
        
        if (!this.validateWorkflow(workflowData)) {
            return;
        }
        
        workflowData.status = 'published';
        workflowData.publishedAt = new Date();
        
        // 添加到工作流列表
        this.workflows.push(workflowData);
        this.saveWorkflows();
        
        this.closeWorkflowBuilder();
        
        if (window.showNotification) {
            showNotification('工作流发布成功', 'success');
        }
    }

    collectWorkflowData() {
        const steps = Array.from(document.querySelectorAll('.workflow-step')).map(step => ({
            type: step.dataset.type,
            name: step.dataset.name || this.getStepConfig(step.dataset.type).label,
            description: step.dataset.description || '',
            position: {
                x: parseInt(step.style.left),
                y: parseInt(step.style.top)
            },
            properties: this.getStepProperties(step)
        }));
        
        return {
            id: 'wf-' + Date.now(),
            name: document.getElementById('workflowName').value || '未命名工作流',
            description: document.getElementById('workflowDescription').value || '',
            priority: document.getElementById('workflowPriority').value,
            trigger: document.getElementById('workflowTrigger').value,
            steps: steps,
            createdAt: new Date(),
            createdBy: 'current-user'
        };
    }

    getStepProperties(stepElement) {
        // 从步骤元素收集属性
        const properties = {};
        Object.keys(stepElement.dataset).forEach(key => {
            if (key !== 'type') {
                properties[key] = stepElement.dataset[key];
            }
        });
        return properties;
    }

    validateWorkflow(workflowData) {
        if (!workflowData.name.trim()) {
            if (window.showNotification) {
                showNotification('请输入工作流名称', 'error');
            }
            return false;
        }
        
        if (workflowData.steps.length === 0) {
            if (window.showNotification) {
                showNotification('工作流至少需要一个步骤', 'error');
            }
            return false;
        }
        
        const hasStart = workflowData.steps.some(step => step.type === 'start');
        const hasEnd = workflowData.steps.some(step => step.type === 'end');
        
        if (!hasStart) {
            if (window.showNotification) {
                showNotification('工作流必须包含开始步骤', 'error');
            }
            return false;
        }
        
        if (!hasEnd) {
            if (window.showNotification) {
                showNotification('工作流必须包含结束步骤', 'error');
            }
            return false;
        }
        
        return true;
    }

    saveWorkflows() {
        try {
            localStorage.setItem('workflows', JSON.stringify(this.workflows));
        } catch (error) {
            console.error('保存工作流失败:', error);
        }
    }

    loadStoredWorkflows() {
        try {
            const stored = localStorage.getItem('workflows');
            if (stored) {
                this.workflows = JSON.parse(stored);
            }
        } catch (error) {
            console.error('加载工作流失败:', error);
            this.workflows = [];
        }
    }

    filterWorkflows() {
        const statusFilter = document.getElementById('workflowStatusFilter').value;
        const priorityFilter = document.getElementById('workflowPriorityFilter').value;
        const searchQuery = document.getElementById('workflowSearchInput').value.toLowerCase();
        
        const workflowItems = document.querySelectorAll('.workflow-item');
        
        workflowItems.forEach(item => {
            const workflow = this.getWorkflowById(item.dataset.id);
            if (!workflow) return;
            
            let visible = true;
            
            // 状态筛选
            if (statusFilter !== 'all' && workflow.status !== statusFilter) {
                visible = false;
            }
            
            // 优先级筛选
            if (priorityFilter !== 'all' && workflow.priority !== priorityFilter) {
                visible = false;
            }
            
            // 搜索筛选
            if (searchQuery && !workflow.name.toLowerCase().includes(searchQuery)) {
                visible = false;
            }
            
            item.style.display = visible ? 'flex' : 'none';
        });
    }

    getWorkflowById(id) {
        return this.workflows.find(wf => wf.id === id) || 
               this.activeWorkflows.find(wf => wf.id === id);
    }

    useTemplate(templateId) {
        const template = this.workflowTemplates.find(t => t.id === templateId);
        if (!template) return;
        
        this.showCreateWorkflow();
        
        // 填充模板数据
        setTimeout(() => {
            document.getElementById('workflowName').value = template.name;
            document.getElementById('workflowDescription').value = template.description;
            
            // 在画布上创建步骤
            this.createStepsFromTemplate(template);
        }, 100);
    }

    createStepsFromTemplate(template) {
        const canvas = document.getElementById('workflowCanvas');
        const canvasContent = canvas.querySelector('.canvas-content');
        
        // 清空画布
        canvasContent.innerHTML = '';
        
        // 创建步骤
        template.steps.forEach((step, index) => {
            const x = 100 + (index % 3) * 200;
            const y = 100 + Math.floor(index / 3) * 150;
            this.addStepToCanvas(step.type, x, y);
        });
    }

    previewTemplate(templateId) {
        const template = this.workflowTemplates.find(t => t.id === templateId);
        if (!template) return;
        
        // 创建预览模态框
        const modal = document.createElement('div');
        modal.className = 'modal template-preview-modal';
        modal.innerHTML = `
            <div class="modal-content">
                <div class="modal-header">
                    <h3>${template.name}</h3>
                    <button class="modal-close" onclick="this.closest('.modal').remove()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="modal-body">
                    <p>${template.description}</p>
                    <div class="template-steps">
                        <h4>工作流步骤</h4>
                        <div class="steps-list">
                            ${template.steps.map((step, index) => `
                                <div class="step-preview">
                                    <span class="step-number">${index + 1}</span>
                                    <div class="step-info">
                                        <i class="${this.getStepConfig(step.type).icon}"></i>
                                        <span>${step.name}</span>
                                    </div>
                                </div>
                            `).join('')}
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button class="btn-secondary" onclick="this.closest('.modal').remove()">关闭</button>
                    <button class="btn-primary" onclick="workflowManager.useTemplate('${templateId}'); this.closest('.modal').remove();">使用模板</button>
                </div>
            </div>
        `;
        
        document.body.appendChild(modal);
        modal.style.display = 'flex';
    }

    loadWorkflowAnalytics() {
        // 模拟分析数据
        const analytics = {
            totalWorkflows: this.workflows.length + 15,
            avgCompletionTime: '4.2小时',
            successRate: '94.5%',
            activeUsers: 12
        };
        
        // 更新统计数据
        document.getElementById('totalWorkflows').textContent = analytics.totalWorkflows;
        document.getElementById('avgCompletionTime').textContent = analytics.avgCompletionTime;
        document.getElementById('successRate').textContent = analytics.successRate;
        document.getElementById('activeUsers').textContent = analytics.activeUsers;
        
        // 创建图表
        this.createWorkflowCharts();
    }

    createWorkflowCharts() {
        // 工作流趋势图
        const trendCtx = document.getElementById('workflowTrendChart');
        if (trendCtx && window.Chart) {
            new Chart(trendCtx, {
                type: 'line',
                data: {
                    labels: ['1月', '2月', '3月', '4月', '5月', '6月'],
                    datasets: [{
                        label: '完成的工作流',
                        data: [12, 19, 15, 25, 22, 30],
                        borderColor: '#10b981',
                        backgroundColor: 'rgba(16, 185, 129, 0.1)',
                        tension: 0.4
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'top'
                        }
                    }
                }
            });
        }
        
        // 工作流状态图
        const statusCtx = document.getElementById('workflowStatusChart');
        if (statusCtx && window.Chart) {
            new Chart(statusCtx, {
                type: 'doughnut',
                data: {
                    labels: ['已完成', '进行中', '已暂停', '失败'],
                    datasets: [{
                        data: [65, 25, 7, 3],
                        backgroundColor: ['#10b981', '#3b82f6', '#f59e0b', '#ef4444']
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom'
                        }
                    }
                }
            });
        }
    }

    // 工作流操作方法
    viewWorkflowDetail(workflowId) {
        this.currentWorkflow = this.getWorkflowById(workflowId);
        if (!this.currentWorkflow) return;
        
        const detailPanel = document.getElementById('workflowDetailPanel');
        if (detailPanel) {
            detailPanel.classList.add('show');
            this.loadWorkflowDetail();
        }
    }

    loadWorkflowDetail() {
        if (!this.currentWorkflow) return;
        
        // 更新标题和状态
        document.getElementById('detailWorkflowName').textContent = this.currentWorkflow.name;
        document.getElementById('detailWorkflowStatus').textContent = this.getStatusLabel(this.currentWorkflow.status);
        document.getElementById('detailWorkflowStatus').className = `workflow-status ${this.currentWorkflow.status}`;
        
        // 加载进度信息
        this.loadWorkflowProgress();
    }

    loadWorkflowProgress() {
        // 模拟进度数据
        const totalSteps = 6;
        const completedSteps = 3;
        const progress = Math.round((completedSteps / totalSteps) * 100);
        
        document.getElementById('totalSteps').textContent = totalSteps;
        document.getElementById('completedSteps').textContent = completedSteps;
        document.getElementById('progressPercentage').textContent = progress + '%';
        document.getElementById('progressFill').style.width = progress + '%';
        
        // 创建步骤时间线
        this.createStepTimeline();
    }

    createStepTimeline() {
        const timeline = document.getElementById('stepTimeline');
        const steps = [
            { name: '提交申请', status: 'completed', completedAt: '2024-01-15 09:30' },
            { name: '初审', status: 'completed', completedAt: '2024-01-15 10:15' },
            { name: '主管审批', status: 'completed', completedAt: '2024-01-15 11:00' },
            { name: '部门审批', status: 'current', startedAt: '2024-01-15 11:30' },
            { name: '最终审批', status: 'pending' },
            { name: '通知申请人', status: 'pending' }
        ];
        
        timeline.innerHTML = steps.map(step => `
            <div class="timeline-item ${step.status}">
                <div class="timeline-marker">
                    <i class="fas fa-${step.status === 'completed' ? 'check' : step.status === 'current' ? 'clock' : 'circle'}"></i>
                </div>
                <div class="timeline-content">
                    <h5>${step.name}</h5>
                    ${step.completedAt ? `<span class="timeline-time">完成于: ${step.completedAt}</span>` : ''}
                    ${step.startedAt ? `<span class="timeline-time">开始于: ${step.startedAt}</span>` : ''}
                    ${step.status === 'pending' ? '<span class="timeline-time">等待中</span>' : ''}
                </div>
            </div>
        `).join('');
    }

    closeDetailPanel() {
        const detailPanel = document.getElementById('workflowDetailPanel');
        if (detailPanel) {
            detailPanel.classList.remove('show');
        }
    }

    pauseWorkflow(workflowId) {
        if (confirm('确定要暂停这个工作流吗？')) {
            // 模拟暂停操作
            if (window.showNotification) {
                showNotification('工作流已暂停', 'info');
            }
            this.loadActiveWorkflows();
        }
    }

    resumeWorkflow(workflowId) {
        // 模拟恢复操作
        if (window.showNotification) {
            showNotification('工作流已恢复', 'success');
        }
        this.loadActiveWorkflows();
    }

    stopWorkflow(workflowId) {
        if (confirm('确定要停止这个工作流吗？此操作不可撤销。')) {
            // 模拟停止操作
            if (window.showNotification) {
                showNotification('工作流已停止', 'warning');
            }
            this.loadActiveWorkflows();
        }
    }

    deleteWorkflow(workflowId) {
        if (confirm('确定要删除这个工作流吗？此操作不可撤销。')) {
            // 从数组中移除
            this.workflows = this.workflows.filter(wf => wf.id !== workflowId);
            this.saveWorkflows();
            
            if (window.showNotification) {
                showNotification('工作流已删除', 'success');
            }
            
            this.loadActiveWorkflows();
        }
    }

    // 画布操作方法
    zoomIn() {
        const canvas = document.getElementById('workflowCanvas');
        const currentScale = parseFloat(canvas.dataset.scale || '1');
        const newScale = Math.min(currentScale + 0.1, 2);
        this.setCanvasScale(newScale);
    }

    zoomOut() {
        const canvas = document.getElementById('workflowCanvas');
        const currentScale = parseFloat(canvas.dataset.scale || '1');
        const newScale = Math.max(currentScale - 0.1, 0.5);
        this.setCanvasScale(newScale);
    }

    resetZoom() {
        this.setCanvasScale(1);
    }

    setCanvasScale(scale) {
        const canvas = document.getElementById('workflowCanvas');
        const canvasContent = canvas.querySelector('.canvas-content');
        
        canvas.dataset.scale = scale;
        canvasContent.style.transform = `scale(${scale})`;
        canvasContent.style.transformOrigin = 'top left';
    }

    autoLayout() {
        const steps = document.querySelectorAll('.workflow-step');
        const startStep = Array.from(steps).find(step => step.dataset.type === 'start');
        
        if (!startStep) {
            if (window.showNotification) {
                showNotification('请先添加开始步骤', 'warning');
            }
            return;
        }
        
        // 简单的自动布局算法
        let x = 100;
        let y = 100;
        const stepWidth = 150;
        const stepHeight = 100;
        
        steps.forEach((step, index) => {
            step.style.left = x + 'px';
            step.style.top = y + 'px';
            
            x += stepWidth + 50;
            if ((index + 1) % 4 === 0) {
                x = 100;
                y += stepHeight + 50;
            }
        });
        
        if (window.showNotification) {
            showNotification('自动布局完成', 'success');
        }
    }
}

// 全局工作流管理器实例
let workflowManager = null;

// 初始化工作流管理系统
function initializeWorkflowManagement() {
    workflowManager = new WorkflowManagement();
    console.log('✅ 工作流管理系统已初始化');
}

// 显示工作流面板
function showWorkflowPanel() {
    if (workflowManager) {
        workflowManager.showWorkflowPanel();
    }
}

// 页面加载时初始化
document.addEventListener('DOMContentLoaded', function() {
    setTimeout(initializeWorkflowManagement, 800);
});
