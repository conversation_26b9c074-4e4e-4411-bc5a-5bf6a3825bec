// 用户菜单功能
class UserMenuManager {
    constructor() {
        this.isMenuOpen = false;
        this.currentUser = null;
        this.loginTime = null;
        
        this.initializeUserMenu();
        this.bindEvents();
    }
    
    initializeUserMenu() {
        // 从localStorage获取当前用户信息
        const userData = localStorage.getItem('currentUser');
        if (userData) {
            this.currentUser = JSON.parse(userData);
            this.updateUserDisplay();
        }
        
        // 获取登录时间
        const loginTimeData = localStorage.getItem('loginTime');
        if (loginTimeData) {
            this.loginTime = new Date(loginTimeData);
            this.updateLoginTimeDisplay();
        } else {
            // 如果没有登录时间，设置为当前时间
            this.loginTime = new Date();
            localStorage.setItem('loginTime', this.loginTime.toISOString());
            this.updateLoginTimeDisplay();
        }
    }
    
    bindEvents() {
        // 点击外部关闭菜单
        document.addEventListener('click', (e) => {
            const userProfile = document.querySelector('.user-profile');
            const userMenu = document.getElementById('userDropdownMenu');
            
            if (userProfile && !userProfile.contains(e.target) && this.isMenuOpen) {
                this.closeUserMenu();
            }
        });
        
        // ESC键关闭菜单
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && this.isMenuOpen) {
                this.closeUserMenu();
            }
        });
    }
    
    updateUserDisplay() {
        if (!this.currentUser) return;

        // 更新侧边栏用户信息
        const userNameEl = document.querySelector('.sidebar .user-name');
        const userRoleEl = document.querySelector('.sidebar .user-role');

        if (userNameEl) userNameEl.textContent = this.currentUser.name || this.currentUser.username;
        if (userRoleEl) userRoleEl.textContent = this.currentUser.role || '用户';

        // 更新右上角用户信息
        const headerUserNameEl = document.getElementById('headerUserName');
        if (headerUserNameEl) headerUserNameEl.textContent = this.currentUser.name || this.currentUser.username;

        // 更新侧边栏下拉菜单中的用户信息
        const currentUserNameEl = document.getElementById('currentUserName');
        const currentUserRoleEl = document.getElementById('currentUserRole');
        const currentUserEmailEl = document.getElementById('currentUserEmail');

        if (currentUserNameEl) currentUserNameEl.textContent = this.currentUser.name || this.currentUser.username;
        if (currentUserRoleEl) currentUserRoleEl.textContent = this.currentUser.role || '用户';
        if (currentUserEmailEl) currentUserEmailEl.textContent = this.currentUser.email || `${this.currentUser.username}@company.com`;

        // 更新右上角下拉菜单中的用户信息
        const dropdownUserNameEl = document.getElementById('dropdownUserName');
        const dropdownUserRoleEl = document.getElementById('dropdownUserRole');
        const dropdownUserEmailEl = document.getElementById('dropdownUserEmail');

        if (dropdownUserNameEl) dropdownUserNameEl.textContent = this.currentUser.name || this.currentUser.username;
        if (dropdownUserRoleEl) dropdownUserRoleEl.textContent = this.currentUser.role || '用户';
        if (dropdownUserEmailEl) dropdownUserEmailEl.textContent = this.currentUser.email || `${this.currentUser.username}@company.com`;

        // 更新用户头像
        const avatarName = encodeURIComponent(this.currentUser.name || this.currentUser.username);
        const avatarUrl = `https://ui-avatars.com/api/?name=${avatarName}&background=6366f1&color=fff`;

        const avatarImgs = document.querySelectorAll('.user-avatar img, .user-avatar-large img, #headerUserAvatar, #dropdownUserAvatar');
        avatarImgs.forEach(img => {
            img.src = avatarUrl;
            img.alt = this.currentUser.name || this.currentUser.username;
        });
    }
    
    updateLoginTimeDisplay() {
        const loginTimeEl = document.getElementById('loginTime');
        if (loginTimeEl && this.loginTime) {
            const now = new Date();
            const diffMs = now - this.loginTime;
            const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
            const diffMinutes = Math.floor((diffMs % (1000 * 60 * 60)) / (1000 * 60));
            
            let timeText;
            if (diffHours > 0) {
                timeText = `登录时间: ${diffHours}小时${diffMinutes}分钟前`;
            } else if (diffMinutes > 0) {
                timeText = `登录时间: ${diffMinutes}分钟前`;
            } else {
                timeText = '登录时间: 刚刚';
            }
            
            loginTimeEl.textContent = timeText;
        }
    }
    
    toggleUserMenu() {
        if (this.isMenuOpen) {
            this.closeUserMenu();
        } else {
            this.openUserMenu();
        }
    }
    
    openUserMenu() {
        const userMenu = document.getElementById('userDropdownMenu');
        if (userMenu) {
            userMenu.classList.add('show');
            this.isMenuOpen = true;
            
            // 更新登录时间显示
            this.updateLoginTimeDisplay();
        }
    }
    
    closeUserMenu() {
        const userMenu = document.getElementById('userDropdownMenu');
        if (userMenu) {
            userMenu.classList.remove('show');
            this.isMenuOpen = false;
        }
    }
    
    logout() {
        // 显示确认对话框
        if (confirm('确定要退出登录吗？')) {
            this.performLogout();
        }
    }
    
    performLogout() {
        try {
            // 清除用户数据
            localStorage.removeItem('isLoggedIn');
            localStorage.removeItem('currentUser');
            localStorage.removeItem('loginTime');
            
            // 清除其他可能的用户相关数据
            localStorage.removeItem('userPreferences');
            localStorage.removeItem('sessionData');
            
            // 显示退出消息
            this.showLogoutMessage();
            
            // 延迟跳转到登录页面
            setTimeout(() => {
                window.location.href = 'login.html';
            }, 1500);
            
        } catch (error) {
            console.error('退出登录时发生错误:', error);
            // 即使出错也要跳转到登录页面
            window.location.href = 'login.html';
        }
    }
    
    showLogoutMessage() {
        // 创建退出消息提示
        const message = document.createElement('div');
        message.className = 'logout-message';
        message.innerHTML = `
            <div class="logout-content">
                <i class="fas fa-sign-out-alt"></i>
                <h3>正在退出...</h3>
                <p>感谢您的使用，即将跳转到登录页面</p>
            </div>
        `;
        
        // 添加样式
        message.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.8);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 9999;
            color: white;
            text-align: center;
        `;
        
        const content = message.querySelector('.logout-content');
        content.style.cssText = `
            background: white;
            color: #333;
            padding: 2rem;
            border-radius: 10px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
            max-width: 300px;
        `;
        
        const icon = content.querySelector('i');
        icon.style.cssText = `
            font-size: 3rem;
            color: #6366f1;
            margin-bottom: 1rem;
        `;
        
        const title = content.querySelector('h3');
        title.style.cssText = `
            margin: 0 0 0.5rem 0;
            font-size: 1.5rem;
            color: #333;
        `;
        
        const text = content.querySelector('p');
        text.style.cssText = `
            margin: 0;
            color: #666;
            font-size: 0.9rem;
        `;
        
        document.body.appendChild(message);
    }
    
    switchAccount() {
        if (confirm('确定要切换账户吗？当前会话将被清除。')) {
            // 清除当前用户数据但保留登录状态标记
            localStorage.removeItem('currentUser');
            localStorage.removeItem('loginTime');
            
            // 跳转到登录页面
            window.location.href = 'login.html';
        }
    }
    
    lockScreen() {
        if (confirm('确定要锁定屏幕吗？需要重新输入密码才能解锁。')) {
            // 设置锁定状态
            localStorage.setItem('screenLocked', 'true');
            localStorage.setItem('lockTime', new Date().toISOString());
            
            // 跳转到登录页面
            window.location.href = 'login.html?locked=true';
        }
    }
    
    showUserProfile() {
        this.closeUserMenu();
        if (window.userAccountManager) {
            userAccountManager.showUserProfile();
        } else {
            showNotification('个人资料功能加载中...', 'info');
        }
    }

    showUserPreferences() {
        this.closeUserMenu();
        showNotification('偏好设置功能开发中...', 'info');
    }

    showHelp() {
        this.closeUserMenu();
        showNotification('帮助中心功能开发中...', 'info');
    }
}

// 全局用户菜单管理器实例
let userMenuManager = null;

// 用户菜单相关的全局函数
function toggleUserMenu() {
    if (userMenuManager) {
        userMenuManager.toggleUserMenu();
    }
}

function closeUserMenu() {
    if (userMenuManager) {
        userMenuManager.closeUserMenu();
    }
}

// 右上角用户菜单控制函数
function toggleHeaderUserMenu() {
    const dropdown = document.getElementById('headerUserDropdown');
    if (dropdown) {
        dropdown.classList.toggle('show');

        // 更新登录时间显示
        if (userMenuManager) {
            userMenuManager.updateLoginTimeDisplay();
        }
    }
}

function closeHeaderUserMenu() {
    const dropdown = document.getElementById('headerUserDropdown');
    if (dropdown) {
        dropdown.classList.remove('show');
    }
}

// 点击外部关闭右上角菜单
document.addEventListener('click', function(e) {
    const userProfile = document.querySelector('.user-profile');
    const dropdown = document.getElementById('headerUserDropdown');

    if (userProfile && dropdown && !userProfile.contains(e.target)) {
        dropdown.classList.remove('show');
    }
});

function logout() {
    if (userMenuManager) {
        userMenuManager.logout();
    }
}

function switchAccount() {
    if (userMenuManager) {
        userMenuManager.switchAccount();
    }
}

function lockScreen() {
    if (userMenuManager) {
        userMenuManager.lockScreen();
    }
}

function showUserProfile() {
    if (userMenuManager) {
        userMenuManager.showUserProfile();
    }
}

function showUserPreferences() {
    if (userMenuManager) {
        userMenuManager.showUserPreferences();
    }
}

function showHelp() {
    if (userMenuManager) {
        userMenuManager.showHelp();
    }
}

function showChangePasswordModal() {
    if (window.userAccountManager) {
        userAccountManager.showChangePasswordModal();
    } else {
        showNotification('功能加载中...', 'info');
    }
}

function viewLoginHistory() {
    if (window.userAccountManager) {
        userAccountManager.viewLoginHistory();
    } else {
        showNotification('功能加载中...', 'info');
    }
}

// 初始化用户菜单
function initializeUserMenu() {
    userMenuManager = new UserMenuManager();
    console.log('✅ 用户菜单已初始化');
}

// 页面加载时初始化
document.addEventListener('DOMContentLoaded', function() {
    // 延迟初始化，确保其他组件已加载
    setTimeout(initializeUserMenu, 100);
});
