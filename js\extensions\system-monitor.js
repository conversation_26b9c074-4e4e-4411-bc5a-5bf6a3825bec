// 系统监控面板
class SystemMonitor {
    constructor() {
        this.isMonitoring = false;
        this.monitoringInterval = null;
        this.systemData = {
            cpu: { current: 0, history: [] },
            memory: { current: 0, history: [] },
            disk: { current: 0, history: [] },
            network: { upload: 0, download: 0, history: [] },
            uptime: 0,
            processes: [],
            alerts: []
        };
        this.thresholds = {
            cpu: { warning: 70, critical: 90 },
            memory: { warning: 80, critical: 95 },
            disk: { warning: 85, critical: 95 }
        };
        
        this.initializeMonitor();
    }

    initializeMonitor() {
        this.createMonitorPanel();
        this.bindEvents();
        this.startMonitoring();
    }

    createMonitorPanel() {
        // 创建系统监控面板
        const panel = document.createElement('div');
        panel.id = 'systemMonitorPanel';
        panel.className = 'system-monitor-panel';
        panel.innerHTML = `
            <div class="monitor-header">
                <h3>
                    <i class="fas fa-desktop"></i>
                    系统监控
                </h3>
                <div class="monitor-controls">
                    <button class="btn-icon" id="refreshMonitor" title="刷新">
                        <i class="fas fa-sync-alt"></i>
                    </button>
                    <button class="btn-icon" id="exportMonitorData" title="导出数据">
                        <i class="fas fa-download"></i>
                    </button>
                    <button class="btn-icon" id="monitorSettings" title="设置">
                        <i class="fas fa-cog"></i>
                    </button>
                    <button class="btn-icon" onclick="systemMonitor.closeMonitorPanel()" title="关闭">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            </div>
            
            <div class="monitor-body">
                <!-- 系统概览 -->
                <div class="monitor-section">
                    <h4>系统概览</h4>
                    <div class="system-overview">
                        <div class="overview-item">
                            <div class="overview-icon cpu">
                                <i class="fas fa-microchip"></i>
                            </div>
                            <div class="overview-content">
                                <div class="overview-label">CPU使用率</div>
                                <div class="overview-value" id="cpuUsage">0%</div>
                                <div class="overview-bar">
                                    <div class="bar-fill" id="cpuBar"></div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="overview-item">
                            <div class="overview-icon memory">
                                <i class="fas fa-memory"></i>
                            </div>
                            <div class="overview-content">
                                <div class="overview-label">内存使用率</div>
                                <div class="overview-value" id="memoryUsage">0%</div>
                                <div class="overview-bar">
                                    <div class="bar-fill" id="memoryBar"></div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="overview-item">
                            <div class="overview-icon disk">
                                <i class="fas fa-hdd"></i>
                            </div>
                            <div class="overview-content">
                                <div class="overview-label">磁盘使用率</div>
                                <div class="overview-value" id="diskUsage">0%</div>
                                <div class="overview-bar">
                                    <div class="bar-fill" id="diskBar"></div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="overview-item">
                            <div class="overview-icon network">
                                <i class="fas fa-network-wired"></i>
                            </div>
                            <div class="overview-content">
                                <div class="overview-label">网络状态</div>
                                <div class="overview-value" id="networkStatus">正常</div>
                                <div class="network-speed">
                                    <span>↑ <span id="uploadSpeed">0 KB/s</span></span>
                                    <span>↓ <span id="downloadSpeed">0 KB/s</span></span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 实时图表 -->
                <div class="monitor-section">
                    <h4>性能趋势</h4>
                    <div class="monitor-chart">
                        <canvas id="systemPerformanceChart"></canvas>
                    </div>
                </div>
                
                <!-- 系统信息 -->
                <div class="monitor-section">
                    <h4>系统信息</h4>
                    <div class="system-info">
                        <div class="info-grid">
                            <div class="info-item">
                                <span class="info-label">运行时间</span>
                                <span class="info-value" id="systemUptime">0天 0小时 0分钟</span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">操作系统</span>
                                <span class="info-value">Windows 11 Pro</span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">处理器</span>
                                <span class="info-value">Intel Core i7-12700K</span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">总内存</span>
                                <span class="info-value">32 GB DDR4</span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">磁盘空间</span>
                                <span class="info-value">1 TB SSD</span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">网络接口</span>
                                <span class="info-value">Gigabit Ethernet</span>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 进程监控 -->
                <div class="monitor-section">
                    <h4>进程监控</h4>
                    <div class="process-monitor">
                        <div class="process-controls">
                            <input type="text" id="processSearch" placeholder="搜索进程..." class="process-search">
                            <button class="btn-secondary" onclick="systemMonitor.refreshProcesses()">刷新</button>
                        </div>
                        <div class="process-list" id="processList">
                            <!-- 进程列表将在这里动态生成 -->
                        </div>
                    </div>
                </div>
                
                <!-- 系统警告 -->
                <div class="monitor-section">
                    <h4>系统警告</h4>
                    <div class="system-alerts" id="systemAlerts">
                        <!-- 系统警告将在这里显示 -->
                    </div>
                </div>
            </div>
        `;
        
        document.body.appendChild(panel);
    }

    bindEvents() {
        // 绑定刷新按钮
        document.getElementById('refreshMonitor')?.addEventListener('click', () => {
            this.refreshSystemData();
        });

        // 绑定导出按钮
        document.getElementById('exportMonitorData')?.addEventListener('click', () => {
            this.exportMonitorData();
        });

        // 绑定进程搜索
        document.getElementById('processSearch')?.addEventListener('input', (e) => {
            this.filterProcesses(e.target.value);
        });

        // 绑定设置按钮
        document.getElementById('monitorSettings')?.addEventListener('click', () => {
            this.showMonitorSettings();
        });
    }

    startMonitoring() {
        if (this.isMonitoring) return;
        
        this.isMonitoring = true;
        this.monitoringInterval = setInterval(() => {
            this.updateSystemData();
        }, 2000); // 每2秒更新一次

        // 初始化数据
        this.updateSystemData();
        this.createPerformanceChart();
    }

    stopMonitoring() {
        this.isMonitoring = false;
        if (this.monitoringInterval) {
            clearInterval(this.monitoringInterval);
            this.monitoringInterval = null;
        }
    }

    updateSystemData() {
        // 模拟系统数据更新
        this.generateSystemData();
        this.updateUI();
        this.checkThresholds();
        this.updatePerformanceChart();
    }

    generateSystemData() {
        // 生成模拟的系统数据
        const now = Date.now();
        
        // CPU使用率 (模拟波动)
        this.systemData.cpu.current = Math.max(0, Math.min(100, 
            this.systemData.cpu.current + (Math.random() - 0.5) * 10
        ));
        if (this.systemData.cpu.current === 0) {
            this.systemData.cpu.current = Math.random() * 30 + 20;
        }
        
        // 内存使用率
        this.systemData.memory.current = Math.max(0, Math.min(100,
            this.systemData.memory.current + (Math.random() - 0.5) * 5
        ));
        if (this.systemData.memory.current === 0) {
            this.systemData.memory.current = Math.random() * 20 + 40;
        }
        
        // 磁盘使用率 (变化较慢)
        this.systemData.disk.current = Math.max(0, Math.min(100,
            this.systemData.disk.current + (Math.random() - 0.5) * 2
        ));
        if (this.systemData.disk.current === 0) {
            this.systemData.disk.current = Math.random() * 15 + 65;
        }
        
        // 网络速度
        this.systemData.network.upload = Math.random() * 1000 + 100;
        this.systemData.network.download = Math.random() * 5000 + 500;
        
        // 更新历史数据
        this.systemData.cpu.history.push({ time: now, value: this.systemData.cpu.current });
        this.systemData.memory.history.push({ time: now, value: this.systemData.memory.current });
        this.systemData.disk.history.push({ time: now, value: this.systemData.disk.current });
        
        // 保持历史数据在合理范围内
        const maxHistory = 50;
        if (this.systemData.cpu.history.length > maxHistory) {
            this.systemData.cpu.history = this.systemData.cpu.history.slice(-maxHistory);
            this.systemData.memory.history = this.systemData.memory.history.slice(-maxHistory);
            this.systemData.disk.history = this.systemData.disk.history.slice(-maxHistory);
        }
        
        // 更新运行时间
        this.systemData.uptime += 2; // 每次更新增加2秒
        
        // 生成进程数据
        this.generateProcessData();
    }

    generateProcessData() {
        const processes = [
            { name: 'chrome.exe', pid: 1234, cpu: Math.random() * 20, memory: Math.random() * 500 + 100 },
            { name: 'node.exe', pid: 5678, cpu: Math.random() * 15, memory: Math.random() * 200 + 50 },
            { name: 'code.exe', pid: 9012, cpu: Math.random() * 10, memory: Math.random() * 300 + 150 },
            { name: 'system', pid: 4, cpu: Math.random() * 5, memory: Math.random() * 100 + 20 },
            { name: 'explorer.exe', pid: 3456, cpu: Math.random() * 8, memory: Math.random() * 150 + 80 }
        ];
        
        this.systemData.processes = processes.sort((a, b) => b.cpu - a.cpu);
    }

    updateUI() {
        // 更新CPU
        const cpuElement = document.getElementById('cpuUsage');
        const cpuBar = document.getElementById('cpuBar');
        if (cpuElement && cpuBar) {
            const cpuValue = Math.round(this.systemData.cpu.current);
            cpuElement.textContent = cpuValue + '%';
            cpuBar.style.width = cpuValue + '%';
            cpuBar.className = 'bar-fill ' + this.getStatusClass('cpu', cpuValue);
        }
        
        // 更新内存
        const memoryElement = document.getElementById('memoryUsage');
        const memoryBar = document.getElementById('memoryBar');
        if (memoryElement && memoryBar) {
            const memoryValue = Math.round(this.systemData.memory.current);
            memoryElement.textContent = memoryValue + '%';
            memoryBar.style.width = memoryValue + '%';
            memoryBar.className = 'bar-fill ' + this.getStatusClass('memory', memoryValue);
        }
        
        // 更新磁盘
        const diskElement = document.getElementById('diskUsage');
        const diskBar = document.getElementById('diskBar');
        if (diskElement && diskBar) {
            const diskValue = Math.round(this.systemData.disk.current);
            diskElement.textContent = diskValue + '%';
            diskBar.style.width = diskValue + '%';
            diskBar.className = 'bar-fill ' + this.getStatusClass('disk', diskValue);
        }
        
        // 更新网络
        const uploadElement = document.getElementById('uploadSpeed');
        const downloadElement = document.getElementById('downloadSpeed');
        if (uploadElement && downloadElement) {
            uploadElement.textContent = this.formatBytes(this.systemData.network.upload) + '/s';
            downloadElement.textContent = this.formatBytes(this.systemData.network.download) + '/s';
        }
        
        // 更新运行时间
        const uptimeElement = document.getElementById('systemUptime');
        if (uptimeElement) {
            uptimeElement.textContent = this.formatUptime(this.systemData.uptime);
        }
        
        // 更新进程列表
        this.updateProcessList();
    }

    getStatusClass(type, value) {
        const threshold = this.thresholds[type];
        if (value >= threshold.critical) return 'critical';
        if (value >= threshold.warning) return 'warning';
        return 'normal';
    }

    formatBytes(bytes) {
        if (bytes < 1024) return bytes.toFixed(0) + ' B';
        if (bytes < 1024 * 1024) return (bytes / 1024).toFixed(1) + ' KB';
        return (bytes / (1024 * 1024)).toFixed(1) + ' MB';
    }

    formatUptime(seconds) {
        const days = Math.floor(seconds / 86400);
        const hours = Math.floor((seconds % 86400) / 3600);
        const minutes = Math.floor((seconds % 3600) / 60);
        return `${days}天 ${hours}小时 ${minutes}分钟`;
    }

    updateProcessList() {
        const processList = document.getElementById('processList');
        if (!processList) return;
        
        processList.innerHTML = `
            <div class="process-header">
                <span>进程名称</span>
                <span>PID</span>
                <span>CPU</span>
                <span>内存</span>
            </div>
            ${this.systemData.processes.map(process => `
                <div class="process-item">
                    <span class="process-name">${process.name}</span>
                    <span class="process-pid">${process.pid}</span>
                    <span class="process-cpu">${process.cpu.toFixed(1)}%</span>
                    <span class="process-memory">${this.formatBytes(process.memory * 1024 * 1024)}</span>
                </div>
            `).join('')}
        `;
    }

    checkThresholds() {
        const alerts = [];
        
        // 检查CPU
        if (this.systemData.cpu.current >= this.thresholds.cpu.critical) {
            alerts.push({
                type: 'critical',
                message: `CPU使用率过高: ${Math.round(this.systemData.cpu.current)}%`,
                timestamp: new Date()
            });
        } else if (this.systemData.cpu.current >= this.thresholds.cpu.warning) {
            alerts.push({
                type: 'warning',
                message: `CPU使用率较高: ${Math.round(this.systemData.cpu.current)}%`,
                timestamp: new Date()
            });
        }
        
        // 检查内存
        if (this.systemData.memory.current >= this.thresholds.memory.critical) {
            alerts.push({
                type: 'critical',
                message: `内存使用率过高: ${Math.round(this.systemData.memory.current)}%`,
                timestamp: new Date()
            });
        } else if (this.systemData.memory.current >= this.thresholds.memory.warning) {
            alerts.push({
                type: 'warning',
                message: `内存使用率较高: ${Math.round(this.systemData.memory.current)}%`,
                timestamp: new Date()
            });
        }
        
        // 检查磁盘
        if (this.systemData.disk.current >= this.thresholds.disk.critical) {
            alerts.push({
                type: 'critical',
                message: `磁盘使用率过高: ${Math.round(this.systemData.disk.current)}%`,
                timestamp: new Date()
            });
        } else if (this.systemData.disk.current >= this.thresholds.disk.warning) {
            alerts.push({
                type: 'warning',
                message: `磁盘使用率较高: ${Math.round(this.systemData.disk.current)}%`,
                timestamp: new Date()
            });
        }
        
        // 更新警告列表
        if (alerts.length > 0) {
            this.systemData.alerts = [...alerts, ...this.systemData.alerts].slice(0, 10);
            this.updateAlertsList();
            
            // 发送实时通知
            alerts.forEach(alert => {
                if (window.realtimeNotifications) {
                    realtimeNotifications.notify('system', '系统监控警告', alert.message, alert.type === 'critical' ? 'high' : 'normal');
                }
            });
        }
    }

    updateAlertsList() {
        const alertsContainer = document.getElementById('systemAlerts');
        if (!alertsContainer) return;
        
        if (this.systemData.alerts.length === 0) {
            alertsContainer.innerHTML = `
                <div class="no-alerts">
                    <i class="fas fa-check-circle"></i>
                    <p>系统运行正常，无警告</p>
                </div>
            `;
            return;
        }
        
        alertsContainer.innerHTML = this.systemData.alerts.map(alert => `
            <div class="alert-item ${alert.type}">
                <div class="alert-icon">
                    <i class="fas fa-${alert.type === 'critical' ? 'exclamation-triangle' : 'exclamation-circle'}"></i>
                </div>
                <div class="alert-content">
                    <div class="alert-message">${alert.message}</div>
                    <div class="alert-time">${alert.timestamp.toLocaleTimeString()}</div>
                </div>
            </div>
        `).join('');
    }

    createPerformanceChart() {
        const ctx = document.getElementById('systemPerformanceChart');
        if (!ctx) return;
        
        this.performanceChart = new Chart(ctx, {
            type: 'line',
            data: {
                labels: [],
                datasets: [{
                    label: 'CPU',
                    data: [],
                    borderColor: '#ef4444',
                    backgroundColor: 'rgba(239, 68, 68, 0.1)',
                    tension: 0.4
                }, {
                    label: '内存',
                    data: [],
                    borderColor: '#f59e0b',
                    backgroundColor: 'rgba(245, 158, 11, 0.1)',
                    tension: 0.4
                }, {
                    label: '磁盘',
                    data: [],
                    borderColor: '#10b981',
                    backgroundColor: 'rgba(16, 185, 129, 0.1)',
                    tension: 0.4
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        max: 100,
                        ticks: {
                            callback: function(value) {
                                return value + '%';
                            }
                        }
                    }
                },
                plugins: {
                    legend: {
                        position: 'top'
                    }
                },
                animation: {
                    duration: 0
                }
            }
        });
    }

    updatePerformanceChart() {
        if (!this.performanceChart) return;
        
        const chart = this.performanceChart;
        const maxPoints = 20;
        
        // 更新标签
        const now = new Date();
        chart.data.labels.push(now.toLocaleTimeString());
        if (chart.data.labels.length > maxPoints) {
            chart.data.labels.shift();
        }
        
        // 更新数据
        chart.data.datasets[0].data.push(this.systemData.cpu.current);
        chart.data.datasets[1].data.push(this.systemData.memory.current);
        chart.data.datasets[2].data.push(this.systemData.disk.current);
        
        // 保持数据点数量
        chart.data.datasets.forEach(dataset => {
            if (dataset.data.length > maxPoints) {
                dataset.data.shift();
            }
        });
        
        chart.update('none');
    }

    showMonitorPanel() {
        const panel = document.getElementById('systemMonitorPanel');
        if (panel) {
            panel.classList.add('show');
        }
    }

    closeMonitorPanel() {
        const panel = document.getElementById('systemMonitorPanel');
        if (panel) {
            panel.classList.remove('show');
        }
    }

    refreshSystemData() {
        this.updateSystemData();
        if (window.showNotification) {
            showNotification('系统数据已刷新', 'success');
        }
    }

    refreshProcesses() {
        this.generateProcessData();
        this.updateProcessList();
        if (window.showNotification) {
            showNotification('进程列表已刷新', 'success');
        }
    }

    filterProcesses(query) {
        const processItems = document.querySelectorAll('.process-item');
        processItems.forEach(item => {
            const processName = item.querySelector('.process-name').textContent.toLowerCase();
            const visible = processName.includes(query.toLowerCase());
            item.style.display = visible ? 'grid' : 'none';
        });
    }

    exportMonitorData() {
        const data = {
            timestamp: new Date().toISOString(),
            systemData: this.systemData,
            thresholds: this.thresholds
        };
        
        const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = `system-monitor-${new Date().toISOString().split('T')[0]}.json`;
        link.click();
        URL.revokeObjectURL(url);
        
        if (window.showNotification) {
            showNotification('监控数据已导出', 'success');
        }
    }

    showMonitorSettings() {
        // 显示监控设置对话框
        const settings = prompt(`请输入监控阈值设置 (格式: cpu_warning,cpu_critical,memory_warning,memory_critical,disk_warning,disk_critical):`, 
            `${this.thresholds.cpu.warning},${this.thresholds.cpu.critical},${this.thresholds.memory.warning},${this.thresholds.memory.critical},${this.thresholds.disk.warning},${this.thresholds.disk.critical}`);
        
        if (settings) {
            const values = settings.split(',').map(v => parseInt(v.trim()));
            if (values.length === 6 && values.every(v => !isNaN(v))) {
                this.thresholds.cpu.warning = values[0];
                this.thresholds.cpu.critical = values[1];
                this.thresholds.memory.warning = values[2];
                this.thresholds.memory.critical = values[3];
                this.thresholds.disk.warning = values[4];
                this.thresholds.disk.critical = values[5];
                
                if (window.showNotification) {
                    showNotification('监控阈值已更新', 'success');
                }
            } else {
                if (window.showNotification) {
                    showNotification('阈值格式错误', 'error');
                }
            }
        }
    }
}

// 全局系统监控实例
let systemMonitor = null;

// 初始化系统监控
function initializeSystemMonitor() {
    systemMonitor = new SystemMonitor();
    console.log('✅ 系统监控已初始化');
}

// 显示系统监控面板
function showSystemMonitor() {
    if (systemMonitor) {
        systemMonitor.showMonitorPanel();
    }
}

// 页面加载时初始化
document.addEventListener('DOMContentLoaded', function() {
    setTimeout(initializeSystemMonitor, 600);
});
