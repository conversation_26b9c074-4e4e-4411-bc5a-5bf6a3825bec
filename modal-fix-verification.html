<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>模态框修复验证</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            padding: 20px;
            background: var(--gray-100);
            font-family: 'Inter', sans-serif;
        }
        .verification-container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: var(--border-radius);
            box-shadow: var(--shadow);
        }
        .test-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }
        .test-card {
            background: var(--gray-50);
            padding: 20px;
            border-radius: var(--border-radius);
            border: 1px solid var(--gray-200);
        }
        .test-card h3 {
            margin-top: 0;
            color: var(--gray-900);
        }
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        .status-success { background: var(--success-color); }
        .status-warning { background: var(--warning-color); }
        .status-error { background: var(--danger-color); }
    </style>
</head>
<body>
    <div class="verification-container">
        <h1>模态框修复验证页面</h1>
        <p>此页面用于验证模态框的修复效果，包括滚动功能和表单提交功能。</p>
        
        <div class="test-grid">
            <div class="test-card">
                <h3><span class="status-indicator status-success"></span>基础功能测试</h3>
                <p>测试模态框的基本显示和关闭功能</p>
                <button class="btn-primary" onclick="testBasicModal()">
                    <i class="fas fa-play"></i>
                    测试基础模态框
                </button>
            </div>
            
            <div class="test-card">
                <h3><span class="status-indicator status-success"></span>滚动功能测试</h3>
                <p>测试模态框内容的滚动功能</p>
                <button class="btn-primary" onclick="testScrollModal()">
                    <i class="fas fa-scroll"></i>
                    测试滚动模态框
                </button>
            </div>
            
            <div class="test-card">
                <h3><span class="status-indicator status-success"></span>表单提交测试</h3>
                <p>测试表单验证和提交功能</p>
                <button class="btn-primary" onclick="testFormModal()">
                    <i class="fas fa-edit"></i>
                    测试表单模态框
                </button>
            </div>
            
            <div class="test-card">
                <h3><span class="status-indicator status-success"></span>响应式测试</h3>
                <p>测试不同屏幕尺寸下的显示效果</p>
                <button class="btn-secondary" onclick="showResponsiveInfo()">
                    <i class="fas fa-mobile-alt"></i>
                    查看响应式信息
                </button>
            </div>
        </div>
        
        <div class="test-card">
            <h3>修复内容总结</h3>
            <ul>
                <li><strong>CSS冲突修复：</strong>移除了重复和冲突的模态框样式规则</li>
                <li><strong>滚动功能修复：</strong>确保模态框内容可以正常滚动，添加了正确的flex布局</li>
                <li><strong>JavaScript修复：</strong>添加了缺失的showNotification函数</li>
                <li><strong>响应式优化：</strong>改进了移动端和桌面端的显示效果</li>
                <li><strong>表单验证：</strong>确保表单提交功能正常工作</li>
            </ul>
        </div>
    </div>

    <!-- 基础测试模态框 -->
    <div class="modal-overlay" id="basicTestModal">
        <div class="modal-container small">
            <div class="modal-header">
                <h3>基础功能测试</h3>
                <button class="modal-close" onclick="closeBasicModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-content">
                <p>这是一个基础的模态框测试。</p>
                <p>如果您能看到这个模态框并且可以正常关闭，说明基础功能正常。</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn-secondary" onclick="closeBasicModal()">关闭</button>
            </div>
        </div>
    </div>

    <!-- 滚动测试模态框 -->
    <div class="modal-overlay" id="scrollTestModal">
        <div class="modal-container">
            <div class="modal-header">
                <h3>滚动功能测试</h3>
                <button class="modal-close" onclick="closeScrollModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-content">
                <h4>请尝试滚动此内容</h4>
                <p>这是第一段内容。如果滚动功能正常，您应该能够向下滚动查看更多内容。</p>
                <p>这是第二段内容。</p>
                <p>这是第三段内容。</p>
                <p>这是第四段内容。</p>
                <p>这是第五段内容。</p>
                <p>这是第六段内容。</p>
                <p>这是第七段内容。</p>
                <p>这是第八段内容。</p>
                <p>这是第九段内容。</p>
                <p>这是第十段内容。</p>
                <p>这是第十一段内容。</p>
                <p>这是第十二段内容。</p>
                <p>这是第十三段内容。</p>
                <p>这是第十四段内容。</p>
                <p>这是第十五段内容。</p>
                <p>这是第十六段内容。</p>
                <p>这是第十七段内容。</p>
                <p>这是第十八段内容。</p>
                <p>这是第十九段内容。</p>
                <p><strong>如果您能看到这段文字，说明滚动功能正常！</strong></p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn-secondary" onclick="closeScrollModal()">关闭</button>
            </div>
        </div>
    </div>

    <!-- 表单测试模态框 -->
    <div class="modal-overlay" id="formTestModal">
        <div class="modal-container">
            <div class="modal-header">
                <h3>表单提交测试</h3>
                <button class="modal-close" onclick="closeFormModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-content">
                <form id="testForm">
                    <div class="form-group">
                        <label for="testName">姓名 *</label>
                        <input type="text" id="testName" name="name" required>
                    </div>
                    <div class="form-group">
                        <label for="testEmail">邮箱 *</label>
                        <input type="email" id="testEmail" name="email" required>
                    </div>
                    <div class="form-group">
                        <label for="testMessage">消息</label>
                        <textarea id="testMessage" name="message" rows="4" placeholder="请输入您的消息..."></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn-secondary" onclick="closeFormModal()">取消</button>
                <button type="button" class="btn-primary" onclick="submitTestForm()">
                    <i class="fas fa-paper-plane"></i>
                    提交测试
                </button>
            </div>
        </div>
    </div>

    <script>
        // 简单的通知函数
        function showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            notification.className = `notification notification-${type}`;
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: ${type === 'success' ? '#10b981' : type === 'error' ? '#ef4444' : '#3b82f6'};
                color: white;
                padding: 12px 20px;
                border-radius: 8px;
                box-shadow: 0 4px 12px rgba(0,0,0,0.15);
                z-index: 10000;
                transform: translateX(100%);
                transition: transform 0.3s ease;
            `;
            notification.textContent = message;
            
            document.body.appendChild(notification);
            
            setTimeout(() => {
                notification.style.transform = 'translateX(0)';
            }, 10);
            
            setTimeout(() => {
                notification.style.transform = 'translateX(100%)';
                setTimeout(() => {
                    if (notification.parentNode) {
                        notification.parentNode.removeChild(notification);
                    }
                }, 300);
            }, 3000);
        }

        // 测试函数
        function testBasicModal() {
            document.getElementById('basicTestModal').classList.add('active');
        }

        function closeBasicModal() {
            document.getElementById('basicTestModal').classList.remove('active');
            showNotification('基础功能测试完成', 'success');
        }

        function testScrollModal() {
            document.getElementById('scrollTestModal').classList.add('active');
        }

        function closeScrollModal() {
            document.getElementById('scrollTestModal').classList.remove('active');
            showNotification('滚动功能测试完成', 'success');
        }

        function testFormModal() {
            document.getElementById('formTestModal').classList.add('active');
        }

        function closeFormModal() {
            document.getElementById('formTestModal').classList.remove('active');
        }

        function submitTestForm() {
            const form = document.getElementById('testForm');
            const formData = new FormData(form);
            
            if (!formData.get('name') || !formData.get('email')) {
                showNotification('请填写所有必填字段', 'error');
                return;
            }
            
            showNotification('表单提交测试成功！', 'success');
            closeFormModal();
        }

        function showResponsiveInfo() {
            const width = window.innerWidth;
            let deviceType = '';
            
            if (width < 480) {
                deviceType = '超小屏幕 (< 480px) - 全屏模态框';
            } else if (width < 768) {
                deviceType = '小屏幕 (480px - 768px) - 优化模态框';
            } else {
                deviceType = '大屏幕 (> 768px) - 标准模态框';
            }
            
            showNotification(`当前设备: ${deviceType}`, 'info');
        }

        // 点击遮罩层关闭模态框
        document.querySelectorAll('.modal-overlay').forEach(overlay => {
            overlay.addEventListener('click', function(e) {
                if (e.target === this) {
                    this.classList.remove('active');
                }
            });
        });

        // ESC键关闭模态框
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                document.querySelectorAll('.modal-overlay.active').forEach(modal => {
                    modal.classList.remove('active');
                });
            }
        });
    </script>
</body>
</html>
