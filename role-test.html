<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>角色权限测试</title>
    <style>
        body {
            font-family: 'Inter', sans-serif;
            margin: 0;
            padding: 20px;
            background: #f8fafc;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .header {
            background: white;
            padding: 20px;
            border-radius: 12px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
        }
        
        .role-switcher {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
        }
        
        .role-btn {
            padding: 10px 20px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 500;
            transition: all 0.3s;
        }
        
        .role-btn.admin { background: #fbbf24; color: white; }
        .role-btn.manager { background: #3b82f6; color: white; }
        .role-btn.user { background: #10b981; color: white; }
        .role-btn.test { background: #8b5cf6; color: white; }
        .role-btn.demo { background: #f59e0b; color: white; }
        
        .role-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
        }
        
        .test-section {
            background: white;
            padding: 20px;
            border-radius: 12px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
        }
        
        .test-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 0;
            border-bottom: 1px solid #e2e8f0;
        }
        
        .test-item:last-child {
            border-bottom: none;
        }
        
        .status {
            padding: 4px 12px;
            border-radius: 6px;
            font-size: 12px;
            font-weight: 500;
        }
        
        .status.allowed {
            background: #dcfce7;
            color: #166534;
        }
        
        .status.denied {
            background: #fee2e2;
            color: #991b1b;
        }
        
        .current-role {
            font-size: 18px;
            font-weight: 600;
            color: #1e293b;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>角色权限测试页面</h1>
            <p>测试不同角色用户的权限控制</p>
            
            <div class="role-switcher">
                <button class="role-btn admin" onclick="switchRole('系统管理员')">系统管理员</button>
                <button class="role-btn manager" onclick="switchRole('部门经理')">部门经理</button>
                <button class="role-btn user" onclick="switchRole('普通用户')">普通用户</button>
                <button class="role-btn test" onclick="switchRole('测试用户')">测试用户</button>
                <button class="role-btn demo" onclick="switchRole('演示用户')">演示用户</button>
            </div>
            
            <div class="current-role">
                当前角色: <span id="currentRole">未设置</span>
            </div>
        </div>
        
        <div class="test-section">
            <h3>页面访问权限</h3>
            <div class="test-item">
                <span>仪表盘</span>
                <span class="status" id="dashboard-access">检测中...</span>
            </div>
            <div class="test-item">
                <span>用户管理</span>
                <span class="status" id="users-access">检测中...</span>
            </div>
            <div class="test-item">
                <span>订单管理</span>
                <span class="status" id="orders-access">检测中...</span>
            </div>
            <div class="test-item">
                <span>数据分析</span>
                <span class="status" id="analytics-access">检测中...</span>
            </div>
            <div class="test-item">
                <span>NER数据管理</span>
                <span class="status" id="ner-access">检测中...</span>
            </div>
            <div class="test-item">
                <span>文件管理</span>
                <span class="status" id="files-access">检测中...</span>
            </div>
            <div class="test-item">
                <span>通知中心</span>
                <span class="status" id="notifications-access">检测中...</span>
            </div>
            <div class="test-item">
                <span>系统设置</span>
                <span class="status" id="settings-access">检测中...</span>
            </div>
        </div>
        
        <div class="test-section">
            <h3>功能权限</h3>
            <div class="test-item">
                <span>创建用户</span>
                <span class="status" id="create-user">检测中...</span>
            </div>
            <div class="test-item">
                <span>删除用户</span>
                <span class="status" id="delete-user">检测中...</span>
            </div>
            <div class="test-item">
                <span>批量操作</span>
                <span class="status" id="bulk-operations">检测中...</span>
            </div>
            <div class="test-item">
                <span>上传文件</span>
                <span class="status" id="upload-file">检测中...</span>
            </div>
            <div class="test-item">
                <span>系统设置</span>
                <span class="status" id="system-settings">检测中...</span>
            </div>
        </div>
        
        <div class="test-section">
            <h3>测试结果</h3>
            <div id="testResults">
                <p>请选择一个角色开始测试</p>
            </div>
        </div>
    </div>

    <script src="role-based-access.js"></script>
    <script>
        // 模拟权限检查
        function switchRole(role) {
            // 更新当前角色显示
            document.getElementById('currentRole').textContent = role;
            
            // 模拟设置用户数据
            const userData = {
                username: role === '系统管理员' ? 'admin' : 
                         role === '部门经理' ? 'manager' :
                         role === '普通用户' ? 'user' :
                         role === '测试用户' ? 'test' : 'demo',
                name: role === '系统管理员' ? '张管理员' : 
                      role === '部门经理' ? '李经理' :
                      role === '普通用户' ? '王用户' :
                      role === '测试用户' ? '测试员' : '演示员',
                role: role
            };
            
            localStorage.setItem('currentUser', JSON.stringify(userData));
            
            // 重新初始化权限控制
            if (window.rbacManager) {
                rbacManager.loadCurrentUser();
                rbacManager.applyRoleBasedAccess();
            } else {
                // 如果权限管理器还没初始化，手动创建一个
                window.rbacManager = new RoleBasedAccessControl();
            }
            
            // 测试权限
            testPermissions();
        }
        
        function testPermissions() {
            if (!window.rbacManager) {
                return;
            }
            
            const pages = ['dashboard', 'users', 'orders', 'analytics', 'ner', 'files', 'notifications', 'settings'];
            
            // 测试页面权限
            pages.forEach(page => {
                const hasAccess = rbacManager.hasPageAccess(page);
                const element = document.getElementById(page + '-access');
                if (element) {
                    element.textContent = hasAccess ? '允许' : '拒绝';
                    element.className = 'status ' + (hasAccess ? 'allowed' : 'denied');
                }
            });
            
            // 测试功能权限
            const features = [
                { id: 'create-user', module: 'users', action: 'create' },
                { id: 'delete-user', module: 'users', action: 'delete' },
                { id: 'bulk-operations', module: 'users', action: 'bulk_operations' },
                { id: 'upload-file', module: 'files', action: 'upload' },
                { id: 'system-settings', module: 'settings', action: 'system_settings' }
            ];
            
            features.forEach(feature => {
                const hasAccess = rbacManager.hasFeatureAccess(feature.module, feature.action);
                const element = document.getElementById(feature.id);
                if (element) {
                    element.textContent = hasAccess ? '允许' : '拒绝';
                    element.className = 'status ' + (hasAccess ? 'allowed' : 'denied');
                }
            });
            
            // 显示测试结果
            const results = document.getElementById('testResults');
            const role = rbacManager.getCurrentUserRole();
            const permissions = rbacManager.getCurrentUserPermissions();
            
            results.innerHTML = `
                <h4>角色: ${role}</h4>
                <p><strong>可访问页面:</strong> ${permissions.pages ? permissions.pages.join(', ') : '无'}</p>
                <p><strong>权限详情:</strong></p>
                <pre style="background: #f1f5f9; padding: 10px; border-radius: 6px; font-size: 12px; overflow-x: auto;">${JSON.stringify(permissions, null, 2)}</pre>
            `;
        }
        
        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 检查是否有当前用户
            const userData = localStorage.getItem('currentUser');
            if (userData) {
                try {
                    const user = JSON.parse(userData);
                    document.getElementById('currentRole').textContent = user.role || '未知';
                    
                    // 延迟测试权限，等待权限管理器初始化
                    setTimeout(testPermissions, 1000);
                } catch (error) {
                    console.error('解析用户数据失败:', error);
                }
            }
        });
    </script>
</body>
</html>
