// API接口管理系统
class APIManagement {
    constructor() {
        this.apiEndpoints = [];
        this.apiHistory = [];
        this.apiTemplates = [];
        this.currentRequest = null;
        this.requestInterceptors = [];
        this.responseInterceptors = [];
        
        this.initializeAPISystem();
        this.loadAPITemplates();
    }

    initializeAPISystem() {
        this.createAPIInterface();
        this.bindAPIEvents();
        this.loadStoredEndpoints();
        this.setupInterceptors();
    }

    createAPIInterface() {
        const apiPanel = document.createElement('div');
        apiPanel.id = 'apiPanel';
        apiPanel.className = 'api-panel';
        apiPanel.innerHTML = `
            <div class="api-header">
                <h3>
                    <i class="fas fa-code"></i>
                    API接口管理
                </h3>
                <div class="api-controls">
                    <button class="btn-secondary" onclick="apiManager.importCollection()">
                        <i class="fas fa-upload"></i>
                        导入集合
                    </button>
                    <button class="btn-primary" onclick="apiManager.createNewRequest()">
                        <i class="fas fa-plus"></i>
                        新建请求
                    </button>
                    <button class="btn-icon" onclick="apiManager.closeAPIPanel()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            </div>
            
            <div class="api-body">
                <div class="api-sidebar">
                    <div class="api-collections">
                        <h4>接口集合</h4>
                        <div class="collection-tree" id="collectionTree">
                            <!-- 接口集合树 -->
                        </div>
                    </div>
                    
                    <div class="api-history">
                        <h4>请求历史</h4>
                        <div class="history-list" id="historyList">
                            <!-- 历史记录列表 -->
                        </div>
                    </div>
                </div>
                
                <div class="api-main">
                    <div class="request-builder" id="requestBuilder">
                        <div class="request-line">
                            <select id="requestMethod" class="method-select">
                                <option value="GET">GET</option>
                                <option value="POST">POST</option>
                                <option value="PUT">PUT</option>
                                <option value="DELETE">DELETE</option>
                                <option value="PATCH">PATCH</option>
                                <option value="HEAD">HEAD</option>
                                <option value="OPTIONS">OPTIONS</option>
                            </select>
                            <input type="text" id="requestUrl" placeholder="输入API地址" class="url-input">
                            <button class="btn-primary send-btn" onclick="apiManager.sendRequest()">
                                <i class="fas fa-paper-plane"></i>
                                发送
                            </button>
                        </div>
                        
                        <div class="request-tabs">
                            <button class="tab-btn active" data-tab="params">参数</button>
                            <button class="tab-btn" data-tab="headers">请求头</button>
                            <button class="tab-btn" data-tab="body">请求体</button>
                            <button class="tab-btn" data-tab="auth">认证</button>
                            <button class="tab-btn" data-tab="tests">测试</button>
                        </div>
                        
                        <div class="request-content">
                            <!-- 参数标签页 -->
                            <div class="tab-content active" id="paramsTab">
                                <div class="params-section">
                                    <h5>查询参数</h5>
                                    <div class="params-table" id="queryParams">
                                        <div class="param-row">
                                            <input type="text" placeholder="键" class="param-key">
                                            <input type="text" placeholder="值" class="param-value">
                                            <button class="btn-icon" onclick="apiManager.removeParam(this)">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </div>
                                    <button class="btn-secondary" onclick="apiManager.addParam('query')">
                                        <i class="fas fa-plus"></i>
                                        添加参数
                                    </button>
                                </div>
                                
                                <div class="params-section">
                                    <h5>路径参数</h5>
                                    <div class="params-table" id="pathParams">
                                        <!-- 路径参数将自动从URL中提取 -->
                                    </div>
                                </div>
                            </div>
                            
                            <!-- 请求头标签页 -->
                            <div class="tab-content" id="headersTab">
                                <div class="headers-table" id="requestHeaders">
                                    <div class="header-row">
                                        <input type="text" placeholder="Header名称" class="header-key">
                                        <input type="text" placeholder="Header值" class="header-value">
                                        <button class="btn-icon" onclick="apiManager.removeHeader(this)">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </div>
                                <button class="btn-secondary" onclick="apiManager.addHeader()">
                                    <i class="fas fa-plus"></i>
                                    添加Header
                                </button>
                                
                                <div class="common-headers">
                                    <h5>常用Headers</h5>
                                    <div class="header-presets">
                                        <button class="preset-btn" onclick="apiManager.addCommonHeader('Content-Type', 'application/json')">
                                            Content-Type: application/json
                                        </button>
                                        <button class="preset-btn" onclick="apiManager.addCommonHeader('Accept', 'application/json')">
                                            Accept: application/json
                                        </button>
                                        <button class="preset-btn" onclick="apiManager.addCommonHeader('Authorization', 'Bearer ')">
                                            Authorization: Bearer
                                        </button>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- 请求体标签页 -->
                            <div class="tab-content" id="bodyTab">
                                <div class="body-type-selector">
                                    <label class="radio-label">
                                        <input type="radio" name="bodyType" value="none" checked>
                                        <span class="radio-mark"></span>
                                        无
                                    </label>
                                    <label class="radio-label">
                                        <input type="radio" name="bodyType" value="json">
                                        <span class="radio-mark"></span>
                                        JSON
                                    </label>
                                    <label class="radio-label">
                                        <input type="radio" name="bodyType" value="form">
                                        <span class="radio-mark"></span>
                                        表单数据
                                    </label>
                                    <label class="radio-label">
                                        <input type="radio" name="bodyType" value="raw">
                                        <span class="radio-mark"></span>
                                        原始数据
                                    </label>
                                </div>
                                
                                <div class="body-content" id="bodyContent">
                                    <div class="body-editor" id="jsonEditor" style="display: none;">
                                        <textarea id="jsonBody" placeholder='{"key": "value"}'></textarea>
                                        <div class="editor-actions">
                                            <button class="btn-secondary" onclick="apiManager.formatJSON()">
                                                <i class="fas fa-code"></i>
                                                格式化
                                            </button>
                                            <button class="btn-secondary" onclick="apiManager.validateJSON()">
                                                <i class="fas fa-check"></i>
                                                验证
                                            </button>
                                        </div>
                                    </div>
                                    
                                    <div class="body-editor" id="formEditor" style="display: none;">
                                        <div class="form-data-table" id="formData">
                                            <div class="form-row">
                                                <input type="text" placeholder="键" class="form-key">
                                                <input type="text" placeholder="值" class="form-value">
                                                <select class="form-type">
                                                    <option value="text">文本</option>
                                                    <option value="file">文件</option>
                                                </select>
                                                <button class="btn-icon" onclick="apiManager.removeFormData(this)">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </div>
                                        </div>
                                        <button class="btn-secondary" onclick="apiManager.addFormData()">
                                            <i class="fas fa-plus"></i>
                                            添加字段
                                        </button>
                                    </div>
                                    
                                    <div class="body-editor" id="rawEditor" style="display: none;">
                                        <textarea id="rawBody" placeholder="输入原始数据"></textarea>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- 认证标签页 -->
                            <div class="tab-content" id="authTab">
                                <div class="auth-type-selector">
                                    <select id="authType">
                                        <option value="none">无认证</option>
                                        <option value="bearer">Bearer Token</option>
                                        <option value="basic">Basic Auth</option>
                                        <option value="apikey">API Key</option>
                                        <option value="oauth2">OAuth 2.0</option>
                                    </select>
                                </div>
                                
                                <div class="auth-config" id="authConfig">
                                    <!-- 认证配置将根据类型动态生成 -->
                                </div>
                            </div>
                            
                            <!-- 测试标签页 -->
                            <div class="tab-content" id="testsTab">
                                <div class="test-editor">
                                    <h5>前置脚本</h5>
                                    <textarea id="preRequestScript" placeholder="// 在请求发送前执行的JavaScript代码"></textarea>
                                    
                                    <h5>测试脚本</h5>
                                    <textarea id="testScript" placeholder="// 在收到响应后执行的测试代码"></textarea>
                                    
                                    <div class="test-snippets">
                                        <h5>代码片段</h5>
                                        <div class="snippet-buttons">
                                            <button class="snippet-btn" onclick="apiManager.addTestSnippet('status')">
                                                检查状态码
                                            </button>
                                            <button class="snippet-btn" onclick="apiManager.addTestSnippet('json')">
                                                解析JSON
                                            </button>
                                            <button class="snippet-btn" onclick="apiManager.addTestSnippet('header')">
                                                检查Header
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="response-viewer" id="responseViewer">
                        <div class="response-header">
                            <h4>响应结果</h4>
                            <div class="response-meta" id="responseMeta">
                                <!-- 响应元信息 -->
                            </div>
                        </div>
                        
                        <div class="response-tabs">
                            <button class="tab-btn active" data-tab="body">响应体</button>
                            <button class="tab-btn" data-tab="headers">响应头</button>
                            <button class="tab-btn" data-tab="cookies">Cookies</button>
                            <button class="tab-btn" data-tab="tests">测试结果</button>
                        </div>
                        
                        <div class="response-content">
                            <div class="tab-content active" id="responseBodyTab">
                                <div class="response-format-selector">
                                    <button class="format-btn active" data-format="pretty">美化</button>
                                    <button class="format-btn" data-format="raw">原始</button>
                                    <button class="format-btn" data-format="preview">预览</button>
                                </div>
                                <div class="response-body" id="responseBody">
                                    <div class="empty-response">
                                        <i class="fas fa-paper-plane"></i>
                                        <p>发送请求以查看响应</p>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="tab-content" id="responseHeadersTab">
                                <div class="response-headers" id="responseHeaders">
                                    <!-- 响应头列表 -->
                                </div>
                            </div>
                            
                            <div class="tab-content" id="responseCookiesTab">
                                <div class="response-cookies" id="responseCookies">
                                    <!-- Cookies列表 -->
                                </div>
                            </div>
                            
                            <div class="tab-content" id="responseTestsTab">
                                <div class="test-results" id="testResults">
                                    <!-- 测试结果 -->
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
        
        document.body.appendChild(apiPanel);
    }

    loadAPITemplates() {
        this.apiTemplates = [
            {
                id: 'user-api',
                name: '用户API',
                description: '用户管理相关接口',
                endpoints: [
                    {
                        name: '获取用户列表',
                        method: 'GET',
                        url: '/api/users',
                        description: '获取所有用户列表'
                    },
                    {
                        name: '创建用户',
                        method: 'POST',
                        url: '/api/users',
                        description: '创建新用户',
                        body: {
                            type: 'json',
                            content: '{"username": "", "email": "", "name": ""}'
                        }
                    },
                    {
                        name: '获取用户详情',
                        method: 'GET',
                        url: '/api/users/{id}',
                        description: '根据ID获取用户详情'
                    },
                    {
                        name: '更新用户',
                        method: 'PUT',
                        url: '/api/users/{id}',
                        description: '更新用户信息',
                        body: {
                            type: 'json',
                            content: '{"username": "", "email": "", "name": ""}'
                        }
                    },
                    {
                        name: '删除用户',
                        method: 'DELETE',
                        url: '/api/users/{id}',
                        description: '删除指定用户'
                    }
                ]
            },
            {
                id: 'order-api',
                name: '订单API',
                description: '订单管理相关接口',
                endpoints: [
                    {
                        name: '获取订单列表',
                        method: 'GET',
                        url: '/api/orders',
                        description: '获取订单列表'
                    },
                    {
                        name: '创建订单',
                        method: 'POST',
                        url: '/api/orders',
                        description: '创建新订单',
                        body: {
                            type: 'json',
                            content: '{"customerId": "", "products": [], "amount": 0}'
                        }
                    },
                    {
                        name: '获取订单详情',
                        method: 'GET',
                        url: '/api/orders/{id}',
                        description: '获取订单详情'
                    },
                    {
                        name: '更新订单状态',
                        method: 'PATCH',
                        url: '/api/orders/{id}/status',
                        description: '更新订单状态',
                        body: {
                            type: 'json',
                            content: '{"status": "processing"}'
                        }
                    }
                ]
            }
        ];
        
        this.renderCollectionTree();
    }

    renderCollectionTree() {
        const tree = document.getElementById('collectionTree');
        if (!tree) return;
        
        tree.innerHTML = this.apiTemplates.map(collection => `
            <div class="collection-item">
                <div class="collection-header" onclick="apiManager.toggleCollection('${collection.id}')">
                    <i class="fas fa-folder collection-icon"></i>
                    <span class="collection-name">${collection.name}</span>
                    <i class="fas fa-chevron-down toggle-icon"></i>
                </div>
                <div class="collection-endpoints" id="endpoints-${collection.id}">
                    ${collection.endpoints.map(endpoint => `
                        <div class="endpoint-item" onclick="apiManager.loadEndpoint('${collection.id}', '${endpoint.name}')">
                            <span class="method-badge ${endpoint.method.toLowerCase()}">${endpoint.method}</span>
                            <span class="endpoint-name">${endpoint.name}</span>
                        </div>
                    `).join('')}
                </div>
            </div>
        `).join('');
    }

    bindAPIEvents() {
        // 标签切换
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('tab-btn') && e.target.closest('.api-panel')) {
                this.switchAPITab(e.target);
            }
        });
        
        // 请求体类型切换
        document.addEventListener('change', (e) => {
            if (e.target.name === 'bodyType') {
                this.switchBodyType(e.target.value);
            }
        });
        
        // 认证类型切换
        const authTypeSelect = document.getElementById('authType');
        if (authTypeSelect) {
            authTypeSelect.addEventListener('change', (e) => {
                this.updateAuthConfig(e.target.value);
            });
        }
        
        // URL变化监听
        const urlInput = document.getElementById('requestUrl');
        if (urlInput) {
            urlInput.addEventListener('input', () => {
                this.extractPathParams();
            });
        }
    }

    switchAPITab(tabBtn) {
        const container = tabBtn.closest('.request-builder, .response-viewer');
        const tabName = tabBtn.dataset.tab;
        
        // 更新标签状态
        container.querySelectorAll('.tab-btn').forEach(btn => btn.classList.remove('active'));
        container.querySelectorAll('.tab-content').forEach(content => content.classList.remove('active'));
        
        tabBtn.classList.add('active');
        const targetTab = container.querySelector(`#${tabName}Tab, #response${tabName.charAt(0).toUpperCase() + tabName.slice(1)}Tab`);
        if (targetTab) {
            targetTab.classList.add('active');
        }
    }

    switchBodyType(type) {
        // 隐藏所有编辑器
        document.querySelectorAll('.body-editor').forEach(editor => {
            editor.style.display = 'none';
        });
        
        // 显示对应编辑器
        if (type !== 'none') {
            const editor = document.getElementById(`${type}Editor`);
            if (editor) {
                editor.style.display = 'block';
            }
        }
    }

    updateAuthConfig(type) {
        const config = document.getElementById('authConfig');
        if (!config) return;
        
        let configHTML = '';
        
        switch (type) {
            case 'bearer':
                configHTML = `
                    <div class="auth-field">
                        <label>Token</label>
                        <input type="text" id="bearerToken" placeholder="输入Bearer Token">
                    </div>
                `;
                break;
            case 'basic':
                configHTML = `
                    <div class="auth-field">
                        <label>用户名</label>
                        <input type="text" id="basicUsername" placeholder="用户名">
                    </div>
                    <div class="auth-field">
                        <label>密码</label>
                        <input type="password" id="basicPassword" placeholder="密码">
                    </div>
                `;
                break;
            case 'apikey':
                configHTML = `
                    <div class="auth-field">
                        <label>Key</label>
                        <input type="text" id="apiKeyName" placeholder="API Key名称">
                    </div>
                    <div class="auth-field">
                        <label>Value</label>
                        <input type="text" id="apiKeyValue" placeholder="API Key值">
                    </div>
                    <div class="auth-field">
                        <label>添加到</label>
                        <select id="apiKeyLocation">
                            <option value="header">Header</option>
                            <option value="query">Query参数</option>
                        </select>
                    </div>
                `;
                break;
            case 'oauth2':
                configHTML = `
                    <div class="auth-field">
                        <label>Access Token</label>
                        <input type="text" id="oauth2Token" placeholder="Access Token">
                    </div>
                    <div class="auth-field">
                        <label>Token Type</label>
                        <select id="oauth2TokenType">
                            <option value="Bearer">Bearer</option>
                            <option value="MAC">MAC</option>
                        </select>
                    </div>
                `;
                break;
        }
        
        config.innerHTML = configHTML;
    }

    extractPathParams() {
        const url = document.getElementById('requestUrl').value;
        const pathParamsContainer = document.getElementById('pathParams');
        if (!pathParamsContainer) return;
        
        // 提取路径参数 {param}
        const pathParams = url.match(/\{([^}]+)\}/g);
        
        if (pathParams) {
            pathParamsContainer.innerHTML = pathParams.map(param => {
                const paramName = param.slice(1, -1);
                return `
                    <div class="param-row">
                        <input type="text" value="${paramName}" class="param-key" readonly>
                        <input type="text" placeholder="输入${paramName}的值" class="param-value">
                        <span class="param-description">路径参数</span>
                    </div>
                `;
            }).join('');
        } else {
            pathParamsContainer.innerHTML = '<p class="empty-params">URL中未发现路径参数</p>';
        }
    }

    addParam(type) {
        const container = type === 'query' ? 
            document.getElementById('queryParams') : 
            document.getElementById('pathParams');
        
        if (!container) return;
        
        const paramRow = document.createElement('div');
        paramRow.className = 'param-row';
        paramRow.innerHTML = `
            <input type="text" placeholder="键" class="param-key">
            <input type="text" placeholder="值" class="param-value">
            <button class="btn-icon" onclick="apiManager.removeParam(this)">
                <i class="fas fa-trash"></i>
            </button>
        `;
        
        container.appendChild(paramRow);
    }

    removeParam(button) {
        button.closest('.param-row').remove();
    }

    addHeader() {
        const container = document.getElementById('requestHeaders');
        if (!container) return;
        
        const headerRow = document.createElement('div');
        headerRow.className = 'header-row';
        headerRow.innerHTML = `
            <input type="text" placeholder="Header名称" class="header-key">
            <input type="text" placeholder="Header值" class="header-value">
            <button class="btn-icon" onclick="apiManager.removeHeader(this)">
                <i class="fas fa-trash"></i>
            </button>
        `;
        
        container.appendChild(headerRow);
    }

    removeHeader(button) {
        button.closest('.header-row').remove();
    }

    addCommonHeader(key, value) {
        const container = document.getElementById('requestHeaders');
        if (!container) return;
        
        // 检查是否已存在
        const existingHeaders = container.querySelectorAll('.header-key');
        for (let header of existingHeaders) {
            if (header.value === key) {
                header.nextElementSibling.value = value;
                return;
            }
        }
        
        // 添加新header
        const headerRow = document.createElement('div');
        headerRow.className = 'header-row';
        headerRow.innerHTML = `
            <input type="text" value="${key}" class="header-key">
            <input type="text" value="${value}" class="header-value">
            <button class="btn-icon" onclick="apiManager.removeHeader(this)">
                <i class="fas fa-trash"></i>
            </button>
        `;
        
        container.appendChild(headerRow);
    }

    addFormData() {
        const container = document.getElementById('formData');
        if (!container) return;
        
        const formRow = document.createElement('div');
        formRow.className = 'form-row';
        formRow.innerHTML = `
            <input type="text" placeholder="键" class="form-key">
            <input type="text" placeholder="值" class="form-value">
            <select class="form-type">
                <option value="text">文本</option>
                <option value="file">文件</option>
            </select>
            <button class="btn-icon" onclick="apiManager.removeFormData(this)">
                <i class="fas fa-trash"></i>
            </button>
        `;
        
        container.appendChild(formRow);
    }

    removeFormData(button) {
        button.closest('.form-row').remove();
    }

    formatJSON() {
        const textarea = document.getElementById('jsonBody');
        if (!textarea) return;
        
        try {
            const json = JSON.parse(textarea.value);
            textarea.value = JSON.stringify(json, null, 2);
            if (window.showNotification) {
                showNotification('JSON格式化成功', 'success');
            }
        } catch (error) {
            if (window.showNotification) {
                showNotification('JSON格式错误: ' + error.message, 'error');
            }
        }
    }

    validateJSON() {
        const textarea = document.getElementById('jsonBody');
        if (!textarea) return;
        
        try {
            JSON.parse(textarea.value);
            if (window.showNotification) {
                showNotification('JSON格式正确', 'success');
            }
        } catch (error) {
            if (window.showNotification) {
                showNotification('JSON格式错误: ' + error.message, 'error');
            }
        }
    }

    addTestSnippet(type) {
        const textarea = document.getElementById('testScript');
        if (!textarea) return;
        
        const snippets = {
            status: `// 检查状态码
pm.test("状态码为200", function () {
    pm.response.to.have.status(200);
});`,
            json: `// 解析JSON响应
pm.test("响应为JSON格式", function () {
    pm.response.to.be.json;
});

const jsonData = pm.response.json();
pm.test("包含必要字段", function () {
    pm.expect(jsonData).to.have.property('data');
});`,
            header: `// 检查响应头
pm.test("包含Content-Type头", function () {
    pm.response.to.have.header("Content-Type");
});`
        };
        
        const snippet = snippets[type];
        if (snippet) {
            textarea.value += (textarea.value ? '\n\n' : '') + snippet;
        }
    }

    toggleCollection(collectionId) {
        const endpoints = document.getElementById(`endpoints-${collectionId}`);
        const icon = document.querySelector(`[onclick*="${collectionId}"] .toggle-icon`);
        
        if (endpoints && icon) {
            endpoints.classList.toggle('expanded');
            icon.classList.toggle('rotated');
        }
    }

    loadEndpoint(collectionId, endpointName) {
        const collection = this.apiTemplates.find(c => c.id === collectionId);
        if (!collection) return;
        
        const endpoint = collection.endpoints.find(e => e.name === endpointName);
        if (!endpoint) return;
        
        // 填充请求信息
        document.getElementById('requestMethod').value = endpoint.method;
        document.getElementById('requestUrl').value = endpoint.url;
        
        // 如果有请求体，填充请求体
        if (endpoint.body) {
            const bodyTypeRadio = document.querySelector(`input[name="bodyType"][value="${endpoint.body.type}"]`);
            if (bodyTypeRadio) {
                bodyTypeRadio.checked = true;
                this.switchBodyType(endpoint.body.type);
                
                if (endpoint.body.type === 'json') {
                    document.getElementById('jsonBody').value = endpoint.body.content;
                }
            }
        }
        
        // 提取路径参数
        this.extractPathParams();
        
        if (window.showNotification) {
            showNotification(`已加载接口: ${endpoint.name}`, 'info');
        }
    }

    async sendRequest() {
        const method = document.getElementById('requestMethod').value;
        const url = document.getElementById('requestUrl').value;
        
        if (!url.trim()) {
            if (window.showNotification) {
                showNotification('请输入API地址', 'warning');
            }
            return;
        }
        
        // 显示加载状态
        const sendBtn = document.querySelector('.send-btn');
        const originalText = sendBtn.innerHTML;
        sendBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 发送中...';
        sendBtn.disabled = true;
        
        try {
            // 构建请求
            const requestConfig = this.buildRequestConfig(method, url);
            
            // 记录请求开始时间
            const startTime = Date.now();
            
            // 发送请求
            const response = await this.makeRequest(requestConfig);
            
            // 计算响应时间
            const responseTime = Date.now() - startTime;
            
            // 显示响应
            this.displayResponse(response, responseTime);
            
            // 保存到历史记录
            this.saveToHistory(requestConfig, response, responseTime);
            
        } catch (error) {
            this.displayError(error);
        } finally {
            // 恢复按钮状态
            sendBtn.innerHTML = originalText;
            sendBtn.disabled = false;
        }
    }

    buildRequestConfig(method, url) {
        const config = {
            method: method,
            url: url,
            headers: {},
            params: {},
            data: null
        };
        
        // 收集Headers
        const headerRows = document.querySelectorAll('#requestHeaders .header-row');
        headerRows.forEach(row => {
            const key = row.querySelector('.header-key').value.trim();
            const value = row.querySelector('.header-value').value.trim();
            if (key && value) {
                config.headers[key] = value;
            }
        });
        
        // 收集查询参数
        const paramRows = document.querySelectorAll('#queryParams .param-row');
        paramRows.forEach(row => {
            const key = row.querySelector('.param-key').value.trim();
            const value = row.querySelector('.param-value').value.trim();
            if (key && value) {
                config.params[key] = value;
            }
        });
        
        // 处理路径参数
        const pathParamRows = document.querySelectorAll('#pathParams .param-row');
        pathParamRows.forEach(row => {
            const key = row.querySelector('.param-key').value.trim();
            const value = row.querySelector('.param-value').value.trim();
            if (key && value) {
                config.url = config.url.replace(`{${key}}`, value);
            }
        });
        
        // 处理请求体
        const bodyType = document.querySelector('input[name="bodyType"]:checked').value;
        if (bodyType !== 'none') {
            switch (bodyType) {
                case 'json':
                    const jsonBody = document.getElementById('jsonBody').value.trim();
                    if (jsonBody) {
                        try {
                            config.data = JSON.parse(jsonBody);
                            config.headers['Content-Type'] = 'application/json';
                        } catch (error) {
                            throw new Error('JSON格式错误: ' + error.message);
                        }
                    }
                    break;
                case 'form':
                    const formData = new FormData();
                    const formRows = document.querySelectorAll('#formData .form-row');
                    formRows.forEach(row => {
                        const key = row.querySelector('.form-key').value.trim();
                        const value = row.querySelector('.form-value').value.trim();
                        const type = row.querySelector('.form-type').value;
                        if (key && value) {
                            formData.append(key, value);
                        }
                    });
                    config.data = formData;
                    break;
                case 'raw':
                    const rawBody = document.getElementById('rawBody').value.trim();
                    if (rawBody) {
                        config.data = rawBody;
                    }
                    break;
            }
        }
        
        // 处理认证
        const authType = document.getElementById('authType').value;
        if (authType !== 'none') {
            this.applyAuthentication(config, authType);
        }
        
        return config;
    }

    applyAuthentication(config, authType) {
        switch (authType) {
            case 'bearer':
                const bearerToken = document.getElementById('bearerToken')?.value;
                if (bearerToken) {
                    config.headers['Authorization'] = `Bearer ${bearerToken}`;
                }
                break;
            case 'basic':
                const username = document.getElementById('basicUsername')?.value;
                const password = document.getElementById('basicPassword')?.value;
                if (username && password) {
                    const credentials = btoa(`${username}:${password}`);
                    config.headers['Authorization'] = `Basic ${credentials}`;
                }
                break;
            case 'apikey':
                const keyName = document.getElementById('apiKeyName')?.value;
                const keyValue = document.getElementById('apiKeyValue')?.value;
                const keyLocation = document.getElementById('apiKeyLocation')?.value;
                if (keyName && keyValue) {
                    if (keyLocation === 'header') {
                        config.headers[keyName] = keyValue;
                    } else {
                        config.params[keyName] = keyValue;
                    }
                }
                break;
            case 'oauth2':
                const oauth2Token = document.getElementById('oauth2Token')?.value;
                const tokenType = document.getElementById('oauth2TokenType')?.value || 'Bearer';
                if (oauth2Token) {
                    config.headers['Authorization'] = `${tokenType} ${oauth2Token}`;
                }
                break;
        }
    }

    async makeRequest(config) {
        // 构建完整URL
        let fullUrl = config.url;
        if (Object.keys(config.params).length > 0) {
            const params = new URLSearchParams(config.params);
            fullUrl += (fullUrl.includes('?') ? '&' : '?') + params.toString();
        }
        
        // 构建fetch选项
        const fetchOptions = {
            method: config.method,
            headers: config.headers
        };
        
        // 添加请求体
        if (config.data && config.method !== 'GET' && config.method !== 'HEAD') {
            if (config.data instanceof FormData) {
                fetchOptions.body = config.data;
                // 删除Content-Type，让浏览器自动设置
                delete fetchOptions.headers['Content-Type'];
            } else if (typeof config.data === 'object') {
                fetchOptions.body = JSON.stringify(config.data);
            } else {
                fetchOptions.body = config.data;
            }
        }
        
        // 发送请求
        const response = await fetch(fullUrl, fetchOptions);
        
        // 解析响应
        const responseData = {
            status: response.status,
            statusText: response.statusText,
            headers: {},
            body: null,
            url: response.url
        };
        
        // 收集响应头
        response.headers.forEach((value, key) => {
            responseData.headers[key] = value;
        });
        
        // 解析响应体
        const contentType = response.headers.get('content-type') || '';
        if (contentType.includes('application/json')) {
            responseData.body = await response.json();
            responseData.bodyType = 'json';
        } else if (contentType.includes('text/')) {
            responseData.body = await response.text();
            responseData.bodyType = 'text';
        } else {
            responseData.body = await response.blob();
            responseData.bodyType = 'blob';
        }
        
        return responseData;
    }

    displayResponse(response, responseTime) {
        // 更新响应元信息
        const responseMeta = document.getElementById('responseMeta');
        if (responseMeta) {
            const statusClass = response.status >= 200 && response.status < 300 ? 'success' : 
                               response.status >= 400 ? 'error' : 'warning';
            
            responseMeta.innerHTML = `
                <span class="status-badge ${statusClass}">${response.status} ${response.statusText}</span>
                <span class="response-time">${responseTime}ms</span>
                <span class="response-size">${this.formatSize(JSON.stringify(response.body).length)}</span>
            `;
        }
        
        // 显示响应体
        this.displayResponseBody(response);
        
        // 显示响应头
        this.displayResponseHeaders(response.headers);
        
        // 运行测试脚本
        this.runTests(response);
    }

    displayResponseBody(response) {
        const responseBody = document.getElementById('responseBody');
        if (!responseBody) return;
        
        if (response.bodyType === 'json') {
            responseBody.innerHTML = `
                <pre class="json-response"><code>${JSON.stringify(response.body, null, 2)}</code></pre>
            `;
        } else if (response.bodyType === 'text') {
            responseBody.innerHTML = `
                <pre class="text-response"><code>${this.escapeHtml(response.body)}</code></pre>
            `;
        } else {
            responseBody.innerHTML = `
                <div class="binary-response">
                    <i class="fas fa-file"></i>
                    <p>二进制数据 (${this.formatSize(response.body.size)})</p>
                </div>
            `;
        }
    }

    displayResponseHeaders(headers) {
        const responseHeaders = document.getElementById('responseHeaders');
        if (!responseHeaders) return;
        
        responseHeaders.innerHTML = Object.entries(headers).map(([key, value]) => `
            <div class="response-header-item">
                <span class="header-key">${key}:</span>
                <span class="header-value">${value}</span>
            </div>
        `).join('');
    }

    displayError(error) {
        const responseBody = document.getElementById('responseBody');
        const responseMeta = document.getElementById('responseMeta');
        
        if (responseMeta) {
            responseMeta.innerHTML = `
                <span class="status-badge error">错误</span>
                <span class="error-message">${error.message}</span>
            `;
        }
        
        if (responseBody) {
            responseBody.innerHTML = `
                <div class="error-response">
                    <i class="fas fa-exclamation-triangle"></i>
                    <h4>请求失败</h4>
                    <p>${error.message}</p>
                </div>
            `;
        }
        
        if (window.showNotification) {
            showNotification('请求失败: ' + error.message, 'error');
        }
    }

    runTests(response) {
        const testScript = document.getElementById('testScript')?.value;
        if (!testScript) return;
        
        const testResults = document.getElementById('testResults');
        if (!testResults) return;
        
        try {
            // 创建测试环境
            const pm = {
                response: {
                    status: response.status,
                    headers: response.headers,
                    json: () => response.body,
                    text: () => typeof response.body === 'string' ? response.body : JSON.stringify(response.body),
                    to: {
                        have: {
                            status: (expectedStatus) => {
                                if (response.status !== expectedStatus) {
                                    throw new Error(`期望状态码 ${expectedStatus}，实际 ${response.status}`);
                                }
                            },
                            header: (headerName) => {
                                if (!response.headers[headerName.toLowerCase()]) {
                                    throw new Error(`缺少响应头: ${headerName}`);
                                }
                            }
                        },
                        be: {
                            json: response.bodyType === 'json'
                        }
                    }
                },
                test: (name, testFn) => {
                    try {
                        testFn();
                        this.addTestResult(name, true);
                    } catch (error) {
                        this.addTestResult(name, false, error.message);
                    }
                },
                expect: (actual) => ({
                    to: {
                        have: {
                            property: (prop) => {
                                if (!(prop in actual)) {
                                    throw new Error(`对象缺少属性: ${prop}`);
                                }
                            }
                        }
                    }
                })
            };
            
            // 清空之前的测试结果
            testResults.innerHTML = '<div class="test-header"><h5>测试结果</h5></div>';
            
            // 执行测试脚本
            eval(testScript);
            
        } catch (error) {
            testResults.innerHTML = `
                <div class="test-error">
                    <i class="fas fa-exclamation-triangle"></i>
                    <p>测试脚本执行错误: ${error.message}</p>
                </div>
            `;
        }
    }

    addTestResult(name, passed, error = null) {
        const testResults = document.getElementById('testResults');
        if (!testResults) return;
        
        const resultItem = document.createElement('div');
        resultItem.className = `test-result ${passed ? 'passed' : 'failed'}`;
        resultItem.innerHTML = `
            <div class="test-result-header">
                <i class="fas fa-${passed ? 'check' : 'times'}"></i>
                <span class="test-name">${name}</span>
            </div>
            ${error ? `<div class="test-error-message">${error}</div>` : ''}
        `;
        
        testResults.appendChild(resultItem);
    }

    saveToHistory(request, response, responseTime) {
        const historyItem = {
            id: Date.now(),
            timestamp: new Date(),
            method: request.method,
            url: request.url,
            status: response.status,
            responseTime: responseTime
        };
        
        this.apiHistory.unshift(historyItem);
        
        // 限制历史记录数量
        if (this.apiHistory.length > 50) {
            this.apiHistory = this.apiHistory.slice(0, 50);
        }
        
        this.updateHistoryList();
        this.saveHistoryToStorage();
    }

    updateHistoryList() {
        const historyList = document.getElementById('historyList');
        if (!historyList) return;
        
        if (this.apiHistory.length === 0) {
            historyList.innerHTML = '<p class="empty-history">暂无请求历史</p>';
            return;
        }
        
        historyList.innerHTML = this.apiHistory.slice(0, 10).map(item => `
            <div class="history-item" onclick="apiManager.loadFromHistory(${item.id})">
                <div class="history-method ${item.method.toLowerCase()}">${item.method}</div>
                <div class="history-info">
                    <div class="history-url">${this.truncateUrl(item.url)}</div>
                    <div class="history-meta">
                        <span class="history-status ${item.status >= 200 && item.status < 300 ? 'success' : 'error'}">${item.status}</span>
                        <span class="history-time">${item.responseTime}ms</span>
                        <span class="history-date">${this.formatDate(item.timestamp)}</span>
                    </div>
                </div>
            </div>
        `).join('');
    }

    loadFromHistory(historyId) {
        const historyItem = this.apiHistory.find(item => item.id === historyId);
        if (!historyItem) return;
        
        document.getElementById('requestMethod').value = historyItem.method;
        document.getElementById('requestUrl').value = historyItem.url;
        
        if (window.showNotification) {
            showNotification('已从历史记录加载请求', 'info');
        }
    }

    saveHistoryToStorage() {
        try {
            localStorage.setItem('apiHistory', JSON.stringify(this.apiHistory));
        } catch (error) {
            console.error('保存API历史记录失败:', error);
        }
    }

    loadStoredEndpoints() {
        try {
            const stored = localStorage.getItem('apiHistory');
            if (stored) {
                this.apiHistory = JSON.parse(stored);
                this.updateHistoryList();
            }
        } catch (error) {
            console.error('加载API历史记录失败:', error);
            this.apiHistory = [];
        }
    }

    setupInterceptors() {
        // 请求拦截器
        this.requestInterceptors.push((config) => {
            // 添加时间戳
            config.timestamp = Date.now();
            return config;
        });
        
        // 响应拦截器
        this.responseInterceptors.push((response) => {
            // 记录响应时间
            response.responseTime = Date.now() - response.timestamp;
            return response;
        });
    }

    // 工具方法
    formatSize(bytes) {
        if (bytes === 0) return '0 B';
        const k = 1024;
        const sizes = ['B', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    formatDate(date) {
        return date.toLocaleString('zh-CN', {
            month: 'short',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        });
    }

    truncateUrl(url, maxLength = 40) {
        if (url.length <= maxLength) return url;
        return url.substring(0, maxLength - 3) + '...';
    }

    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    // 面板控制方法
    showAPIPanel() {
        const panel = document.getElementById('apiPanel');
        if (panel) {
            panel.classList.add('show');
        }
    }

    closeAPIPanel() {
        const panel = document.getElementById('apiPanel');
        if (panel) {
            panel.classList.remove('show');
        }
    }

    createNewRequest() {
        // 清空所有输入
        document.getElementById('requestMethod').value = 'GET';
        document.getElementById('requestUrl').value = '';
        
        // 清空参数
        document.getElementById('queryParams').innerHTML = `
            <div class="param-row">
                <input type="text" placeholder="键" class="param-key">
                <input type="text" placeholder="值" class="param-value">
                <button class="btn-icon" onclick="apiManager.removeParam(this)">
                    <i class="fas fa-trash"></i>
                </button>
            </div>
        `;
        
        // 清空请求头
        document.getElementById('requestHeaders').innerHTML = `
            <div class="header-row">
                <input type="text" placeholder="Header名称" class="header-key">
                <input type="text" placeholder="Header值" class="header-value">
                <button class="btn-icon" onclick="apiManager.removeHeader(this)">
                    <i class="fas fa-trash"></i>
                </button>
            </div>
        `;
        
        // 重置请求体
        document.querySelector('input[name="bodyType"][value="none"]').checked = true;
        this.switchBodyType('none');
        
        // 重置认证
        document.getElementById('authType').value = 'none';
        this.updateAuthConfig('none');
        
        // 清空响应
        document.getElementById('responseBody').innerHTML = `
            <div class="empty-response">
                <i class="fas fa-paper-plane"></i>
                <p>发送请求以查看响应</p>
            </div>
        `;
        
        if (window.showNotification) {
            showNotification('已创建新请求', 'info');
        }
    }

    importCollection() {
        const input = document.createElement('input');
        input.type = 'file';
        input.accept = '.json';
        input.onchange = (e) => {
            const file = e.target.files[0];
            if (file) {
                const reader = new FileReader();
                reader.onload = (e) => {
                    try {
                        const collection = JSON.parse(e.target.result);
                        this.apiTemplates.push(collection);
                        this.renderCollectionTree();
                        if (window.showNotification) {
                            showNotification('接口集合导入成功', 'success');
                        }
                    } catch (error) {
                        if (window.showNotification) {
                            showNotification('导入失败: ' + error.message, 'error');
                        }
                    }
                };
                reader.readAsText(file);
            }
        };
        input.click();
    }
}

// 全局API管理器实例
let apiManager = null;

// 初始化API管理系统
function initializeAPIManagement() {
    apiManager = new APIManagement();
    console.log('✅ API管理系统已初始化');
}

// 显示API面板
function showAPIPanel() {
    if (apiManager) {
        apiManager.showAPIPanel();
    }
}

// 页面加载时初始化
document.addEventListener('DOMContentLoaded', function() {
    setTimeout(initializeAPIManagement, 1000);
});
