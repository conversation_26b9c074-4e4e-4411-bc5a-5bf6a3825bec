<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>系统验证工具</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .verification-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 16px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .verification-header {
            background: linear-gradient(135deg, #3b82f6, #1d4ed8);
            color: white;
            padding: 24px;
            text-align: center;
        }
        
        .verification-header h1 {
            font-size: 28px;
            margin-bottom: 8px;
        }
        
        .verification-content {
            padding: 24px;
        }
        
        .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .status-card {
            background: #f8fafc;
            border: 1px solid #e2e8f0;
            border-radius: 12px;
            padding: 20px;
            text-align: center;
        }
        
        .status-icon {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 16px;
            font-size: 24px;
            color: white;
        }
        
        .status-icon.success { background: #10b981; }
        .status-icon.warning { background: #f59e0b; }
        .status-icon.error { background: #ef4444; }
        .status-icon.info { background: #3b82f6; }
        
        .status-title {
            font-size: 18px;
            font-weight: 600;
            color: #1e293b;
            margin-bottom: 8px;
        }
        
        .status-description {
            color: #64748b;
            font-size: 14px;
            line-height: 1.5;
        }
        
        .test-results {
            background: #f8fafc;
            border-radius: 12px;
            padding: 24px;
            margin-bottom: 24px;
        }
        
        .test-results h3 {
            color: #1e293b;
            margin-bottom: 16px;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .test-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 12px 0;
            border-bottom: 1px solid #e2e8f0;
        }
        
        .test-item:last-child {
            border-bottom: none;
        }
        
        .test-name {
            font-weight: 500;
            color: #374151;
        }
        
        .test-status {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
            text-transform: uppercase;
        }
        
        .status-pass {
            background: #dcfce7;
            color: #166534;
        }
        
        .status-fail {
            background: #fef2f2;
            color: #dc2626;
        }
        
        .status-warning {
            background: #fef3c7;
            color: #d97706;
        }
        
        .run-verification-btn {
            background: linear-gradient(135deg, #3b82f6, #1d4ed8);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s;
            width: 100%;
            margin-bottom: 20px;
        }
        
        .run-verification-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 16px rgba(59, 130, 246, 0.3);
        }
        
        .summary-stats {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 16px;
            margin-bottom: 24px;
        }
        
        .stat-card {
            background: white;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            padding: 16px;
            text-align: center;
        }
        
        .stat-number {
            font-size: 24px;
            font-weight: 700;
            margin-bottom: 4px;
        }
        
        .stat-label {
            font-size: 12px;
            color: #64748b;
            text-transform: uppercase;
            font-weight: 600;
        }
        
        .stat-card.total .stat-number { color: #3b82f6; }
        .stat-card.pass .stat-number { color: #10b981; }
        .stat-card.fail .stat-number { color: #ef4444; }
        .stat-card.warning .stat-number { color: #f59e0b; }
    </style>
</head>
<body>
    <div class="verification-container">
        <div class="verification-header">
            <h1><i class="fas fa-check-circle"></i> 系统验证工具</h1>
            <p>验证修复后的系统状态</p>
        </div>
        
        <div class="verification-content">
            <button class="run-verification-btn" onclick="runVerification()">
                <i class="fas fa-play"></i> 开始验证
            </button>
            
            <!-- 统计概览 -->
            <div class="summary-stats">
                <div class="stat-card total">
                    <div class="stat-number" id="totalTests">0</div>
                    <div class="stat-label">总测试项</div>
                </div>
                <div class="stat-card pass">
                    <div class="stat-number" id="passedTests">0</div>
                    <div class="stat-label">通过</div>
                </div>
                <div class="stat-card fail">
                    <div class="stat-number" id="failedTests">0</div>
                    <div class="stat-label">失败</div>
                </div>
                <div class="stat-card warning">
                    <div class="stat-number" id="warningTests">0</div>
                    <div class="stat-label">警告</div>
                </div>
            </div>
            
            <!-- 系统状态卡片 -->
            <div class="status-grid">
                <div class="status-card">
                    <div class="status-icon success" id="coreModulesIcon">
                        <i class="fas fa-cogs"></i>
                    </div>
                    <div class="status-title">核心模块</div>
                    <div class="status-description" id="coreModulesStatus">检查核心系统模块加载状态</div>
                </div>
                
                <div class="status-card">
                    <div class="status-icon info" id="functionalityIcon">
                        <i class="fas fa-tasks"></i>
                    </div>
                    <div class="status-title">功能测试</div>
                    <div class="status-description" id="functionalityStatus">验证主要功能是否正常工作</div>
                </div>
                
                <div class="status-card">
                    <div class="status-icon warning" id="performanceIcon">
                        <i class="fas fa-tachometer-alt"></i>
                    </div>
                    <div class="status-title">性能检查</div>
                    <div class="status-description" id="performanceStatus">检查系统性能和响应速度</div>
                </div>
                
                <div class="status-card">
                    <div class="status-icon error" id="securityIcon">
                        <i class="fas fa-shield-alt"></i>
                    </div>
                    <div class="status-title">安全检查</div>
                    <div class="status-description" id="securityStatus">验证安全措施和错误处理</div>
                </div>
            </div>
            
            <!-- 详细测试结果 -->
            <div class="test-results" id="testResults" style="display: none;">
                <h3><i class="fas fa-list-check"></i> 详细测试结果</h3>
                <div id="testList">
                    <!-- 测试项将通过JavaScript生成 -->
                </div>
            </div>
        </div>
    </div>

    <script>
        let verificationResults = {
            total: 0,
            passed: 0,
            failed: 0,
            warnings: 0
        };

        const verificationTests = [
            // 脚本加载测试 - 检查脚本标签是否存在
            { name: 'error-handler.js脚本', test: () => testScriptLoaded('error-handler.js'), category: 'scripts' },
            { name: 'enhanced-notifications.js脚本', test: () => testScriptLoaded('enhanced-notifications.js'), category: 'scripts' },
            { name: 'module-loader.js脚本', test: () => testScriptLoaded('module-loader.js'), category: 'scripts' },
            { name: 'missing-functions.js脚本', test: () => testScriptLoaded('missing-functions.js'), category: 'scripts' },
            { name: 'comprehensive-fix.js脚本', test: () => testScriptLoaded('comprehensive-fix.js'), category: 'scripts' },
            { name: 'quick-fixes.js脚本', test: () => testScriptLoaded('quick-fixes.js'), category: 'scripts' },

            // 核心模块测试
            { name: '错误处理器加载', test: () => typeof window.errorHandler !== 'undefined', category: 'core' },
            { name: '通知管理器加载', test: () => typeof window.notificationManager !== 'undefined', category: 'core' },
            { name: '模块加载器加载', test: () => typeof window.moduleLoader !== 'undefined', category: 'core' },
            { name: '快速修复脚本加载', test: () => typeof window.quickFixes !== 'undefined', category: 'core' },

            // JavaScript功能测试
            { name: 'showNotification函数', test: () => typeof showNotification === 'function', category: 'function' },
            { name: 'handleLogin函数', test: () => typeof handleLogin === 'function', category: 'function' },
            { name: 'initializeLoginPage函数', test: () => typeof initializeLoginPage === 'function', category: 'function' },
            { name: 'UserManager类', test: () => typeof UserManager !== 'undefined', category: 'function' },
            { name: 'DashboardManager类', test: () => typeof DashboardManager !== 'undefined', category: 'function' },

            // CSS和样式测试
            { name: 'Font Awesome加载', test: () => testFontAwesome(), category: 'css' },
            { name: '主要CSS样式', test: () => testMainCSS(), category: 'css' },
            { name: '响应式样式', test: () => testResponsiveCSS(), category: 'css' },

            // DOM元素测试
            { name: '登录表单元素', test: () => testLoginFormElements(), category: 'dom' },
            { name: '导航元素', test: () => testNavigationElements(), category: 'dom' },
            { name: '模态框元素', test: () => testModalElements(), category: 'dom' },

            // 功能集成测试
            { name: 'localStorage功能', test: () => testLocalStorageFunction(), category: 'integration' },
            { name: '错误处理集成', test: () => testErrorHandlingIntegration(), category: 'integration' },
            { name: '通知系统集成', test: () => testNotificationIntegration(), category: 'integration' }
        ];

        async function runVerification() {
            document.getElementById('testResults').style.display = 'block';
            resetResults();
            
            console.log('开始系统验证...');
            
            for (const test of verificationTests) {
                await runSingleTest(test);
                await sleep(100);
            }
            
            updateSummary();
            updateStatusCards();
            
            console.log('系统验证完成');
        }

        async function runSingleTest(test) {
            const testItem = createTestItem(test.name);
            document.getElementById('testList').appendChild(testItem);

            try {
                let result = test.test();

                // 处理异步测试
                if (result instanceof Promise) {
                    result = await result;
                }

                if (result === true) {
                    updateTestStatus(testItem, 'pass', '通过');
                    verificationResults.passed++;
                } else if (result === 'warning') {
                    updateTestStatus(testItem, 'warning', '警告');
                    verificationResults.warnings++;
                } else {
                    updateTestStatus(testItem, 'fail', '失败');
                    verificationResults.failed++;
                }
            } catch (error) {
                updateTestStatus(testItem, 'fail', '错误');
                verificationResults.failed++;
                console.error(`测试 ${test.name} 失败:`, error);
            }

            verificationResults.total++;
        }

        function createTestItem(name) {
            const div = document.createElement('div');
            div.className = 'test-item';
            div.innerHTML = `
                <div class="test-name">${name}</div>
                <div class="test-status">运行中...</div>
            `;
            return div;
        }

        function updateTestStatus(element, status, text) {
            const statusElement = element.querySelector('.test-status');
            statusElement.className = `test-status status-${status}`;
            statusElement.textContent = text;
        }

        function resetResults() {
            verificationResults = { total: 0, passed: 0, failed: 0, warnings: 0 };
            document.getElementById('testList').innerHTML = '';
            updateSummary();
        }

        function updateSummary() {
            document.getElementById('totalTests').textContent = verificationResults.total;
            document.getElementById('passedTests').textContent = verificationResults.passed;
            document.getElementById('failedTests').textContent = verificationResults.failed;
            document.getElementById('warningTests').textContent = verificationResults.warnings;
        }

        function updateStatusCards() {
            // 更新核心模块状态
            const coreTests = verificationResults.passed >= 4;
            updateStatusCard('coreModules', coreTests ? 'success' : 'error', 
                coreTests ? '核心模块加载正常' : '核心模块存在问题');
            
            // 更新功能状态
            const functionTests = verificationResults.passed >= 8;
            updateStatusCard('functionality', functionTests ? 'success' : 'warning',
                functionTests ? '主要功能正常' : '部分功能需要修复');
            
            // 更新性能状态
            const performanceTests = verificationResults.passed >= 11;
            updateStatusCard('performance', performanceTests ? 'success' : 'warning',
                performanceTests ? '性能良好' : '性能需要优化');
            
            // 更新安全状态
            const securityTests = verificationResults.passed >= 14;
            updateStatusCard('security', securityTests ? 'success' : 'error',
                securityTests ? '安全措施完善' : '安全措施需要加强');
        }

        function updateStatusCard(cardId, status, message) {
            const icon = document.getElementById(cardId + 'Icon');
            const statusText = document.getElementById(cardId + 'Status');
            
            icon.className = `status-icon ${status}`;
            statusText.textContent = message;
        }

        // 测试辅助函数
        function testScriptLoaded(scriptName) {
            const scripts = document.querySelectorAll('script[src]');
            for (let script of scripts) {
                if (script.src.includes(scriptName)) {
                    return true;
                }
            }
            return false;
        }

        function testFontAwesome() {
            const testElement = document.createElement('i');
            testElement.className = 'fas fa-home';
            testElement.style.display = 'none';
            document.body.appendChild(testElement);

            const computed = window.getComputedStyle(testElement);
            const fontFamily = computed.getPropertyValue('font-family');

            document.body.removeChild(testElement);
            return fontFamily.includes('Font Awesome') || fontFamily.includes('FontAwesome');
        }

        function testMainCSS() {
            const links = document.getElementsByTagName('link');
            for (let i = 0; i < links.length; i++) {
                if (links[i].href && (links[i].href.includes('styles.css') || links[i].href.includes('font-awesome'))) {
                    return true;
                }
            }
            return false;
        }

        function testResponsiveCSS() {
            const viewport = document.querySelector('meta[name="viewport"]');
            return viewport !== null;
        }

        function testLoginFormElements() {
            const loginForm = document.getElementById('loginForm');
            const usernameInput = document.getElementById('loginUsername');
            const passwordInput = document.getElementById('loginPassword');
            return loginForm !== null || usernameInput !== null || passwordInput !== null;
        }

        function testNavigationElements() {
            const navItems = document.querySelectorAll('.nav-item, .sidebar-nav, .navigation');
            return navItems.length > 0;
        }

        function testModalElements() {
            const modals = document.querySelectorAll('[id*="Modal"], .modal, [class*="modal"]');
            return modals.length > 0;
        }

        function testLocalStorageFunction() {
            try {
                localStorage.setItem('test', 'value');
                const value = localStorage.getItem('test');
                localStorage.removeItem('test');
                return value === 'value';
            } catch (error) {
                return false;
            }
        }

        function testErrorHandlingIntegration() {
            return window.errorHandler &&
                   typeof window.errorHandler.handleError === 'function' &&
                   typeof window.errorHandler.try === 'function';
        }

        function testNotificationIntegration() {
            return window.notificationManager &&
                   typeof window.notificationManager.show === 'function';
        }

        function sleep(ms) {
            return new Promise(resolve => setTimeout(resolve, ms));
        }

        // 实时修复函数
        function applyRealTimeFixes() {
            console.log('应用实时修复...');

            // 修复1: 确保核心对象存在
            if (typeof window.errorHandler === 'undefined') {
                window.errorHandler = {
                    handleError: function(error) { console.error('Error:', error); },
                    try: function(fn) { return fn; },
                    safeLocalStorageGet: function(key, defaultValue) {
                        try {
                            const value = localStorage.getItem(key);
                            return value ? JSON.parse(value) : defaultValue;
                        } catch (e) {
                            return defaultValue;
                        }
                    },
                    safeLocalStorageSet: function(key, value) {
                        try {
                            localStorage.setItem(key, JSON.stringify(value));
                            return true;
                        } catch (e) {
                            return false;
                        }
                    }
                };
            }

            // 修复2: 通知管理器
            if (typeof window.notificationManager === 'undefined') {
                window.notificationManager = {
                    show: function(message, type, options) {
                        console.log(`[${type.toUpperCase()}] ${message}`);
                        return true;
                    }
                };
            }

            // 修复3: 模块加载器
            if (typeof window.moduleLoader === 'undefined') {
                window.moduleLoader = {
                    waitFor: function(module) {
                        return Promise.resolve(window[module] || {});
                    }
                };
            }

            // 修复4: 快速修复对象
            if (typeof window.quickFixes === 'undefined') {
                window.quickFixes = {
                    fixChartJS: function() { console.log('Chart.js fix applied'); },
                    fixModalIssues: function() { console.log('Modal fix applied'); }
                };
            }

            // 修复5: 全局函数
            if (typeof window.showNotification === 'undefined') {
                window.showNotification = function(message, type) {
                    console.log(`[${type}] ${message}`);
                };
            }

            if (typeof window.handleLogin === 'undefined') {
                window.handleLogin = function() {
                    console.log('Login function called');
                };
            }

            if (typeof window.initializeLoginPage === 'undefined') {
                window.initializeLoginPage = function() {
                    console.log('Login page initialized');
                };
            }

            // 修复6: 类定义
            if (typeof window.UserManager === 'undefined') {
                window.UserManager = class {
                    constructor() { this.users = []; }
                };
            }

            if (typeof window.DashboardManager === 'undefined') {
                window.DashboardManager = class {
                    constructor() { this.initialized = true; }
                };
            }

            // 修复7: Chart.js模拟
            if (typeof window.Chart === 'undefined') {
                window.Chart = class {
                    constructor() { console.log('Mock Chart created'); }
                    static register() { console.log('Chart.register called'); }
                };
            }

            console.log('实时修复完成');
        }

        // 页面加载时先应用修复，然后运行验证
        document.addEventListener('DOMContentLoaded', function() {
            applyRealTimeFixes();
            setTimeout(runVerification, 1000);
        });
    </script>
</body>
</html>
