// 图表初始化工具

// 全局图表实例存储
window.chartInstances = window.chartInstances || new Map();

// 图表配置
const chartConfigs = {
    // 默认配置
    defaults: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                labels: {
                    font: {
                        family: 'Inter',
                        size: 12
                    },
                    usePointStyle: true,
                    padding: 20
                }
            },
            tooltip: {
                backgroundColor: 'rgba(0, 0, 0, 0.8)',
                titleColor: '#ffffff',
                bodyColor: '#ffffff',
                borderColor: '#667eea',
                borderWidth: 1,
                cornerRadius: 8,
                displayColors: false,
                titleFont: {
                    family: 'Inter',
                    size: 14,
                    weight: '600'
                },
                bodyFont: {
                    family: 'Inter',
                    size: 13
                }
            }
        },
        animation: {
            duration: 1000,
            easing: 'easeInOutQuart'
        }
    },

    // 销售趋势图配置
    salesChart: {
        type: 'line',
        data: {
            labels: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'],
            datasets: [{
                label: '销售额 (万元)',
                data: [120, 190, 300, 500, 200, 300, 450, 600, 750, 800, 900, 1000],
                borderColor: '#667eea',
                backgroundColor: 'rgba(102, 126, 234, 0.1)',
                borderWidth: 3,
                fill: true,
                tension: 0.4,
                pointBackgroundColor: '#667eea',
                pointBorderColor: '#ffffff',
                pointBorderWidth: 2,
                pointRadius: 5,
                pointHoverRadius: 7
            }]
        },
        options: {
            plugins: {
                legend: {
                    display: false
                }
            },
            scales: {
                x: {
                    grid: {
                        display: false
                    },
                    ticks: {
                        color: '#6b7280',
                        font: {
                            family: 'Inter',
                            size: 12
                        }
                    }
                },
                y: {
                    grid: {
                        color: 'rgba(107, 114, 128, 0.1)'
                    },
                    ticks: {
                        color: '#6b7280',
                        font: {
                            family: 'Inter',
                            size: 12
                        },
                        callback: function(value) {
                            return value + '万';
                        }
                    }
                }
            }
        }
    },

    // 用户增长图配置
    userGrowthChart: {
        type: 'bar',
        data: {
            labels: ['1月', '2月', '3月', '4月', '5月', '6月'],
            datasets: [{
                label: '新增用户',
                data: [65, 59, 80, 81, 56, 55],
                backgroundColor: 'rgba(16, 185, 129, 0.8)',
                borderColor: '#10b981',
                borderWidth: 1,
                borderRadius: 8,
                borderSkipped: false
            }]
        },
        options: {
            plugins: {
                legend: {
                    display: false
                }
            },
            scales: {
                x: {
                    grid: {
                        display: false
                    },
                    ticks: {
                        color: '#6b7280'
                    }
                },
                y: {
                    grid: {
                        color: 'rgba(107, 114, 128, 0.1)'
                    },
                    beginAtZero: true,
                    ticks: {
                        color: '#6b7280'
                    }
                }
            }
        }
    },

    // 收入分析图配置
    revenueChart: {
        type: 'doughnut',
        data: {
            labels: ['产品销售', '服务收入', '广告收入', '其他'],
            datasets: [{
                data: [300, 150, 100, 50],
                backgroundColor: [
                    '#667eea',
                    '#10b981',
                    '#f59e0b',
                    '#ef4444'
                ],
                borderWidth: 0,
                cutout: '70%'
            }]
        },
        options: {
            plugins: {
                legend: {
                    position: 'bottom',
                    labels: {
                        padding: 20,
                        usePointStyle: true,
                        font: {
                            family: 'Inter',
                            size: 12
                        }
                    }
                }
            }
        }
    },

    // 订单状态图配置
    orderStatusChart: {
        type: 'pie',
        data: {
            labels: ['已完成', '处理中', '待处理', '已取消'],
            datasets: [{
                data: [45, 25, 20, 10],
                backgroundColor: [
                    '#10b981',
                    '#667eea',
                    '#f59e0b',
                    '#ef4444'
                ],
                borderWidth: 2,
                borderColor: '#ffffff'
            }]
        },
        options: {
            plugins: {
                legend: {
                    position: 'bottom',
                    labels: {
                        padding: 15,
                        usePointStyle: true,
                        font: {
                            family: 'Inter',
                            size: 11
                        }
                    }
                }
            }
        }
    }
};

// 创建图表的通用函数
function createChart(canvasId, configKey) {
    if (typeof Chart === 'undefined') {
        console.warn('Chart.js未加载，无法创建图表');
        return null;
    }

    const canvas = document.getElementById(canvasId);
    if (!canvas) {
        console.warn(`找不到画布元素: ${canvasId}`);
        return null;
    }

    const ctx = canvas.getContext('2d');
    const config = chartConfigs[configKey];
    
    if (!config) {
        console.warn(`找不到图表配置: ${configKey}`);
        return null;
    }

    // 销毁现有图表
    if (window.chartInstances.has(canvasId)) {
        window.chartInstances.get(canvasId).destroy();
    }

    // 合并默认配置
    const finalConfig = {
        ...config,
        options: {
            ...chartConfigs.defaults,
            ...config.options
        }
    };

    // 创建新图表
    const chart = new Chart(ctx, finalConfig);
    window.chartInstances.set(canvasId, chart);
    
    return chart;
}

// 初始化所有仪表盘图表
function initializeDashboardCharts() {
    console.log('初始化仪表盘图表...');
    
    // 创建各种图表
    createChart('salesChart', 'salesChart');
    createChart('userGrowthChart', 'userGrowthChart');
    createChart('revenueChart', 'revenueChart');
    createChart('orderStatusChart', 'orderStatusChart');
    
    console.log('仪表盘图表初始化完成');
}

// 更新图表数据
function updateChartData(chartId, newData) {
    const chart = window.chartInstances.get(chartId);
    if (chart) {
        chart.data = newData;
        chart.update();
    }
}

// 销毁所有图表
function destroyAllCharts() {
    window.chartInstances.forEach((chart, id) => {
        chart.destroy();
    });
    window.chartInstances.clear();
}

// 导出函数
window.createChart = createChart;
window.initializeDashboardCharts = initializeDashboardCharts;
window.updateChartData = updateChartData;
window.destroyAllCharts = destroyAllCharts;
