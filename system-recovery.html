<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>系统恢复工具</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        }
        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            margin: 8px;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.2s;
        }
        .btn-primary { background: #6366f1; color: white; }
        .btn-success { background: #10b981; color: white; }
        .btn-warning { background: #f59e0b; color: white; }
        .btn-danger { background: #ef4444; color: white; }
        .btn:hover { transform: translateY(-1px); box-shadow: 0 4px 12px rgba(0,0,0,0.15); }
        .status {
            padding: 12px;
            margin: 10px 0;
            border-radius: 6px;
            border-left: 4px solid;
        }
        .success { background: #d1fae5; border-color: #10b981; color: #065f46; }
        .error { background: #fee2e2; border-color: #ef4444; color: #991b1b; }
        .warning { background: #fef3c7; border-color: #f59e0b; color: #92400e; }
        .info { background: #dbeafe; border-color: #3b82f6; color: #1e40af; }
        h1 { color: #1f2937; margin-bottom: 20px; }
        h2 { color: #374151; margin: 20px 0 10px 0; }
        .step { margin: 15px 0; padding: 15px; background: #f9fafb; border-radius: 6px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 系统恢复工具</h1>
        <p>如果管理界面无法正常使用，请按照以下步骤进行恢复：</p>
        
        <div id="status"></div>
        
        <div class="step">
            <h2>步骤 1: 系统诊断</h2>
            <button class="btn btn-primary" onclick="runDiagnostics()">运行系统诊断</button>
        </div>
        
        <div class="step">
            <h2>步骤 2: 修复登录状态</h2>
            <button class="btn btn-success" onclick="fixLoginState()">修复登录状态</button>
            <button class="btn btn-warning" onclick="clearLoginData()">清除登录数据</button>
        </div>
        
        <div class="step">
            <h2>步骤 3: 重置系统数据</h2>
            <button class="btn btn-warning" onclick="resetSystemData()">重置系统数据</button>
            <button class="btn btn-danger" onclick="clearAllData()">清除所有数据</button>
        </div>
        
        <div class="step">
            <h2>步骤 4: 进入系统</h2>
            <button class="btn btn-primary" onclick="goToLogin()">进入登录页面</button>
            <button class="btn btn-success" onclick="goToMain()">进入管理界面</button>
        </div>
        
        <div class="step">
            <h2>紧急恢复</h2>
            <button class="btn btn-danger" onclick="emergencyReset()">紧急完全重置</button>
        </div>
    </div>

    <script>
        function log(message, type = 'info') {
            const statusDiv = document.getElementById('status');
            const div = document.createElement('div');
            div.className = `status ${type}`;
            div.innerHTML = `<strong>${new Date().toLocaleTimeString()}</strong> ${message}`;
            statusDiv.appendChild(div);
            statusDiv.scrollTop = statusDiv.scrollHeight;
        }

        function runDiagnostics() {
            log('开始系统诊断...', 'info');
            
            try {
                // 检查基本功能
                if (typeof localStorage !== 'undefined') {
                    log('✓ LocalStorage 可用', 'success');
                } else {
                    log('✗ LocalStorage 不可用', 'error');
                }
                
                // 检查文件访问
                Promise.all([
                    fetch('index.html').then(r => r.ok),
                    fetch('app.js').then(r => r.ok),
                    fetch('styles.css').then(r => r.ok),
                    fetch('login.html').then(r => r.ok)
                ]).then(results => {
                    const files = ['index.html', 'app.js', 'styles.css', 'login.html'];
                    results.forEach((ok, i) => {
                        if (ok) {
                            log(`✓ ${files[i]} 可访问`, 'success');
                        } else {
                            log(`✗ ${files[i]} 无法访问`, 'error');
                        }
                    });
                }).catch(err => {
                    log(`文件访问检查失败: ${err.message}`, 'error');
                });
                
                // 检查登录状态
                const isLoggedIn = localStorage.getItem('isLoggedIn');
                const currentUser = localStorage.getItem('currentUser');
                
                if (isLoggedIn === 'true' && currentUser) {
                    try {
                        const user = JSON.parse(currentUser);
                        log(`✓ 用户已登录: ${user.name || user.username}`, 'success');
                    } catch (e) {
                        log('✗ 用户数据格式错误', 'error');
                    }
                } else {
                    log('! 用户未登录', 'warning');
                }
                
                // 检查其他数据
                const keys = Object.keys(localStorage);
                log(`本地存储包含 ${keys.length} 个数据项`, 'info');
                
                log('系统诊断完成', 'success');
                
            } catch (error) {
                log(`诊断过程出错: ${error.message}`, 'error');
            }
        }

        function fixLoginState() {
            try {
                const userInfo = {
                    username: 'admin',
                    name: '张管理员',
                    role: '系统管理员',
                    email: '<EMAIL>',
                    phone: '13800138001',
                    department: '技术部',
                    avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=40&h=40&fit=crop&crop=face',
                    bio: '系统管理员，负责系统维护和用户管理',
                    joinDate: '2023-01-01',
                    employeeId: 'EMP001'
                };
                
                localStorage.setItem('isLoggedIn', 'true');
                localStorage.setItem('currentUser', JSON.stringify(userInfo));
                
                log('✓ 登录状态已修复', 'success');
                log(`✓ 用户信息已设置: ${userInfo.name}`, 'success');
                
            } catch (error) {
                log(`修复登录状态失败: ${error.message}`, 'error');
            }
        }

        function clearLoginData() {
            try {
                localStorage.removeItem('isLoggedIn');
                localStorage.removeItem('currentUser');
                log('✓ 登录数据已清除', 'success');
            } catch (error) {
                log(`清除登录数据失败: ${error.message}`, 'error');
            }
        }

        function resetSystemData() {
            try {
                // 保留登录状态，清除其他数据
                const isLoggedIn = localStorage.getItem('isLoggedIn');
                const currentUser = localStorage.getItem('currentUser');
                
                localStorage.clear();
                
                if (isLoggedIn && currentUser) {
                    localStorage.setItem('isLoggedIn', isLoggedIn);
                    localStorage.setItem('currentUser', currentUser);
                }
                
                // 重新设置默认数据
                const defaultSettings = {
                    theme: 'light',
                    sidebarCollapsed: false,
                    notifications: true
                };
                
                localStorage.setItem('systemSettings', JSON.stringify(defaultSettings));
                
                log('✓ 系统数据已重置', 'success');
                
            } catch (error) {
                log(`重置系统数据失败: ${error.message}`, 'error');
            }
        }

        function clearAllData() {
            if (confirm('确定要清除所有数据吗？这将删除所有设置和用户数据。')) {
                try {
                    localStorage.clear();
                    sessionStorage.clear();
                    log('✓ 所有数据已清除', 'success');
                } catch (error) {
                    log(`清除数据失败: ${error.message}`, 'error');
                }
            }
        }

        function goToLogin() {
            log('正在跳转到登录页面...', 'info');
            setTimeout(() => {
                window.location.href = 'login.html';
            }, 1000);
        }

        function goToMain() {
            // 确保有登录状态
            if (localStorage.getItem('isLoggedIn') !== 'true') {
                fixLoginState();
            }
            
            log('正在跳转到管理界面...', 'info');
            setTimeout(() => {
                window.location.href = 'index.html';
            }, 1000);
        }

        function emergencyReset() {
            if (confirm('紧急重置将清除所有数据并重新设置系统。确定继续吗？')) {
                try {
                    // 清除所有数据
                    localStorage.clear();
                    sessionStorage.clear();
                    
                    // 重新设置基本数据
                    fixLoginState();
                    resetSystemData();
                    
                    log('✓ 紧急重置完成', 'success');
                    log('系统已恢复到初始状态', 'info');
                    
                    setTimeout(() => {
                        if (confirm('重置完成！是否立即进入管理界面？')) {
                            window.location.href = 'index.html';
                        }
                    }, 2000);
                    
                } catch (error) {
                    log(`紧急重置失败: ${error.message}`, 'error');
                }
            }
        }

        // 页面加载时自动运行诊断
        window.onload = function() {
            log('系统恢复工具已启动', 'info');
            setTimeout(runDiagnostics, 500);
        };
    </script>
</body>
</html>