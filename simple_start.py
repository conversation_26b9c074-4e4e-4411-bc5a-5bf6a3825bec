#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
现代化企业级管理系统简化启动脚本
"""

import os
import sys
import time
import webbrowser
from http.server import HTTPServer, SimpleHTTPRequestHandler
from socketserver import ThreadingMixIn
from datetime import datetime

class ThreadingHTTPServer(ThreadingMixIn, HTTPServer):
    """支持多线程的HTTP服务器"""
    daemon_threads = True

def print_banner():
    """打印启动横幅"""
    print("=" * 80)
    print("🚀 现代化企业级管理系统 v2.0.0")
    print("=" * 80)
    print("📊 核心功能模块:")
    print("   ✅ 高级数据分析 - 多维度数据分析和可视化")
    print("   ✅ 实时通信系统 - 即时消息、视频通话、协作功能")
    print("   ✅ 高级安全中心 - 威胁检测、合规管理、安全策略")
    print("   ✅ 高级工作流引擎 - 可视化流程设计、任务管理、系统集成")
    print("   ✅ 智能助手系统 - 对话交互、智能问答、数据洞察、学习能力")
    print()
    print("🌐 访问地址: http://localhost:8000")
    print("🔐 登录页面: http://localhost:8000/login.html")
    print("📊 管理仪表板: http://localhost:8000/index.html")
    print()
    print("👤 测试账号:")
    print("   • admin / admin123 (系统管理员)")
    print("   • manager / manager123 (部门经理)")
    print("   • user / user123 (普通用户)")
    print("=" * 80)

def start_server():
    """启动HTTP服务器"""
    class CustomHandler(SimpleHTTPRequestHandler):
        def end_headers(self):
            # 添加CORS头部和安全头部
            self.send_header('Access-Control-Allow-Origin', '*')
            self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS, PUT, DELETE')
            self.send_header('Access-Control-Allow-Headers', 'Content-Type, Authorization, X-Requested-With')
            self.send_header('X-Content-Type-Options', 'nosniff')
            self.send_header('X-Frame-Options', 'DENY')
            self.send_header('X-XSS-Protection', '1; mode=block')
            super().end_headers()
        
        def do_OPTIONS(self):
            # 处理预检请求
            self.send_response(200)
            self.end_headers()
        
        def log_message(self, format, *args):
            # 记录访问日志
            timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            print(f"[{timestamp}] {format % args}")
    
    try:
        server = ThreadingHTTPServer(('localhost', 8000), CustomHandler)
        print("✅ HTTP服务器已启动在端口 8000")
        print("📊 系统运行中... (按 Ctrl+C 停止)")
        print("=" * 80)
        server.serve_forever()
    except KeyboardInterrupt:
        print("\n⏹️  系统正在关闭...")
        print("👋 感谢使用现代化企业级管理系统！")
    except Exception as e:
        print(f"❌ 服务器启动失败: {e}")

def main():
    """主函数"""
    print_banner()
    
    print("🔧 正在初始化系统组件...")
    print("   📊 高级数据分析模块")
    print("   💬 实时通信系统")
    print("   🔐 高级安全中心")
    print("   🔄 高级工作流引擎")
    print("   🤖 智能助手系统")
    print()
    
    # 自动打开浏览器
    print("🌐 正在打开浏览器...")
    try:
        webbrowser.open('http://localhost:8000/login.html')
        print("✅ 浏览器已自动打开")
    except:
        print("⚠️  无法自动打开浏览器，请手动访问 http://localhost:8000/login.html")
    
    print()
    print("💡 使用指南:")
    print("   1. 使用测试账号登录系统")
    print("   2. 探索五大核心功能模块")
    print("   3. 体验现代化的企业级管理功能")
    print("   4. 按 Ctrl+C 停止系统")
    print()
    
    # 启动服务器
    start_server()

if __name__ == "__main__":
    main()
