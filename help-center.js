// 帮助中心功能

// 初始化帮助中心
function initializeHelpCenter() {
    initializeHelpNavigation();
    initializeFaqToggle();
    initializeSupportForm();
    initializeHelpSearch();
}

// 初始化帮助导航
function initializeHelpNavigation() {
    const navItems = document.querySelectorAll('.help-nav-item');
    const sections = document.querySelectorAll('.help-section');

    navItems.forEach(item => {
        item.addEventListener('click', function() {
            const targetSection = this.getAttribute('data-section');
            
            // 更新导航状态
            navItems.forEach(nav => nav.classList.remove('active'));
            this.classList.add('active');
            
            // 显示对应内容
            sections.forEach(section => section.classList.remove('active'));
            const targetElement = document.getElementById(targetSection);
            if (targetElement) {
                targetElement.classList.add('active');
            }
        });
    });
}

// 初始化FAQ折叠功能
function initializeFaqToggle() {
    // FAQ切换功能在HTML中已经通过onclick绑定
}

// FAQ切换函数
function toggleFaq(element) {
    const faqItem = element.parentElement;
    const answer = faqItem.querySelector('.faq-answer');
    const icon = element.querySelector('i');
    
    // 切换展开/收起状态
    faqItem.classList.toggle('active');
    
    if (faqItem.classList.contains('active')) {
        answer.style.maxHeight = answer.scrollHeight + 'px';
        icon.style.transform = 'rotate(180deg)';
    } else {
        answer.style.maxHeight = '0';
        icon.style.transform = 'rotate(0deg)';
    }
}

// 初始化支持表单
function initializeSupportForm() {
    const form = document.getElementById('supportForm');
    if (form) {
        form.addEventListener('submit', function(e) {
            e.preventDefault();
            handleSupportSubmission();
        });
    }
}

// 处理支持请求提交
function handleSupportSubmission() {
    const subject = document.getElementById('supportSubject').value.trim();
    const priority = document.getElementById('supportPriority').value;
    const message = document.getElementById('supportMessage').value.trim();

    if (!subject || !message) {
        showNotification('请填写完整的支持请求信息', 'error');
        return;
    }

    // 模拟提交过程
    const submitBtn = document.querySelector('#supportForm button[type="submit"]');
    const originalText = submitBtn.innerHTML;
    
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 提交中...';
    submitBtn.disabled = true;

    setTimeout(() => {
        // 重置表单
        document.getElementById('supportForm').reset();
        
        // 恢复按钮
        submitBtn.innerHTML = originalText;
        submitBtn.disabled = false;
        
        // 显示成功消息
        showNotification('支持请求已提交，我们会尽快回复您', 'success');
        
        // 可以在这里添加实际的API调用
        console.log('支持请求:', { subject, priority, message });
    }, 2000);
}

// 初始化帮助搜索
function initializeHelpSearch() {
    const searchInput = document.getElementById('helpSearch');
    if (searchInput) {
        // 防抖搜索
        let searchTimeout;
        searchInput.addEventListener('input', function() {
            clearTimeout(searchTimeout);
            searchTimeout = setTimeout(() => {
                performHelpSearch(this.value.trim());
            }, 300);
        });
    }
}

// 执行帮助搜索
function performHelpSearch(query) {
    if (!query) {
        clearSearchHighlights();
        return;
    }

    // 搜索内容
    const searchResults = searchHelpContent(query);
    highlightSearchResults(searchResults);
    
    if (searchResults.length > 0) {
        showNotification(`找到 ${searchResults.length} 个相关结果`, 'info');
    } else {
        showNotification('未找到相关内容', 'warning');
    }
}

// 搜索帮助内容
function searchHelpContent(query) {
    const results = [];
    const sections = document.querySelectorAll('.help-section');
    
    sections.forEach(section => {
        const sectionId = section.id;
        const textContent = section.textContent.toLowerCase();
        
        if (textContent.includes(query.toLowerCase())) {
            // 查找具体匹配的元素
            const elements = section.querySelectorAll('h3, h4, p, li');
            elements.forEach(element => {
                if (element.textContent.toLowerCase().includes(query.toLowerCase())) {
                    results.push({
                        section: sectionId,
                        element: element,
                        text: element.textContent
                    });
                }
            });
        }
    });
    
    return results;
}

// 高亮搜索结果
function highlightSearchResults(results) {
    clearSearchHighlights();
    
    results.forEach(result => {
        const element = result.element;
        const text = element.textContent;
        const query = document.getElementById('helpSearch').value.trim();
        
        // 创建高亮版本
        const highlightedText = text.replace(
            new RegExp(query, 'gi'),
            `<mark class="search-highlight">$&</mark>`
        );
        
        element.innerHTML = highlightedText;
        element.classList.add('search-result');
    });
}

// 清除搜索高亮
function clearSearchHighlights() {
    const highlighted = document.querySelectorAll('.search-result');
    highlighted.forEach(element => {
        element.innerHTML = element.textContent;
        element.classList.remove('search-result');
    });
    
    const marks = document.querySelectorAll('.search-highlight');
    marks.forEach(mark => {
        mark.outerHTML = mark.textContent;
    });
}

// 搜索函数（供HTML调用）
function searchHelp() {
    const query = document.getElementById('helpSearch').value.trim();
    performHelpSearch(query);
}

// ===== 视频播放功能 =====

// 视频播放管理器
class VideoPlayerManager {
    constructor() {
        this.playerModal = null;
        this.videoElement = null;
        this.currentVideo = null;
        this.playlist = [];
        this.currentIndex = 0;
        this.isFullscreen = false;
        this.watchProgress = {};
        
        this.init();
    }
    
    init() {
        this.playerModal = document.getElementById('videoPlayerModalOverlay');
        this.videoElement = document.getElementById('mainVideoPlayer');
        
        this.bindEvents();
        this.loadPlaylist();
    }
    
    bindEvents() {
        if (!this.videoElement) return;
        
        // 视频事件
        this.videoElement.addEventListener('loadstart', () => this.showLoading());
        this.videoElement.addEventListener('loadedmetadata', () => this.onVideoLoaded());
        this.videoElement.addEventListener('canplay', () => this.hideLoading());
        this.videoElement.addEventListener('error', () => this.showError());
        this.videoElement.addEventListener('timeupdate', () => this.updateProgress());
        this.videoElement.addEventListener('ended', () => this.onVideoEnded());
        this.videoElement.addEventListener('play', () => this.updatePlayButton(true));
        this.videoElement.addEventListener('pause', () => this.updatePlayButton(false));
        
        // 进度条事件
        const progressBar = document.getElementById('videoProgressBar');
        if (progressBar) {
            progressBar.addEventListener('click', (e) => this.seekVideo(e));
        }
        
        // 音量控制
        const volumeSlider = document.getElementById('volumeSlider');
        if (volumeSlider) {
            volumeSlider.addEventListener('input', (e) => this.setVolume(e.target.value));
        }
        
        // 播放速度
        const speedSelector = document.getElementById('playbackSpeed');
        if (speedSelector) {
            speedSelector.addEventListener('change', (e) => this.setPlaybackSpeed(e.target.value));
        }
        
        // 全屏事件
        document.addEventListener('fullscreenchange', () => this.onFullscreenChange());
        document.addEventListener('webkitfullscreenchange', () => this.onFullscreenChange());
        document.addEventListener('mozfullscreenchange', () => this.onFullscreenChange());
        document.addEventListener('MSFullscreenChange', () => this.onFullscreenChange());
        
        // 键盘快捷键
        document.addEventListener('keydown', (e) => this.handleKeyboard(e));
    }
    
    // 加载播放列表
    loadPlaylist() {
        this.playlist = [
            {
                id: 'intro',
                title: '系统介绍',
                description: '了解现代企业管理系统的基本功能和特性',
                duration: '05:30',
                thumbnail: 'https://via.placeholder.com/320x180/6366f1/ffffff?text=系统介绍',
                src: 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4',
                subtitles: null
            },
            {
                id: 'login',
                title: '登录和认证',
                description: '学习如何登录系统和管理账户安全',
                duration: '03:45',
                thumbnail: 'https://via.placeholder.com/320x180/10b981/ffffff?text=登录认证',
                src: 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/ElephantsDream.mp4',
                subtitles: null
            },
            {
                id: 'user-management',
                title: '用户管理',
                description: '如何添加、编辑和管理系统用户',
                duration: '07:20',
                thumbnail: 'https://via.placeholder.com/320x180/f59e0b/ffffff?text=用户管理',
                src: 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/ForBiggerBlazes.mp4',
                subtitles: null
            },
            {
                id: 'data-analysis',
                title: '数据分析',
                description: '使用数据分析功能生成报表和图表',
                duration: '06:15',
                thumbnail: 'https://via.placeholder.com/320x180/ef4444/ffffff?text=数据分析',
                src: 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/ForBiggerEscapes.mp4',
                subtitles: null
            },
            {
                id: 'file-management',
                title: '文件管理',
                description: '文件上传、下载和组织管理',
                duration: '04:50',
                thumbnail: 'https://via.placeholder.com/320x180/8b5cf6/ffffff?text=文件管理',
                src: 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/ForBiggerFun.mp4',
                subtitles: null
            }
        ];
    }
    
    // 播放视频
    playVideo(videoId) {
        const video = this.playlist.find(v => v.id === videoId);
        if (!video) {
            showNotification('视频不存在', 'error');
            return;
        }
        
        this.currentVideo = video;
        this.currentIndex = this.playlist.findIndex(v => v.id === videoId);
        
        this.showModal();
        this.loadVideo(video);
        this.updateVideoInfo(video);
        this.renderPlaylist();
    }
    
    // 显示播放器模态框
    showModal() {
        this.playerModal.classList.add('active');
        document.body.style.overflow = 'hidden';
    }
    
    // 关闭播放器模态框
    closeModal() {
        this.pauseVideo();
        this.playerModal.classList.remove('active');
        document.body.style.overflow = '';
        this.saveWatchProgress();
    }
    
    // 加载视频
    loadVideo(video) {
        if (!this.videoElement) return;
        
        this.showLoading();
        
        const source = document.getElementById('videoSource');
        if (source) {
            source.src = video.src;
        }
        
        this.videoElement.load();
        
        // 恢复观看进度
        const savedProgress = this.watchProgress[video.id];
        if (savedProgress && savedProgress.time > 10) {
            this.videoElement.addEventListener('loadedmetadata', () => {
                this.videoElement.currentTime = savedProgress.time;
            }, { once: true });
        }
    }
    
    // 更新视频信息
    updateVideoInfo(video) {
        document.getElementById('videoPlayerTitle').textContent = video.title;
        document.getElementById('videoTitle').textContent = video.title;
        document.getElementById('videoDuration').textContent = `时长: ${video.duration}`;
        document.getElementById('videoDescription').textContent = video.description;
        
        // 模拟视频大小和质量信息
        document.getElementById('videoSize').textContent = '大小: 45.2 MB';
        document.getElementById('videoQuality').textContent = '质量: 1080p';
    }
    
    // 渲染播放列表
    renderPlaylist() {
        const container = document.getElementById('playlistItems');
        if (!container) return;
        
        const playlistHtml = this.playlist.map((video, index) => `
            <div class="playlist-item ${video.id === this.currentVideo?.id ? 'active' : ''}" 
                 onclick="playVideoFromPlaylist(${index})">
                <div class="thumbnail">
                    <i class="fas fa-play"></i>
                </div>
                <div class="info">
                    <div class="title">${video.title}</div>
                    <div class="duration">${video.duration}</div>
                </div>
            </div>
        `).join('');
        
        container.innerHTML = playlistHtml;
    }
    
    // 显示加载状态
    showLoading() {
        const overlay = document.getElementById('videoOverlay');
        const loading = document.getElementById('videoLoading');
        const error = document.getElementById('videoError');
        
        if (overlay) overlay.style.display = 'flex';
        if (loading) loading.style.display = 'flex';
        if (error) error.style.display = 'none';
    }
    
    // 隐藏加载状态
    hideLoading() {
        const overlay = document.getElementById('videoOverlay');
        if (overlay) overlay.style.display = 'none';
    }
    
    // 显示错误状态
    showError() {
        const overlay = document.getElementById('videoOverlay');
        const loading = document.getElementById('videoLoading');
        const error = document.getElementById('videoError');
        
        if (overlay) overlay.style.display = 'flex';
        if (loading) loading.style.display = 'none';
        if (error) error.style.display = 'flex';
    }
    
    // 视频加载完成
    onVideoLoaded() {
        const duration = this.videoElement.duration;
        document.getElementById('totalTime').textContent = this.formatTime(duration);
        this.hideLoading();
    }
    
    // 更新播放进度
    updateProgress() {
        if (!this.videoElement) return;
        
        const currentTime = this.videoElement.currentTime;
        const duration = this.videoElement.duration;
        
        if (duration > 0) {
            const progress = (currentTime / duration) * 100;
            
            // 更新进度条
            const progressFilled = document.getElementById('videoProgressFilled');
            const progressHandle = document.getElementById('videoProgressHandle');
            
            if (progressFilled) progressFilled.style.width = progress + '%';
            if (progressHandle) progressHandle.style.left = progress + '%';
            
            // 更新时间显示
            document.getElementById('currentTime').textContent = this.formatTime(currentTime);
            
            // 更新观看进度
            const watchProgress = Math.round(progress);
            document.getElementById('watchProgress').textContent = watchProgress + '%';
            
            // 保存观看进度
            if (this.currentVideo) {
                this.watchProgress[this.currentVideo.id] = {
                    time: currentTime,
                    progress: watchProgress,
                    lastWatched: new Date().toISOString()
                };
            }
        }
    }
    
    // 视频播放结束
    onVideoEnded() {
        // 标记为已完成
        if (this.currentVideo) {
            this.watchProgress[this.currentVideo.id].completed = true;
        }
        
        // 自动播放下一个视频
        this.playNext();
    }
    
    // 播放/暂停切换
    togglePlayPause() {
        if (!this.videoElement) return;
        
        if (this.videoElement.paused) {
            this.videoElement.play();
        } else {
            this.videoElement.pause();
        }
    }
    
    // 更新播放按钮
    updatePlayButton(isPlaying) {
        const playPauseBtn = document.getElementById('playPauseBtn');
        if (playPauseBtn) {
            const icon = playPauseBtn.querySelector('i');
            if (icon) {
                icon.className = isPlaying ? 'fas fa-pause' : 'fas fa-play';
            }
        }
    }
    
    // 静音切换
    toggleMute() {
        if (!this.videoElement) return;
        
        this.videoElement.muted = !this.videoElement.muted;
        
        const muteBtn = document.getElementById('muteBtn');
        if (muteBtn) {
            const icon = muteBtn.querySelector('i');
            if (icon) {
                icon.className = this.videoElement.muted ? 'fas fa-volume-mute' : 'fas fa-volume-up';
            }
        }
    }
    
    // 设置音量
    setVolume(volume) {
        if (!this.videoElement) return;
        
        this.videoElement.volume = volume / 100;
        
        // 更新静音按钮
        const muteBtn = document.getElementById('muteBtn');
        if (muteBtn) {
            const icon = muteBtn.querySelector('i');
            if (icon) {
                if (volume == 0) {
                    icon.className = 'fas fa-volume-mute';
                } else if (volume < 50) {
                    icon.className = 'fas fa-volume-down';
                } else {
                    icon.className = 'fas fa-volume-up';
                }
            }
        }
    }
    
    // 设置播放速度
    setPlaybackSpeed(speed) {
        if (!this.videoElement) return;
        
        this.videoElement.playbackRate = parseFloat(speed);
    }
    
    // 跳转到指定时间
    seekVideo(event) {
        if (!this.videoElement) return;
        
        const progressBar = event.currentTarget;
        const rect = progressBar.getBoundingClientRect();
        const clickX = event.clientX - rect.left;
        const percentage = clickX / rect.width;
        
        this.videoElement.currentTime = percentage * this.videoElement.duration;
    }
    
    // 全屏切换
    toggleFullscreen() {
        const container = this.playerModal.querySelector('.modal-container');
        
        if (!this.isFullscreen) {
            this.enterFullscreen(container);
        } else {
            this.exitFullscreen();
        }
    }
    
    // 进入全屏
    enterFullscreen(element) {
        if (element.requestFullscreen) {
            element.requestFullscreen();
        } else if (element.webkitRequestFullscreen) {
            element.webkitRequestFullscreen();
        } else if (element.mozRequestFullScreen) {
            element.mozRequestFullScreen();
        } else if (element.msRequestFullscreen) {
            element.msRequestFullscreen();
        }
    }
    
    // 退出全屏
    exitFullscreen() {
        if (document.exitFullscreen) {
            document.exitFullscreen();
        } else if (document.webkitExitFullscreen) {
            document.webkitExitFullscreen();
        } else if (document.mozCancelFullScreen) {
            document.mozCancelFullScreen();
        } else if (document.msExitFullscreen) {
            document.msExitFullscreen();
        }
    }
    
    // 全屏状态改变
    onFullscreenChange() {
        this.isFullscreen = !!(document.fullscreenElement || 
                              document.webkitFullscreenElement || 
                              document.mozFullScreenElement || 
                              document.msFullscreenElement);
        
        const fullscreenBtn = document.getElementById('fullscreenBtn');
        if (fullscreenBtn) {
            const icon = fullscreenBtn.querySelector('i');
            if (icon) {
                icon.className = this.isFullscreen ? 'fas fa-compress' : 'fas fa-expand';
            }
        }
    }
    
    // 画中画模式
    togglePictureInPicture() {
        if (!this.videoElement) return;
        
        if (document.pictureInPictureElement) {
            document.exitPictureInPicture();
        } else if (this.videoElement.requestPictureInPicture) {
            this.videoElement.requestPictureInPicture().catch(error => {
                showNotification('画中画模式不支持', 'warning');
            });
        } else {
            showNotification('浏览器不支持画中画模式', 'warning');
        }
    }
    
    // 字幕切换
    toggleSubtitles() {
        if (!this.videoElement) return;
        
        const tracks = this.videoElement.textTracks;
        if (tracks.length > 0) {
            const track = tracks[0];
            track.mode = track.mode === 'showing' ? 'hidden' : 'showing';
            
            const subtitlesBtn = document.getElementById('subtitlesBtn');
            if (subtitlesBtn) {
                subtitlesBtn.style.color = track.mode === 'showing' ? 'var(--primary-color)' : '';
            }
        } else {
            showNotification('该视频没有字幕', 'info');
        }
    }
    
    // 播放下一个视频
    playNext() {
        if (this.currentIndex < this.playlist.length - 1) {
            this.playVideo(this.playlist[this.currentIndex + 1].id);
        } else {
            showNotification('播放列表已结束', 'info');
        }
    }
    
    // 播放上一个视频
    playPrevious() {
        if (this.currentIndex > 0) {
            this.playVideo(this.playlist[this.currentIndex - 1].id);
        } else {
            showNotification('已经是第一个视频', 'info');
        }
    }
    
    // 从播放列表播放
    playFromPlaylist(index) {
        if (index >= 0 && index < this.playlist.length) {
            this.playVideo(this.playlist[index].id);
        }
    }
    
    // 键盘快捷键处理
    handleKeyboard(event) {
        if (!this.playerModal.classList.contains('active')) return;
        
        switch (event.code) {
            case 'Space':
                event.preventDefault();
                this.togglePlayPause();
                break;
            case 'KeyF':
                event.preventDefault();
                this.toggleFullscreen();
                break;
            case 'KeyM':
                event.preventDefault();
                this.toggleMute();
                break;
            case 'ArrowLeft':
                event.preventDefault();
                this.videoElement.currentTime -= 10;
                break;
            case 'ArrowRight':
                event.preventDefault();
                this.videoElement.currentTime += 10;
                break;
            case 'ArrowUp':
                event.preventDefault();
                this.setVolume(Math.min(100, this.videoElement.volume * 100 + 10));
                break;
            case 'ArrowDown':
                event.preventDefault();
                this.setVolume(Math.max(0, this.videoElement.volume * 100 - 10));
                break;
            case 'Escape':
                if (this.isFullscreen) {
                    this.exitFullscreen();
                } else {
                    this.closeModal();
                }
                break;
        }
    }
    
    // 格式化时间
    formatTime(seconds) {
        const minutes = Math.floor(seconds / 60);
        const remainingSeconds = Math.floor(seconds % 60);
        return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
    }
    
    // 保存观看进度
    saveWatchProgress() {
        localStorage.setItem('videoWatchProgress', JSON.stringify(this.watchProgress));
    }
    
    // 加载观看进度
    loadWatchProgress() {
        const saved = localStorage.getItem('videoWatchProgress');
        if (saved) {
            try {
                this.watchProgress = JSON.parse(saved);
            } catch (error) {
                console.error('加载观看进度失败:', error);
                this.watchProgress = {};
            }
        }
    }
    
    // 暂停视频
    pauseVideo() {
        if (this.videoElement && !this.videoElement.paused) {
            this.videoElement.pause();
        }
    }
    
    // 重试加载视频
    retryLoad() {
        if (this.currentVideo) {
            this.loadVideo(this.currentVideo);
        }
    }
}

// 创建全局视频播放管理器实例
let videoPlayerManager;

// 视频播放功能
function playVideo(videoId) {
    if (!videoPlayerManager) {
        videoPlayerManager = new VideoPlayerManager();
    }
    videoPlayerManager.playVideo(videoId);
}

// 复制API示例代码
function copyApiExample(button) {
    const codeBlock = button.parentElement.querySelector('code');
    const text = codeBlock.textContent;
    
    navigator.clipboard.writeText(text).then(() => {
        showNotification('代码已复制到剪贴板', 'success');
        
        // 临时改变按钮文本
        const originalText = button.innerHTML;
        button.innerHTML = '<i class="fas fa-check"></i> 已复制';
        button.classList.add('copied');
        
        setTimeout(() => {
            button.innerHTML = originalText;
            button.classList.remove('copied');
        }, 2000);
    }).catch(() => {
        showNotification('复制失败，请手动复制', 'error');
    });
}

// 导出帮助文档
function exportHelpDocs() {
    showNotification('正在生成帮助文档...', 'info');
    
    // 模拟导出过程
    setTimeout(() => {
        showNotification('帮助文档导出完成', 'success');
        // 这里可以触发实际的文件下载
    }, 2000);
}

// 反馈帮助内容
function feedbackHelp(helpful) {
    const message = helpful ? '感谢您的反馈！' : '我们会改进这部分内容';
    showNotification(message, 'success');
    
    // 这里可以发送反馈数据到服务器
    console.log('帮助反馈:', helpful);
}

// 打印帮助页面
function printHelp() {
    window.print();
}

// 分享帮助链接
function shareHelp() {
    const url = window.location.href;
    
    if (navigator.share) {
        navigator.share({
            title: '帮助中心',
            url: url
        });
    } else {
        // 复制链接到剪贴板
        navigator.clipboard.writeText(url).then(() => {
            showNotification('链接已复制到剪贴板', 'success');
        });
    }
}

// 页面加载时初始化
document.addEventListener('DOMContentLoaded', function() {
    // 检查是否在帮助页面
    const helpPage = document.getElementById('help');
    if (helpPage && helpPage.classList.contains('active')) {
        initializeHelpCenter();
    }
});

// 导出函数供全局使用
window.toggleFaq = toggleFaq;
window.searchHelp = searchHelp;
window.playVideo = playVideo;
window.copyApiExample = copyApiExample;
window.exportHelpDocs = exportHelpDocs;
window.feedbackHelp = feedbackHelp;
window.printHelp = printHelp;
window.shareHelp = shareHelp;
window.initializeHelpCenter = initializeHelpCenter;
// ===== 视频播放器全局函数 =====

function closeVideoPlayerModal() {
    if (videoPlayerManager) {
        videoPlayerManager.closeModal();
    }
}

function togglePlayPause() {
    if (videoPlayerManager) {
        videoPlayerManager.togglePlayPause();
    }
}

function toggleMute() {
    if (videoPlayerManager) {
        videoPlayerManager.toggleMute();
    }
}

function toggleVideoFullscreen() {
    if (videoPlayerManager) {
        videoPlayerManager.toggleFullscreen();
    }
}

function togglePictureInPicture() {
    if (videoPlayerManager) {
        videoPlayerManager.togglePictureInPicture();
    }
}

function toggleSubtitles() {
    if (videoPlayerManager) {
        videoPlayerManager.toggleSubtitles();
    }
}

function playVideoFromPlaylist(index) {
    if (videoPlayerManager) {
        videoPlayerManager.playFromPlaylist(index);
    }
}

function retryVideoLoad() {
    if (videoPlayerManager) {
        videoPlayerManager.retryLoad();
    }
}

function downloadVideo() {
    if (videoPlayerManager && videoPlayerManager.currentVideo) {
        showNotification(`下载视频: ${videoPlayerManager.currentVideo.title}`, 'info');
    }
}

function shareVideo() {
    if (videoPlayerManager && videoPlayerManager.currentVideo) {
        showNotification(`分享视频: ${videoPlayerManager.currentVideo.title}`, 'info');
    }
}

function addToFavorites() {
    if (videoPlayerManager && videoPlayerManager.currentVideo) {
        showNotification(`已收藏视频: ${videoPlayerManager.currentVideo.title}`, 'success');
    }
}

// 更新全局导出
window.closeVideoPlayerModal = closeVideoPlayerModal;
window.togglePlayPause = togglePlayPause;
window.toggleMute = toggleMute;
window.toggleVideoFullscreen = toggleVideoFullscreen;
window.togglePictureInPicture = togglePictureInPicture;
window.toggleSubtitles = toggleSubtitles;
window.playVideoFromPlaylist = playVideoFromPlaylist;
window.retryVideoLoad = retryVideoLoad;
window.downloadVideo = downloadVideo;
window.shareVideo = shareVideo;
window.addToFavorites = addToFavorites;