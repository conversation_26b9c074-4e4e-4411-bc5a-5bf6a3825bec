// UI/UX 增强功能

// 加载状态管理
class LoadingManager {
    constructor() {
        this.loadingStates = new Map();
        this.createLoadingElements();
    }

    // 创建加载元素
    createLoadingElements() {
        // 全屏加载遮罩
        const fullScreenLoader = document.createElement('div');
        fullScreenLoader.id = 'fullScreenLoader';
        fullScreenLoader.className = 'loading-overlay';
        fullScreenLoader.innerHTML = `
            <div class="loading-spinner">
                <div class="spinner-ring"></div>
                <div class="loading-text">加载中...</div>
            </div>
        `;
        document.body.appendChild(fullScreenLoader);

        // 页面加载指示器
        const pageLoader = document.createElement('div');
        pageLoader.id = 'pageLoader';
        pageLoader.className = 'page-loading';
        pageLoader.innerHTML = `
            <div class="page-loading-content">
                <div class="loading-dots">
                    <div class="dot"></div>
                    <div class="dot"></div>
                    <div class="dot"></div>
                </div>
                <div class="loading-message">正在加载页面内容...</div>
            </div>
        `;
        document.body.appendChild(pageLoader);
    }

    // 显示全屏加载
    showFullScreenLoading(message = '加载中...') {
        const loader = document.getElementById('fullScreenLoader');
        const text = loader.querySelector('.loading-text');
        text.textContent = message;
        loader.classList.add('active');
    }

    // 隐藏全屏加载
    hideFullScreenLoading() {
        const loader = document.getElementById('fullScreenLoader');
        loader.classList.remove('active');
    }

    // 显示页面加载
    showPageLoading(message = '正在加载页面内容...') {
        const loader = document.getElementById('pageLoader');
        const text = loader.querySelector('.loading-message');
        text.textContent = message;
        loader.classList.add('active');
    }

    // 隐藏页面加载
    hidePageLoading() {
        const loader = document.getElementById('pageLoader');
        loader.classList.remove('active');
    }

    // 显示元素加载状态
    showElementLoading(elementId, message = '加载中...') {
        const element = document.getElementById(elementId);
        if (!element) return;

        const loadingElement = document.createElement('div');
        loadingElement.className = 'element-loading';
        loadingElement.innerHTML = `
            <div class="element-loading-content">
                <div class="mini-spinner"></div>
                <span>${message}</span>
            </div>
        `;

        element.style.position = 'relative';
        element.appendChild(loadingElement);
        this.loadingStates.set(elementId, loadingElement);
    }

    // 隐藏元素加载状态
    hideElementLoading(elementId) {
        const loadingElement = this.loadingStates.get(elementId);
        if (loadingElement && loadingElement.parentNode) {
            loadingElement.parentNode.removeChild(loadingElement);
            this.loadingStates.delete(elementId);
        }
    }
}

// 骨架屏管理
class SkeletonManager {
    // 创建表格骨架屏
    createTableSkeleton(tableId, rows = 5, columns = 6) {
        const table = document.getElementById(tableId);
        if (!table) return;

        const tbody = table.querySelector('tbody');
        if (!tbody) return;

        tbody.innerHTML = '';
        
        for (let i = 0; i < rows; i++) {
            const row = document.createElement('tr');
            row.className = 'skeleton-row';
            
            for (let j = 0; j < columns; j++) {
                const cell = document.createElement('td');
                cell.innerHTML = '<div class="skeleton-item"></div>';
                row.appendChild(cell);
            }
            
            tbody.appendChild(row);
        }
    }

    // 创建卡片骨架屏
    createCardSkeleton(containerId, count = 4) {
        const container = document.getElementById(containerId);
        if (!container) return;

        container.innerHTML = '';
        
        for (let i = 0; i < count; i++) {
            const card = document.createElement('div');
            card.className = 'skeleton-card';
            card.innerHTML = `
                <div class="skeleton-header">
                    <div class="skeleton-item skeleton-title"></div>
                    <div class="skeleton-item skeleton-subtitle"></div>
                </div>
                <div class="skeleton-content">
                    <div class="skeleton-item skeleton-text"></div>
                    <div class="skeleton-item skeleton-text short"></div>
                    <div class="skeleton-item skeleton-text"></div>
                </div>
            `;
            container.appendChild(card);
        }
    }

    // 创建统计卡片骨架屏
    createStatsSkeleton(containerId) {
        const container = document.getElementById(containerId);
        if (!container) return;

        container.innerHTML = '';
        
        for (let i = 0; i < 4; i++) {
            const card = document.createElement('div');
            card.className = 'skeleton-stat-card';
            card.innerHTML = `
                <div class="skeleton-stat-content">
                    <div class="skeleton-item skeleton-icon"></div>
                    <div class="skeleton-stat-info">
                        <div class="skeleton-item skeleton-number"></div>
                        <div class="skeleton-item skeleton-label"></div>
                    </div>
                </div>
            `;
            container.appendChild(card);
        }
    }

    // 移除骨架屏
    removeSkeleton(containerId) {
        const container = document.getElementById(containerId);
        if (!container) return;

        const skeletonElements = container.querySelectorAll('[class*="skeleton"]');
        skeletonElements.forEach(element => {
            element.remove();
        });
    }
}

// 动画管理
class AnimationManager {
    constructor() {
        this.observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };
        this.setupIntersectionObserver();
    }

    // 设置交叉观察器
    setupIntersectionObserver() {
        if ('IntersectionObserver' in window) {
            this.observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        this.animateElement(entry.target);
                        this.observer.unobserve(entry.target);
                    }
                });
            }, this.observerOptions);

            // 观察所有需要动画的元素
            this.observeElements();
        }
    }

    // 观察元素
    observeElements() {
        const animatedElements = document.querySelectorAll('.animate-on-scroll');
        animatedElements.forEach(element => {
            this.observer.observe(element);
        });
    }

    // 动画元素
    animateElement(element) {
        const animationType = element.dataset.animation || 'fadeInUp';
        element.classList.add('animated', animationType);
    }

    // 添加淡入动画
    fadeIn(element, duration = 300) {
        element.style.opacity = '0';
        element.style.transition = `opacity ${duration}ms ease-in-out`;
        
        setTimeout(() => {
            element.style.opacity = '1';
        }, 10);
    }

    // 添加滑入动画
    slideIn(element, direction = 'left', duration = 300) {
        const transforms = {
            left: 'translateX(-100%)',
            right: 'translateX(100%)',
            up: 'translateY(-100%)',
            down: 'translateY(100%)'
        };

        element.style.transform = transforms[direction];
        element.style.transition = `transform ${duration}ms ease-out`;
        
        setTimeout(() => {
            element.style.transform = 'translate(0, 0)';
        }, 10);
    }

    // 添加缩放动画
    scaleIn(element, duration = 300) {
        element.style.transform = 'scale(0.8)';
        element.style.opacity = '0';
        element.style.transition = `transform ${duration}ms ease-out, opacity ${duration}ms ease-out`;
        
        setTimeout(() => {
            element.style.transform = 'scale(1)';
            element.style.opacity = '1';
        }, 10);
    }

    // 数字计数动画
    animateNumber(element, targetNumber, duration = 1000) {
        const startNumber = 0;
        const increment = targetNumber / (duration / 16);
        let currentNumber = startNumber;

        const timer = setInterval(() => {
            currentNumber += increment;
            if (currentNumber >= targetNumber) {
                currentNumber = targetNumber;
                clearInterval(timer);
            }
            element.textContent = Math.floor(currentNumber).toLocaleString();
        }, 16);
    }

    // 进度条动画
    animateProgressBar(element, targetWidth, duration = 1000) {
        element.style.width = '0%';
        element.style.transition = `width ${duration}ms ease-out`;
        
        setTimeout(() => {
            element.style.width = targetWidth + '%';
        }, 10);
    }
}

// 通知管理增强
class NotificationManager {
    constructor() {
        this.notifications = [];
        this.maxNotifications = 5;
        this.createNotificationContainer();
    }

    // 创建通知容器
    createNotificationContainer() {
        const container = document.createElement('div');
        container.id = 'notificationContainer';
        container.className = 'notification-container';
        document.body.appendChild(container);
    }

    // 显示通知
    show(message, type = 'info', duration = 4000, actions = []) {
        const notification = this.createNotification(message, type, actions);
        this.addNotification(notification);

        // 自动移除
        if (duration > 0) {
            setTimeout(() => {
                this.removeNotification(notification);
            }, duration);
        }

        return notification;
    }

    // 创建通知元素
    createNotification(message, type, actions) {
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;
        
        const icon = this.getNotificationIcon(type);
        
        let actionsHtml = '';
        if (actions.length > 0) {
            actionsHtml = '<div class="notification-actions">';
            actions.forEach(action => {
                actionsHtml += `<button class="notification-btn" onclick="${action.callback}">${action.text}</button>`;
            });
            actionsHtml += '</div>';
        }

        notification.innerHTML = `
            <div class="notification-content">
                <div class="notification-icon">
                    <i class="fas ${icon}"></i>
                </div>
                <div class="notification-message">${message}</div>
                <button class="notification-close" onclick="notificationManager.removeNotification(this.parentElement.parentElement)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            ${actionsHtml}
        `;

        return notification;
    }

    // 获取通知图标
    getNotificationIcon(type) {
        const icons = {
            success: 'fa-check-circle',
            error: 'fa-exclamation-circle',
            warning: 'fa-exclamation-triangle',
            info: 'fa-info-circle'
        };
        return icons[type] || icons.info;
    }

    // 添加通知
    addNotification(notification) {
        const container = document.getElementById('notificationContainer');
        
        // 限制通知数量
        if (this.notifications.length >= this.maxNotifications) {
            this.removeNotification(this.notifications[0]);
        }

        container.appendChild(notification);
        this.notifications.push(notification);

        // 添加进入动画
        setTimeout(() => {
            notification.classList.add('show');
        }, 10);
    }

    // 移除通知
    removeNotification(notification) {
        if (!notification || !notification.parentNode) return;

        notification.classList.add('hide');
        
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
            
            const index = this.notifications.indexOf(notification);
            if (index > -1) {
                this.notifications.splice(index, 1);
            }
        }, 300);
    }

    // 清除所有通知
    clearAll() {
        this.notifications.forEach(notification => {
            this.removeNotification(notification);
        });
    }
}

// 主题管理
class ThemeManager {
    constructor() {
        this.currentTheme = localStorage.getItem('theme') || 'light';
        this.applyTheme(this.currentTheme);
    }

    // 应用主题
    applyTheme(theme) {
        document.body.className = document.body.className.replace(/theme-\w+/g, '');
        document.body.classList.add(`theme-${theme}`);
        this.currentTheme = theme;
        localStorage.setItem('theme', theme);
    }

    // 切换主题
    toggleTheme() {
        const newTheme = this.currentTheme === 'light' ? 'dark' : 'light';
        this.applyTheme(newTheme);
        return newTheme;
    }

    // 获取当前主题
    getCurrentTheme() {
        return this.currentTheme;
    }
}

// 初始化所有管理器
const loadingManager = new LoadingManager();
const skeletonManager = new SkeletonManager();
const animationManager = new AnimationManager();
const notificationManager = new NotificationManager();
const themeManager = new ThemeManager();

// 全局函数
window.showLoading = (message) => loadingManager.showFullScreenLoading(message);
window.hideLoading = () => loadingManager.hideFullScreenLoading();
window.showPageLoading = (message) => loadingManager.showPageLoading(message);
window.hidePageLoading = () => loadingManager.hidePageLoading();
window.showElementLoading = (id, message) => loadingManager.showElementLoading(id, message);
window.hideElementLoading = (id) => loadingManager.hideElementLoading(id);

window.createTableSkeleton = (id, rows, cols) => skeletonManager.createTableSkeleton(id, rows, cols);
window.createCardSkeleton = (id, count) => skeletonManager.createCardSkeleton(id, count);
window.createStatsSkeleton = (id) => skeletonManager.createStatsSkeleton(id);
window.removeSkeleton = (id) => skeletonManager.removeSkeleton(id);

window.showNotificationEnhanced = (message, type, duration, actions) => notificationManager.show(message, type, duration, actions);
window.clearAllNotifications = () => notificationManager.clearAll();

window.toggleTheme = () => themeManager.toggleTheme();
window.getCurrentTheme = () => themeManager.getCurrentTheme();

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    // 添加页面加载动画
    setTimeout(() => {
        document.body.classList.add('loaded');
    }, 100);
    
    // 为现有元素添加动画类
    const cards = document.querySelectorAll('.stat-card, .dashboard-card');
    cards.forEach((card, index) => {
        card.classList.add('animate-on-scroll');
        card.dataset.animation = 'fadeInUp';
        card.style.animationDelay = `${index * 100}ms`;
    });
});

// 设置下拉菜单功能
function toggleSettingsDropdown() {
    const dropdown = document.getElementById('settingsDropdown');
    if (dropdown) {
        dropdown.classList.toggle('show');
    }
}

// 点击外部关闭下拉菜单
document.addEventListener('click', (e) => {
    const settingsDropdown = document.querySelector('.settings-dropdown');
    const dropdown = document.getElementById('settingsDropdown');

    if (settingsDropdown && dropdown && !settingsDropdown.contains(e.target)) {
        dropdown.classList.remove('show');
    }
});

// 设置菜单项功能
function showLanguageSettings() {
    if (window.i18nSystem) {
        i18nSystem.toggleLanguageDropdown();
    }
    toggleSettingsDropdown();
}

function showAccountSettings() {
    if (window.showNotification) {
        showNotification('账户设置功能开发中...', 'info');
    }
    toggleSettingsDropdown();
}

function showSystemSettings() {
    if (window.showNotification) {
        showNotification('系统设置功能开发中...', 'info');
    }
    toggleSettingsDropdown();
}

function showAbout() {
    // 创建关于对话框
    const modal = document.createElement('div');
    modal.className = 'modal about-modal';
    modal.innerHTML = `
        <div class="modal-content">
            <div class="modal-header">
                <h3>关于系统</h3>
                <button class="modal-close" onclick="this.closest('.modal').remove()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <div class="about-content">
                    <div class="about-logo">
                        <i class="fas fa-rocket"></i>
                    </div>
                    <h4>恋雪二游总导航页</h4>
                    <p class="version">版本 2.0.0</p>
                    <p class="description">
                        一个现代化的企业级管理系统，集成了用户管理、订单处理、数据分析、
                        工作流管理、实时通知、多语言支持等功能。
                    </p>
                    <div class="features">
                        <h5>主要功能</h5>
                        <ul>
                            <li>📊 高级数据可视化</li>
                            <li>🔔 实时通知系统</li>
                            <li>🔍 智能搜索功能</li>
                            <li>🖥️ 系统监控面板</li>
                            <li>🔄 工作流管理</li>
                            <li>🌍 多语言支持</li>
                            <li>🎨 主题定制</li>
                            <li>📁 数据导入导出</li>
                        </ul>
                    </div>
                    <div class="tech-stack">
                        <h5>技术栈</h5>
                        <div class="tech-tags">
                            <span class="tech-tag">HTML5</span>
                            <span class="tech-tag">CSS3</span>
                            <span class="tech-tag">JavaScript ES6+</span>
                            <span class="tech-tag">Chart.js</span>
                            <span class="tech-tag">Font Awesome</span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn-primary" onclick="this.closest('.modal').remove()">确定</button>
            </div>
        </div>
    `;

    document.body.appendChild(modal);
    modal.style.display = 'flex';
    toggleSettingsDropdown();
}
