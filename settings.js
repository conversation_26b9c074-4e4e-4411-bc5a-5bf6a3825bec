// 系统设置功能

// 默认设置
const defaultSettings = {
    profile: {
        name: '',
        email: '',
        phone: '',
        department: '',
        bio: ''
    },
    interface: {
        theme: 'light',
        sidebarExpanded: true,
        animationsEnabled: true,
        language: 'zh-CN'
    },
    notifications: {
        desktop: true,
        email: true,
        sound: false,
        frequency: 'realtime'
    },
    security: {
        autoLogout: 'never',
        twoFactorAuth: false
    },
    data: {
        autoBackup: true,
        backupFrequency: 'daily'
    }
};

// 当前设置
let currentSettings = { ...defaultSettings };

// 初始化设置页面
function initializeSettings() {
    loadSettings();
    populateSettingsForm();
    setupSettingsEventListeners();
}

// 加载设置
function loadSettings() {
    // 从localStorage加载设置
    const savedSettings = localStorage.getItem('systemSettings');
    if (savedSettings) {
        try {
            currentSettings = { ...defaultSettings, ...JSON.parse(savedSettings) };
        } catch (error) {
            console.error('加载设置失败:', error);
            currentSettings = { ...defaultSettings };
        }
    }
    
    // 从当前用户信息加载个人资料
    const currentUser = localStorage.getItem('currentUser');
    if (currentUser) {
        try {
            const user = JSON.parse(currentUser);
            currentSettings.profile.name = user.name || '';
            currentSettings.profile.email = user.email || '';
        } catch (error) {
            console.error('加载用户信息失败:', error);
        }
    }
}

// 填充设置表单
function populateSettingsForm() {
    // 个人资料
    document.getElementById('profileName').value = currentSettings.profile.name;
    document.getElementById('profileEmail').value = currentSettings.profile.email;
    document.getElementById('profilePhone').value = currentSettings.profile.phone;
    document.getElementById('profileDepartment').value = currentSettings.profile.department;
    document.getElementById('profileBio').value = currentSettings.profile.bio;
    
    // 界面设置
    document.getElementById('themeMode').value = currentSettings.interface.theme;
    document.getElementById('sidebarExpanded').checked = currentSettings.interface.sidebarExpanded;
    document.getElementById('animationsEnabled').checked = currentSettings.interface.animationsEnabled;
    document.getElementById('languageSelect').value = currentSettings.interface.language;
    
    // 通知设置
    document.getElementById('desktopNotifications').checked = currentSettings.notifications.desktop;
    document.getElementById('emailNotifications').checked = currentSettings.notifications.email;
    document.getElementById('soundNotifications').checked = currentSettings.notifications.sound;
    document.getElementById('notificationFrequency').value = currentSettings.notifications.frequency;
    
    // 安全设置
    document.getElementById('autoLogout').value = currentSettings.security.autoLogout;
    document.getElementById('twoFactorAuth').checked = currentSettings.security.twoFactorAuth;
    
    // 数据设置
    document.getElementById('autoBackup').checked = currentSettings.data.autoBackup;
    document.getElementById('backupFrequency').value = currentSettings.data.backupFrequency;
}

// 设置事件监听器
function setupSettingsEventListeners() {
    // 主题切换
    document.getElementById('themeMode').addEventListener('change', function() {
        applyTheme(this.value);
    });
    
    // 侧边栏状态
    document.getElementById('sidebarExpanded').addEventListener('change', function() {
        applySidebarState(this.checked);
    });
    
    // 动画效果
    document.getElementById('animationsEnabled').addEventListener('change', function() {
        applyAnimationState(this.checked);
    });
    
    // 语言设置
    document.getElementById('languageSelect').addEventListener('change', function() {
        applyLanguage(this.value);
    });
    
    // 通知权限
    document.getElementById('desktopNotifications').addEventListener('change', function() {
        if (this.checked) {
            requestNotificationPermission();
        }
    });
}

// 应用主题
function applyTheme(theme) {
    const body = document.body;
    
    // 移除现有主题类
    body.classList.remove('theme-light', 'theme-dark', 'theme-auto');
    
    if (theme === 'auto') {
        // 跟随系统主题
        const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
        body.classList.add(prefersDark ? 'theme-dark' : 'theme-light');
    } else {
        body.classList.add(`theme-${theme}`);
    }
    
    currentSettings.interface.theme = theme;
    showNotification(`主题已切换为${getThemeText(theme)}`, 'success');
}

// 获取主题文本
function getThemeText(theme) {
    const themeMap = {
        'light': '浅色模式',
        'dark': '深色模式',
        'auto': '跟随系统'
    };
    return themeMap[theme] || '未知';
}

// 应用侧边栏状态
function applySidebarState(expanded) {
    const sidebar = document.getElementById('sidebar');
    if (sidebar) {
        if (expanded) {
            sidebar.classList.remove('collapsed');
        } else {
            sidebar.classList.add('collapsed');
        }
    }
    
    currentSettings.interface.sidebarExpanded = expanded;
    localStorage.setItem('sidebarCollapsed', !expanded);
    showNotification(`侧边栏${expanded ? '展开' : '折叠'}状态已保存`, 'success');
}

// 应用动画状态
function applyAnimationState(enabled) {
    const body = document.body;
    
    if (enabled) {
        body.classList.remove('no-animations');
    } else {
        body.classList.add('no-animations');
    }
    
    currentSettings.interface.animationsEnabled = enabled;
    showNotification(`动画效果已${enabled ? '启用' : '禁用'}`, 'success');
}

// 应用语言设置
function applyLanguage(language) {
    currentSettings.interface.language = language;
    showNotification(`语言已设置为${getLanguageText(language)}`, 'success');
    
    // 这里可以实现实际的语言切换逻辑
    // 例如加载不同的语言包或重新渲染界面
}

// 获取语言文本
function getLanguageText(language) {
    const languageMap = {
        'zh-CN': '简体中文',
        'zh-TW': '繁体中文',
        'en-US': 'English',
        'ja-JP': '日本語'
    };
    return languageMap[language] || '未知';
}

// 请求通知权限
function requestNotificationPermission() {
    if ('Notification' in window) {
        Notification.requestPermission().then(function(permission) {
            if (permission === 'granted') {
                showNotification('桌面通知权限已授予', 'success');
                // 发送测试通知
                new Notification('ModernCorp 系统', {
                    body: '桌面通知已启用',
                    icon: '/favicon.ico'
                });
            } else {
                showNotification('桌面通知权限被拒绝', 'warning');
                document.getElementById('desktopNotifications').checked = false;
            }
        });
    } else {
        showNotification('您的浏览器不支持桌面通知', 'error');
        document.getElementById('desktopNotifications').checked = false;
    }
}

// 保存所有设置
function saveAllSettings() {
    // 收集表单数据
    currentSettings.profile.name = document.getElementById('profileName').value;
    currentSettings.profile.email = document.getElementById('profileEmail').value;
    currentSettings.profile.phone = document.getElementById('profilePhone').value;
    currentSettings.profile.department = document.getElementById('profileDepartment').value;
    currentSettings.profile.bio = document.getElementById('profileBio').value;
    
    currentSettings.interface.theme = document.getElementById('themeMode').value;
    currentSettings.interface.sidebarExpanded = document.getElementById('sidebarExpanded').checked;
    currentSettings.interface.animationsEnabled = document.getElementById('animationsEnabled').checked;
    currentSettings.interface.language = document.getElementById('languageSelect').value;
    
    currentSettings.notifications.desktop = document.getElementById('desktopNotifications').checked;
    currentSettings.notifications.email = document.getElementById('emailNotifications').checked;
    currentSettings.notifications.sound = document.getElementById('soundNotifications').checked;
    currentSettings.notifications.frequency = document.getElementById('notificationFrequency').value;
    
    currentSettings.security.autoLogout = document.getElementById('autoLogout').value;
    currentSettings.security.twoFactorAuth = document.getElementById('twoFactorAuth').checked;
    
    currentSettings.data.autoBackup = document.getElementById('autoBackup').checked;
    currentSettings.data.backupFrequency = document.getElementById('backupFrequency').value;
    
    // 保存到localStorage
    localStorage.setItem('systemSettings', JSON.stringify(currentSettings));
    
    // 更新用户信息
    const currentUser = localStorage.getItem('currentUser');
    if (currentUser) {
        try {
            const user = JSON.parse(currentUser);
            user.name = currentSettings.profile.name;
            user.email = currentSettings.profile.email;
            localStorage.setItem('currentUser', JSON.stringify(user));
            
            // 更新页面显示的用户信息
            updateUserInfo(user);
        } catch (error) {
            console.error('更新用户信息失败:', error);
        }
    }
    
    showNotification('所有设置已保存', 'success');
}

// 重置设置
function resetSettings() {
    if (confirm('确定要重置所有设置吗？此操作不可撤销！')) {
        currentSettings = { ...defaultSettings };
        populateSettingsForm();
        
        // 应用默认设置
        applyTheme(defaultSettings.interface.theme);
        applySidebarState(defaultSettings.interface.sidebarExpanded);
        applyAnimationState(defaultSettings.interface.animationsEnabled);
        
        // 清除localStorage中的设置
        localStorage.removeItem('systemSettings');
        
        showNotification('设置已重置为默认值', 'success');
    }
}

// 查看登录历史
function viewLoginHistory() {
    const loginHistory = [
        { time: '2024-01-15 10:30:00', ip: '*************', device: 'Chrome on Windows', location: '北京' },
        { time: '2024-01-14 16:45:00', ip: '*************', device: 'Safari on macOS', location: '上海' },
        { time: '2024-01-13 09:15:00', ip: '*************', device: 'Firefox on Linux', location: '广州' }
    ];
    
    let historyHtml = '<div class="login-history">';
    historyHtml += '<h4>最近登录记录</h4>';
    historyHtml += '<div class="history-list">';
    
    loginHistory.forEach(record => {
        historyHtml += `
            <div class="history-item">
                <div class="history-time">${record.time}</div>
                <div class="history-details">
                    <div>IP: ${record.ip}</div>
                    <div>设备: ${record.device}</div>
                    <div>位置: ${record.location}</div>
                </div>
            </div>
        `;
    });
    
    historyHtml += '</div></div>';
    
    // 显示登录历史
    showLoginHistoryModal();
}

// 显示修改密码模态框
function showChangePasswordModal() {
    showPasswordModal();
}

// 导出用户数据
function exportUserData() {
    const userData = {
        profile: currentSettings.profile,
        settings: currentSettings,
        exportTime: new Date().toISOString()
    };
    
    const dataStr = JSON.stringify(userData, null, 2);
    const dataBlob = new Blob([dataStr], { type: 'application/json' });
    
    const link = document.createElement('a');
    link.href = URL.createObjectURL(dataBlob);
    link.download = `user_data_${new Date().toISOString().split('T')[0]}.json`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    
    showNotification('用户数据导出成功', 'success');
}

// 清除缓存
function clearCache() {
    if (confirm('确定要清除所有缓存数据吗？这将清除您的登录状态和本地设置。')) {
        // 清除localStorage（除了重要的用户数据）
        const importantKeys = ['currentUser', 'isLoggedIn'];
        const keysToKeep = {};
        
        importantKeys.forEach(key => {
            const value = localStorage.getItem(key);
            if (value) {
                keysToKeep[key] = value;
            }
        });
        
        localStorage.clear();
        
        // 恢复重要数据
        Object.keys(keysToKeep).forEach(key => {
            localStorage.setItem(key, keysToKeep[key]);
        });
        
        // 清除sessionStorage
        sessionStorage.clear();
        
        showNotification('缓存已清除', 'success');
        
        // 建议刷新页面
        setTimeout(() => {
            if (confirm('缓存已清除，建议刷新页面以确保设置生效。是否立即刷新？')) {
                window.location.reload();
            }
        }, 2000);
    }
}

// 页面加载时初始化
document.addEventListener('DOMContentLoaded', function() {
    // 检查是否在设置页面
    if (document.getElementById('profileName')) {
        initializeSettings();
    }
});
