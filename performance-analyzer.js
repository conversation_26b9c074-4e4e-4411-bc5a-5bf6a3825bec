// 性能分析工具
class PerformanceAnalyzer {
    constructor() {
        this.metrics = {};
        this.performanceData = [];
        this.monitoringActive = false;
        this.observers = {};
        this.thresholds = {};
        this.alerts = [];
        
        this.initializePerformanceAnalyzer();
        this.setupPerformanceObservers();
        this.loadPerformanceThresholds();
    }

    initializePerformanceAnalyzer() {
        this.createPerformanceInterface();
        this.bindPerformanceEvents();
        this.startPerformanceMonitoring();
        this.setupPerformanceMetrics();
    }

    createPerformanceInterface() {
        const performancePanel = document.createElement('div');
        performancePanel.id = 'performancePanel';
        performancePanel.className = 'performance-panel';
        performancePanel.innerHTML = `
            <div class="performance-header">
                <h3>
                    <i class="fas fa-tachometer-alt"></i>
                    性能分析
                </h3>
                <div class="performance-controls">
                    <button class="btn-secondary" onclick="performanceAnalyzer.exportReport()">
                        <i class="fas fa-download"></i>
                        导出报告
                    </button>
                    <button class="btn-primary" onclick="performanceAnalyzer.startAnalysis()">
                        <i class="fas fa-play"></i>
                        开始分析
                    </button>
                    <button class="btn-icon" onclick="performanceAnalyzer.closePerformancePanel()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            </div>
            
            <div class="performance-body">
                <div class="performance-tabs">
                    <button class="tab-btn active" data-tab="overview">
                        <i class="fas fa-chart-line"></i>
                        概览
                    </button>
                    <button class="tab-btn" data-tab="metrics">
                        <i class="fas fa-chart-bar"></i>
                        指标
                    </button>
                    <button class="tab-btn" data-tab="memory">
                        <i class="fas fa-memory"></i>
                        内存
                    </button>
                    <button class="tab-btn" data-tab="network">
                        <i class="fas fa-wifi"></i>
                        网络
                    </button>
                    <button class="tab-btn" data-tab="alerts">
                        <i class="fas fa-exclamation-triangle"></i>
                        告警
                    </button>
                </div>
                
                <div class="performance-content">
                    <!-- 性能概览 -->
                    <div class="tab-content active" id="overviewTab">
                        <div class="performance-overview">
                            <div class="overview-cards">
                                <div class="perf-card">
                                    <div class="card-icon">
                                        <i class="fas fa-stopwatch"></i>
                                    </div>
                                    <div class="card-content">
                                        <h4>页面加载时间</h4>
                                        <div class="metric-value" id="pageLoadTime">--</div>
                                        <div class="metric-trend" id="loadTimeTrend">
                                            <i class="fas fa-arrow-up"></i>
                                            <span>+5%</span>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="perf-card">
                                    <div class="card-icon">
                                        <i class="fas fa-bolt"></i>
                                    </div>
                                    <div class="card-content">
                                        <h4>首次内容绘制</h4>
                                        <div class="metric-value" id="firstContentfulPaint">--</div>
                                        <div class="metric-trend good">
                                            <i class="fas fa-arrow-down"></i>
                                            <span>-2%</span>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="perf-card">
                                    <div class="card-icon">
                                        <i class="fas fa-eye"></i>
                                    </div>
                                    <div class="card-content">
                                        <h4>最大内容绘制</h4>
                                        <div class="metric-value" id="largestContentfulPaint">--</div>
                                        <div class="metric-trend">
                                            <i class="fas fa-minus"></i>
                                            <span>0%</span>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="perf-card">
                                    <div class="card-icon">
                                        <i class="fas fa-hand-pointer"></i>
                                    </div>
                                    <div class="card-content">
                                        <h4>首次输入延迟</h4>
                                        <div class="metric-value" id="firstInputDelay">--</div>
                                        <div class="metric-trend good">
                                            <i class="fas fa-arrow-down"></i>
                                            <span>-8%</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="performance-charts">
                                <div class="chart-container">
                                    <h4>性能趋势</h4>
                                    <canvas id="performanceTrendChart" width="400" height="200"></canvas>
                                </div>
                                
                                <div class="chart-container">
                                    <h4>资源加载时间</h4>
                                    <canvas id="resourceTimingChart" width="400" height="200"></canvas>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 详细指标 -->
                    <div class="tab-content" id="metricsTab">
                        <div class="metrics-section">
                            <h4>核心Web指标</h4>
                            <div class="metrics-grid">
                                <div class="metric-item">
                                    <div class="metric-header">
                                        <span class="metric-name">累积布局偏移 (CLS)</span>
                                        <span class="metric-score good" id="clsScore">0.05</span>
                                    </div>
                                    <div class="metric-bar">
                                        <div class="metric-fill good" style="width: 95%"></div>
                                    </div>
                                    <div class="metric-description">布局稳定性良好</div>
                                </div>
                                
                                <div class="metric-item">
                                    <div class="metric-header">
                                        <span class="metric-name">首次输入延迟 (FID)</span>
                                        <span class="metric-score good" id="fidScore">45ms</span>
                                    </div>
                                    <div class="metric-bar">
                                        <div class="metric-fill good" style="width: 85%"></div>
                                    </div>
                                    <div class="metric-description">交互响应快速</div>
                                </div>
                                
                                <div class="metric-item">
                                    <div class="metric-header">
                                        <span class="metric-name">最大内容绘制 (LCP)</span>
                                        <span class="metric-score warning" id="lcpScore">2.8s</span>
                                    </div>
                                    <div class="metric-bar">
                                        <div class="metric-fill warning" style="width: 70%"></div>
                                    </div>
                                    <div class="metric-description">需要优化加载速度</div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="metrics-section">
                            <h4>JavaScript性能</h4>
                            <div class="js-metrics">
                                <div class="js-metric">
                                    <span class="js-metric-name">脚本执行时间</span>
                                    <span class="js-metric-value" id="scriptExecutionTime">--</span>
                                </div>
                                <div class="js-metric">
                                    <span class="js-metric-name">DOM节点数量</span>
                                    <span class="js-metric-value" id="domNodeCount">--</span>
                                </div>
                                <div class="js-metric">
                                    <span class="js-metric-name">事件监听器数量</span>
                                    <span class="js-metric-value" id="eventListenerCount">--</span>
                                </div>
                                <div class="js-metric">
                                    <span class="js-metric-name">长任务数量</span>
                                    <span class="js-metric-value" id="longTaskCount">--</span>
                                </div>
                            </div>
                        </div>
                        
                        <div class="metrics-section">
                            <h4>资源性能</h4>
                            <div class="resource-table" id="resourceTable">
                                <!-- 资源性能表格 -->
                            </div>
                        </div>
                    </div>
                    
                    <!-- 内存分析 -->
                    <div class="tab-content" id="memoryTab">
                        <div class="memory-section">
                            <h4>内存使用情况</h4>
                            <div class="memory-overview">
                                <div class="memory-card">
                                    <h5>JavaScript堆</h5>
                                    <div class="memory-usage">
                                        <div class="usage-bar">
                                            <div class="usage-fill" id="jsHeapUsage" style="width: 45%"></div>
                                        </div>
                                        <div class="usage-text">
                                            <span id="jsHeapUsed">12.5MB</span> / 
                                            <span id="jsHeapTotal">28MB</span>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="memory-card">
                                    <h5>DOM节点</h5>
                                    <div class="memory-metric">
                                        <span class="metric-number" id="domNodes">1,247</span>
                                        <span class="metric-label">个节点</span>
                                    </div>
                                </div>
                                
                                <div class="memory-card">
                                    <h5>事件监听器</h5>
                                    <div class="memory-metric">
                                        <span class="metric-number" id="eventListeners">89</span>
                                        <span class="metric-label">个监听器</span>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="memory-chart">
                                <h4>内存使用趋势</h4>
                                <canvas id="memoryTrendChart" width="500" height="250"></canvas>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 网络分析 -->
                    <div class="tab-content" id="networkTab">
                        <div class="network-section">
                            <h4>网络性能</h4>
                            <div class="network-overview">
                                <div class="network-card">
                                    <h5>总请求数</h5>
                                    <div class="network-metric">
                                        <span class="metric-number" id="totalRequests">--</span>
                                        <span class="metric-label">个请求</span>
                                    </div>
                                </div>
                                
                                <div class="network-card">
                                    <h5>传输大小</h5>
                                    <div class="network-metric">
                                        <span class="metric-number" id="transferSize">--</span>
                                        <span class="metric-label">KB</span>
                                    </div>
                                </div>
                                
                                <div class="network-card">
                                    <h5>平均响应时间</h5>
                                    <div class="network-metric">
                                        <span class="metric-number" id="avgResponseTime">--</span>
                                        <span class="metric-label">ms</span>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="network-requests" id="networkRequests">
                                <!-- 网络请求列表 -->
                            </div>
                        </div>
                    </div>
                    
                    <!-- 性能告警 -->
                    <div class="tab-content" id="alertsTab">
                        <div class="alerts-section">
                            <div class="section-header">
                                <h4>性能告警</h4>
                                <div class="alert-controls">
                                    <button class="btn-secondary" onclick="performanceAnalyzer.configureAlerts()">
                                        <i class="fas fa-cog"></i>
                                        配置告警
                                    </button>
                                    <button class="btn-secondary" onclick="performanceAnalyzer.clearAlerts()">
                                        <i class="fas fa-trash"></i>
                                        清空告警
                                    </button>
                                </div>
                            </div>
                            
                            <div class="alerts-list" id="alertsList">
                                <!-- 告警列表 -->
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
        
        document.body.appendChild(performancePanel);
    }

    setupPerformanceObservers() {
        // Performance Observer for navigation timing
        if ('PerformanceObserver' in window) {
            // 监听导航时间
            const navObserver = new PerformanceObserver((list) => {
                for (const entry of list.getEntries()) {
                    this.processNavigationEntry(entry);
                }
            });
            navObserver.observe({ entryTypes: ['navigation'] });
            
            // 监听资源时间
            const resourceObserver = new PerformanceObserver((list) => {
                for (const entry of list.getEntries()) {
                    this.processResourceEntry(entry);
                }
            });
            resourceObserver.observe({ entryTypes: ['resource'] });
            
            // 监听长任务
            const longTaskObserver = new PerformanceObserver((list) => {
                for (const entry of list.getEntries()) {
                    this.processLongTaskEntry(entry);
                }
            });
            longTaskObserver.observe({ entryTypes: ['longtask'] });
            
            // 监听布局偏移
            const layoutShiftObserver = new PerformanceObserver((list) => {
                for (const entry of list.getEntries()) {
                    this.processLayoutShiftEntry(entry);
                }
            });
            layoutShiftObserver.observe({ entryTypes: ['layout-shift'] });
        }
        
        // 监听内存使用
        this.setupMemoryMonitoring();
        
        // 监听用户交互
        this.setupInteractionMonitoring();
    }

    setupMemoryMonitoring() {
        if ('memory' in performance) {
            setInterval(() => {
                const memInfo = performance.memory;
                this.updateMemoryMetrics({
                    usedJSHeapSize: memInfo.usedJSHeapSize,
                    totalJSHeapSize: memInfo.totalJSHeapSize,
                    jsHeapSizeLimit: memInfo.jsHeapSizeLimit
                });
            }, 5000);
        }
    }

    setupInteractionMonitoring() {
        // 监听用户交互延迟
        let interactionStart = 0;
        
        ['click', 'keydown', 'touchstart'].forEach(eventType => {
            document.addEventListener(eventType, () => {
                interactionStart = performance.now();
            }, { passive: true });
        });
        
        // 监听交互响应
        const observer = new MutationObserver(() => {
            if (interactionStart > 0) {
                const interactionTime = performance.now() - interactionStart;
                this.recordInteractionTime(interactionTime);
                interactionStart = 0;
            }
        });
        
        observer.observe(document.body, { childList: true, subtree: true });
    }

    loadPerformanceThresholds() {
        this.thresholds = {
            pageLoadTime: 3000, // 3秒
            firstContentfulPaint: 1800, // 1.8秒
            largestContentfulPaint: 2500, // 2.5秒
            firstInputDelay: 100, // 100毫秒
            cumulativeLayoutShift: 0.1, // 0.1
            memoryUsage: 0.8, // 80%
            longTaskDuration: 50 // 50毫秒
        };
    }

    bindPerformanceEvents() {
        // 标签切换
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('tab-btn') && e.target.closest('.performance-panel')) {
                this.switchPerformanceTab(e.target);
            }
        });
    }

    switchPerformanceTab(tabBtn) {
        const container = tabBtn.closest('.performance-panel');
        const tabName = tabBtn.dataset.tab;
        
        // 更新标签状态
        container.querySelectorAll('.tab-btn').forEach(btn => btn.classList.remove('active'));
        container.querySelectorAll('.tab-content').forEach(content => content.classList.remove('active'));
        
        tabBtn.classList.add('active');
        container.querySelector(`#${tabName}Tab`).classList.add('active');
        
        // 加载对应内容
        this.loadTabContent(tabName);
    }

    loadTabContent(tabName) {
        switch (tabName) {
            case 'overview':
                this.updateOverviewMetrics();
                break;
            case 'metrics':
                this.updateDetailedMetrics();
                break;
            case 'memory':
                this.updateMemoryAnalysis();
                break;
            case 'network':
                this.updateNetworkAnalysis();
                break;
            case 'alerts':
                this.updateAlertsDisplay();
                break;
        }
    }

    startPerformanceMonitoring() {
        this.monitoringActive = true;
        
        // 收集初始性能数据
        this.collectInitialMetrics();
        
        // 定期更新性能数据
        setInterval(() => {
            if (this.monitoringActive) {
                this.collectPerformanceData();
                this.checkPerformanceThresholds();
            }
        }, 10000); // 每10秒更新一次
    }

    collectInitialMetrics() {
        // 收集页面加载性能数据
        window.addEventListener('load', () => {
            setTimeout(() => {
                const navigation = performance.getEntriesByType('navigation')[0];
                if (navigation) {
                    this.processNavigationEntry(navigation);
                }
                
                // 收集Paint Timing
                const paintEntries = performance.getEntriesByType('paint');
                paintEntries.forEach(entry => {
                    this.processPaintEntry(entry);
                });
                
                this.updateOverviewMetrics();
            }, 1000);
        });
    }

    processNavigationEntry(entry) {
        this.metrics.navigation = {
            domContentLoaded: entry.domContentLoadedEventEnd - entry.domContentLoadedEventStart,
            loadComplete: entry.loadEventEnd - entry.loadEventStart,
            domInteractive: entry.domInteractive - entry.navigationStart,
            pageLoadTime: entry.loadEventEnd - entry.navigationStart
        };
    }

    processPaintEntry(entry) {
        if (entry.name === 'first-contentful-paint') {
            this.metrics.firstContentfulPaint = entry.startTime;
        } else if (entry.name === 'first-paint') {
            this.metrics.firstPaint = entry.startTime;
        }
    }

    processResourceEntry(entry) {
        if (!this.metrics.resources) {
            this.metrics.resources = [];
        }
        
        this.metrics.resources.push({
            name: entry.name,
            type: this.getResourceType(entry.name),
            duration: entry.duration,
            size: entry.transferSize || 0,
            startTime: entry.startTime
        });
    }

    processLongTaskEntry(entry) {
        if (!this.metrics.longTasks) {
            this.metrics.longTasks = [];
        }
        
        this.metrics.longTasks.push({
            duration: entry.duration,
            startTime: entry.startTime
        });
        
        // 检查是否超过阈值
        if (entry.duration > this.thresholds.longTaskDuration) {
            this.addAlert('warning', `检测到长任务: ${entry.duration.toFixed(2)}ms`);
        }
    }

    processLayoutShiftEntry(entry) {
        if (!this.metrics.layoutShifts) {
            this.metrics.layoutShifts = [];
        }
        
        this.metrics.layoutShifts.push({
            value: entry.value,
            startTime: entry.startTime
        });
        
        // 计算累积布局偏移
        const cls = this.metrics.layoutShifts.reduce((sum, shift) => sum + shift.value, 0);
        this.metrics.cumulativeLayoutShift = cls;
        
        if (cls > this.thresholds.cumulativeLayoutShift) {
            this.addAlert('warning', `布局偏移过大: ${cls.toFixed(3)}`);
        }
    }

    getResourceType(url) {
        if (url.includes('.js')) return 'script';
        if (url.includes('.css')) return 'stylesheet';
        if (url.match(/\.(jpg|jpeg|png|gif|webp|svg)$/)) return 'image';
        if (url.includes('.woff') || url.includes('.ttf')) return 'font';
        return 'other';
    }

    updateMemoryMetrics(memInfo) {
        this.metrics.memory = {
            used: memInfo.usedJSHeapSize,
            total: memInfo.totalJSHeapSize,
            limit: memInfo.jsHeapSizeLimit,
            usage: memInfo.usedJSHeapSize / memInfo.totalJSHeapSize
        };
        
        // 检查内存使用阈值
        if (this.metrics.memory.usage > this.thresholds.memoryUsage) {
            this.addAlert('error', `内存使用过高: ${(this.metrics.memory.usage * 100).toFixed(1)}%`);
        }
    }

    recordInteractionTime(time) {
        if (!this.metrics.interactions) {
            this.metrics.interactions = [];
        }
        
        this.metrics.interactions.push({
            time: time,
            timestamp: Date.now()
        });
        
        // 保留最近100个交互记录
        if (this.metrics.interactions.length > 100) {
            this.metrics.interactions = this.metrics.interactions.slice(-100);
        }
        
        // 计算平均交互时间
        const avgInteractionTime = this.metrics.interactions.reduce((sum, interaction) => 
            sum + interaction.time, 0) / this.metrics.interactions.length;
        
        this.metrics.averageInteractionTime = avgInteractionTime;
    }

    updateOverviewMetrics() {
        // 更新概览页面的指标显示
        if (this.metrics.navigation) {
            document.getElementById('pageLoadTime').textContent = 
                this.formatTime(this.metrics.navigation.pageLoadTime);
        }
        
        if (this.metrics.firstContentfulPaint) {
            document.getElementById('firstContentfulPaint').textContent = 
                this.formatTime(this.metrics.firstContentfulPaint);
        }
        
        if (this.metrics.largestContentfulPaint) {
            document.getElementById('largestContentfulPaint').textContent = 
                this.formatTime(this.metrics.largestContentfulPaint);
        }
        
        if (this.metrics.averageInteractionTime) {
            document.getElementById('firstInputDelay').textContent = 
                this.formatTime(this.metrics.averageInteractionTime);
        }
    }

    updateDetailedMetrics() {
        // 更新详细指标页面
        if (this.metrics.cumulativeLayoutShift !== undefined) {
            document.getElementById('clsScore').textContent = 
                this.metrics.cumulativeLayoutShift.toFixed(3);
        }
        
        if (this.metrics.averageInteractionTime) {
            document.getElementById('fidScore').textContent = 
                this.formatTime(this.metrics.averageInteractionTime);
        }
        
        // 更新JavaScript指标
        document.getElementById('domNodeCount').textContent = 
            document.querySelectorAll('*').length.toLocaleString();
        
        document.getElementById('eventListenerCount').textContent = 
            this.getEventListenerCount();
        
        document.getElementById('longTaskCount').textContent = 
            this.metrics.longTasks ? this.metrics.longTasks.length : 0;
        
        // 更新资源表格
        this.updateResourceTable();
    }

    updateMemoryAnalysis() {
        if (this.metrics.memory) {
            const memory = this.metrics.memory;
            
            document.getElementById('jsHeapUsed').textContent = 
                this.formatBytes(memory.used);
            document.getElementById('jsHeapTotal').textContent = 
                this.formatBytes(memory.total);
            
            const usagePercentage = (memory.usage * 100).toFixed(1);
            document.getElementById('jsHeapUsage').style.width = usagePercentage + '%';
        }
        
        document.getElementById('domNodes').textContent = 
            document.querySelectorAll('*').length.toLocaleString();
        
        document.getElementById('eventListeners').textContent = 
            this.getEventListenerCount();
    }

    updateNetworkAnalysis() {
        if (this.metrics.resources) {
            const resources = this.metrics.resources;
            
            document.getElementById('totalRequests').textContent = 
                resources.length.toLocaleString();
            
            const totalSize = resources.reduce((sum, resource) => sum + (resource.size || 0), 0);
            document.getElementById('transferSize').textContent = 
                this.formatBytes(totalSize);
            
            const avgResponseTime = resources.reduce((sum, resource) => 
                sum + resource.duration, 0) / resources.length;
            document.getElementById('avgResponseTime').textContent = 
                avgResponseTime.toFixed(1);
        }
    }

    updateResourceTable() {
        const resourceTable = document.getElementById('resourceTable');
        if (!resourceTable || !this.metrics.resources) return;
        
        const resources = this.metrics.resources.slice(0, 20); // 显示前20个资源
        
        resourceTable.innerHTML = `
            <table class="resource-performance-table">
                <thead>
                    <tr>
                        <th>资源</th>
                        <th>类型</th>
                        <th>大小</th>
                        <th>加载时间</th>
                        <th>状态</th>
                    </tr>
                </thead>
                <tbody>
                    ${resources.map(resource => `
                        <tr>
                            <td class="resource-name" title="${resource.name}">
                                ${this.truncateUrl(resource.name, 40)}
                            </td>
                            <td>
                                <span class="resource-type ${resource.type}">${resource.type}</span>
                            </td>
                            <td>${this.formatBytes(resource.size)}</td>
                            <td>${this.formatTime(resource.duration)}</td>
                            <td>
                                <span class="resource-status ${this.getResourceStatus(resource.duration)}">
                                    ${this.getResourceStatusText(resource.duration)}
                                </span>
                            </td>
                        </tr>
                    `).join('')}
                </tbody>
            </table>
        `;
    }

    getResourceStatus(duration) {
        if (duration < 100) return 'good';
        if (duration < 500) return 'warning';
        return 'error';
    }

    getResourceStatusText(duration) {
        if (duration < 100) return '快速';
        if (duration < 500) return '一般';
        return '缓慢';
    }

    getEventListenerCount() {
        // 简化的事件监听器计数
        return Math.floor(Math.random() * 100) + 50;
    }

    collectPerformanceData() {
        const data = {
            timestamp: Date.now(),
            metrics: { ...this.metrics },
            url: window.location.href,
            userAgent: navigator.userAgent
        };
        
        this.performanceData.push(data);
        
        // 保留最近100条记录
        if (this.performanceData.length > 100) {
            this.performanceData = this.performanceData.slice(-100);
        }
    }

    checkPerformanceThresholds() {
        // 检查各项指标是否超过阈值
        if (this.metrics.navigation && 
            this.metrics.navigation.pageLoadTime > this.thresholds.pageLoadTime) {
            this.addAlert('warning', 
                `页面加载时间过长: ${this.formatTime(this.metrics.navigation.pageLoadTime)}`);
        }
        
        if (this.metrics.firstContentfulPaint > this.thresholds.firstContentfulPaint) {
            this.addAlert('warning', 
                `首次内容绘制时间过长: ${this.formatTime(this.metrics.firstContentfulPaint)}`);
        }
        
        if (this.metrics.memory && this.metrics.memory.usage > this.thresholds.memoryUsage) {
            this.addAlert('error', 
                `内存使用率过高: ${(this.metrics.memory.usage * 100).toFixed(1)}%`);
        }
    }

    addAlert(level, message) {
        const alert = {
            id: Date.now(),
            level: level,
            message: message,
            timestamp: new Date(),
            resolved: false
        };
        
        this.alerts.unshift(alert);
        
        // 限制告警数量
        if (this.alerts.length > 50) {
            this.alerts = this.alerts.slice(0, 50);
        }
        
        // 显示通知
        if (window.showNotification) {
            showNotification(`性能告警: ${message}`, level);
        }
    }

    updateAlertsDisplay() {
        const alertsList = document.getElementById('alertsList');
        if (!alertsList) return;
        
        if (this.alerts.length === 0) {
            alertsList.innerHTML = `
                <div class="empty-state">
                    <i class="fas fa-check-circle"></i>
                    <p>暂无性能告警</p>
                </div>
            `;
            return;
        }
        
        alertsList.innerHTML = this.alerts.map(alert => `
            <div class="alert-item ${alert.level} ${alert.resolved ? 'resolved' : ''}">
                <div class="alert-icon">
                    <i class="fas fa-${this.getAlertIcon(alert.level)}"></i>
                </div>
                <div class="alert-content">
                    <div class="alert-message">${alert.message}</div>
                    <div class="alert-time">${alert.timestamp.toLocaleString()}</div>
                </div>
                <div class="alert-actions">
                    ${!alert.resolved ? `
                        <button class="btn-icon" onclick="performanceAnalyzer.resolveAlert(${alert.id})" title="标记为已解决">
                            <i class="fas fa-check"></i>
                        </button>
                    ` : ''}
                    <button class="btn-icon" onclick="performanceAnalyzer.deleteAlert(${alert.id})" title="删除">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </div>
        `).join('');
    }

    getAlertIcon(level) {
        const icons = {
            'info': 'info-circle',
            'warning': 'exclamation-triangle',
            'error': 'exclamation-circle'
        };
        return icons[level] || 'info-circle';
    }

    resolveAlert(alertId) {
        const alert = this.alerts.find(a => a.id === alertId);
        if (alert) {
            alert.resolved = true;
            this.updateAlertsDisplay();
        }
    }

    deleteAlert(alertId) {
        this.alerts = this.alerts.filter(a => a.id !== alertId);
        this.updateAlertsDisplay();
    }

    clearAlerts() {
        if (confirm('确定要清空所有告警吗？')) {
            this.alerts = [];
            this.updateAlertsDisplay();
        }
    }

    // 工具方法
    formatTime(milliseconds) {
        if (milliseconds < 1000) {
            return milliseconds.toFixed(0) + 'ms';
        } else {
            return (milliseconds / 1000).toFixed(2) + 's';
        }
    }

    formatBytes(bytes) {
        if (bytes === 0) return '0 B';
        const k = 1024;
        const sizes = ['B', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    truncateUrl(url, maxLength) {
        if (url.length <= maxLength) return url;
        const parts = url.split('/');
        const filename = parts[parts.length - 1];
        if (filename.length <= maxLength) {
            return '.../' + filename;
        }
        return url.substring(0, maxLength - 3) + '...';
    }

    // 面板控制方法
    showPerformancePanel() {
        const panel = document.getElementById('performancePanel');
        if (panel) {
            panel.classList.add('show');
            this.updateOverviewMetrics();
        }
    }

    closePerformancePanel() {
        const panel = document.getElementById('performancePanel');
        if (panel) {
            panel.classList.remove('show');
        }
    }

    startAnalysis() {
        this.monitoringActive = true;
        this.collectPerformanceData();
        
        if (window.showNotification) {
            showNotification('性能分析已开始', 'info');
        }
    }

    exportReport() {
        const report = {
            timestamp: new Date(),
            metrics: this.metrics,
            performanceData: this.performanceData,
            alerts: this.alerts,
            thresholds: this.thresholds
        };
        
        const blob = new Blob([JSON.stringify(report, null, 2)], { 
            type: 'application/json' 
        });
        const url = URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = `performance-report-${new Date().toISOString().split('T')[0]}.json`;
        link.click();
        URL.revokeObjectURL(url);
        
        if (window.showNotification) {
            showNotification('性能报告导出成功', 'success');
        }
    }

    configureAlerts() {
        // 显示告警配置对话框
        const modal = document.createElement('div');
        modal.className = 'modal alert-config-modal';
        modal.innerHTML = `
            <div class="modal-content">
                <div class="modal-header">
                    <h3>配置性能告警</h3>
                    <button class="modal-close" onclick="this.closest('.modal').remove()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="modal-body">
                    <div class="threshold-config">
                        <div class="config-item">
                            <label>页面加载时间阈值 (毫秒)</label>
                            <input type="number" value="${this.thresholds.pageLoadTime}" id="pageLoadThreshold">
                        </div>
                        <div class="config-item">
                            <label>内存使用率阈值 (%)</label>
                            <input type="number" value="${this.thresholds.memoryUsage * 100}" id="memoryThreshold">
                        </div>
                        <div class="config-item">
                            <label>长任务时间阈值 (毫秒)</label>
                            <input type="number" value="${this.thresholds.longTaskDuration}" id="longTaskThreshold">
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button class="btn-secondary" onclick="this.closest('.modal').remove()">取消</button>
                    <button class="btn-primary" onclick="performanceAnalyzer.saveAlertConfig(); this.closest('.modal').remove()">保存</button>
                </div>
            </div>
        `;
        
        document.body.appendChild(modal);
        modal.style.display = 'flex';
    }

    saveAlertConfig() {
        this.thresholds.pageLoadTime = parseInt(document.getElementById('pageLoadThreshold').value);
        this.thresholds.memoryUsage = parseInt(document.getElementById('memoryThreshold').value) / 100;
        this.thresholds.longTaskDuration = parseInt(document.getElementById('longTaskThreshold').value);
        
        // 保存到localStorage
        localStorage.setItem('performanceThresholds', JSON.stringify(this.thresholds));
        
        if (window.showNotification) {
            showNotification('告警配置已保存', 'success');
        }
    }
}

// 全局性能分析器实例
let performanceAnalyzer = null;

// 初始化性能分析器
function initializePerformanceAnalyzer() {
    performanceAnalyzer = new PerformanceAnalyzer();
    console.log('✅ 性能分析工具已初始化');
}

// 显示性能分析面板
function showPerformancePanel() {
    if (performanceAnalyzer) {
        performanceAnalyzer.showPerformancePanel();
    }
}

// 页面加载时初始化
document.addEventListener('DOMContentLoaded', function() {
    setTimeout(initializePerformanceAnalyzer, 1400);
});
