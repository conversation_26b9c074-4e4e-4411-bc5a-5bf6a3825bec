// 智能助手系统
class AIAssistant {
    constructor() {
        this.conversations = [];
        this.knowledgeBase = [];
        this.userPreferences = {};
        this.learningData = [];
        this.suggestions = [];
        this.currentConversation = null;
        this.isListening = false;
        this.isTyping = false;
        this.contextHistory = [];
        this.personalitySettings = {
            tone: 'friendly',
            verbosity: 'balanced',
            expertise: 'general'
        };
        
        this.initializeAssistant();
        this.setupNLP();
        this.loadKnowledgeBase();
        this.startContextAnalysis();
    }

    initializeAssistant() {
        this.createAssistantInterface();
        this.bindAssistantEvents();
        this.setupVoiceRecognition();
        this.loadUserPreferences();
    }

    createAssistantInterface() {
        const assistantPanel = document.createElement('div');
        assistantPanel.id = 'aiAssistantPanel';
        assistantPanel.className = 'ai-assistant-panel';
        assistantPanel.innerHTML = `
            <div class="assistant-header">
                <div class="assistant-avatar">
                    <div class="avatar-image">
                        <i class="fas fa-robot"></i>
                    </div>
                    <div class="status-indicator" id="assistantStatus">
                        <div class="status-dot online"></div>
                        <span>在线</span>
                    </div>
                </div>
                <div class="assistant-info">
                    <h3>AI助手</h3>
                    <p>我是您的智能助手，随时为您提供帮助</p>
                </div>
                <div class="assistant-controls">
                    <button class="btn-icon" onclick="aiAssistant.toggleSettings()" title="设置">
                        <i class="fas fa-cog"></i>
                    </button>
                    <button class="btn-icon" onclick="aiAssistant.clearConversation()" title="清空对话">
                        <i class="fas fa-trash"></i>
                    </button>
                    <button class="btn-icon" onclick="aiAssistant.minimizeAssistant()" title="最小化">
                        <i class="fas fa-minus"></i>
                    </button>
                    <button class="btn-icon" onclick="aiAssistant.closeAssistant()" title="关闭">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            </div>
            
            <div class="assistant-body">
                <div class="conversation-area" id="conversationArea">
                    <div class="welcome-message">
                        <div class="message-bubble assistant">
                            <div class="message-avatar">
                                <i class="fas fa-robot"></i>
                            </div>
                            <div class="message-content">
                                <div class="message-text">
                                    您好！我是您的AI助手。我可以帮助您：
                                    <ul>
                                        <li>📊 分析数据和生成报告</li>
                                        <li>🔍 查找信息和回答问题</li>
                                        <li>⚙️ 操作系统功能</li>
                                        <li>💡 提供智能建议</li>
                                        <li>📚 学习和记忆您的偏好</li>
                                    </ul>
                                    有什么我可以帮助您的吗？
                                </div>
                                <div class="message-time">${new Date().toLocaleTimeString()}</div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="conversation-messages" id="conversationMessages">
                        <!-- 对话消息将在这里显示 -->
                    </div>
                    
                    <div class="typing-indicator" id="typingIndicator" style="display: none;">
                        <div class="message-bubble assistant">
                            <div class="message-avatar">
                                <i class="fas fa-robot"></i>
                            </div>
                            <div class="message-content">
                                <div class="typing-animation">
                                    <span></span>
                                    <span></span>
                                    <span></span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="input-area">
                    <div class="quick-actions" id="quickActions">
                        <button class="quick-action-btn" onclick="aiAssistant.sendQuickMessage('帮我分析今天的数据')">
                            <i class="fas fa-chart-bar"></i>
                            <span>数据分析</span>
                        </button>
                        <button class="quick-action-btn" onclick="aiAssistant.sendQuickMessage('生成系统报告')">
                            <i class="fas fa-file-alt"></i>
                            <span>生成报告</span>
                        </button>
                        <button class="quick-action-btn" onclick="aiAssistant.sendQuickMessage('优化系统性能')">
                            <i class="fas fa-tachometer-alt"></i>
                            <span>性能优化</span>
                        </button>
                        <button class="quick-action-btn" onclick="aiAssistant.sendQuickMessage('安全检查')">
                            <i class="fas fa-shield-alt"></i>
                            <span>安全检查</span>
                        </button>
                    </div>
                    
                    <div class="message-input-container">
                        <div class="input-wrapper">
                            <textarea 
                                id="messageInput" 
                                placeholder="输入您的问题或需求..." 
                                rows="1"
                                onkeydown="aiAssistant.handleInputKeydown(event)"
                                oninput="aiAssistant.handleInputChange(event)"
                            ></textarea>
                            <div class="input-actions">
                                <button class="btn-icon voice-btn" onclick="aiAssistant.toggleVoiceInput()" title="语音输入">
                                    <i class="fas fa-microphone" id="voiceIcon"></i>
                                </button>
                                <button class="btn-icon attach-btn" onclick="aiAssistant.attachFile()" title="附加文件">
                                    <i class="fas fa-paperclip"></i>
                                </button>
                                <button class="btn-primary send-btn" onclick="aiAssistant.sendMessage()" title="发送">
                                    <i class="fas fa-paper-plane"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="assistant-suggestions" id="assistantSuggestions">
                <!-- 智能建议将在这里显示 -->
            </div>
        `;
        
        document.body.appendChild(assistantPanel);
        
        // 创建助手设置面板
        this.createSettingsPanel();
        
        // 创建浮动助手按钮
        this.createFloatingButton();
        
        // 创建知识库管理界面
        this.createKnowledgeBaseInterface();
    }

    createFloatingButton() {
        // 检查是否已经存在浮动按钮，避免重复创建
        const existingBtn = document.getElementById('aiAssistantFloatingBtn');
        if (existingBtn) {
            existingBtn.remove();
        }

        const floatingBtn = document.createElement('div');
        floatingBtn.id = 'aiAssistantFloatingBtn';
        floatingBtn.className = 'ai-assistant-floating-btn';
        floatingBtn.innerHTML = `
            <div class="floating-btn-content" onclick="aiAssistant.toggleAssistant()">
                <i class="fas fa-robot"></i>
                <div class="notification-badge" id="assistantNotificationBadge" style="display: none;">
                    <span id="notificationCount">0</span>
                </div>
            </div>
            <div class="floating-btn-tooltip">AI助手</div>
        `;

        document.body.appendChild(floatingBtn);
    }

    createSettingsPanel() {
        const settingsPanel = document.createElement('div');
        settingsPanel.id = 'assistantSettingsPanel';
        settingsPanel.className = 'assistant-settings-modal';
        settingsPanel.innerHTML = `
            <div class="modal-content">
                <div class="modal-header">
                    <h3>AI助手设置</h3>
                    <button class="modal-close" onclick="aiAssistant.closeSettings()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="modal-body">
                    <div class="settings-tabs">
                        <button class="settings-tab active" data-tab="personality">个性化</button>
                        <button class="settings-tab" data-tab="behavior">行为设置</button>
                        <button class="settings-tab" data-tab="knowledge">知识库</button>
                        <button class="settings-tab" data-tab="privacy">隐私设置</button>
                    </div>
                    
                    <div class="settings-content">
                        <!-- 个性化设置 -->
                        <div class="settings-tab-content active" id="personalityTab">
                            <h4>助手个性化</h4>
                            <div class="setting-group">
                                <label>语调风格</label>
                                <select id="toneSelect">
                                    <option value="friendly">友好</option>
                                    <option value="professional">专业</option>
                                    <option value="casual">随意</option>
                                    <option value="formal">正式</option>
                                </select>
                            </div>
                            <div class="setting-group">
                                <label>回答详细程度</label>
                                <select id="verbositySelect">
                                    <option value="brief">简洁</option>
                                    <option value="balanced">平衡</option>
                                    <option value="detailed">详细</option>
                                </select>
                            </div>
                            <div class="setting-group">
                                <label>专业领域</label>
                                <select id="expertiseSelect">
                                    <option value="general">通用</option>
                                    <option value="technical">技术</option>
                                    <option value="business">商务</option>
                                    <option value="creative">创意</option>
                                </select>
                            </div>
                            <div class="setting-group">
                                <label>助手名称</label>
                                <input type="text" id="assistantName" placeholder="给您的助手起个名字">
                            </div>
                        </div>
                        
                        <!-- 行为设置 -->
                        <div class="settings-tab-content" id="behaviorTab">
                            <h4>行为设置</h4>
                            <div class="setting-group">
                                <label class="checkbox-label">
                                    <input type="checkbox" id="autoSuggestions">
                                    <span class="checkmark"></span>
                                    自动提供建议
                                </label>
                            </div>
                            <div class="setting-group">
                                <label class="checkbox-label">
                                    <input type="checkbox" id="contextAwareness">
                                    <span class="checkmark"></span>
                                    上下文感知
                                </label>
                            </div>
                            <div class="setting-group">
                                <label class="checkbox-label">
                                    <input type="checkbox" id="proactiveHelp">
                                    <span class="checkmark"></span>
                                    主动帮助
                                </label>
                            </div>
                            <div class="setting-group">
                                <label class="checkbox-label">
                                    <input type="checkbox" id="learningMode">
                                    <span class="checkmark"></span>
                                    学习模式
                                </label>
                            </div>
                            <div class="setting-group">
                                <label>响应延迟（毫秒）</label>
                                <input type="range" id="responseDelay" min="500" max="3000" value="1000">
                                <span id="delayValue">1000ms</span>
                            </div>
                        </div>
                        
                        <!-- 知识库设置 -->
                        <div class="settings-tab-content" id="knowledgeTab">
                            <h4>知识库管理</h4>
                            <div class="knowledge-stats">
                                <div class="stat-item">
                                    <div class="stat-value" id="knowledgeCount">0</div>
                                    <div class="stat-label">知识条目</div>
                                </div>
                                <div class="stat-item">
                                    <div class="stat-value" id="conversationCount">0</div>
                                    <div class="stat-label">对话记录</div>
                                </div>
                                <div class="stat-item">
                                    <div class="stat-value" id="learningCount">0</div>
                                    <div class="stat-label">学习数据</div>
                                </div>
                            </div>
                            <div class="knowledge-actions">
                                <button class="btn-secondary" onclick="aiAssistant.importKnowledge()">
                                    <i class="fas fa-upload"></i>
                                    导入知识
                                </button>
                                <button class="btn-secondary" onclick="aiAssistant.exportKnowledge()">
                                    <i class="fas fa-download"></i>
                                    导出知识
                                </button>
                                <button class="btn-secondary" onclick="aiAssistant.clearKnowledge()">
                                    <i class="fas fa-trash"></i>
                                    清空知识库
                                </button>
                            </div>
                        </div>
                        
                        <!-- 隐私设置 -->
                        <div class="settings-tab-content" id="privacyTab">
                            <h4>隐私设置</h4>
                            <div class="setting-group">
                                <label class="checkbox-label">
                                    <input type="checkbox" id="saveConversations">
                                    <span class="checkmark"></span>
                                    保存对话记录
                                </label>
                            </div>
                            <div class="setting-group">
                                <label class="checkbox-label">
                                    <input type="checkbox" id="shareAnalytics">
                                    <span class="checkmark"></span>
                                    共享使用分析
                                </label>
                            </div>
                            <div class="setting-group">
                                <label class="checkbox-label">
                                    <input type="checkbox" id="personalizedAds">
                                    <span class="checkmark"></span>
                                    个性化推荐
                                </label>
                            </div>
                            <div class="setting-group">
                                <label>数据保留期限</label>
                                <select id="dataRetention">
                                    <option value="7">7天</option>
                                    <option value="30">30天</option>
                                    <option value="90">90天</option>
                                    <option value="365">1年</option>
                                    <option value="forever">永久</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button class="btn-secondary" onclick="aiAssistant.resetSettings()">重置</button>
                    <button class="btn-primary" onclick="aiAssistant.saveSettings()">保存设置</button>
                </div>
            </div>
        `;
        
        document.body.appendChild(settingsPanel);
    }

    createKnowledgeBaseInterface() {
        const knowledgeInterface = document.createElement('div');
        knowledgeInterface.id = 'knowledgeBaseInterface';
        knowledgeInterface.className = 'knowledge-base-modal';
        knowledgeInterface.innerHTML = `
            <div class="modal-content large">
                <div class="modal-header">
                    <h3>知识库管理</h3>
                    <button class="modal-close" onclick="aiAssistant.closeKnowledgeBase()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="modal-body">
                    <div class="knowledge-toolbar">
                        <div class="search-box">
                            <input type="text" id="knowledgeSearch" placeholder="搜索知识库...">
                            <button class="search-btn">
                                <i class="fas fa-search"></i>
                            </button>
                        </div>
                        <div class="knowledge-actions">
                            <button class="btn-secondary" onclick="aiAssistant.addKnowledge()">
                                <i class="fas fa-plus"></i>
                                添加知识
                            </button>
                            <button class="btn-secondary" onclick="aiAssistant.trainAssistant()">
                                <i class="fas fa-brain"></i>
                                训练助手
                            </button>
                        </div>
                    </div>
                    
                    <div class="knowledge-content">
                        <div class="knowledge-categories">
                            <h4>知识分类</h4>
                            <div class="category-list" id="knowledgeCategories">
                                <div class="category-item active" data-category="all">
                                    <i class="fas fa-globe"></i>
                                    <span>全部</span>
                                    <span class="count">0</span>
                                </div>
                                <div class="category-item" data-category="system">
                                    <i class="fas fa-cog"></i>
                                    <span>系统</span>
                                    <span class="count">0</span>
                                </div>
                                <div class="category-item" data-category="business">
                                    <i class="fas fa-briefcase"></i>
                                    <span>业务</span>
                                    <span class="count">0</span>
                                </div>
                                <div class="category-item" data-category="technical">
                                    <i class="fas fa-code"></i>
                                    <span>技术</span>
                                    <span class="count">0</span>
                                </div>
                                <div class="category-item" data-category="faq">
                                    <i class="fas fa-question-circle"></i>
                                    <span>常见问题</span>
                                    <span class="count">0</span>
                                </div>
                            </div>
                        </div>
                        
                        <div class="knowledge-list">
                            <div class="knowledge-items" id="knowledgeItems">
                                <!-- 知识条目将在这里显示 -->
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
        
        document.body.appendChild(knowledgeInterface);
    }

    setupNLP() {
        // 设置自然语言处理
        this.nlpPatterns = {
            greeting: /^(你好|hi|hello|嗨|您好)/i,
            question: /^(什么|怎么|如何|为什么|哪里|谁|when|what|how|why|where|who)/i,
            request: /^(请|帮我|帮助|can you|could you|please)/i,
            data: /(数据|报告|统计|分析|图表|data|report|chart)/i,
            system: /(系统|功能|设置|配置|system|function|setting)/i,
            security: /(安全|权限|密码|加密|security|permission|password)/i
        };
        
        this.responseTemplates = {
            greeting: [
                "您好！很高兴为您服务。",
                "嗨！有什么我可以帮助您的吗？",
                "您好！我是您的AI助手，随时为您提供帮助。"
            ],
            unknown: [
                "抱歉，我不太理解您的问题。能否换个方式表达？",
                "我需要更多信息来帮助您。能详细说明一下吗？",
                "这个问题有点复杂，让我学习一下再回答您。"
            ],
            thinking: [
                "让我想想...",
                "正在分析您的问题...",
                "稍等，我正在处理..."
            ]
        };
    }

    loadKnowledgeBase() {
        // 加载知识库
        this.knowledgeBase = [
            {
                id: 'kb_001',
                category: 'system',
                question: '如何创建新用户？',
                answer: '您可以通过用户管理页面点击"添加用户"按钮来创建新用户。需要填写用户名、邮箱和角色信息。',
                keywords: ['用户', '创建', '添加', '注册'],
                confidence: 0.9
            },
            {
                id: 'kb_002',
                category: 'business',
                question: '如何生成销售报告？',
                answer: '在报告中心选择"销售报告"，设置时间范围和筛选条件，然后点击"生成报告"即可。',
                keywords: ['销售', '报告', '生成', '统计'],
                confidence: 0.85
            },
            {
                id: 'kb_003',
                category: 'technical',
                question: '系统性能如何优化？',
                answer: '可以通过清理缓存、优化数据库查询、升级硬件配置等方式来提升系统性能。',
                keywords: ['性能', '优化', '缓存', '数据库'],
                confidence: 0.8
            }
        ];
        
        this.updateKnowledgeStats();
    }

    bindAssistantEvents() {
        // 绑定事件
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('settings-tab')) {
                this.switchSettingsTab(e.target);
            }
            
            if (e.target.classList.contains('category-item')) {
                this.switchKnowledgeCategory(e.target);
            }
        });
        
        // 输入框自动调整高度
        const messageInput = document.getElementById('messageInput');
        if (messageInput) {
            messageInput.addEventListener('input', () => {
                this.adjustInputHeight();
            });
        }
        
        // 设置面板事件
        document.getElementById('responseDelay')?.addEventListener('input', (e) => {
            document.getElementById('delayValue').textContent = e.target.value + 'ms';
        });
    }

    setupVoiceRecognition() {
        // 设置语音识别
        if ('webkitSpeechRecognition' in window || 'SpeechRecognition' in window) {
            const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
            this.recognition = new SpeechRecognition();
            this.recognition.continuous = false;
            this.recognition.interimResults = false;
            this.recognition.lang = 'zh-CN';
            
            this.recognition.onresult = (event) => {
                const transcript = event.results[0][0].transcript;
                document.getElementById('messageInput').value = transcript;
                this.sendMessage();
            };
            
            this.recognition.onerror = (event) => {
                console.error('语音识别错误:', event.error);
                this.isListening = false;
                this.updateVoiceButton();
            };
            
            this.recognition.onend = () => {
                this.isListening = false;
                this.updateVoiceButton();
            };
        }
    }

    startContextAnalysis() {
        // 启动上下文分析
        setInterval(() => {
            this.analyzeContext();
            this.generateSuggestions();
        }, 30000); // 每30秒分析一次
    }

    loadUserPreferences() {
        // 加载用户偏好
        const saved = localStorage.getItem('aiAssistantPreferences');
        if (saved) {
            this.userPreferences = JSON.parse(saved);
            this.applyPreferences();
        }
    }

    // 消息处理方法
    sendMessage() {
        const input = document.getElementById('messageInput');
        const message = input.value.trim();
        
        if (!message) return;
        
        // 添加用户消息
        this.addMessage(message, 'user');
        
        // 清空输入框
        input.value = '';
        this.adjustInputHeight();
        
        // 显示正在输入指示器
        this.showTypingIndicator();
        
        // 处理消息
        setTimeout(() => {
            this.processMessage(message);
        }, this.personalitySettings.responseDelay || 1000);
    }

    sendQuickMessage(message) {
        document.getElementById('messageInput').value = message;
        this.sendMessage();
    }

    addMessage(text, sender, type = 'text') {
        const messagesContainer = document.getElementById('conversationMessages');
        const messageElement = document.createElement('div');
        messageElement.className = `message-bubble ${sender}`;
        
        const time = new Date().toLocaleTimeString();
        
        messageElement.innerHTML = `
            <div class="message-avatar">
                <i class="fas fa-${sender === 'user' ? 'user' : 'robot'}"></i>
            </div>
            <div class="message-content">
                <div class="message-text">${this.formatMessage(text, type)}</div>
                <div class="message-time">${time}</div>
            </div>
        `;
        
        messagesContainer.appendChild(messageElement);
        messagesContainer.scrollTop = messagesContainer.scrollHeight;
        
        // 保存到对话历史
        if (!this.currentConversation) {
            this.currentConversation = {
                id: 'conv_' + Date.now(),
                messages: [],
                startTime: new Date()
            };
        }
        
        this.currentConversation.messages.push({
            text: text,
            sender: sender,
            type: type,
            timestamp: new Date()
        });
    }

    formatMessage(text, type) {
        if (type === 'html') {
            return text;
        }
        
        // 处理Markdown格式
        return text
            .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
            .replace(/\*(.*?)\*/g, '<em>$1</em>')
            .replace(/`(.*?)`/g, '<code>$1</code>')
            .replace(/\n/g, '<br>');
    }

    processMessage(message) {
        this.hideTypingIndicator();
        
        // 分析消息意图
        const intent = this.analyzeIntent(message);
        
        // 生成回复
        const response = this.generateResponse(message, intent);
        
        // 添加助手回复
        this.addMessage(response.text, 'assistant', response.type);
        
        // 执行相关操作
        if (response.action) {
            this.executeAction(response.action);
        }
        
        // 学习和优化
        this.learnFromInteraction(message, response);
    }

    analyzeIntent(message) {
        const intent = {
            type: 'unknown',
            confidence: 0,
            entities: []
        };
        
        // 简单的意图识别
        for (const [type, pattern] of Object.entries(this.nlpPatterns)) {
            if (pattern.test(message)) {
                intent.type = type;
                intent.confidence = 0.8;
                break;
            }
        }
        
        return intent;
    }

    generateResponse(message, intent) {
        // 搜索知识库
        const knowledgeMatch = this.searchKnowledgeBase(message);
        
        if (knowledgeMatch && knowledgeMatch.confidence > 0.7) {
            return {
                text: knowledgeMatch.answer,
                type: 'text',
                action: null
            };
        }
        
        // 根据意图生成回复
        switch (intent.type) {
            case 'greeting':
                return {
                    text: this.getRandomTemplate('greeting'),
                    type: 'text',
                    action: null
                };
                
            case 'data':
                return {
                    text: '我可以帮您分析数据。请告诉我您需要分析哪些数据？',
                    type: 'text',
                    action: 'show_data_options'
                };
                
            case 'system':
                return {
                    text: '关于系统功能，我可以帮您：\n• 查看系统状态\n• 配置系统设置\n• 管理用户权限\n• 监控系统性能',
                    type: 'text',
                    action: 'show_system_options'
                };
                
            default:
                return {
                    text: this.getRandomTemplate('unknown'),
                    type: 'text',
                    action: 'learn_from_unknown'
                };
        }
    }

    searchKnowledgeBase(query) {
        let bestMatch = null;
        let highestScore = 0;
        
        for (const item of this.knowledgeBase) {
            const score = this.calculateSimilarity(query, item);
            if (score > highestScore) {
                highestScore = score;
                bestMatch = { ...item, confidence: score };
            }
        }
        
        return bestMatch;
    }

    calculateSimilarity(query, knowledgeItem) {
        const queryWords = query.toLowerCase().split(/\s+/);
        const keywords = knowledgeItem.keywords.map(k => k.toLowerCase());
        
        let matches = 0;
        for (const word of queryWords) {
            if (keywords.some(keyword => keyword.includes(word) || word.includes(keyword))) {
                matches++;
            }
        }
        
        return matches / queryWords.length;
    }

    getRandomTemplate(type) {
        const templates = this.responseTemplates[type] || this.responseTemplates.unknown;
        return templates[Math.floor(Math.random() * templates.length)];
    }

    showTypingIndicator() {
        document.getElementById('typingIndicator').style.display = 'block';
        const conversationArea = document.getElementById('conversationArea');
        conversationArea.scrollTop = conversationArea.scrollHeight;
    }

    hideTypingIndicator() {
        document.getElementById('typingIndicator').style.display = 'none';
    }

    // 界面控制方法
    toggleAssistant() {
        const panel = document.getElementById('aiAssistantPanel');
        const floatingBtn = document.getElementById('aiAssistantFloatingBtn');
        
        if (panel.classList.contains('show')) {
            panel.classList.remove('show');
            floatingBtn.classList.remove('hidden');
        } else {
            panel.classList.add('show');
            floatingBtn.classList.add('hidden');
        }
    }

    minimizeAssistant() {
        const panel = document.getElementById('aiAssistantPanel');
        const floatingBtn = document.getElementById('aiAssistantFloatingBtn');
        
        panel.classList.remove('show');
        floatingBtn.classList.remove('hidden');
    }

    closeAssistant() {
        const panel = document.getElementById('aiAssistantPanel');
        panel.classList.remove('show');
    }

    // 其他方法的占位符
    handleInputKeydown(event) {
        if (event.key === 'Enter' && !event.shiftKey) {
            event.preventDefault();
            this.sendMessage();
        }
    }

    handleInputChange(event) {
        this.adjustInputHeight();
    }

    adjustInputHeight() {
        const input = document.getElementById('messageInput');
        input.style.height = 'auto';
        input.style.height = Math.min(input.scrollHeight, 120) + 'px';
    }

    toggleVoiceInput() {
        if (!this.recognition) {
            alert('您的浏览器不支持语音识别功能');
            return;
        }
        
        if (this.isListening) {
            this.recognition.stop();
        } else {
            this.recognition.start();
            this.isListening = true;
            this.updateVoiceButton();
        }
    }

    updateVoiceButton() {
        const voiceIcon = document.getElementById('voiceIcon');
        if (this.isListening) {
            voiceIcon.className = 'fas fa-stop';
            voiceIcon.style.color = '#ef4444';
        } else {
            voiceIcon.className = 'fas fa-microphone';
            voiceIcon.style.color = '';
        }
    }

    attachFile() {
        console.log('附加文件功能');
    }

    clearConversation() {
        document.getElementById('conversationMessages').innerHTML = '';
        this.currentConversation = null;
    }

    toggleSettings() {
        const settingsPanel = document.getElementById('assistantSettingsPanel');
        settingsPanel.style.display = settingsPanel.style.display === 'flex' ? 'none' : 'flex';
    }

    closeSettings() {
        document.getElementById('assistantSettingsPanel').style.display = 'none';
    }

    switchSettingsTab(tabBtn) {
        const tabName = tabBtn.dataset.tab;
        
        // 更新标签状态
        document.querySelectorAll('.settings-tab').forEach(tab => tab.classList.remove('active'));
        document.querySelectorAll('.settings-tab-content').forEach(content => content.classList.remove('active'));
        
        tabBtn.classList.add('active');
        document.getElementById(tabName + 'Tab').classList.add('active');
    }

    analyzeContext() {
        // 分析当前页面上下文
        const currentPage = window.location.pathname;
        const activeElements = document.querySelectorAll('.active, .selected, .current');
        const recentActions = this.getRecentUserActions();

        this.contextHistory.push({
            timestamp: new Date(),
            page: currentPage,
            activeElements: Array.from(activeElements).map(el => ({
                tag: el.tagName,
                class: el.className,
                id: el.id
            })),
            actions: recentActions
        });

        // 保留最近50个上下文记录
        if (this.contextHistory.length > 50) {
            this.contextHistory = this.contextHistory.slice(-50);
        }
    }

    getRecentUserActions() {
        // 模拟获取最近的用户操作
        return [
            { action: 'click', element: 'button', timestamp: Date.now() - 30000 },
            { action: 'scroll', element: 'page', timestamp: Date.now() - 60000 }
        ];
    }

    generateSuggestions() {
        const suggestions = [];
        const currentContext = this.getCurrentContext();

        // 基于当前页面生成建议
        if (currentContext.page.includes('dashboard')) {
            suggestions.push({
                id: 'suggest_1',
                type: 'action',
                title: '查看今日数据摘要',
                description: '我可以为您生成今天的关键数据摘要',
                action: 'generate_daily_summary',
                priority: 'high'
            });
        }

        if (currentContext.page.includes('users')) {
            suggestions.push({
                id: 'suggest_2',
                type: 'tip',
                title: '用户管理技巧',
                description: '您知道可以批量导入用户吗？',
                action: 'show_bulk_import_guide',
                priority: 'medium'
            });
        }

        // 基于时间生成建议
        const hour = new Date().getHours();
        if (hour >= 9 && hour <= 17) {
            suggestions.push({
                id: 'suggest_3',
                type: 'reminder',
                title: '工作时间提醒',
                description: '记得定期保存您的工作进度',
                action: 'show_save_reminder',
                priority: 'low'
            });
        }

        this.suggestions = suggestions;
        this.updateSuggestionsDisplay();
    }

    getCurrentContext() {
        return this.contextHistory[this.contextHistory.length - 1] || {
            page: window.location.pathname,
            activeElements: [],
            actions: []
        };
    }

    updateSuggestionsDisplay() {
        const suggestionsContainer = document.getElementById('assistantSuggestions');
        if (!suggestionsContainer || this.suggestions.length === 0) return;

        suggestionsContainer.innerHTML = `
            <div class="suggestions-header">
                <h4>智能建议</h4>
                <button class="btn-icon" onclick="aiAssistant.dismissSuggestions()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="suggestions-list">
                ${this.suggestions.map(suggestion => `
                    <div class="suggestion-item ${suggestion.priority}" data-suggestion-id="${suggestion.id}">
                        <div class="suggestion-icon">
                            <i class="fas fa-${this.getSuggestionIcon(suggestion.type)}"></i>
                        </div>
                        <div class="suggestion-content">
                            <h5>${suggestion.title}</h5>
                            <p>${suggestion.description}</p>
                        </div>
                        <div class="suggestion-actions">
                            <button class="btn-sm btn-primary" onclick="aiAssistant.applySuggestion('${suggestion.id}')">
                                应用
                            </button>
                            <button class="btn-sm btn-secondary" onclick="aiAssistant.dismissSuggestion('${suggestion.id}')">
                                忽略
                            </button>
                        </div>
                    </div>
                `).join('')}
            </div>
        `;

        suggestionsContainer.style.display = 'block';
    }

    getSuggestionIcon(type) {
        const icons = {
            'action': 'play',
            'tip': 'lightbulb',
            'reminder': 'bell',
            'warning': 'exclamation-triangle',
            'info': 'info-circle'
        };
        return icons[type] || 'star';
    }

    applySuggestion(suggestionId) {
        const suggestion = this.suggestions.find(s => s.id === suggestionId);
        if (suggestion) {
            this.executeAction(suggestion.action);
            this.dismissSuggestion(suggestionId);
        }
    }

    dismissSuggestion(suggestionId) {
        this.suggestions = this.suggestions.filter(s => s.id !== suggestionId);
        this.updateSuggestionsDisplay();
    }

    dismissSuggestions() {
        document.getElementById('assistantSuggestions').style.display = 'none';
    }

    executeAction(action) {
        switch (action) {
            case 'show_data_options':
                this.showDataAnalysisOptions();
                break;
            case 'show_system_options':
                this.showSystemOptions();
                break;
            case 'generate_daily_summary':
                this.generateDailySummary();
                break;
            case 'show_bulk_import_guide':
                this.showBulkImportGuide();
                break;
            case 'show_save_reminder':
                this.showSaveReminder();
                break;
            default:
                console.log('执行操作:', action);
        }
    }

    showDataAnalysisOptions() {
        const options = `
            <div class="action-options">
                <h4>数据分析选项</h4>
                <div class="option-buttons">
                    <button class="option-btn" onclick="aiAssistant.analyzeUserData()">
                        <i class="fas fa-users"></i>
                        <span>用户数据分析</span>
                    </button>
                    <button class="option-btn" onclick="aiAssistant.analyzeSalesData()">
                        <i class="fas fa-chart-line"></i>
                        <span>销售数据分析</span>
                    </button>
                    <button class="option-btn" onclick="aiAssistant.analyzeSystemPerformance()">
                        <i class="fas fa-tachometer-alt"></i>
                        <span>系统性能分析</span>
                    </button>
                    <button class="option-btn" onclick="aiAssistant.generateCustomReport()">
                        <i class="fas fa-file-alt"></i>
                        <span>自定义报告</span>
                    </button>
                </div>
            </div>
        `;

        this.addMessage(options, 'assistant', 'html');
    }

    showSystemOptions() {
        const options = `
            <div class="action-options">
                <h4>系统功能</h4>
                <div class="option-buttons">
                    <button class="option-btn" onclick="aiAssistant.checkSystemStatus()">
                        <i class="fas fa-heartbeat"></i>
                        <span>系统状态检查</span>
                    </button>
                    <button class="option-btn" onclick="aiAssistant.optimizePerformance()">
                        <i class="fas fa-rocket"></i>
                        <span>性能优化</span>
                    </button>
                    <button class="option-btn" onclick="aiAssistant.manageUsers()">
                        <i class="fas fa-users-cog"></i>
                        <span>用户管理</span>
                    </button>
                    <button class="option-btn" onclick="aiAssistant.configureSettings()">
                        <i class="fas fa-cog"></i>
                        <span>系统配置</span>
                    </button>
                </div>
            </div>
        `;

        this.addMessage(options, 'assistant', 'html');
    }

    generateDailySummary() {
        const summary = `
            <div class="daily-summary">
                <h4>📊 今日数据摘要</h4>
                <div class="summary-stats">
                    <div class="stat-item">
                        <div class="stat-value">1,247</div>
                        <div class="stat-label">活跃用户</div>
                        <div class="stat-change positive">+12%</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value">89</div>
                        <div class="stat-label">新注册</div>
                        <div class="stat-change positive">+5%</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value">156</div>
                        <div class="stat-label">订单数</div>
                        <div class="stat-change negative">-3%</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value">98.5%</div>
                        <div class="stat-label">系统可用性</div>
                        <div class="stat-change stable">稳定</div>
                    </div>
                </div>
                <div class="summary-insights">
                    <h5>💡 关键洞察</h5>
                    <ul>
                        <li>用户活跃度较昨日提升12%，主要来自移动端访问</li>
                        <li>新用户注册稳步增长，转化率保持在较高水平</li>
                        <li>订单数略有下降，建议关注用户购买行为变化</li>
                        <li>系统运行稳定，无重大异常事件</li>
                    </ul>
                </div>
            </div>
        `;

        this.addMessage(summary, 'assistant', 'html');
    }

    learnFromInteraction(message, response) {
        // 记录学习数据
        const learningEntry = {
            id: 'learn_' + Date.now(),
            userMessage: message,
            assistantResponse: response.text,
            intent: this.analyzeIntent(message),
            context: this.getCurrentContext(),
            timestamp: new Date(),
            feedback: null // 用户反馈
        };

        this.learningData.push(learningEntry);

        // 保留最近1000条学习数据
        if (this.learningData.length > 1000) {
            this.learningData = this.learningData.slice(-1000);
        }

        // 更新知识库
        this.updateKnowledgeFromLearning();
    }

    updateKnowledgeFromLearning() {
        // 分析学习数据，提取新的知识点
        const recentLearning = this.learningData.slice(-10);
        const commonPatterns = this.findCommonPatterns(recentLearning);

        for (const pattern of commonPatterns) {
            if (pattern.frequency > 3 && !this.knowledgeExists(pattern.question)) {
                this.knowledgeBase.push({
                    id: 'kb_learned_' + Date.now(),
                    category: 'learned',
                    question: pattern.question,
                    answer: pattern.answer,
                    keywords: pattern.keywords,
                    confidence: Math.min(pattern.frequency / 10, 0.9),
                    source: 'learning'
                });
            }
        }

        this.updateKnowledgeStats();
    }

    findCommonPatterns(learningData) {
        // 简化的模式识别
        const patterns = [];
        const questionMap = new Map();

        for (const entry of learningData) {
            const normalizedQuestion = entry.userMessage.toLowerCase().trim();
            if (questionMap.has(normalizedQuestion)) {
                questionMap.get(normalizedQuestion).frequency++;
            } else {
                questionMap.set(normalizedQuestion, {
                    question: entry.userMessage,
                    answer: entry.assistantResponse,
                    keywords: this.extractKeywords(entry.userMessage),
                    frequency: 1
                });
            }
        }

        return Array.from(questionMap.values());
    }

    extractKeywords(text) {
        // 简单的关键词提取
        const stopWords = ['的', '是', '在', '有', '和', '了', '我', '你', '他', '她', '它'];
        const words = text.toLowerCase().split(/\s+/);
        return words.filter(word => word.length > 1 && !stopWords.includes(word));
    }

    knowledgeExists(question) {
        return this.knowledgeBase.some(item =>
            item.question.toLowerCase() === question.toLowerCase()
        );
    }

    updateKnowledgeStats() {
        const knowledgeCount = document.getElementById('knowledgeCount');
        const conversationCount = document.getElementById('conversationCount');
        const learningCount = document.getElementById('learningCount');

        if (knowledgeCount) knowledgeCount.textContent = this.knowledgeBase.length;
        if (conversationCount) conversationCount.textContent = this.conversations.length;
        if (learningCount) learningCount.textContent = this.learningData.length;
    }

    applyPreferences() {
        // 应用用户偏好设置
        if (this.userPreferences.tone) {
            this.personalitySettings.tone = this.userPreferences.tone;
        }
        if (this.userPreferences.verbosity) {
            this.personalitySettings.verbosity = this.userPreferences.verbosity;
        }
        if (this.userPreferences.expertise) {
            this.personalitySettings.expertise = this.userPreferences.expertise;
        }

        // 更新界面元素
        const toneSelect = document.getElementById('toneSelect');
        const verbositySelect = document.getElementById('verbositySelect');
        const expertiseSelect = document.getElementById('expertiseSelect');

        if (toneSelect) toneSelect.value = this.personalitySettings.tone;
        if (verbositySelect) verbositySelect.value = this.personalitySettings.verbosity;
        if (expertiseSelect) expertiseSelect.value = this.personalitySettings.expertise;
    }

    saveSettings() {
        // 保存设置
        const settings = {
            tone: document.getElementById('toneSelect')?.value,
            verbosity: document.getElementById('verbositySelect')?.value,
            expertise: document.getElementById('expertiseSelect')?.value,
            assistantName: document.getElementById('assistantName')?.value,
            autoSuggestions: document.getElementById('autoSuggestions')?.checked,
            contextAwareness: document.getElementById('contextAwareness')?.checked,
            proactiveHelp: document.getElementById('proactiveHelp')?.checked,
            learningMode: document.getElementById('learningMode')?.checked,
            responseDelay: document.getElementById('responseDelay')?.value,
            saveConversations: document.getElementById('saveConversations')?.checked,
            shareAnalytics: document.getElementById('shareAnalytics')?.checked,
            personalizedAds: document.getElementById('personalizedAds')?.checked,
            dataRetention: document.getElementById('dataRetention')?.value
        };

        this.userPreferences = settings;
        this.personalitySettings = {
            tone: settings.tone,
            verbosity: settings.verbosity,
            expertise: settings.expertise
        };

        localStorage.setItem('aiAssistantPreferences', JSON.stringify(settings));

        this.addMessage('设置已保存！我会根据您的偏好来调整我的行为。', 'assistant');
        this.closeSettings();
    }

    resetSettings() {
        // 重置设置
        this.userPreferences = {};
        this.personalitySettings = {
            tone: 'friendly',
            verbosity: 'balanced',
            expertise: 'general'
        };

        localStorage.removeItem('aiAssistantPreferences');
        this.applyPreferences();

        this.addMessage('设置已重置为默认值。', 'assistant');
    }

    // 数据分析方法
    analyzeUserData() {
        this.addMessage('正在分析用户数据...', 'assistant');

        setTimeout(() => {
            const analysis = `
                <div class="data-analysis">
                    <h4>👥 用户数据分析报告</h4>
                    <div class="analysis-charts">
                        <div class="chart-placeholder">
                            <i class="fas fa-chart-bar"></i>
                            <p>用户增长趋势图</p>
                        </div>
                    </div>
                    <div class="analysis-insights">
                        <h5>📈 关键发现</h5>
                        <ul>
                            <li>本月新用户注册量增长25%</li>
                            <li>用户活跃度保持稳定，日活跃率78%</li>
                            <li>移动端用户占比达到65%</li>
                            <li>用户留存率较上月提升8%</li>
                        </ul>
                    </div>
                </div>
            `;
            this.addMessage(analysis, 'assistant', 'html');
        }, 2000);
    }

    analyzeSalesData() {
        this.addMessage('正在分析销售数据...', 'assistant');

        setTimeout(() => {
            const analysis = `
                <div class="data-analysis">
                    <h4>💰 销售数据分析报告</h4>
                    <div class="analysis-metrics">
                        <div class="metric">
                            <span class="metric-value">¥125,680</span>
                            <span class="metric-label">本月销售额</span>
                        </div>
                        <div class="metric">
                            <span class="metric-value">342</span>
                            <span class="metric-label">订单数量</span>
                        </div>
                        <div class="metric">
                            <span class="metric-value">¥367</span>
                            <span class="metric-label">平均订单价值</span>
                        </div>
                    </div>
                    <div class="analysis-insights">
                        <h5>💡 销售洞察</h5>
                        <ul>
                            <li>销售额较上月增长15%，主要来自新产品线</li>
                            <li>客单价稳步提升，用户购买力增强</li>
                            <li>周末销售表现突出，占总销售额的35%</li>
                            <li>建议加强移动端购买体验优化</li>
                        </ul>
                    </div>
                </div>
            `;
            this.addMessage(analysis, 'assistant', 'html');
        }, 2000);
    }

    checkSystemStatus() {
        this.addMessage('正在检查系统状态...', 'assistant');

        setTimeout(() => {
            const status = `
                <div class="system-status">
                    <h4>🖥️ 系统状态检查</h4>
                    <div class="status-items">
                        <div class="status-item healthy">
                            <i class="fas fa-server"></i>
                            <span class="status-label">服务器状态</span>
                            <span class="status-value">正常</span>
                        </div>
                        <div class="status-item healthy">
                            <i class="fas fa-database"></i>
                            <span class="status-label">数据库连接</span>
                            <span class="status-value">正常</span>
                        </div>
                        <div class="status-item warning">
                            <i class="fas fa-memory"></i>
                            <span class="status-label">内存使用率</span>
                            <span class="status-value">78%</span>
                        </div>
                        <div class="status-item healthy">
                            <i class="fas fa-network-wired"></i>
                            <span class="status-label">网络连接</span>
                            <span class="status-value">正常</span>
                        </div>
                    </div>
                    <div class="status-summary">
                        <p>✅ 系统整体运行正常，建议关注内存使用情况</p>
                    </div>
                </div>
            `;
            this.addMessage(status, 'assistant', 'html');
        }, 1500);
    }

    // 知识库管理方法
    switchKnowledgeCategory(categoryItem) {
        const category = categoryItem.dataset.category;

        // 更新分类状态
        document.querySelectorAll('.category-item').forEach(item => item.classList.remove('active'));
        categoryItem.classList.add('active');

        // 筛选知识条目
        this.renderKnowledgeItems(category);
    }

    renderKnowledgeItems(category = 'all') {
        const itemsContainer = document.getElementById('knowledgeItems');
        if (!itemsContainer) return;

        const filteredItems = category === 'all'
            ? this.knowledgeBase
            : this.knowledgeBase.filter(item => item.category === category);

        if (filteredItems.length === 0) {
            itemsContainer.innerHTML = `
                <div class="empty-knowledge">
                    <i class="fas fa-book-open"></i>
                    <p>该分类下暂无知识条目</p>
                </div>
            `;
            return;
        }

        itemsContainer.innerHTML = filteredItems.map(item => `
            <div class="knowledge-item">
                <div class="knowledge-header">
                    <h5>${item.question}</h5>
                    <div class="knowledge-meta">
                        <span class="category-tag ${item.category}">${this.getCategoryName(item.category)}</span>
                        <span class="confidence-score">${Math.round(item.confidence * 100)}%</span>
                    </div>
                </div>
                <div class="knowledge-content">
                    <p>${item.answer}</p>
                </div>
                <div class="knowledge-keywords">
                    ${item.keywords.map(keyword => `<span class="keyword-tag">${keyword}</span>`).join('')}
                </div>
                <div class="knowledge-actions">
                    <button class="btn-icon" onclick="aiAssistant.editKnowledge('${item.id}')" title="编辑">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="btn-icon" onclick="aiAssistant.deleteKnowledge('${item.id}')" title="删除">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </div>
        `).join('');
    }

    getCategoryName(category) {
        const names = {
            'system': '系统',
            'business': '业务',
            'technical': '技术',
            'faq': '常见问题',
            'learned': '学习获得'
        };
        return names[category] || category;
    }

    addKnowledge() {
        const question = prompt('请输入问题:');
        const answer = prompt('请输入答案:');
        const category = prompt('请输入分类 (system/business/technical/faq):') || 'faq';

        if (question && answer) {
            const newKnowledge = {
                id: 'kb_' + Date.now(),
                category: category,
                question: question,
                answer: answer,
                keywords: this.extractKeywords(question + ' ' + answer),
                confidence: 0.8,
                source: 'manual'
            };

            this.knowledgeBase.push(newKnowledge);
            this.updateKnowledgeStats();
            this.renderKnowledgeItems();

            this.addMessage(`已添加新的知识条目："${question}"`, 'assistant');
        }
    }

    importKnowledge() {
        this.addMessage('知识导入功能开发中...', 'assistant');
    }

    exportKnowledge() {
        const data = JSON.stringify(this.knowledgeBase, null, 2);
        const blob = new Blob([data], { type: 'application/json' });
        const url = URL.createObjectURL(blob);

        const a = document.createElement('a');
        a.href = url;
        a.download = 'knowledge-base.json';
        a.click();

        URL.revokeObjectURL(url);
        this.addMessage('知识库已导出到文件', 'assistant');
    }

    clearKnowledge() {
        if (confirm('确定要清空知识库吗？此操作不可恢复。')) {
            this.knowledgeBase = [];
            this.updateKnowledgeStats();
            this.renderKnowledgeItems();
            this.addMessage('知识库已清空', 'assistant');
        }
    }

    closeKnowledgeBase() {
        document.getElementById('knowledgeBaseInterface').style.display = 'none';
    }
}

// 全局AI助手实例
let aiAssistant = null;

// 初始化AI助手
function initializeAIAssistant() {
    // 防止重复初始化
    if (aiAssistant) {
        console.log('⚠️ AI助手已经初始化，跳过重复初始化');
        return;
    }

    aiAssistant = new AIAssistant();
    console.log('✅ 智能助手系统已初始化');
}

// 显示AI助手
function showAIAssistant() {
    if (aiAssistant) {
        aiAssistant.toggleAssistant();
    } else {
        console.log('AI助手尚未初始化');
        initializeAIAssistant();
        setTimeout(() => {
            if (aiAssistant) {
                aiAssistant.toggleAssistant();
            }
        }, 100);
    }
}

// 页面加载时初始化
document.addEventListener('DOMContentLoaded', function() {
    setTimeout(initializeAIAssistant, 2000);
});
