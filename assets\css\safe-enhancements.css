/* 安全的UI增强样式 - 不覆盖现有布局 */

/* 只为新添加的组件提供样式 */

/* 快速操作区域 - 新组件 */
.quick-actions-section {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 12px;
    padding: 20px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
    backdrop-filter: blur(10px);
}

.quick-actions-title {
    font-size: 18px;
    font-weight: 600;
    color: #1f2937;
    margin: 0 0 16px 0;
}

.quick-actions-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 16px;
}

.quick-action-btn {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 16px;
    background: #f8fafc;
    border: 1px solid #e5e7eb;
    border-radius: 12px;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    color: #374151;
}

.quick-action-btn:hover {
    background: #667eea;
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.quick-action-icon {
    width: 40px;
    height: 40px;
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #667eea;
    color: white;
    font-size: 18px;
    flex-shrink: 0;
}

.quick-action-btn:hover .quick-action-icon {
    background: white;
    color: #667eea;
}

/* 搜索工具栏 - 新组件 */
.page-toolbar {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 12px;
    padding: 16px 20px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
    backdrop-filter: blur(10px);
}

.toolbar-row {
    display: flex;
    align-items: center;
    gap: 16px;
    flex-wrap: wrap;
}

.toolbar-section {
    display: flex;
    align-items: center;
    gap: 12px;
}

.toolbar-divider {
    width: 1px;
    height: 24px;
    background: #e5e7eb;
    margin: 0 8px;
}

/* 搜索框增强 */
.search-box {
    position: relative;
    min-width: 300px;
}

.search-box input {
    width: 100%;
    padding: 12px 16px 12px 44px;
    border: 1px solid #e5e7eb;
    border-radius: 12px;
    font-size: 14px;
    transition: all 0.3s ease;
}

.search-box input:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.search-box .search-icon {
    position: absolute;
    left: 16px;
    top: 50%;
    transform: translateY(-50%);
    color: #6b7280;
    font-size: 16px;
}

/* 筛选器组 */
.filter-group {
    display: flex;
    align-items: center;
    gap: 8px;
}

.filter-group label {
    font-size: 14px;
    color: #374151;
    font-weight: 500;
}

.filter-select {
    padding: 10px 16px;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    background: white;
    font-size: 14px;
    min-width: 120px;
    cursor: pointer;
    transition: border-color 0.3s ease;
}

.filter-select:focus {
    outline: none;
    border-color: #667eea;
}

/* 数据表格容器 - 新组件 */
.data-table-container {
    background: rgba(255, 255, 255, 0.98);
    border-radius: 16px;
    overflow: hidden;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.table-header {
    padding: 20px 24px;
    border-bottom: 1px solid #f3f4f6;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.table-title {
    font-size: 18px;
    font-weight: 600;
    color: #1f2937;
    margin: 0;
}

.table-actions {
    display: flex;
    gap: 8px;
}

/* 现代表格样式 */
.modern-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 14px;
}

.modern-table th {
    background: #f9fafb;
    padding: 16px;
    text-align: left;
    font-weight: 600;
    color: #374151;
    border-bottom: 1px solid #e5e7eb;
}

.modern-table td {
    padding: 16px;
    border-bottom: 1px solid #f3f4f6;
    vertical-align: middle;
}

.modern-table tbody tr {
    transition: background-color 0.2s ease;
}

.modern-table tbody tr:hover {
    background-color: #f9fafb;
}

/* 分页器 */
.pagination-container {
    padding: 20px 24px;
    border-top: 1px solid #f3f4f6;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.pagination-info {
    color: #6b7280;
    font-size: 14px;
}

.pagination-controls {
    display: flex;
    gap: 8px;
}

.pagination-btn {
    padding: 8px 12px;
    border: 1px solid #e5e7eb;
    background: white;
    border-radius: 6px;
    cursor: pointer;
    font-size: 14px;
    transition: all 0.2s ease;
}

.pagination-btn:hover:not(:disabled) {
    background: #f3f4f6;
}

.pagination-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.pagination-btn.active {
    background: #667eea;
    color: white;
    border-color: #667eea;
}

/* 批量操作工具栏 */
.bulk-actions-toolbar {
    padding: 16px 24px;
    background: #f8fafc;
    border-bottom: 1px solid #e5e7eb;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.bulk-actions-info {
    color: #374151;
    font-weight: 500;
}

.bulk-actions-buttons {
    display: flex;
    gap: 8px;
}

/* 响应式设计 */
@media (max-width: 1024px) {
    .toolbar-row {
        flex-direction: column;
        align-items: stretch;
        gap: 12px;
    }
    
    .search-box {
        min-width: auto;
    }
    
    .quick-actions-grid {
        grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
        gap: 12px;
    }
    
    .table-header {
        flex-direction: column;
        gap: 12px;
        align-items: stretch;
    }
    
    .pagination-container {
        flex-direction: column;
        gap: 12px;
        align-items: center;
    }
}

@media (max-width: 768px) {
    .quick-actions-grid {
        grid-template-columns: 1fr;
    }
    
    .modern-table {
        font-size: 12px;
    }
    
    .modern-table th,
    .modern-table td {
        padding: 12px 8px;
    }
    
    .bulk-actions-toolbar {
        flex-direction: column;
        gap: 12px;
        align-items: stretch;
    }
    
    .bulk-actions-buttons {
        justify-content: center;
        flex-wrap: wrap;
    }
}
