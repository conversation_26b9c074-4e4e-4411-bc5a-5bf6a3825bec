<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ModernCorp - 现代企业管理系统</title>
    <link rel="stylesheet" href="login.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <div class="login-background">
        <div class="background-shapes">
            <div class="shape shape-1"></div>
            <div class="shape shape-2"></div>
            <div class="shape shape-3"></div>
        </div>
    </div>

    <div class="login-container">
        <div class="login-card">
            <div class="login-header">
                <div class="logo">
                    <div class="logo-icon">
                        <i class="fas fa-cube"></i>
                    </div>
                    <div class="logo-text">
                        <h1>ModernCorp</h1>
                        <span>现代企业管理系统</span>
                    </div>
                </div>
                <p>欢迎回来！请登录您的账户以继续</p>
            </div>

            <form class="login-form" id="loginForm">
                <div class="form-tabs">
                    <button type="button" class="tab-btn active" data-tab="login">
                        <i class="fas fa-sign-in-alt"></i>
                        <span>登录</span>
                    </button>
                    <button type="button" class="tab-btn" data-tab="register">
                        <i class="fas fa-user-plus"></i>
                        <span>注册</span>
                    </button>
                </div>

                <!-- 登录表单 -->
                <div class="tab-content active" id="login">
                    <div class="form-group">
                        <label for="loginUsername">用户名或邮箱</label>
                        <div class="input-wrapper">
                            <i class="fas fa-user"></i>
                            <input type="text" id="loginUsername" name="username" placeholder="请输入用户名或邮箱" required>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="loginPassword">密码</label>
                        <div class="input-wrapper">
                            <i class="fas fa-lock"></i>
                            <input type="password" id="loginPassword" name="password" placeholder="请输入密码" required>
                            <button type="button" class="toggle-password">
                                <i class="fas fa-eye"></i>
                            </button>
                        </div>
                    </div>

                    <div class="form-options">
                        <label class="checkbox-wrapper">
                            <input type="checkbox" name="remember">
                            <span class="checkmark">
                                <i class="fas fa-check"></i>
                            </span>
                            <span class="checkbox-label">记住我</span>
                        </label>
                        <a href="#" class="forgot-password">忘记密码？</a>
                    </div>

                    <button type="submit" class="btn-primary" onclick="doLogin()">
                        <i class="fas fa-sign-in-alt"></i>
                        <span>立即登录</span>
                    </button>

                    <div class="divider">
                        <span>或者</span>
                    </div>

                    <div class="social-login">
                        <button type="button" class="social-btn google">
                            <i class="fab fa-google"></i>
                            <span>使用 Google 登录</span>
                        </button>
                        <button type="button" class="social-btn github">
                            <i class="fab fa-github"></i>
                            <span>使用 GitHub 登录</span>
                        </button>
                    </div>
                </div>

                <!-- 注册表单 -->
                <div class="tab-content" id="register">
                    <div class="form-row">
                        <div class="form-group">
                            <label for="firstName">姓名</label>
                            <div class="input-wrapper">
                                <i class="fas fa-user"></i>
                                <input type="text" id="firstName" name="firstName" placeholder="请输入您的姓名" required>
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="lastName">姓氏</label>
                            <div class="input-wrapper">
                                <i class="fas fa-user"></i>
                                <input type="text" id="lastName" name="lastName" placeholder="请输入您的姓氏" required>
                            </div>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="regUsername">用户名</label>
                        <div class="input-wrapper">
                            <i class="fas fa-at"></i>
                            <input type="text" id="regUsername" name="regUsername" placeholder="至少6个字符，字母数字组合" required>
                        </div>
                        <div class="error-message" id="usernameError"></div>
                    </div>

                    <div class="form-group">
                        <label for="email">邮箱地址</label>
                        <div class="input-wrapper">
                            <i class="fas fa-envelope"></i>
                            <input type="email" id="email" name="email" placeholder="请输入有效的邮箱地址" required>
                        </div>
                        <div class="error-message" id="emailError"></div>
                    </div>

                    <div class="form-group">
                        <label for="regPassword">密码</label>
                        <div class="input-wrapper">
                            <i class="fas fa-lock"></i>
                            <input type="password" id="regPassword" name="regPassword" placeholder="至少8位，包含大小写字母和数字" required>
                            <button type="button" class="toggle-password">
                                <i class="fas fa-eye"></i>
                            </button>
                        </div>
                        <div class="password-strength">
                            <div class="strength-bar">
                                <div class="strength-fill"></div>
                            </div>
                            <span class="strength-text">密码强度</span>
                        </div>
                        <div class="error-message" id="passwordError"></div>
                    </div>

                    <div class="form-group">
                        <label for="confirmPassword">确认密码</label>
                        <div class="input-wrapper">
                            <i class="fas fa-lock"></i>
                            <input type="password" id="confirmPassword" name="confirmPassword" placeholder="请再次输入密码" required>
                        </div>
                        <div class="error-message" id="confirmError"></div>
                    </div>

                    <div class="form-group">
                        <label class="checkbox-wrapper">
                            <input type="checkbox" name="terms" required>
                            <span class="checkmark">
                                <i class="fas fa-check"></i>
                            </span>
                            <span class="checkbox-label">
                                我同意 <a href="#" class="terms-link">服务条款</a> 和 <a href="#" class="terms-link">隐私政策</a>
                            </span>
                        </label>
                    </div>

                    <button type="submit" class="btn-primary">
                        <i class="fas fa-user-plus"></i>
                        <span>创建账户</span>
                    </button>

                    <!-- 调试按钮 -->
                    <button type="button" onclick="testRegister()" style="margin-top: 10px; width: 100%; background: #f59e0b; color: white; border: none; padding: 12px; border-radius: 8px; cursor: pointer;">
                        🔧 测试注册功能
                    </button>

                    <div class="divider">
                        <span>或者</span>
                    </div>

                    <div class="social-login">
                        <button type="button" class="social-btn google">
                            <i class="fab fa-google"></i>
                            <span>使用 Google 注册</span>
                        </button>
                        <button type="button" class="social-btn github">
                            <i class="fab fa-github"></i>
                            <span>使用 GitHub 注册</span>
                        </button>
                    </div>
                </div>
            </form>

            <!-- 角色快速切换 -->
            <div class="role-switcher">
                <div class="role-switcher-header">
                    <i class="fas fa-users"></i>
                    <span>快速登录测试</span>
                </div>
                <div class="role-buttons">
                    <button type="button" class="role-btn admin" onclick="quickLogin('admin')">
                        <i class="fas fa-crown"></i>
                        <span>系统管理员</span>
                        <small>admin / admin123</small>
                    </button>
                    <button type="button" class="role-btn manager" onclick="quickLogin('manager')">
                        <i class="fas fa-user-tie"></i>
                        <span>部门经理</span>
                        <small>manager / manager123</small>
                    </button>
                    <button type="button" class="role-btn user" onclick="quickLogin('user')">
                        <i class="fas fa-user"></i>
                        <span>普通用户</span>
                        <small>user / user123</small>
                    </button>
                    <button type="button" class="role-btn test" onclick="quickLogin('test')">
                        <i class="fas fa-vial"></i>
                        <span>测试用户</span>
                        <small>test / test123</small>
                    </button>
                    <button type="button" class="role-btn demo" onclick="quickLogin('demo')">
                        <i class="fas fa-eye"></i>
                        <span>演示用户</span>
                        <small>demo / demo123</small>
                    </button>
                </div>
            </div>
        </div>

        <!-- 功能特色 -->
        <div class="features-section">
            <h3>为什么选择 ModernCorp？</h3>
            <div class="features-list">
                <div class="feature-item">
                    <i class="fas fa-shield-alt"></i>
                    <div class="feature-content">
                        <h4>安全可靠</h4>
                        <p>企业级安全保障，数据加密存储</p>
                    </div>
                </div>
                <div class="feature-item">
                    <i class="fas fa-chart-line"></i>
                    <div class="feature-content">
                        <h4>数据分析</h4>
                        <p>实时数据分析，智能业务洞察</p>
                    </div>
                </div>
                <div class="feature-item">
                    <i class="fas fa-users"></i>
                    <div class="feature-content">
                        <h4>团队协作</h4>
                        <p>高效团队协作，提升工作效率</p>
                    </div>
                </div>
                <div class="feature-item">
                    <i class="fas fa-mobile-alt"></i>
                    <div class="feature-content">
                        <h4>移动优先</h4>
                        <p>响应式设计，随时随地办公</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 核心系统模块 -->
    <script src="error-handler.js"></script>
    <script src="enhanced-notifications.js"></script>
    <script src="module-loader.js"></script>
    <script src="missing-functions.js"></script>
    <script src="comprehensive-fix.js"></script>
    <script src="quick-fixes.js"></script>

    <!-- 登录功能模块 -->
    <script src="login.js"></script>
    <script>
        // 测试注册功能
        function testRegister() {
            console.log('测试注册功能被调用');

            // 填充测试数据
            document.getElementById('firstName').value = '张';
            document.getElementById('lastName').value = '三';
            document.getElementById('regUsername').value = 'testuser123';
            document.getElementById('email').value = '<EMAIL>';
            document.getElementById('regPassword').value = 'TestPass123';
            document.getElementById('confirmPassword').value = 'TestPass123';
            document.querySelector('input[name="terms"]').checked = true;

            console.log('测试数据已填充');

            // 直接调用注册函数
            if (typeof handleRegister === 'function') {
                console.log('调用 handleRegister 函数');
                handleRegister();
            } else {
                console.log('handleRegister 函数不存在');
                alert('handleRegister 函数不存在！');
            }
        }

        // 页面加载后的额外检查
        document.addEventListener('DOMContentLoaded', function() {
            console.log('页面加载完成，开始检查...');

            // 检查关键元素
            const elements = {
                form: document.getElementById('loginForm'),
                registerTab: document.getElementById('register'),
                registerBtn: document.querySelector('#register .btn-primary'),
                firstName: document.getElementById('firstName'),
                lastName: document.getElementById('lastName'),
                regUsername: document.getElementById('regUsername'),
                email: document.getElementById('email'),
                regPassword: document.getElementById('regPassword'),
                confirmPassword: document.getElementById('confirmPassword'),
                terms: document.querySelector('input[name="terms"]')
            };

            console.log('元素检查结果:');
            Object.keys(elements).forEach(key => {
                console.log(`${key}: ${elements[key] ? '✅ 存在' : '❌ 不存在'}`);
            });

            // 检查函数
            console.log('函数检查结果:');
            console.log(`handleRegister: ${typeof handleRegister === 'function' ? '✅ 存在' : '❌ 不存在'}`);
            console.log(`showNotification: ${typeof showNotification === 'function' ? '✅ 存在' : '❌ 不存在'}`);

            // 添加额外的事件监听器来调试
            const registerBtn = document.querySelector('#register .btn-primary');
            if (registerBtn) {
                registerBtn.addEventListener('click', function(e) {
                    console.log('注册按钮被点击，事件类型:', e.type);
                    console.log('按钮类型:', this.type);
                    console.log('表单:', this.form);
                });
            }
        });
    </script>
</body>
</html>