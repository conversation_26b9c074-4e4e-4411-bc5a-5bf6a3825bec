// 国际化多语言支持系统
class InternationalizationSystem {
    constructor() {
        this.currentLanguage = 'zh-CN';
        this.supportedLanguages = {
            'zh-CN': { name: '简体中文', flag: '🇨🇳', rtl: false },
            'zh-TW': { name: '繁體中文', flag: '🇹🇼', rtl: false },
            'en-US': { name: 'English', flag: '🇺🇸', rtl: false },
            'ja-JP': { name: '日本語', flag: '🇯🇵', rtl: false },
            'ko-KR': { name: '한국어', flag: '🇰🇷', rtl: false },
            'ar-SA': { name: 'العربية', flag: '🇸🇦', rtl: true },
            'es-ES': { name: '<PERSON>spaño<PERSON>', flag: '🇪🇸', rtl: false },
            'fr-FR': { name: 'Français', flag: '🇫🇷', rtl: false },
            'de-DE': { name: 'Deutsch', flag: '🇩🇪', rtl: false },
            'ru-RU': { name: 'Русский', flag: '🇷🇺', rtl: false }
        };
        this.translations = {};
        this.fallbackLanguage = 'en-US';
        this.dateTimeFormats = {};
        this.numberFormats = {};
        
        this.initializeI18n();
    }

    initializeI18n() {
        this.loadTranslations();
        this.loadStoredLanguage();
        this.createLanguageSelector();
        this.bindLanguageEvents();
        this.setupDateTimeFormats();
        this.setupNumberFormats();
        this.applyLanguage();
    }

    loadTranslations() {
        // 加载翻译文件
        this.translations = {
            'zh-CN': {
                // 通用
                'common.save': '保存',
                'common.cancel': '取消',
                'common.delete': '删除',
                'common.edit': '编辑',
                'common.add': '添加',
                'common.search': '搜索',
                'common.filter': '筛选',
                'common.export': '导出',
                'common.import': '导入',
                'common.refresh': '刷新',
                'common.close': '关闭',
                'common.confirm': '确认',
                'common.loading': '加载中...',
                'common.success': '成功',
                'common.error': '错误',
                'common.warning': '警告',
                'common.info': '信息',
                
                // 导航
                'nav.dashboard': '仪表盘',
                'nav.users': '用户管理',
                'nav.orders': '订单管理',
                'nav.analytics': '数据分析',
                'nav.ner': 'NER数据管理',
                'nav.files': '文件管理',
                'nav.notifications': '通知中心',
                'nav.settings': '系统设置',
                'nav.workflow': '工作流',
                
                // 用户管理
                'users.title': '用户管理',
                'users.add': '添加用户',
                'users.username': '用户名',
                'users.name': '姓名',
                'users.email': '邮箱',
                'users.role': '角色',
                'users.department': '部门',
                'users.status': '状态',
                'users.actions': '操作',
                'users.active': '活跃',
                'users.inactive': '非活跃',
                
                // 订单管理
                'orders.title': '订单管理',
                'orders.id': '订单号',
                'orders.customer': '客户',
                'orders.product': '产品',
                'orders.amount': '金额',
                'orders.status': '状态',
                'orders.date': '日期',
                'orders.pending': '待处理',
                'orders.processing': '处理中',
                'orders.completed': '已完成',
                'orders.cancelled': '已取消',
                
                // 通知
                'notifications.title': '通知中心',
                'notifications.markAllRead': '全部标记为已读',
                'notifications.clear': '清空',
                'notifications.noNotifications': '暂无通知',
                'notifications.system': '系统通知',
                'notifications.user': '用户通知',
                'notifications.order': '订单通知',
                
                // 设置
                'settings.title': '系统设置',
                'settings.language': '语言设置',
                'settings.theme': '主题设置',
                'settings.profile': '个人资料',
                'settings.security': '安全设置',
                'settings.notifications': '通知设置',
                
                // 工作流
                'workflow.title': '工作流管理',
                'workflow.create': '创建工作流',
                'workflow.templates': '模板库',
                'workflow.active': '进行中',
                'workflow.completed': '已完成',
                'workflow.analytics': '分析',
                'workflow.start': '开始',
                'workflow.task': '任务',
                'workflow.approval': '审批',
                'workflow.condition': '条件',
                'workflow.notification': '通知',
                'workflow.delay': '延时',
                'workflow.end': '结束',
                
                // 时间格式
                'time.justNow': '刚刚',
                'time.minutesAgo': '{0}分钟前',
                'time.hoursAgo': '{0}小时前',
                'time.daysAgo': '{0}天前',
                'time.today': '今天',
                'time.yesterday': '昨天',
                'time.thisWeek': '本周',
                'time.lastWeek': '上周',
                'time.thisMonth': '本月',
                'time.lastMonth': '上月'
            },
            
            'en-US': {
                // Common
                'common.save': 'Save',
                'common.cancel': 'Cancel',
                'common.delete': 'Delete',
                'common.edit': 'Edit',
                'common.add': 'Add',
                'common.search': 'Search',
                'common.filter': 'Filter',
                'common.export': 'Export',
                'common.import': 'Import',
                'common.refresh': 'Refresh',
                'common.close': 'Close',
                'common.confirm': 'Confirm',
                'common.loading': 'Loading...',
                'common.success': 'Success',
                'common.error': 'Error',
                'common.warning': 'Warning',
                'common.info': 'Info',
                
                // Navigation
                'nav.dashboard': 'Dashboard',
                'nav.users': 'User Management',
                'nav.orders': 'Order Management',
                'nav.analytics': 'Analytics',
                'nav.ner': 'NER Data Management',
                'nav.files': 'File Management',
                'nav.notifications': 'Notifications',
                'nav.settings': 'Settings',
                'nav.workflow': 'Workflow',
                
                // User Management
                'users.title': 'User Management',
                'users.add': 'Add User',
                'users.username': 'Username',
                'users.name': 'Name',
                'users.email': 'Email',
                'users.role': 'Role',
                'users.department': 'Department',
                'users.status': 'Status',
                'users.actions': 'Actions',
                'users.active': 'Active',
                'users.inactive': 'Inactive',
                
                // Order Management
                'orders.title': 'Order Management',
                'orders.id': 'Order ID',
                'orders.customer': 'Customer',
                'orders.product': 'Product',
                'orders.amount': 'Amount',
                'orders.status': 'Status',
                'orders.date': 'Date',
                'orders.pending': 'Pending',
                'orders.processing': 'Processing',
                'orders.completed': 'Completed',
                'orders.cancelled': 'Cancelled',
                
                // Notifications
                'notifications.title': 'Notifications',
                'notifications.markAllRead': 'Mark All as Read',
                'notifications.clear': 'Clear All',
                'notifications.noNotifications': 'No notifications',
                'notifications.system': 'System',
                'notifications.user': 'User',
                'notifications.order': 'Order',
                
                // Settings
                'settings.title': 'Settings',
                'settings.language': 'Language',
                'settings.theme': 'Theme',
                'settings.profile': 'Profile',
                'settings.security': 'Security',
                'settings.notifications': 'Notifications',
                
                // Workflow
                'workflow.title': 'Workflow Management',
                'workflow.create': 'Create Workflow',
                'workflow.templates': 'Templates',
                'workflow.active': 'Active',
                'workflow.completed': 'Completed',
                'workflow.analytics': 'Analytics',
                'workflow.start': 'Start',
                'workflow.task': 'Task',
                'workflow.approval': 'Approval',
                'workflow.condition': 'Condition',
                'workflow.notification': 'Notification',
                'workflow.delay': 'Delay',
                'workflow.end': 'End',
                
                // Time formats
                'time.justNow': 'Just now',
                'time.minutesAgo': '{0} minutes ago',
                'time.hoursAgo': '{0} hours ago',
                'time.daysAgo': '{0} days ago',
                'time.today': 'Today',
                'time.yesterday': 'Yesterday',
                'time.thisWeek': 'This week',
                'time.lastWeek': 'Last week',
                'time.thisMonth': 'This month',
                'time.lastMonth': 'Last month'
            },
            
            'ja-JP': {
                // 共通
                'common.save': '保存',
                'common.cancel': 'キャンセル',
                'common.delete': '削除',
                'common.edit': '編集',
                'common.add': '追加',
                'common.search': '検索',
                'common.filter': 'フィルター',
                'common.export': 'エクスポート',
                'common.import': 'インポート',
                'common.refresh': '更新',
                'common.close': '閉じる',
                'common.confirm': '確認',
                'common.loading': '読み込み中...',
                'common.success': '成功',
                'common.error': 'エラー',
                'common.warning': '警告',
                'common.info': '情報',
                
                // ナビゲーション
                'nav.dashboard': 'ダッシュボード',
                'nav.users': 'ユーザー管理',
                'nav.orders': '注文管理',
                'nav.analytics': '分析',
                'nav.ner': 'NERデータ管理',
                'nav.files': 'ファイル管理',
                'nav.notifications': '通知',
                'nav.settings': '設定',
                'nav.workflow': 'ワークフロー',
                
                // ユーザー管理
                'users.title': 'ユーザー管理',
                'users.add': 'ユーザー追加',
                'users.username': 'ユーザー名',
                'users.name': '名前',
                'users.email': 'メール',
                'users.role': '役割',
                'users.department': '部署',
                'users.status': 'ステータス',
                'users.actions': 'アクション',
                'users.active': 'アクティブ',
                'users.inactive': '非アクティブ'
            }
        };
    }

    createLanguageSelector() {
        // 创建语言选择器
        const languageSelector = document.createElement('div');
        languageSelector.id = 'languageSelector';
        languageSelector.className = 'language-selector';
        languageSelector.innerHTML = `
            <button class="language-btn" onclick="i18nSystem.toggleLanguageDropdown()">
                <span class="current-language-flag">${this.supportedLanguages[this.currentLanguage].flag}</span>
                <span class="current-language-name">${this.supportedLanguages[this.currentLanguage].name}</span>
                <i class="fas fa-chevron-down"></i>
            </button>
            <div class="language-dropdown" id="languageDropdown">
                ${Object.entries(this.supportedLanguages).map(([code, lang]) => `
                    <div class="language-option ${code === this.currentLanguage ? 'active' : ''}" 
                         onclick="i18nSystem.changeLanguage('${code}')">
                        <span class="language-flag">${lang.flag}</span>
                        <span class="language-name">${lang.name}</span>
                        ${code === this.currentLanguage ? '<i class="fas fa-check"></i>' : ''}
                    </div>
                `).join('')}
            </div>
        `;
        
        // 添加到页面头部
        const header = document.querySelector('.header-actions');
        if (header) {
            header.insertBefore(languageSelector, header.firstChild);
        }
    }

    bindLanguageEvents() {
        // 点击外部关闭下拉菜单
        document.addEventListener('click', (e) => {
            const selector = document.getElementById('languageSelector');
            const dropdown = document.getElementById('languageDropdown');
            
            if (selector && dropdown && !selector.contains(e.target)) {
                dropdown.classList.remove('show');
            }
        });
    }

    setupDateTimeFormats() {
        this.dateTimeFormats = {
            'zh-CN': {
                date: { year: 'numeric', month: 'long', day: 'numeric' },
                time: { hour: '2-digit', minute: '2-digit' },
                datetime: { year: 'numeric', month: 'short', day: 'numeric', hour: '2-digit', minute: '2-digit' },
                relative: { numeric: 'auto', style: 'long' }
            },
            'en-US': {
                date: { year: 'numeric', month: 'long', day: 'numeric' },
                time: { hour: '2-digit', minute: '2-digit', hour12: true },
                datetime: { year: 'numeric', month: 'short', day: 'numeric', hour: '2-digit', minute: '2-digit', hour12: true },
                relative: { numeric: 'auto', style: 'long' }
            },
            'ja-JP': {
                date: { year: 'numeric', month: 'long', day: 'numeric' },
                time: { hour: '2-digit', minute: '2-digit' },
                datetime: { year: 'numeric', month: 'short', day: 'numeric', hour: '2-digit', minute: '2-digit' },
                relative: { numeric: 'auto', style: 'long' }
            }
        };
    }

    setupNumberFormats() {
        this.numberFormats = {
            'zh-CN': {
                currency: { style: 'currency', currency: 'CNY' },
                decimal: { minimumFractionDigits: 2, maximumFractionDigits: 2 },
                percent: { style: 'percent' }
            },
            'en-US': {
                currency: { style: 'currency', currency: 'USD' },
                decimal: { minimumFractionDigits: 2, maximumFractionDigits: 2 },
                percent: { style: 'percent' }
            },
            'ja-JP': {
                currency: { style: 'currency', currency: 'JPY' },
                decimal: { minimumFractionDigits: 2, maximumFractionDigits: 2 },
                percent: { style: 'percent' }
            }
        };
    }

    loadStoredLanguage() {
        const stored = localStorage.getItem('selectedLanguage');
        if (stored && this.supportedLanguages[stored]) {
            this.currentLanguage = stored;
        } else {
            // 检测浏览器语言
            const browserLang = navigator.language || navigator.userLanguage;
            if (this.supportedLanguages[browserLang]) {
                this.currentLanguage = browserLang;
            }
        }
    }

    toggleLanguageDropdown() {
        const dropdown = document.getElementById('languageDropdown');
        if (dropdown) {
            dropdown.classList.toggle('show');
        }
    }

    changeLanguage(languageCode) {
        if (!this.supportedLanguages[languageCode]) return;
        
        this.currentLanguage = languageCode;
        localStorage.setItem('selectedLanguage', languageCode);
        
        this.applyLanguage();
        this.updateLanguageSelector();
        this.toggleLanguageDropdown();
        
        if (window.showNotification) {
            showNotification(this.t('common.success'), 'success');
        }
    }

    applyLanguage() {
        // 应用语言到页面
        this.translatePage();
        this.updateDocumentDirection();
        this.updateDateTimeDisplays();
        this.updateNumberDisplays();
        
        // 触发语言变更事件
        document.dispatchEvent(new CustomEvent('languageChanged', {
            detail: { language: this.currentLanguage }
        }));
    }

    translatePage() {
        // 翻译所有带有 data-i18n 属性的元素
        document.querySelectorAll('[data-i18n]').forEach(element => {
            const key = element.getAttribute('data-i18n');
            const translation = this.t(key);
            
            if (element.tagName === 'INPUT' && (element.type === 'text' || element.type === 'search')) {
                element.placeholder = translation;
            } else {
                element.textContent = translation;
            }
        });
        
        // 翻译特定的元素
        this.translateSpecificElements();
    }

    translateSpecificElements() {
        // 翻译导航菜单
        const navItems = {
            'dashboard': 'nav.dashboard',
            'users': 'nav.users',
            'orders': 'nav.orders',
            'analytics': 'nav.analytics',
            'ner': 'nav.ner',
            'files': 'nav.files',
            'notifications': 'nav.notifications',
            'settings': 'nav.settings'
        };
        
        Object.entries(navItems).forEach(([page, key]) => {
            const element = document.querySelector(`[data-page="${page}"] .nav-text`);
            if (element) {
                element.textContent = this.t(key);
            }
        });
        
        // 翻译页面标题
        const pageTitles = document.querySelectorAll('.page-title h2');
        pageTitles.forEach(title => {
            const pageId = title.closest('.page').id;
            const titleKey = `${pageId}.title`;
            if (this.hasTranslation(titleKey)) {
                title.textContent = this.t(titleKey);
            }
        });
        
        // 翻译按钮
        this.translateButtons();
        
        // 翻译表格头部
        this.translateTableHeaders();
    }

    translateButtons() {
        const buttonMappings = {
            '添加用户': 'users.add',
            '导出用户': 'common.export',
            '导入用户': 'common.import',
            '保存': 'common.save',
            '取消': 'common.cancel',
            '删除': 'common.delete',
            '编辑': 'common.edit',
            '搜索': 'common.search',
            '筛选': 'common.filter',
            '刷新': 'common.refresh',
            '关闭': 'common.close'
        };
        
        Object.entries(buttonMappings).forEach(([text, key]) => {
            const buttons = Array.from(document.querySelectorAll('button')).filter(btn => 
                btn.textContent.trim() === text
            );
            buttons.forEach(button => {
                button.textContent = this.t(key);
            });
        });
    }

    translateTableHeaders() {
        // 用户管理表格
        const userHeaders = {
            '用户名': 'users.username',
            '姓名': 'users.name',
            '邮箱': 'users.email',
            '角色': 'users.role',
            '部门': 'users.department',
            '状态': 'users.status',
            '操作': 'users.actions'
        };
        
        this.translateTableHeadersByMapping(userHeaders);
        
        // 订单管理表格
        const orderHeaders = {
            '订单号': 'orders.id',
            '客户': 'orders.customer',
            '产品': 'orders.product',
            '金额': 'orders.amount',
            '状态': 'orders.status',
            '日期': 'orders.date'
        };
        
        this.translateTableHeadersByMapping(orderHeaders);
    }

    translateTableHeadersByMapping(mapping) {
        Object.entries(mapping).forEach(([text, key]) => {
            const headers = Array.from(document.querySelectorAll('th')).filter(th => 
                th.textContent.trim() === text
            );
            headers.forEach(header => {
                header.textContent = this.t(key);
            });
        });
    }

    updateDocumentDirection() {
        const isRTL = this.supportedLanguages[this.currentLanguage].rtl;
        document.documentElement.dir = isRTL ? 'rtl' : 'ltr';
        document.documentElement.lang = this.currentLanguage;
        
        // 更新CSS类
        document.body.classList.toggle('rtl', isRTL);
    }

    updateLanguageSelector() {
        const currentFlag = document.querySelector('.current-language-flag');
        const currentName = document.querySelector('.current-language-name');
        
        if (currentFlag && currentName) {
            const lang = this.supportedLanguages[this.currentLanguage];
            currentFlag.textContent = lang.flag;
            currentName.textContent = lang.name;
        }
        
        // 更新下拉菜单中的选中状态
        document.querySelectorAll('.language-option').forEach(option => {
            option.classList.remove('active');
            const checkIcon = option.querySelector('.fas.fa-check');
            if (checkIcon) checkIcon.remove();
        });
        
        const activeOption = document.querySelector(`[onclick*="${this.currentLanguage}"]`);
        if (activeOption) {
            activeOption.classList.add('active');
            activeOption.insertAdjacentHTML('beforeend', '<i class="fas fa-check"></i>');
        }
    }

    updateDateTimeDisplays() {
        // 更新所有日期时间显示
        document.querySelectorAll('[data-datetime]').forEach(element => {
            const timestamp = element.getAttribute('data-datetime');
            const format = element.getAttribute('data-format') || 'datetime';
            const date = new Date(timestamp);
            
            element.textContent = this.formatDateTime(date, format);
        });
        
        // 更新相对时间显示
        document.querySelectorAll('[data-relative-time]').forEach(element => {
            const timestamp = element.getAttribute('data-relative-time');
            const date = new Date(timestamp);
            
            element.textContent = this.formatRelativeTime(date);
        });
    }

    updateNumberDisplays() {
        // 更新所有数字显示
        document.querySelectorAll('[data-number]').forEach(element => {
            const number = parseFloat(element.getAttribute('data-number'));
            const format = element.getAttribute('data-number-format') || 'decimal';
            
            element.textContent = this.formatNumber(number, format);
        });
    }

    // 翻译函数
    t(key, params = {}) {
        const translation = this.getTranslation(key);
        return this.interpolate(translation, params);
    }

    getTranslation(key) {
        const currentTranslations = this.translations[this.currentLanguage] || {};
        const fallbackTranslations = this.translations[this.fallbackLanguage] || {};
        
        return currentTranslations[key] || fallbackTranslations[key] || key;
    }

    hasTranslation(key) {
        const currentTranslations = this.translations[this.currentLanguage] || {};
        const fallbackTranslations = this.translations[this.fallbackLanguage] || {};
        
        return !!(currentTranslations[key] || fallbackTranslations[key]);
    }

    interpolate(text, params) {
        return text.replace(/\{(\w+)\}/g, (match, key) => {
            return params[key] !== undefined ? params[key] : match;
        });
    }

    // 格式化函数
    formatDateTime(date, format = 'datetime') {
        const options = this.dateTimeFormats[this.currentLanguage]?.[format] || 
                      this.dateTimeFormats[this.fallbackLanguage]?.[format] || {};
        
        return new Intl.DateTimeFormat(this.currentLanguage, options).format(date);
    }

    formatRelativeTime(date) {
        const now = new Date();
        const diffInSeconds = Math.floor((now - date) / 1000);
        
        if (diffInSeconds < 60) {
            return this.t('time.justNow');
        } else if (diffInSeconds < 3600) {
            const minutes = Math.floor(diffInSeconds / 60);
            return this.t('time.minutesAgo', { 0: minutes });
        } else if (diffInSeconds < 86400) {
            const hours = Math.floor(diffInSeconds / 3600);
            return this.t('time.hoursAgo', { 0: hours });
        } else {
            const days = Math.floor(diffInSeconds / 86400);
            return this.t('time.daysAgo', { 0: days });
        }
    }

    formatNumber(number, format = 'decimal') {
        const options = this.numberFormats[this.currentLanguage]?.[format] || 
                      this.numberFormats[this.fallbackLanguage]?.[format] || {};
        
        return new Intl.NumberFormat(this.currentLanguage, options).format(number);
    }

    formatCurrency(amount, currency) {
        const options = { style: 'currency', currency: currency || 'USD' };
        return new Intl.NumberFormat(this.currentLanguage, options).format(amount);
    }

    formatPercent(value) {
        const options = { style: 'percent', minimumFractionDigits: 1, maximumFractionDigits: 1 };
        return new Intl.NumberFormat(this.currentLanguage, options).format(value / 100);
    }

    // 工具函数
    getCurrentLanguage() {
        return this.currentLanguage;
    }

    getSupportedLanguages() {
        return this.supportedLanguages;
    }

    isRTL() {
        return this.supportedLanguages[this.currentLanguage].rtl;
    }

    addTranslations(language, translations) {
        if (!this.translations[language]) {
            this.translations[language] = {};
        }
        Object.assign(this.translations[language], translations);
    }

    // 动态加载翻译文件
    async loadTranslationFile(language) {
        try {
            const response = await fetch(`/locales/${language}.json`);
            if (response.ok) {
                const translations = await response.json();
                this.addTranslations(language, translations);
                return true;
            }
        } catch (error) {
            console.warn(`Failed to load translation file for ${language}:`, error);
        }
        return false;
    }

    // 导出翻译数据
    exportTranslations(language) {
        const translations = this.translations[language];
        if (!translations) return null;
        
        const blob = new Blob([JSON.stringify(translations, null, 2)], { 
            type: 'application/json' 
        });
        const url = URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = `${language}.json`;
        link.click();
        URL.revokeObjectURL(url);
    }
}

// 全局国际化系统实例
let i18nSystem = null;

// 初始化国际化系统
function initializeI18nSystem() {
    i18nSystem = new InternationalizationSystem();
    console.log('✅ 国际化系统已初始化');
}

// 全局翻译函数
function t(key, params = {}) {
    return i18nSystem ? i18nSystem.t(key, params) : key;
}

// 页面加载时初始化
document.addEventListener('DOMContentLoaded', function() {
    setTimeout(initializeI18nSystem, 100);
});
