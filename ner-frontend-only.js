// 纯前端NER数据生成器
// 不依赖后端API，直接在浏览器中运行

class FrontendNERGenerator {
    constructor() {
        this.personNames = [
            "张三", "李四", "王五", "赵六", "陈七", "刘八", "杨九", "黄十",
            "周明", "吴亮", "郑强", "王芳", "李娜", "张伟", "刘洋", "陈静",
            "马云", "马化腾", "李彦宏", "雷军", "任正非", "董明珠"
        ];
        
        this.locations = [
            "北京", "上海", "广州", "深圳", "杭州", "南京", "武汉", "成都",
            "西安", "重庆", "天津", "青岛", "大连", "厦门", "苏州", "无锡"
        ];
        
        this.organizations = [
            "阿里巴巴", "腾讯", "百度", "字节跳动", "美团", "滴滴", "京东", "网易",
            "华为", "小米", "OPPO", "vivo", "联想", "海尔", "格力", "美的"
        ];
        
        this.templates = [
            "{person}在{location}的{organization}工作了三年。",
            "{person}于2023年从{location}来到{organization}。",
            "{organization}位于{location}，由{person}创立于2020年。",
            "2024年，{person}在{location}成立了{organization}。",
            "{person}计划明年访问{location}的{organization}。",
            "{organization}的{person}表示，今年将在{location}举办活动。",
            "据{organization}消息，{person}将于下月在{location}发表演讲。",
            "{location}的{organization}宣布，{person}获得了100万元奖金。"
        ];
        
        this.currentDataset = [];
        this.currentStats = {};
    }
    
    generateTimeEntity() {
        const timeFormats = [
            () => `${2020 + Math.floor(Math.random() * 5)}年${1 + Math.floor(Math.random() * 12)}月${1 + Math.floor(Math.random() * 28)}日`,
            () => `${2020 + Math.floor(Math.random() * 5)}年${1 + Math.floor(Math.random() * 12)}月`,
            () => `${2020 + Math.floor(Math.random() * 5)}年`,
            () => ["昨天", "今天", "明天", "上周", "下周", "上个月", "下个月"][Math.floor(Math.random() * 7)],
            () => ["去年", "今年", "明年", "前年", "后年"][Math.floor(Math.random() * 5)],
            () => ["上午", "下午", "晚上", "凌晨", "中午"][Math.floor(Math.random() * 5)]
        ];
        return timeFormats[Math.floor(Math.random() * timeFormats.length)]();
    }
    
    generateNumberEntity() {
        const numberFormats = [
            () => String(1 + Math.floor(Math.random() * 1000)),
            () => `${1 + Math.floor(Math.random() * 999)}.${1 + Math.floor(Math.random() * 99)}`,
            () => `${1 + Math.floor(Math.random() * 999)}万`,
            () => `${1 + Math.floor(Math.random() * 999)}亿`,
            () => `${1 + Math.floor(Math.random() * 100)}%`
        ];
        return numberFormats[Math.floor(Math.random() * numberFormats.length)]();
    }
    
    generateSample() {
        const template = this.templates[Math.floor(Math.random() * this.templates.length)];
        
        const person = template.includes('{person}') ? 
            this.personNames[Math.floor(Math.random() * this.personNames.length)] : null;
        const location = template.includes('{location}') ? 
            this.locations[Math.floor(Math.random() * this.locations.length)] : null;
        const organization = template.includes('{organization}') ? 
            this.organizations[Math.floor(Math.random() * this.organizations.length)] : null;
        const timeEntity = template.includes('{time}') ? this.generateTimeEntity() : null;
        const number = template.includes('{number}') ? this.generateNumberEntity() : null;
        
        let sentence = template;
        if (person) sentence = sentence.replace('{person}', person);
        if (location) sentence = sentence.replace('{location}', location);
        if (organization) sentence = sentence.replace('{organization}', organization);
        if (timeEntity) sentence = sentence.replace('{time}', timeEntity);
        if (number) sentence = sentence.replace('{number}', number);
        
        const tokens = Array.from(sentence);
        const labels = new Array(tokens.length).fill('O');
        
        // 标注实体
        if (person) this.labelEntity(sentence, person, 'PERSON', labels);
        if (location) this.labelEntity(sentence, location, 'LOCATION', labels);
        if (organization) this.labelEntity(sentence, organization, 'ORGANIZATION', labels);
        if (timeEntity) this.labelEntity(sentence, timeEntity, 'TIME', labels);
        if (number) this.labelEntity(sentence, number, 'NUMBER', labels);
        
        return { text: sentence, tokens, labels };
    }
    
    labelEntity(sentence, entity, entityType, labels) {
        const start = sentence.indexOf(entity);
        if (start !== -1) {
            labels[start] = `B-${entityType}`;
            for (let i = start + 1; i < start + entity.length && i < labels.length; i++) {
                labels[i] = `I-${entityType}`;
            }
        }
    }
    
    generateDataset(count) {
        this.currentDataset = [];
        for (let i = 0; i < count; i++) {
            const sample = this.generateSample();
            sample.id = i + 1;
            this.currentDataset.push(sample);
        }
        
        this.currentStats = this.calculateStats(this.currentDataset);
        return this.currentDataset;
    }
    
    calculateStats(dataset) {
        const stats = {
            total_samples: dataset.length,
            entity_counts: { PERSON: 0, LOCATION: 0, ORGANIZATION: 0, TIME: 0, NUMBER: 0 },
            avg_sentence_length: 0
        };
        
        let totalLength = 0;
        dataset.forEach(sample => {
            totalLength += sample.text.length;
            sample.labels.forEach(label => {
                if (label.startsWith('B-')) {
                    const entityType = label.substring(2);
                    if (stats.entity_counts[entityType] !== undefined) {
                        stats.entity_counts[entityType]++;
                    }
                }
            });
        });
        
        stats.avg_sentence_length = dataset.length > 0 ? totalLength / dataset.length : 0;
        return stats;
    }
    
    exportData(format = 'json') {
        if (this.currentDataset.length === 0) {
            alert('暂无数据，请先生成数据');
            return;
        }
        
        let content, filename, mimeType;
        
        if (format === 'json') {
            content = JSON.stringify(this.currentDataset, null, 2);
            filename = `ner_data_${new Date().toISOString().slice(0, 10)}.json`;
            mimeType = 'application/json';
        } else if (format === 'conll') {
            content = this.currentDataset.map(sample => {
                return sample.tokens.map((token, i) => `${token}\t${sample.labels[i]}`).join('\n') + '\n';
            }).join('\n');
            filename = `ner_data_${new Date().toISOString().slice(0, 10)}.conll`;
            mimeType = 'text/plain';
        } else {
            content = this.currentDataset.map(sample => 
                `Text: ${sample.text}\nLabels: ${sample.labels.join(' ')}\n${'='.repeat(50)}\n`
            ).join('\n');
            filename = `ner_data_${new Date().toISOString().slice(0, 10)}.txt`;
            mimeType = 'text/plain';
        }
        
        const blob = new Blob([content], { type: mimeType });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = filename;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
    }
}

// 全局NER生成器实例
const frontendNERGenerator = new FrontendNERGenerator();
let nerChart = null;

// 重写NER管理函数以使用前端生成器
function generateNERData() {
    try {
        showNERLoading(true);
        
        const sampleCount = parseInt(document.getElementById('nerSampleCount')?.value || 1000);
        
        if (sampleCount < 100 || sampleCount > 10000) {
            showNotification('样本数量必须在100-10000之间', 'warning');
            return;
        }
        
        // 生成数据
        const dataset = frontendNERGenerator.generateDataset(sampleCount);
        const stats = frontendNERGenerator.currentStats;
        
        // 更新显示
        updateNERStatsDisplay(stats);
        updateNERPreview(dataset.slice(0, 5));
        updateNERChart();
        
        showNotification(`成功生成 ${sampleCount} 条NER训练数据！`, 'success');
        
    } catch (error) {
        console.error('生成数据失败:', error);
        showNotification(`生成失败: ${error.message}`, 'error');
    } finally {
        showNERLoading(false);
    }
}

function downloadNERData() {
    const format = document.getElementById('nerOutputFormat')?.value || 'json';
    frontendNERGenerator.exportData(format);
    showNotification('数据下载成功！', 'success');
}

function clearNERData() {
    if (!confirm('确定要清空所有NER数据吗？')) return;
    
    frontendNERGenerator.currentDataset = [];
    frontendNERGenerator.currentStats = {};
    
    updateNERStatsDisplay({});
    updateNERPreview([]);
    if (nerChart) {
        nerChart.data.datasets[0].data = [0, 0, 0, 0, 0];
        nerChart.update();
    }
    
    showNotification('数据已清空', 'success');
}

function updateNERStatsDisplay(stats) {
    document.getElementById('totalNERSamples').textContent = stats.total_samples || 0;
    
    const entityCounts = stats.entity_counts || {};
    document.getElementById('totalPersonEntities').textContent = entityCounts.PERSON || 0;
    document.getElementById('totalLocationEntities').textContent = entityCounts.LOCATION || 0;
    document.getElementById('totalOrgEntities').textContent = entityCounts.ORGANIZATION || 0;
}

function updateNERPreview(previewData) {
    const previewContainer = document.getElementById('nerDataPreview');
    if (!previewContainer) return;
    
    if (!previewData || previewData.length === 0) {
        previewContainer.innerHTML = `
            <div class="preview-placeholder">
                <i class="fas fa-database"></i>
                <p>暂无数据，请先生成NER训练数据</p>
            </div>
        `;
        return;
    }
    
    let html = '<div class="ner-samples">';
    previewData.forEach(sample => {
        html += `
            <div class="ner-sample">
                <div class="sample-header">
                    <span class="sample-id">样本 #${sample.id}</span>
                    <span class="sample-length">${sample.text.length} 字符</span>
                </div>
                <div class="sample-text">${highlightEntities(sample.text, sample.tokens, sample.labels)}</div>
                <div class="sample-stats">
                    <span class="entity-count">实体数: ${sample.labels.filter(l => l.startsWith('B-')).length}</span>
                </div>
            </div>
        `;
    });
    html += '</div>';
    previewContainer.innerHTML = html;
}

function highlightEntities(text, tokens, labels) {
    let result = '';
    let currentEntity = null;
    
    for (let i = 0; i < tokens.length; i++) {
        const token = tokens[i];
        const label = labels[i];
        
        if (label.startsWith('B-')) {
            if (currentEntity) result += '</span>';
            const entityType = label.substring(2).toLowerCase();
            result += `<span class="entity entity-${entityType}" title="${label}">`;
            currentEntity = entityType;
        } else if (label === 'O' && currentEntity) {
            result += '</span>';
            currentEntity = null;
        }
        
        result += token;
    }
    
    if (currentEntity) result += '</span>';
    return result;
}

function updateNERChart() {
    const ctx = document.getElementById('nerEntityChart');
    if (!ctx) return;
    
    if (nerChart) nerChart.destroy();
    
    const stats = frontendNERGenerator.currentStats;
    if (!stats.entity_counts) return;
    
    const chartType = document.getElementById('nerChartType')?.value || 'pie';
    const entityCounts = stats.entity_counts;
    
    nerChart = new Chart(ctx, {
        type: chartType,
        data: {
            labels: Object.keys(entityCounts),
            datasets: [{
                data: Object.values(entityCounts),
                backgroundColor: ['#FF6384', '#36A2EB', '#FFCE56', '#4BC0C0', '#9966FF']
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: { position: 'bottom' },
                title: { display: true, text: '实体类型分布' }
            }
        }
    });
}

function showNERLoading(show) {
    const generateBtn = document.querySelector('button[onclick="generateNERData()"]');
    if (generateBtn) {
        if (show) {
            generateBtn.disabled = true;
            generateBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 生成中...';
        } else {
            generateBtn.disabled = false;
            generateBtn.innerHTML = '<i class="fas fa-magic"></i> 生成数据';
        }
    }
}

function resetNERConfig() {
    document.getElementById('nerSampleCount').value = 1000;
    document.getElementById('enablePerson').checked = true;
    document.getElementById('enableLocation').checked = true;
    document.getElementById('enableOrganization').checked = true;
    document.getElementById('enableTime').checked = true;
    document.getElementById('enableNumber').checked = true;
    document.getElementById('nerOutputFormat').value = 'json';
    showNotification('配置已重置', 'info');
}

function refreshNERPreview() {
    const dataset = frontendNERGenerator.currentDataset;
    updateNERPreview(dataset.slice(0, 5));
    showNotification('预览已刷新', 'success');
}

function exportNERSample() {
    frontendNERGenerator.exportData('json');
}

// 初始化NER管理
function initializeNERManagement() {
    console.log('✅ 前端NER管理器已初始化');
    updateNERChart();
}

console.log('🧠 前端NER生成器已加载');
