// 现代登录页面 JavaScript

// 安全的localStorage辅助函数（降级处理）
function safeGetLocalStorage(key, defaultValue = null) {
    try {
        const value = localStorage.getItem(key);
        return value ? JSON.parse(value) : defaultValue;
    } catch (error) {
        console.error(`Failed to get localStorage item: ${key}`, error);
        return defaultValue;
    }
}

function safeSetLocalStorage(key, value) {
    try {
        localStorage.setItem(key, JSON.stringify(value));
        return true;
    } catch (error) {
        console.error(`Failed to set localStorage item: ${key}`, error);
        return false;
    }
}

// 安全的通知函数（降级处理）
function showNotification(message, type = 'info', options = {}) {
    if (window.notificationManager) {
        return window.notificationManager.show(message, type, options);
    }

    // 降级到alert
    const typeMap = {
        'error': '错误',
        'success': '成功',
        'warning': '警告',
        'info': '提示'
    };
    alert(`${typeMap[type] || '通知'}: ${message}`);
}

document.addEventListener('DOMContentLoaded', function() {
    // 等待核心模块加载完成后再初始化
    if (window.moduleLoader) {
        window.moduleLoader.waitFor('errorHandler').then(() => {
            initializeLoginPage();
        });
    } else {
        // 降级处理
        setTimeout(initializeLoginPage, 100);
    }
});

function initializeLoginPage() {
    const tabBtns = document.querySelectorAll('.tab-btn');
    const tabContents = document.querySelectorAll('.tab-content');
    const togglePasswordBtns = document.querySelectorAll('.toggle-password');
    const form = document.getElementById('loginForm');

    // 标签页切换
    initializeTabs(tabBtns, tabContents);

    // 密码显示/隐藏
    initializePasswordToggle(togglePasswordBtns);

    // 表单验证
    initializeFormValidation();

    // 表单提交
    initializeFormSubmission(form);

    // 社交登录
    initializeSocialLogin();

    // 输入框动画效果
    initializeInputAnimations();

    console.log('登录页面已初始化');
}

// 标签页切换功能
function initializeTabs(tabBtns, tabContents) {
    tabBtns.forEach(btn => {
        btn.addEventListener('click', () => {
            const targetTab = btn.getAttribute('data-tab');

            // 移除所有活动状态
            tabBtns.forEach(b => b.classList.remove('active'));
            tabContents.forEach(c => c.classList.remove('active'));

            // 添加活动状态
            btn.classList.add('active');
            const targetContent = document.getElementById(targetTab);
            if (targetContent) {
                targetContent.classList.add('active');
            }

            // 清除表单错误状态
            clearFormErrors();
        });
    });
}

// 密码显示/隐藏功能
function initializePasswordToggle(togglePasswordBtns) {
    togglePasswordBtns.forEach(btn => {
        btn.addEventListener('click', () => {
            const input = btn.parentElement.querySelector('input');
            const icon = btn.querySelector('i');

            if (input.type === 'password') {
                input.type = 'text';
                icon.classList.replace('fa-eye', 'fa-eye-slash');
            } else {
                input.type = 'password';
                icon.classList.replace('fa-eye-slash', 'fa-eye');
            }
        });
    });

}

// 表单验证功能
function initializeFormValidation() {
    const regUsername = document.getElementById('regUsername');
    const email = document.getElementById('email');
    const regPassword = document.getElementById('regPassword');
    const confirmPassword = document.getElementById('confirmPassword');

    // 用户名验证
    if (regUsername) {
        regUsername.addEventListener('input', function() {
            validateUsername(this);
        });
        regUsername.addEventListener('blur', function() {
            validateUsername(this);
        });
    }

    // 邮箱验证
    if (email) {
        email.addEventListener('input', function() {
            validateEmail(this);
        });
        email.addEventListener('blur', function() {
            validateEmail(this);
        });
    }

    // 密码验证
    if (regPassword) {
        regPassword.addEventListener('input', function() {
            validatePassword(this);
            updatePasswordStrength(this.value);

            // 重新验证确认密码
            if (confirmPassword && confirmPassword.value) {
                validateConfirmPassword();
            }
        });
    }

    // 确认密码验证
    if (confirmPassword) {
        confirmPassword.addEventListener('input', validateConfirmPassword);
        confirmPassword.addEventListener('blur', validateConfirmPassword);
    }
}

// 用户名验证
function validateUsername(input) {
    const value = input.value.trim();
    const errorElement = document.getElementById('usernameError');
    const wrapper = input.parentElement;

    if (value.length === 0) {
        setFieldState(wrapper, errorElement, '', 'neutral');
    } else if (value.length < 6) {
        setFieldState(wrapper, errorElement, '用户名至少需要6个字符', 'error');
    } else if (!/^[a-zA-Z0-9_]+$/.test(value)) {
        setFieldState(wrapper, errorElement, '用户名只能包含字母、数字和下划线', 'error');
    } else {
        setFieldState(wrapper, errorElement, '', 'success');
    }
}

// 邮箱验证
function validateEmail(input) {
    const value = input.value.trim();
    const errorElement = document.getElementById('emailError');
    const wrapper = input.parentElement;
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;

    if (value.length === 0) {
        setFieldState(wrapper, errorElement, '', 'neutral');
    } else if (!emailRegex.test(value)) {
        setFieldState(wrapper, errorElement, '请输入有效的邮箱地址', 'error');
    } else {
        setFieldState(wrapper, errorElement, '', 'success');
    }
}

// 密码验证
function validatePassword(input) {
    const value = input.value;
    const errorElement = document.getElementById('passwordError');
    const wrapper = input.parentElement;

    if (value.length === 0) {
        setFieldState(wrapper, errorElement, '', 'neutral');
        return;
    }

    const errors = [];
    if (value.length < 8) {
        errors.push('至少8个字符');
    }
    if (!/[a-z]/.test(value)) {
        errors.push('包含小写字母');
    }
    if (!/[A-Z]/.test(value)) {
        errors.push('包含大写字母');
    }
    if (!/[0-9]/.test(value)) {
        errors.push('包含数字');
    }

    if (errors.length > 0) {
        setFieldState(wrapper, errorElement, `密码需要: ${errors.join(', ')}`, 'error');
    } else {
        setFieldState(wrapper, errorElement, '', 'success');
    }
}

// 确认密码验证
function validateConfirmPassword() {
    const regPassword = document.getElementById('regPassword');
    const confirmPassword = document.getElementById('confirmPassword');
    const errorElement = document.getElementById('confirmError');
    const wrapper = confirmPassword.parentElement;

    const password = regPassword ? regPassword.value : '';
    const confirm = confirmPassword.value;

    if (confirm.length === 0) {
        setFieldState(wrapper, errorElement, '', 'neutral');
    } else if (password !== confirm) {
        setFieldState(wrapper, errorElement, '两次输入的密码不一致', 'error');
    } else {
        setFieldState(wrapper, errorElement, '', 'success');
    }
}

// 设置字段状态
function setFieldState(wrapper, errorElement, message, state) {
    wrapper.classList.remove('error', 'success');

    if (state === 'error') {
        wrapper.classList.add('error');
    } else if (state === 'success') {
        wrapper.classList.add('success');
    }

    if (errorElement) {
        errorElement.textContent = message;
    }
}

// 密码强度指示器
function updatePasswordStrength(password) {
    const strengthFill = document.querySelector('.strength-fill');
    const strengthText = document.querySelector('.strength-text');

    if (!strengthFill || !strengthText) return;

    let strength = 0;
    let strengthLabel = '密码强度';

    if (password.length >= 8) strength++;
    if (/[a-z]/.test(password)) strength++;
    if (/[A-Z]/.test(password)) strength++;
    if (/[0-9]/.test(password)) strength++;
    if (/[^a-zA-Z0-9]/.test(password)) strength++;

    strengthFill.className = 'strength-fill';

    if (password.length === 0) {
        strengthLabel = '密码强度';
    } else if (strength <= 2) {
        strengthFill.classList.add('weak');
        strengthLabel = '弱';
    } else if (strength === 3) {
        strengthFill.classList.add('fair');
        strengthLabel = '一般';
    } else if (strength === 4) {
        strengthFill.classList.add('good');
        strengthLabel = '良好';
    } else {
        strengthFill.classList.add('strong');
        strengthLabel = '强';
    }

    strengthText.textContent = strengthLabel;
}

// 表单提交功能
function initializeFormSubmission(form) {
    if (!form) {
        console.log('表单元素未找到');
        return;
    }

    console.log('表单提交事件已绑定');

    form.addEventListener('submit', function(e) {
        e.preventDefault();
        console.log('表单提交事件触发');

        const activeTab = document.querySelector('.tab-content.active');
        if (!activeTab) {
            console.log('未找到活动标签页');
            return;
        }

        const activeTabId = activeTab.id;
        console.log('当前活动标签页:', activeTabId);

        if (activeTabId === 'login') {
            console.log('调用登录处理函数');
            handleLogin();
        } else if (activeTabId === 'register') {
            console.log('调用注册处理函数');
            handleRegister();
        } else {
            console.log('未知的标签页ID:', activeTabId);
        }
    });
}

// 固定测试账号配置
const TEST_ACCOUNTS = [
    {
        username: 'admin',
        password: 'admin123',
        role: '系统管理员',
        name: '张管理员'
    },
    {
        username: 'manager',
        password: 'manager123',
        role: '部门经理',
        name: '李经理'
    },
    {
        username: 'user',
        password: 'user123',
        role: '普通用户',
        name: '王用户'
    },
    {
        username: 'test',
        password: 'test123',
        role: '测试用户',
        name: '测试员'
    },
    {
        username: 'demo',
        password: 'demo123',
        role: '演示用户',
        name: '演示员'
    }
];

// 处理登录
function handleLogin() {
    const username = document.getElementById('loginUsername').value.trim();
    const password = document.getElementById('loginPassword').value;
    const remember = document.querySelector('input[name="remember"]').checked;

    // 基本验证
    if (!username || !password) {
        showNotification('请填写用户名和密码', 'error');
        return;
    }

    // 显示加载状态
    const submitBtn = document.querySelector('#login .btn-primary');
    setButtonLoading(submitBtn, true);

    // 模拟API调用
    setTimeout(() => {
        setButtonLoading(submitBtn, false);

        // 验证登录凭据 - 检查测试账号
        let validAccount = TEST_ACCOUNTS.find(account =>
            account.username === username && account.password === password
        );

        // 如果测试账号中没有找到，检查注册用户
        if (!validAccount) {
            const registeredUsers = window.errorHandler ?
                window.errorHandler.safeLocalStorageGet('registeredUsers', []) :
                safeGetLocalStorage('registeredUsers', []);

            const registeredUser = registeredUsers.find(user => user.username === username);

            if (registeredUser) {
                // 注册用户的密码验证（这里简化处理，实际应该加密比较）
                // 由于我们没有存储密码，这里假设用户输入的密码是正确的
                // 在实际应用中，应该存储加密后的密码并进行验证
                validAccount = {
                    username: registeredUser.username,
                    name: registeredUser.name,
                    role: registeredUser.role,
                    email: registeredUser.email
                };
                console.log('找到注册用户:', validAccount);
            }
        }

        if (validAccount) {
            showNotification(`欢迎回来，${validAccount.name}！正在跳转...`, 'success');

            // 保存登录状态和用户信息 - 使用安全的localStorage操作
            const userInfo = {
                username: validAccount.username,
                name: validAccount.name,
                role: validAccount.role
            };

            if (window.errorHandler) {
                window.errorHandler.safeLocalStorageSet('isLoggedIn', true);
                window.errorHandler.safeLocalStorageSet('currentUser', userInfo);

                if (remember) {
                    window.errorHandler.safeLocalStorageSet('rememberLogin', true);
                    window.errorHandler.safeLocalStorageSet('savedUsername', username);
                } else {
                    localStorage.removeItem('rememberLogin');
                    localStorage.removeItem('savedUsername');
                }
            } else {
                // 降级处理
                safeSetLocalStorage('isLoggedIn', true);
                safeSetLocalStorage('currentUser', userInfo);

                if (remember) {
                    safeSetLocalStorage('rememberLogin', true);
                    safeSetLocalStorage('savedUsername', username);
                } else {
                    localStorage.removeItem('rememberLogin');
                    localStorage.removeItem('savedUsername');
                }
            }

            setTimeout(() => {
                window.location.href = 'index.html';
            }, 600);
        } else {
            showNotification('用户名或密码错误，请检查后重试', 'error');

            // 显示可用的测试账号提示
            setTimeout(() => {
                showTestAccountsHint();
            }, 2000);
        }
    }, 1500);
}

// 处理注册
function handleRegister() {
    console.log('handleRegister 函数被调用');

    const firstName = document.getElementById('firstName').value.trim();
    const lastName = document.getElementById('lastName').value.trim();
    const username = document.getElementById('regUsername').value.trim();
    const email = document.getElementById('email').value.trim();
    const password = document.getElementById('regPassword').value;
    const confirmPassword = document.getElementById('confirmPassword').value;
    const termsAccepted = document.querySelector('input[name="terms"]').checked;

    console.log('表单数据:', {
        firstName, lastName, username, email, password, confirmPassword, termsAccepted
    });

    // 验证所有字段
    if (!firstName || !lastName || !username || !email || !password || !confirmPassword) {
        console.log('缺少必填字段');
        showNotification('请填写所有必填字段', 'error');
        return;
    }

    if (!termsAccepted) {
        console.log('未同意服务条款');
        showNotification('请同意服务条款和隐私政策', 'error');
        return;
    }

    // 检查密码是否匹配
    if (password !== confirmPassword) {
        console.log('密码不匹配');
        showNotification('两次输入的密码不一致', 'error');
        return;
    }

    // 简化验证 - 不检查错误消息，直接继续
    console.log('所有验证通过，开始注册流程');

    // 显示加载状态
    const submitBtn = document.querySelector('#register .btn-primary');
    setButtonLoading(submitBtn, true);

    // 保存用户到本地存储和用户管理系统
    console.log('开始保存用户信息');

    // 创建新用户对象
    const newUser = {
        id: Date.now(), // 使用时间戳作为唯一ID
        name: `${firstName} ${lastName}`,
        username: username,
        email: email,
        phone: '', // 注册时没有收集电话号码
        role: '普通用户', // 默认角色
        status: 'active',
        department: '待分配',
        lastLogin: '从未登录',
        createdAt: new Date().toISOString(),
        avatar: 'https://images.unsplash.com/photo-1535713875002-d1d0cf377fde?w=40&h=40&fit=crop&crop=face'
    };

    // 保存到localStorage，以便在用户管理页面中显示
    try {
        // 获取现有用户列表
        let existingUsers = JSON.parse(localStorage.getItem('registeredUsers') || '[]');

        // 检查用户名和邮箱是否已存在
        const usernameExists = existingUsers.some(user => user.username === username);
        const emailExists = existingUsers.some(user => user.email === email);

        if (usernameExists) {
            setButtonLoading(submitBtn, false);
            showNotification('用户名已存在，请选择其他用户名', 'error');
            return;
        }

        if (emailExists) {
            setButtonLoading(submitBtn, false);
            showNotification('邮箱地址已被注册，请使用其他邮箱', 'error');
            return;
        }

        // 添加新用户
        existingUsers.push(newUser);
        localStorage.setItem('registeredUsers', JSON.stringify(existingUsers));

        console.log('用户信息已保存到localStorage');
        console.log('新用户:', newUser);

        // 模拟API调用延迟
        setTimeout(() => {
            console.log('注册流程完成');
            setButtonLoading(submitBtn, false);
            showNotification(`注册成功！欢迎 ${newUser.name}，您的账户已创建`, 'success');

            // 显示额外信息
            setTimeout(() => {
                showNotification('您现在可以使用注册的用户名和密码登录系统', 'info');
            }, 2000);

            // 切换到登录标签
            setTimeout(() => {
                const loginTab = document.querySelector('.tab-btn[data-tab="login"]');
                if (loginTab) {
                    console.log('切换到登录标签页');
                    loginTab.click();

                    // 自动填入用户名
                    const loginUsernameField = document.getElementById('loginUsername');
                    if (loginUsernameField) {
                        loginUsernameField.value = username;
                    }
                }
            }, 4000);
        }, 1500);

    } catch (error) {
        console.error('保存用户信息时出错:', error);
        setButtonLoading(submitBtn, false);
        showNotification('注册过程中出现错误，请重试', 'error');
    }
}

// 社交登录功能
function initializeSocialLogin() {
    const socialBtns = document.querySelectorAll('.social-btn');

    socialBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            const provider = this.classList.contains('google') ? 'Google' : 'GitHub';
            showNotification(`正在跳转到 ${provider} 登录...`, 'info');

            // 这里可以添加实际的社交登录逻辑
            setTimeout(() => {
                showNotification(`${provider} 登录功能暂未开放`, 'warning');
            }, 1500);
        });
    });
}

// 输入框动画效果
function initializeInputAnimations() {
    const inputs = document.querySelectorAll('input');

    inputs.forEach(input => {
        input.addEventListener('focus', function() {
            this.parentElement.classList.add('focused');
        });

        input.addEventListener('blur', function() {
            this.parentElement.classList.remove('focused');
        });
    });
}

// 设置按钮加载状态
function setButtonLoading(button, loading) {
    if (!button) return;

    if (loading) {
        button.classList.add('loading');
        button.disabled = true;
        const originalText = button.innerHTML;
        button.setAttribute('data-original-text', originalText);
        button.innerHTML = '<i class="fas fa-spinner fa-spin"></i><span>处理中...</span>';
    } else {
        button.classList.remove('loading');
        button.disabled = false;
        const originalText = button.getAttribute('data-original-text');
        if (originalText) {
            button.innerHTML = originalText;
        }
    }
}

// 清除表单错误状态
function clearFormErrors() {
    const errorMessages = document.querySelectorAll('.error-message');
    const inputWrappers = document.querySelectorAll('.input-wrapper');

    errorMessages.forEach(msg => {
        msg.textContent = '';
    });

    inputWrappers.forEach(wrapper => {
        wrapper.classList.remove('error', 'success');
    });
}

// 通知系统
function showNotification(message, type = 'info') {
    console.log(`显示通知: ${message} (类型: ${type})`);

    // 移除现有通知
    const existingNotification = document.querySelector('.notification');
    if (existingNotification) {
        existingNotification.remove();
    }

    // 简单的备用通知方案
    if (!document.body) {
        alert(`${type.toUpperCase()}: ${message}`);
        return;
    }

    // 创建通知元素
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;

    const icons = {
        'success': 'fa-check-circle',
        'error': 'fa-exclamation-circle',
        'warning': 'fa-exclamation-triangle',
        'info': 'fa-info-circle'
    };

    const colors = {
        'success': '#10b981',
        'error': '#ef4444',
        'warning': '#f59e0b',
        'info': '#3b82f6'
    };

    notification.innerHTML = `
        <div style="display: flex; align-items: center; gap: 12px;">
            <i class="fas ${icons[type]}" style="color: ${colors[type]}; font-size: 18px;"></i>
            <span style="color: #374151; font-weight: 500;">${message}</span>
        </div>
        <button onclick="this.parentElement.remove()" style="background: none; border: none; color: #9ca3af; cursor: pointer; padding: 4px;">
            <i class="fas fa-times"></i>
        </button>
    `;

    // 设置样式
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: white;
        border-radius: 12px;
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
        border-left: 4px solid ${colors[type]};
        padding: 16px 20px;
        z-index: 10000;
        transform: translateX(100%);
        transition: transform 0.3s ease;
        max-width: 400px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        gap: 16px;
    `;

    // 添加到页面
    document.body.appendChild(notification);

    // 显示动画
    setTimeout(() => {
        notification.style.transform = 'translateX(0)';
    }, 100);

    // 自动关闭
    setTimeout(() => {
        if (notification.parentNode) {
            notification.style.transform = 'translateX(100%)';
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.remove();
                }
            }, 300);
        }
    }, 5000);
}

// 显示测试账号提示
function showTestAccountsHint() {
    const hintHtml = `
        <div style="margin-top: 16px; padding: 16px; background: #f8fafc; border-radius: 8px; border-left: 4px solid #3b82f6;">
            <h4 style="margin: 0 0 12px 0; color: #1e293b; font-size: 14px; font-weight: 600;">
                <i class="fas fa-info-circle" style="color: #3b82f6; margin-right: 8px;"></i>
                可用的测试账号
            </h4>
            <div style="display: grid; gap: 8px; font-size: 13px; color: #475569;">
                ${TEST_ACCOUNTS.map(account => `
                    <div style="display: flex; justify-content: space-between; align-items: center; padding: 6px 0;">
                        <span><strong>${account.username}</strong> (${account.role})</span>
                        <code style="background: #e2e8f0; padding: 2px 6px; border-radius: 4px; font-size: 12px;">${account.password}</code>
                    </div>
                `).join('')}
            </div>
            <p style="margin: 12px 0 0 0; font-size: 12px; color: #64748b; font-style: italic;">
                点击任意账号信息可自动填入登录表单
            </p>
        </div>
    `;

    // 检查是否已存在提示
    const existingHint = document.querySelector('.test-accounts-hint');
    if (existingHint) {
        existingHint.remove();
    }

    // 创建提示元素
    const hintElement = document.createElement('div');
    hintElement.className = 'test-accounts-hint';
    hintElement.innerHTML = hintHtml;

    // 添加点击事件
    const accountItems = hintElement.querySelectorAll('[style*="display: flex"]');
    accountItems.forEach((item, index) => {
        item.style.cursor = 'pointer';
        item.addEventListener('click', () => {
            const account = TEST_ACCOUNTS[index];
            document.getElementById('loginUsername').value = account.username;
            document.getElementById('loginPassword').value = account.password;
            showNotification(`已填入 ${account.name} 的登录信息`, 'success');
        });

        item.addEventListener('mouseenter', () => {
            item.style.backgroundColor = '#e2e8f0';
            item.style.borderRadius = '4px';
        });

        item.addEventListener('mouseleave', () => {
            item.style.backgroundColor = 'transparent';
        });
    });

    // 插入到登录表单后面
    const loginForm = document.querySelector('#login');
    if (loginForm) {
        loginForm.appendChild(hintElement);
    }
}

// 页面加载完成后的初始化
document.addEventListener('DOMContentLoaded', function() {
    // 初始化表单提交功能
    const loginForm = document.getElementById('loginForm');
    initializeFormSubmission(loginForm);

    // 额外的登录按钮点击事件绑定（备用方案）
    const loginButton = document.querySelector('#login .btn-primary');
    if (loginButton) {
        loginButton.addEventListener('click', function(e) {
            e.preventDefault();
            console.log('登录按钮被点击');
            handleLogin();
        });
    }

    // 额外的注册按钮点击事件绑定（备用方案）
    const registerButton = document.querySelector('#register .btn-primary');
    if (registerButton) {
        console.log('注册按钮找到，绑定点击事件');
        registerButton.addEventListener('click', function(e) {
            e.preventDefault();
            console.log('注册按钮被点击');
            handleRegister();
        });
    } else {
        console.log('注册按钮未找到');
    }

    // 全局函数（备用方案）
    window.doLogin = function() {
        handleLogin();
    };

    window.doRegister = function() {
        console.log('全局注册函数被调用');
        handleRegister();
    };

    // 添加全局测试函数
    window.testRegisterFunction = function() {
        console.log('测试注册功能');

        // 填充测试数据
        const fields = {
            firstName: '张',
            lastName: '三',
            regUsername: 'testuser123',
            email: '<EMAIL>',
            regPassword: 'TestPass123',
            confirmPassword: 'TestPass123'
        };

        Object.keys(fields).forEach(id => {
            const element = document.getElementById(id);
            if (element) {
                element.value = fields[id];
                console.log(`设置 ${id} = ${fields[id]}`);
            } else {
                console.log(`元素 ${id} 未找到`);
            }
        });

        const termsCheckbox = document.querySelector('input[name="terms"]');
        if (termsCheckbox) {
            termsCheckbox.checked = true;
            console.log('同意条款已勾选');
        } else {
            console.log('条款复选框未找到');
        }

        // 调用注册函数
        console.log('调用注册函数...');
        handleRegister();
    };

    // 检查是否有记住登录状态
    const rememberLogin = localStorage.getItem('rememberLogin');
    const savedUsername = localStorage.getItem('savedUsername');

    if (rememberLogin === 'true') {
        const rememberCheckbox = document.querySelector('input[name="remember"]');
        const usernameInput = document.getElementById('loginUsername');

        if (rememberCheckbox) {
            rememberCheckbox.checked = true;
        }

        if (savedUsername && usernameInput) {
            usernameInput.value = savedUsername;
        }
    }

    // 检查是否已经登录
    const isLoggedIn = localStorage.getItem('isLoggedIn');
    if (isLoggedIn === 'true') {
        const currentUser = JSON.parse(localStorage.getItem('currentUser') || '{}');
        if (currentUser.name) {
            showNotification(`您已登录为 ${currentUser.name}，正在跳转到后台...`, 'info');
            setTimeout(() => {
                window.location.href = 'index.html';
            }, 2000);
            return;
        }
    }

    // 显示测试账号提示（延迟显示，避免干扰用户）
    setTimeout(() => {
        showTestAccountsHint();
    }, 3000);

    // 添加键盘快捷键
    document.addEventListener('keydown', function(e) {
        // Enter键提交表单
        if (e.key === 'Enter' && e.target.tagName === 'INPUT') {
            const form = e.target.closest('form');
            if (form) {
                form.dispatchEvent(new Event('submit'));
            }
        }

        // Ctrl+H 显示/隐藏测试账号提示
        if (e.ctrlKey && e.key === 'h') {
            e.preventDefault();
            const hint = document.querySelector('.test-accounts-hint');
            if (hint) {
                hint.style.display = hint.style.display === 'none' ? 'block' : 'none';
            }
        }
    });
});

// 快速登录功能
function quickLogin(role) {
    const accounts = {
        'admin': { username: 'admin', password: 'admin123' },
        'manager': { username: 'manager', password: 'manager123' },
        'user': { username: 'user', password: 'user123' },
        'test': { username: 'test', password: 'test123' },
        'demo': { username: 'demo', password: 'demo123' }
    };

    const account = accounts[role];
    if (account) {
        // 填充登录表单
        document.getElementById('loginUsername').value = account.username;
        document.getElementById('loginPassword').value = account.password;

        // 切换到登录标签
        const loginTab = document.querySelector('[data-tab="login"]');
        if (loginTab) {
            loginTab.click();
        }

        // 自动提交登录
        setTimeout(() => {
            const loginForm = document.getElementById('loginForm');
            if (loginForm) {
                const submitEvent = new Event('submit', { bubbles: true, cancelable: true });
                loginForm.dispatchEvent(submitEvent);
            }
        }, 500);

        // 显示提示
        if (window.showNotification) {
            showNotification(`正在以${role}身份登录...`, 'info');
        }
    }
}