<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>简单注册测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 50px auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input[type="text"], input[type="email"], input[type="password"] {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 16px;
            box-sizing: border-box;
        }
        .checkbox-group {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        .btn {
            background: #007bff;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin-right: 10px;
        }
        .btn:hover {
            background: #0056b3;
        }
        .btn-test {
            background: #28a745;
        }
        .btn-test:hover {
            background: #1e7e34;
        }
        .status {
            margin-top: 20px;
            padding: 15px;
            border-radius: 5px;
            font-weight: bold;
        }
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            margin-top: 20px;
            font-family: monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>简单注册功能测试</h1>
        <p>这是一个简化的注册测试页面，用于排除干扰因素。</p>
        
        <form id="registerForm">
            <div class="form-group">
                <label for="firstName">姓名 *</label>
                <input type="text" id="firstName" name="firstName" required>
            </div>
            
            <div class="form-group">
                <label for="lastName">姓氏 *</label>
                <input type="text" id="lastName" name="lastName" required>
            </div>
            
            <div class="form-group">
                <label for="regUsername">用户名 *</label>
                <input type="text" id="regUsername" name="regUsername" required>
            </div>
            
            <div class="form-group">
                <label for="email">邮箱地址 *</label>
                <input type="email" id="email" name="email" required>
            </div>
            
            <div class="form-group">
                <label for="regPassword">密码 *</label>
                <input type="password" id="regPassword" name="regPassword" required>
            </div>
            
            <div class="form-group">
                <label for="confirmPassword">确认密码 *</label>
                <input type="password" id="confirmPassword" name="confirmPassword" required>
            </div>
            
            <div class="form-group">
                <div class="checkbox-group">
                    <input type="checkbox" id="terms" name="terms" required>
                    <label for="terms">我同意服务条款和隐私政策 *</label>
                </div>
            </div>
            
            <button type="submit" class="btn">注册账户</button>
            <button type="button" class="btn btn-test" onclick="fillTestData()">填充测试数据</button>
            <button type="button" class="btn btn-test" onclick="testRegisterDirect()">直接测试注册</button>
        </form>
        
        <div id="status" class="status" style="display: none;"></div>
        <div id="log" class="log"></div>
    </div>

    <script>
        // 日志系统
        function addLog(message) {
            const log = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            log.innerHTML += `[${timestamp}] ${message}<br>`;
            log.scrollTop = log.scrollHeight;
            console.log(message);
        }
        
        // 显示状态
        function showStatus(message, type = 'info') {
            const status = document.getElementById('status');
            status.textContent = message;
            status.className = `status ${type}`;
            status.style.display = 'block';
            addLog(`状态: ${message} (${type})`);
        }
        
        // 填充测试数据
        function fillTestData() {
            document.getElementById('firstName').value = '张';
            document.getElementById('lastName').value = '三';
            document.getElementById('regUsername').value = 'testuser123';
            document.getElementById('email').value = '<EMAIL>';
            document.getElementById('regPassword').value = 'TestPass123';
            document.getElementById('confirmPassword').value = 'TestPass123';
            document.getElementById('terms').checked = true;
            addLog('测试数据已填充');
            showStatus('测试数据已填充', 'info');
        }
        
        // 简化的注册函数
        function handleRegister() {
            addLog('开始处理注册...');
            
            const firstName = document.getElementById('firstName').value.trim();
            const lastName = document.getElementById('lastName').value.trim();
            const username = document.getElementById('regUsername').value.trim();
            const email = document.getElementById('email').value.trim();
            const password = document.getElementById('regPassword').value;
            const confirmPassword = document.getElementById('confirmPassword').value;
            const termsAccepted = document.getElementById('terms').checked;
            
            addLog(`表单数据: 姓名=${firstName}, 姓氏=${lastName}, 用户名=${username}, 邮箱=${email}, 密码长度=${password.length}, 确认密码长度=${confirmPassword.length}, 同意条款=${termsAccepted}`);
            
            // 验证
            if (!firstName || !lastName || !username || !email || !password || !confirmPassword) {
                showStatus('请填写所有必填字段', 'error');
                return false;
            }
            
            if (!termsAccepted) {
                showStatus('请同意服务条款和隐私政策', 'error');
                return false;
            }
            
            if (password !== confirmPassword) {
                showStatus('两次输入的密码不一致', 'error');
                return false;
            }
            
            if (password.length < 6) {
                showStatus('密码至少需要6个字符', 'error');
                return false;
            }
            
            // 模拟注册成功
            addLog('验证通过，开始注册...');
            showStatus('正在注册...', 'info');
            
            setTimeout(() => {
                showStatus('注册成功！欢迎加入我们！', 'success');
                addLog('注册完成');
            }, 1000);
            
            return true;
        }
        
        // 直接测试注册
        function testRegisterDirect() {
            addLog('=== 直接测试注册功能 ===');
            fillTestData();
            setTimeout(() => {
                handleRegister();
            }, 100);
        }
        
        // 表单提交事件
        document.getElementById('registerForm').addEventListener('submit', function(e) {
            e.preventDefault();
            addLog('表单提交事件触发');
            handleRegister();
        });
        
        // 页面加载完成
        document.addEventListener('DOMContentLoaded', function() {
            addLog('页面加载完成');
            addLog('注册测试页面已准备就绪');
            showStatus('页面已加载，可以开始测试', 'info');
        });
    </script>
</body>
</html>
