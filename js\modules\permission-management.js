// 高级权限管理系统
class PermissionManagement {
    constructor() {
        this.currentUser = null;
        this.roles = {};
        this.permissions = {};
        this.userPermissions = {};
        this.permissionGroups = {};
        this.auditLog = [];
        
        this.initializePermissionSystem();
        this.loadDefaultRoles();
        this.loadCurrentUser();
    }

    initializePermissionSystem() {
        this.createPermissionInterface();
        this.bindPermissionEvents();
        this.setupPermissionChecks();
        this.loadStoredPermissions();
    }

    createPermissionInterface() {
        const permissionPanel = document.createElement('div');
        permissionPanel.id = 'permissionPanel';
        permissionPanel.className = 'permission-panel';
        permissionPanel.innerHTML = `
            <div class="permission-header">
                <h3>
                    <i class="fas fa-shield-alt"></i>
                    权限管理
                </h3>
                <div class="permission-controls">
                    <button class="btn-secondary" onclick="permissionManager.exportPermissions()">
                        <i class="fas fa-download"></i>
                        导出配置
                    </button>
                    <button class="btn-primary" onclick="permissionManager.createRole()">
                        <i class="fas fa-plus"></i>
                        新建角色
                    </button>
                    <button class="btn-icon" onclick="permissionManager.closePermissionPanel()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            </div>
            
            <div class="permission-body">
                <div class="permission-tabs">
                    <button class="tab-btn active" data-tab="roles">
                        <i class="fas fa-users-cog"></i>
                        角色管理
                    </button>
                    <button class="tab-btn" data-tab="permissions">
                        <i class="fas fa-key"></i>
                        权限配置
                    </button>
                    <button class="tab-btn" data-tab="users">
                        <i class="fas fa-user-shield"></i>
                        用户权限
                    </button>
                    <button class="tab-btn" data-tab="audit">
                        <i class="fas fa-history"></i>
                        审计日志
                    </button>
                </div>
                
                <div class="permission-content">
                    <!-- 角色管理 -->
                    <div class="tab-content active" id="rolesTab">
                        <div class="roles-section">
                            <div class="section-header">
                                <h4>系统角色</h4>
                                <div class="role-filters">
                                    <select id="roleStatusFilter">
                                        <option value="all">所有状态</option>
                                        <option value="active">启用</option>
                                        <option value="inactive">禁用</option>
                                    </select>
                                    <input type="text" id="roleSearchInput" placeholder="搜索角色...">
                                </div>
                            </div>
                            <div class="roles-grid" id="rolesGrid">
                                <!-- 角色卡片 -->
                            </div>
                        </div>
                    </div>
                    
                    <!-- 权限配置 -->
                    <div class="tab-content" id="permissionsTab">
                        <div class="permissions-section">
                            <div class="section-header">
                                <h4>权限配置</h4>
                                <div class="permission-actions">
                                    <button class="btn-secondary" onclick="permissionManager.addPermissionGroup()">
                                        <i class="fas fa-plus"></i>
                                        添加权限组
                                    </button>
                                    <button class="btn-secondary" onclick="permissionManager.resetPermissions()">
                                        <i class="fas fa-undo"></i>
                                        重置默认
                                    </button>
                                </div>
                            </div>
                            <div class="permissions-tree" id="permissionsTree">
                                <!-- 权限树 -->
                            </div>
                        </div>
                    </div>
                    
                    <!-- 用户权限 -->
                    <div class="tab-content" id="usersTab">
                        <div class="users-section">
                            <div class="section-header">
                                <h4>用户权限分配</h4>
                                <div class="user-filters">
                                    <select id="userRoleFilter">
                                        <option value="all">所有角色</option>
                                    </select>
                                    <select id="userDepartmentFilter">
                                        <option value="all">所有部门</option>
                                        <option value="tech">技术部</option>
                                        <option value="sales">销售部</option>
                                        <option value="hr">人事部</option>
                                        <option value="finance">财务部</option>
                                    </select>
                                </div>
                            </div>
                            <div class="users-table-container">
                                <table class="users-permission-table" id="usersPermissionTable">
                                    <thead>
                                        <tr>
                                            <th>用户</th>
                                            <th>角色</th>
                                            <th>部门</th>
                                            <th>权限状态</th>
                                            <th>最后登录</th>
                                            <th>操作</th>
                                        </tr>
                                    </thead>
                                    <tbody id="usersPermissionTableBody">
                                        <!-- 用户权限列表 -->
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 审计日志 -->
                    <div class="tab-content" id="auditTab">
                        <div class="audit-section">
                            <div class="section-header">
                                <h4>权限审计日志</h4>
                                <div class="audit-filters">
                                    <select id="auditActionFilter">
                                        <option value="all">所有操作</option>
                                        <option value="login">登录</option>
                                        <option value="permission_change">权限变更</option>
                                        <option value="role_change">角色变更</option>
                                        <option value="access_denied">访问拒绝</option>
                                    </select>
                                    <input type="date" id="auditDateFilter">
                                    <button class="btn-secondary" onclick="permissionManager.exportAuditLog()">
                                        <i class="fas fa-download"></i>
                                        导出日志
                                    </button>
                                </div>
                            </div>
                            <div class="audit-timeline" id="auditTimeline">
                                <!-- 审计日志时间线 -->
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
        
        document.body.appendChild(permissionPanel);
        
        // 创建角色编辑器
        this.createRoleEditor();
        
        // 创建权限分配器
        this.createPermissionAssigner();
    }

    createRoleEditor() {
        const roleEditor = document.createElement('div');
        roleEditor.id = 'roleEditor';
        roleEditor.className = 'role-editor-modal';
        roleEditor.innerHTML = `
            <div class="modal-content">
                <div class="modal-header">
                    <h3 id="roleEditorTitle">编辑角色</h3>
                    <button class="modal-close" onclick="permissionManager.closeRoleEditor()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="modal-body">
                    <div class="role-form">
                        <div class="form-group">
                            <label>角色名称</label>
                            <input type="text" id="roleName" placeholder="输入角色名称">
                        </div>
                        <div class="form-group">
                            <label>角色描述</label>
                            <textarea id="roleDescription" placeholder="描述角色职责和权限范围"></textarea>
                        </div>
                        <div class="form-group">
                            <label>角色级别</label>
                            <select id="roleLevel">
                                <option value="1">1级 - 超级管理员</option>
                                <option value="2">2级 - 系统管理员</option>
                                <option value="3">3级 - 部门管理员</option>
                                <option value="4">4级 - 普通用户</option>
                                <option value="5">5级 - 访客用户</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label>角色状态</label>
                            <div class="radio-group">
                                <label class="radio-label">
                                    <input type="radio" name="roleStatus" value="active" checked>
                                    <span class="radio-mark"></span>
                                    启用
                                </label>
                                <label class="radio-label">
                                    <input type="radio" name="roleStatus" value="inactive">
                                    <span class="radio-mark"></span>
                                    禁用
                                </label>
                            </div>
                        </div>
                        <div class="form-group">
                            <label>权限配置</label>
                            <div class="permission-selector" id="permissionSelector">
                                <!-- 权限选择器 -->
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button class="btn-secondary" onclick="permissionManager.closeRoleEditor()">取消</button>
                    <button class="btn-primary" onclick="permissionManager.saveRole()">保存角色</button>
                </div>
            </div>
        `;
        
        document.body.appendChild(roleEditor);
    }

    createPermissionAssigner() {
        const permissionAssigner = document.createElement('div');
        permissionAssigner.id = 'permissionAssigner';
        permissionAssigner.className = 'permission-assigner-modal';
        permissionAssigner.innerHTML = `
            <div class="modal-content">
                <div class="modal-header">
                    <h3>分配用户权限</h3>
                    <button class="modal-close" onclick="permissionManager.closePermissionAssigner()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="modal-body">
                    <div class="user-info" id="assignerUserInfo">
                        <!-- 用户信息 -->
                    </div>
                    <div class="permission-assignment">
                        <div class="assignment-tabs">
                            <button class="tab-btn active" data-tab="role">角色分配</button>
                            <button class="tab-btn" data-tab="custom">自定义权限</button>
                        </div>
                        <div class="assignment-content">
                            <div class="tab-content active" id="roleAssignmentTab">
                                <div class="role-selection" id="roleSelection">
                                    <!-- 角色选择 -->
                                </div>
                            </div>
                            <div class="tab-content" id="customPermissionTab">
                                <div class="custom-permissions" id="customPermissions">
                                    <!-- 自定义权限 -->
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button class="btn-secondary" onclick="permissionManager.closePermissionAssigner()">取消</button>
                    <button class="btn-primary" onclick="permissionManager.saveUserPermissions()">保存权限</button>
                </div>
            </div>
        `;
        
        document.body.appendChild(permissionAssigner);
    }

    loadDefaultRoles() {
        this.roles = {
            'super_admin': {
                id: 'super_admin',
                name: '超级管理员',
                description: '拥有系统所有权限，可以管理所有功能和用户',
                level: 1,
                status: 'active',
                permissions: ['*'], // 所有权限
                color: '#ef4444',
                icon: 'fas fa-crown'
            },
            'admin': {
                id: 'admin',
                name: '系统管理员',
                description: '管理系统配置、用户和大部分功能',
                level: 2,
                status: 'active',
                permissions: [
                    'user.manage', 'role.manage', 'system.config',
                    'data.export', 'audit.view', 'workflow.manage'
                ],
                color: '#f59e0b',
                icon: 'fas fa-user-shield'
            },
            'manager': {
                id: 'manager',
                name: '部门经理',
                description: '管理部门用户和业务数据',
                level: 3,
                status: 'active',
                permissions: [
                    'user.view', 'user.edit', 'data.view', 'data.export',
                    'order.manage', 'report.generate'
                ],
                color: '#10b981',
                icon: 'fas fa-users'
            },
            'user': {
                id: 'user',
                name: '普通用户',
                description: '基本的系统使用权限',
                level: 4,
                status: 'active',
                permissions: [
                    'data.view', 'profile.edit', 'file.upload',
                    'notification.view'
                ],
                color: '#3b82f6',
                icon: 'fas fa-user'
            },
            'guest': {
                id: 'guest',
                name: '访客用户',
                description: '只读权限，无法修改数据',
                level: 5,
                status: 'active',
                permissions: ['data.view', 'profile.view'],
                color: '#6b7280',
                icon: 'fas fa-eye'
            }
        };
        
        // 定义权限组
        this.permissionGroups = {
            'user': {
                name: '用户管理',
                icon: 'fas fa-users',
                permissions: {
                    'user.view': '查看用户',
                    'user.create': '创建用户',
                    'user.edit': '编辑用户',
                    'user.delete': '删除用户',
                    'user.manage': '管理用户'
                }
            },
            'role': {
                name: '角色管理',
                icon: 'fas fa-user-tag',
                permissions: {
                    'role.view': '查看角色',
                    'role.create': '创建角色',
                    'role.edit': '编辑角色',
                    'role.delete': '删除角色',
                    'role.manage': '管理角色'
                }
            },
            'data': {
                name: '数据管理',
                icon: 'fas fa-database',
                permissions: {
                    'data.view': '查看数据',
                    'data.edit': '编辑数据',
                    'data.delete': '删除数据',
                    'data.export': '导出数据',
                    'data.import': '导入数据'
                }
            },
            'order': {
                name: '订单管理',
                icon: 'fas fa-shopping-cart',
                permissions: {
                    'order.view': '查看订单',
                    'order.create': '创建订单',
                    'order.edit': '编辑订单',
                    'order.delete': '删除订单',
                    'order.manage': '管理订单'
                }
            },
            'system': {
                name: '系统管理',
                icon: 'fas fa-cogs',
                permissions: {
                    'system.config': '系统配置',
                    'system.monitor': '系统监控',
                    'system.backup': '系统备份',
                    'system.log': '系统日志'
                }
            },
            'workflow': {
                name: '工作流管理',
                icon: 'fas fa-project-diagram',
                permissions: {
                    'workflow.view': '查看工作流',
                    'workflow.create': '创建工作流',
                    'workflow.edit': '编辑工作流',
                    'workflow.delete': '删除工作流',
                    'workflow.manage': '管理工作流'
                }
            },
            'file': {
                name: '文件管理',
                icon: 'fas fa-folder',
                permissions: {
                    'file.view': '查看文件',
                    'file.upload': '上传文件',
                    'file.download': '下载文件',
                    'file.delete': '删除文件'
                }
            },
            'report': {
                name: '报表管理',
                icon: 'fas fa-chart-bar',
                permissions: {
                    'report.view': '查看报表',
                    'report.generate': '生成报表',
                    'report.export': '导出报表'
                }
            },
            'audit': {
                name: '审计管理',
                icon: 'fas fa-history',
                permissions: {
                    'audit.view': '查看审计日志',
                    'audit.export': '导出审计日志'
                }
            },
            'notification': {
                name: '通知管理',
                icon: 'fas fa-bell',
                permissions: {
                    'notification.view': '查看通知',
                    'notification.send': '发送通知',
                    'notification.manage': '管理通知'
                }
            },
            'profile': {
                name: '个人资料',
                icon: 'fas fa-user-circle',
                permissions: {
                    'profile.view': '查看个人资料',
                    'profile.edit': '编辑个人资料'
                }
            }
        };
        
        this.renderRoles();
        this.renderPermissionsTree();
    }

    loadCurrentUser() {
        // 从localStorage或API获取当前用户信息
        const storedUser = localStorage.getItem('currentUser');
        if (storedUser) {
            this.currentUser = JSON.parse(storedUser);
        } else {
            // 默认用户（用于演示）
            this.currentUser = {
                id: 'admin',
                username: 'admin',
                name: '系统管理员',
                role: 'admin',
                permissions: this.roles.admin.permissions,
                department: 'tech',
                lastLogin: new Date()
            };
        }
        
        this.applyUserPermissions();
    }

    bindPermissionEvents() {
        // 标签切换
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('tab-btn') && e.target.closest('.permission-panel')) {
                this.switchPermissionTab(e.target);
            }
        });
        
        // 角色筛选
        document.getElementById('roleStatusFilter')?.addEventListener('change', () => {
            this.filterRoles();
        });
        
        document.getElementById('roleSearchInput')?.addEventListener('input', () => {
            this.filterRoles();
        });
        
        // 用户筛选
        document.getElementById('userRoleFilter')?.addEventListener('change', () => {
            this.filterUsers();
        });
        
        document.getElementById('userDepartmentFilter')?.addEventListener('change', () => {
            this.filterUsers();
        });
        
        // 审计日志筛选
        document.getElementById('auditActionFilter')?.addEventListener('change', () => {
            this.filterAuditLog();
        });
        
        document.getElementById('auditDateFilter')?.addEventListener('change', () => {
            this.filterAuditLog();
        });
    }

    setupPermissionChecks() {
        // 设置权限检查
        this.checkPagePermissions();
        this.hideUnauthorizedElements();
        
        // 监听页面变化
        const observer = new MutationObserver(() => {
            this.hideUnauthorizedElements();
        });
        
        observer.observe(document.body, { childList: true, subtree: true });
    }

    checkPagePermissions() {
        const currentPage = document.querySelector('.page.active')?.id;
        const requiredPermission = this.getPagePermission(currentPage);
        
        if (requiredPermission && !this.hasPermission(requiredPermission)) {
            this.showAccessDenied(currentPage);
            this.logAuditEvent('access_denied', `尝试访问未授权页面: ${currentPage}`);
        }
    }

    getPagePermission(page) {
        const pagePermissions = {
            'users': 'user.view',
            'orders': 'order.view',
            'analytics': 'data.view',
            'files': 'file.view',
            'ner': 'data.view'
        };
        
        return pagePermissions[page];
    }

    hasPermission(permission) {
        if (!this.currentUser || !this.currentUser.permissions) {
            return false;
        }
        
        // 超级管理员拥有所有权限
        if (this.currentUser.permissions.includes('*')) {
            return true;
        }
        
        // 检查具体权限
        return this.currentUser.permissions.includes(permission);
    }

    hideUnauthorizedElements() {
        // 隐藏没有权限的元素
        const protectedElements = document.querySelectorAll('[data-permission]');
        
        protectedElements.forEach(element => {
            const requiredPermission = element.dataset.permission;
            if (!this.hasPermission(requiredPermission)) {
                element.style.display = 'none';
            } else {
                element.style.display = '';
            }
        });
    }

    showAccessDenied(page) {
        const accessDeniedModal = document.createElement('div');
        accessDeniedModal.className = 'modal access-denied-modal';
        accessDeniedModal.innerHTML = `
            <div class="modal-content">
                <div class="modal-header">
                    <h3>访问被拒绝</h3>
                </div>
                <div class="modal-body">
                    <div class="access-denied-content">
                        <div class="access-denied-icon">
                            <i class="fas fa-shield-alt"></i>
                        </div>
                        <h4>权限不足</h4>
                        <p>您没有访问"${page}"页面的权限。</p>
                        <p>如需访问此功能，请联系系统管理员。</p>
                    </div>
                </div>
                <div class="modal-footer">
                    <button class="btn-primary" onclick="this.closest('.modal').remove(); permissionManager.navigateToAuthorizedPage()">
                        返回首页
                    </button>
                </div>
            </div>
        `;
        
        document.body.appendChild(accessDeniedModal);
        accessDeniedModal.style.display = 'flex';
    }

    navigateToAuthorizedPage() {
        // 导航到有权限的页面
        const dashboardBtn = document.querySelector('[data-page="dashboard"]');
        if (dashboardBtn) {
            dashboardBtn.click();
        }
    }

    switchPermissionTab(tabBtn) {
        const container = tabBtn.closest('.permission-panel');
        const tabName = tabBtn.dataset.tab;
        
        // 更新标签状态
        container.querySelectorAll('.tab-btn').forEach(btn => btn.classList.remove('active'));
        container.querySelectorAll('.tab-content').forEach(content => content.classList.remove('active'));
        
        tabBtn.classList.add('active');
        container.querySelector(`#${tabName}Tab`).classList.add('active');
        
        // 加载对应内容
        this.loadTabContent(tabName);
    }

    loadTabContent(tabName) {
        switch (tabName) {
            case 'roles':
                this.renderRoles();
                break;
            case 'permissions':
                this.renderPermissionsTree();
                break;
            case 'users':
                this.renderUsersPermissions();
                break;
            case 'audit':
                this.renderAuditLog();
                break;
        }
    }

    renderRoles() {
        const rolesGrid = document.getElementById('rolesGrid');
        if (!rolesGrid) return;
        
        rolesGrid.innerHTML = Object.values(this.roles).map(role => `
            <div class="role-card ${role.status}" data-role="${role.id}">
                <div class="role-header">
                    <div class="role-icon" style="background: ${role.color}">
                        <i class="${role.icon}"></i>
                    </div>
                    <div class="role-info">
                        <h5>${role.name}</h5>
                        <span class="role-level">级别 ${role.level}</span>
                    </div>
                    <div class="role-status ${role.status}">
                        ${role.status === 'active' ? '启用' : '禁用'}
                    </div>
                </div>
                <div class="role-description">
                    ${role.description}
                </div>
                <div class="role-permissions">
                    <span class="permission-count">${role.permissions.length} 个权限</span>
                    <div class="permission-preview">
                        ${role.permissions.slice(0, 3).map(p => `<span class="permission-tag">${p}</span>`).join('')}
                        ${role.permissions.length > 3 ? '<span class="permission-more">...</span>' : ''}
                    </div>
                </div>
                <div class="role-actions">
                    <button class="btn-icon" onclick="permissionManager.editRole('${role.id}')" title="编辑">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="btn-icon" onclick="permissionManager.duplicateRole('${role.id}')" title="复制">
                        <i class="fas fa-copy"></i>
                    </button>
                    <button class="btn-icon" onclick="permissionManager.deleteRole('${role.id}')" title="删除">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </div>
        `).join('');
    }

    renderPermissionsTree() {
        const permissionsTree = document.getElementById('permissionsTree');
        if (!permissionsTree) return;
        
        permissionsTree.innerHTML = Object.entries(this.permissionGroups).map(([groupKey, group]) => `
            <div class="permission-group">
                <div class="group-header" onclick="permissionManager.togglePermissionGroup('${groupKey}')">
                    <i class="${group.icon}"></i>
                    <span class="group-name">${group.name}</span>
                    <i class="fas fa-chevron-down toggle-icon"></i>
                </div>
                <div class="group-permissions" id="group-${groupKey}">
                    ${Object.entries(group.permissions).map(([permKey, permName]) => `
                        <div class="permission-item">
                            <div class="permission-info">
                                <span class="permission-name">${permName}</span>
                                <span class="permission-key">${permKey}</span>
                            </div>
                            <div class="permission-roles">
                                ${this.getRolesWithPermission(permKey).map(role => `
                                    <span class="role-badge" style="background: ${role.color}">${role.name}</span>
                                `).join('')}
                            </div>
                        </div>
                    `).join('')}
                </div>
            </div>
        `).join('');
    }

    getRolesWithPermission(permission) {
        return Object.values(this.roles).filter(role => 
            role.permissions.includes(permission) || role.permissions.includes('*')
        );
    }

    renderUsersPermissions() {
        // 模拟用户数据
        const users = [
            {
                id: 'admin',
                username: 'admin',
                name: '系统管理员',
                role: 'admin',
                department: 'tech',
                status: 'active',
                lastLogin: new Date(Date.now() - 2 * 60 * 60 * 1000)
            },
            {
                id: 'manager1',
                username: 'manager',
                name: '李经理',
                role: 'manager',
                department: 'sales',
                status: 'active',
                lastLogin: new Date(Date.now() - 4 * 60 * 60 * 1000)
            },
            {
                id: 'user1',
                username: 'user',
                name: '王用户',
                role: 'user',
                department: 'sales',
                status: 'active',
                lastLogin: new Date(Date.now() - 24 * 60 * 60 * 1000)
            }
        ];
        
        const tableBody = document.getElementById('usersPermissionTableBody');
        if (!tableBody) return;
        
        tableBody.innerHTML = users.map(user => {
            const role = this.roles[user.role];
            return `
                <tr data-user="${user.id}">
                    <td>
                        <div class="user-info">
                            <div class="user-avatar">
                                <i class="fas fa-user"></i>
                            </div>
                            <div class="user-details">
                                <span class="user-name">${user.name}</span>
                                <span class="user-username">@${user.username}</span>
                            </div>
                        </div>
                    </td>
                    <td>
                        <span class="role-badge" style="background: ${role?.color || '#6b7280'}">
                            ${role?.name || '未分配'}
                        </span>
                    </td>
                    <td>${this.getDepartmentName(user.department)}</td>
                    <td>
                        <span class="status-badge ${user.status}">
                            ${user.status === 'active' ? '正常' : '禁用'}
                        </span>
                    </td>
                    <td>${this.formatLastLogin(user.lastLogin)}</td>
                    <td>
                        <div class="action-buttons">
                            <button class="btn-icon" onclick="permissionManager.assignUserPermissions('${user.id}')" title="分配权限">
                                <i class="fas fa-key"></i>
                            </button>
                            <button class="btn-icon" onclick="permissionManager.viewUserPermissions('${user.id}')" title="查看权限">
                                <i class="fas fa-eye"></i>
                            </button>
                        </div>
                    </td>
                </tr>
            `;
        }).join('');
    }

    getDepartmentName(dept) {
        const departments = {
            'tech': '技术部',
            'sales': '销售部',
            'hr': '人事部',
            'finance': '财务部'
        };
        return departments[dept] || dept;
    }

    formatLastLogin(date) {
        const now = new Date();
        const diff = now - date;
        const hours = Math.floor(diff / (1000 * 60 * 60));
        
        if (hours < 1) return '刚刚';
        if (hours < 24) return `${hours}小时前`;
        const days = Math.floor(hours / 24);
        return `${days}天前`;
    }

    renderAuditLog() {
        // 模拟审计日志数据
        const auditEvents = [
            {
                id: 1,
                action: 'login',
                user: '系统管理员',
                description: '用户登录系统',
                timestamp: new Date(Date.now() - 30 * 60 * 1000),
                ip: '*************',
                result: 'success'
            },
            {
                id: 2,
                action: 'permission_change',
                user: '系统管理员',
                description: '修改用户权限：王用户',
                timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000),
                ip: '*************',
                result: 'success'
            },
            {
                id: 3,
                action: 'access_denied',
                user: '王用户',
                description: '尝试访问用户管理页面',
                timestamp: new Date(Date.now() - 4 * 60 * 60 * 1000),
                ip: '*************',
                result: 'denied'
            }
        ];
        
        const auditTimeline = document.getElementById('auditTimeline');
        if (!auditTimeline) return;
        
        auditTimeline.innerHTML = auditEvents.map(event => `
            <div class="audit-event ${event.result}">
                <div class="event-icon">
                    <i class="fas fa-${this.getAuditIcon(event.action)}"></i>
                </div>
                <div class="event-content">
                    <div class="event-header">
                        <span class="event-action">${this.getActionLabel(event.action)}</span>
                        <span class="event-time">${this.formatAuditTime(event.timestamp)}</span>
                    </div>
                    <div class="event-description">${event.description}</div>
                    <div class="event-meta">
                        <span class="event-user">用户: ${event.user}</span>
                        <span class="event-ip">IP: ${event.ip}</span>
                        <span class="event-result ${event.result}">${event.result === 'success' ? '成功' : '失败'}</span>
                    </div>
                </div>
            </div>
        `).join('');
    }

    getAuditIcon(action) {
        const icons = {
            'login': 'sign-in-alt',
            'permission_change': 'key',
            'role_change': 'user-tag',
            'access_denied': 'ban'
        };
        return icons[action] || 'info-circle';
    }

    getActionLabel(action) {
        const labels = {
            'login': '用户登录',
            'permission_change': '权限变更',
            'role_change': '角色变更',
            'access_denied': '访问拒绝'
        };
        return labels[action] || action;
    }

    formatAuditTime(date) {
        return date.toLocaleString('zh-CN', {
            month: 'short',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        });
    }

    // 权限管理操作方法
    showPermissionPanel() {
        const panel = document.getElementById('permissionPanel');
        if (panel) {
            panel.classList.add('show');
        }
    }

    closePermissionPanel() {
        const panel = document.getElementById('permissionPanel');
        if (panel) {
            panel.classList.remove('show');
        }
    }

    createRole() {
        this.showRoleEditor();
    }

    editRole(roleId) {
        const role = this.roles[roleId];
        if (!role) return;
        
        this.showRoleEditor(role);
    }

    showRoleEditor(role = null) {
        const editor = document.getElementById('roleEditor');
        const title = document.getElementById('roleEditorTitle');
        
        if (role) {
            title.textContent = '编辑角色';
            document.getElementById('roleName').value = role.name;
            document.getElementById('roleDescription').value = role.description;
            document.getElementById('roleLevel').value = role.level;
            document.querySelector(`input[name="roleStatus"][value="${role.status}"]`).checked = true;
        } else {
            title.textContent = '新建角色';
            document.getElementById('roleName').value = '';
            document.getElementById('roleDescription').value = '';
            document.getElementById('roleLevel').value = '4';
            document.querySelector('input[name="roleStatus"][value="active"]').checked = true;
        }
        
        this.renderPermissionSelector(role?.permissions || []);
        editor.style.display = 'flex';
    }

    renderPermissionSelector(selectedPermissions) {
        const selector = document.getElementById('permissionSelector');
        if (!selector) return;
        
        selector.innerHTML = Object.entries(this.permissionGroups).map(([groupKey, group]) => `
            <div class="permission-group-selector">
                <div class="group-header">
                    <label class="checkbox-label">
                        <input type="checkbox" class="group-checkbox" data-group="${groupKey}" 
                               onchange="permissionManager.togglePermissionGroup('${groupKey}', this.checked)">
                        <span class="checkmark"></span>
                        <i class="${group.icon}"></i>
                        ${group.name}
                    </label>
                </div>
                <div class="group-permissions">
                    ${Object.entries(group.permissions).map(([permKey, permName]) => `
                        <label class="checkbox-label permission-checkbox">
                            <input type="checkbox" value="${permKey}" 
                                   ${selectedPermissions.includes(permKey) ? 'checked' : ''}>
                            <span class="checkmark"></span>
                            ${permName}
                        </label>
                    `).join('')}
                </div>
            </div>
        `).join('');
    }

    closeRoleEditor() {
        const editor = document.getElementById('roleEditor');
        if (editor) {
            editor.style.display = 'none';
        }
    }

    saveRole() {
        const name = document.getElementById('roleName').value.trim();
        const description = document.getElementById('roleDescription').value.trim();
        const level = parseInt(document.getElementById('roleLevel').value);
        const status = document.querySelector('input[name="roleStatus"]:checked').value;
        
        if (!name) {
            if (window.showNotification) {
                showNotification('请输入角色名称', 'warning');
            }
            return;
        }
        
        // 收集选中的权限
        const selectedPermissions = Array.from(
            document.querySelectorAll('#permissionSelector input[type="checkbox"]:checked')
        ).map(cb => cb.value).filter(v => v);
        
        const roleId = name.toLowerCase().replace(/\s+/g, '_');
        
        this.roles[roleId] = {
            id: roleId,
            name: name,
            description: description,
            level: level,
            status: status,
            permissions: selectedPermissions,
            color: this.generateRoleColor(),
            icon: 'fas fa-user-tag'
        };
        
        this.saveRolesToStorage();
        this.renderRoles();
        this.closeRoleEditor();
        
        if (window.showNotification) {
            showNotification('角色保存成功', 'success');
        }
        
        this.logAuditEvent('role_change', `创建/修改角色: ${name}`);
    }

    generateRoleColor() {
        const colors = ['#ef4444', '#f59e0b', '#10b981', '#3b82f6', '#8b5cf6', '#ec4899'];
        return colors[Math.floor(Math.random() * colors.length)];
    }

    duplicateRole(roleId) {
        const role = this.roles[roleId];
        if (!role) return;
        
        const newRole = {
            ...role,
            id: role.id + '_copy',
            name: role.name + ' (副本)'
        };
        
        this.roles[newRole.id] = newRole;
        this.saveRolesToStorage();
        this.renderRoles();
        
        if (window.showNotification) {
            showNotification('角色复制成功', 'success');
        }
    }

    deleteRole(roleId) {
        if (confirm('确定要删除这个角色吗？此操作不可撤销。')) {
            delete this.roles[roleId];
            this.saveRolesToStorage();
            this.renderRoles();
            
            if (window.showNotification) {
                showNotification('角色删除成功', 'success');
            }
            
            this.logAuditEvent('role_change', `删除角色: ${roleId}`);
        }
    }

    assignUserPermissions(userId) {
        // 显示权限分配器
        this.showPermissionAssigner(userId);
    }

    showPermissionAssigner(userId) {
        const assigner = document.getElementById('permissionAssigner');
        // 实现权限分配逻辑
        assigner.style.display = 'flex';
    }

    closePermissionAssigner() {
        const assigner = document.getElementById('permissionAssigner');
        if (assigner) {
            assigner.style.display = 'none';
        }
    }

    logAuditEvent(action, description) {
        const event = {
            id: Date.now(),
            action: action,
            user: this.currentUser?.name || '未知用户',
            description: description,
            timestamp: new Date(),
            ip: '127.0.0.1', // 实际应用中应获取真实IP
            result: 'success'
        };
        
        this.auditLog.unshift(event);
        
        // 限制日志数量
        if (this.auditLog.length > 1000) {
            this.auditLog = this.auditLog.slice(0, 1000);
        }
        
        this.saveAuditLogToStorage();
    }

    applyUserPermissions() {
        // 应用用户权限到界面
        this.hideUnauthorizedElements();
        
        // 更新导航菜单
        this.updateNavigationPermissions();
    }

    updateNavigationPermissions() {
        const navItems = document.querySelectorAll('.nav-item[data-page]');
        
        navItems.forEach(item => {
            const page = item.dataset.page;
            const requiredPermission = this.getPagePermission(page);
            
            if (requiredPermission && !this.hasPermission(requiredPermission)) {
                item.style.display = 'none';
            } else {
                item.style.display = '';
            }
        });
    }

    // 存储方法
    saveRolesToStorage() {
        try {
            localStorage.setItem('systemRoles', JSON.stringify(this.roles));
        } catch (error) {
            console.error('保存角色失败:', error);
        }
    }

    saveAuditLogToStorage() {
        try {
            localStorage.setItem('auditLog', JSON.stringify(this.auditLog));
        } catch (error) {
            console.error('保存审计日志失败:', error);
        }
    }

    loadStoredPermissions() {
        try {
            const storedRoles = localStorage.getItem('systemRoles');
            if (storedRoles) {
                this.roles = { ...this.roles, ...JSON.parse(storedRoles) };
            }
            
            const storedAuditLog = localStorage.getItem('auditLog');
            if (storedAuditLog) {
                this.auditLog = JSON.parse(storedAuditLog);
            }
        } catch (error) {
            console.error('加载权限配置失败:', error);
        }
    }

    // 筛选方法
    filterRoles() {
        const statusFilter = document.getElementById('roleStatusFilter').value;
        const searchQuery = document.getElementById('roleSearchInput').value.toLowerCase();
        
        const roleCards = document.querySelectorAll('.role-card');
        
        roleCards.forEach(card => {
            const roleId = card.dataset.role;
            const role = this.roles[roleId];
            
            let visible = true;
            
            // 状态筛选
            if (statusFilter !== 'all' && role.status !== statusFilter) {
                visible = false;
            }
            
            // 搜索筛选
            if (searchQuery && !role.name.toLowerCase().includes(searchQuery)) {
                visible = false;
            }
            
            card.style.display = visible ? 'block' : 'none';
        });
    }

    filterUsers() {
        // 实现用户筛选逻辑
        this.renderUsersPermissions();
    }

    filterAuditLog() {
        // 实现审计日志筛选逻辑
        this.renderAuditLog();
    }

    // 导出方法
    exportPermissions() {
        const permissionData = {
            roles: this.roles,
            permissionGroups: this.permissionGroups,
            exportTime: new Date().toISOString()
        };
        
        const blob = new Blob([JSON.stringify(permissionData, null, 2)], { 
            type: 'application/json' 
        });
        const url = URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = `permissions-${new Date().toISOString().split('T')[0]}.json`;
        link.click();
        URL.revokeObjectURL(url);
        
        if (window.showNotification) {
            showNotification('权限配置导出成功', 'success');
        }
    }

    exportAuditLog() {
        const auditData = {
            logs: this.auditLog,
            exportTime: new Date().toISOString()
        };
        
        const blob = new Blob([JSON.stringify(auditData, null, 2)], { 
            type: 'application/json' 
        });
        const url = URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = `audit-log-${new Date().toISOString().split('T')[0]}.json`;
        link.click();
        URL.revokeObjectURL(url);
        
        if (window.showNotification) {
            showNotification('审计日志导出成功', 'success');
        }
    }
}

// 全局权限管理器实例
let permissionManager = null;

// 初始化权限管理系统
function initializePermissionManagement() {
    permissionManager = new PermissionManagement();
    console.log('✅ 权限管理系统已初始化');
}

// 显示权限管理面板
function showPermissionPanel() {
    if (permissionManager) {
        permissionManager.showPermissionPanel();
    }
}

// 检查权限的全局函数
function hasPermission(permission) {
    return permissionManager ? permissionManager.hasPermission(permission) : false;
}

// 页面加载时初始化
document.addEventListener('DOMContentLoaded', function() {
    setTimeout(initializePermissionManagement, 1200);
});
