/**
 * 全局错误处理系统
 * 统一处理JavaScript错误，提供友好的错误提示
 */

class ErrorHandler {
    constructor() {
        this.errors = [];
        this.maxErrors = 50;
        this.debugMode = false;
        this.init();
    }

    init() {
        this.setupGlobalErrorHandling();
        this.setupUnhandledRejectionHandling();
        this.detectDebugMode();
    }

    detectDebugMode() {
        // 检测是否为开发环境
        this.debugMode = window.location.hostname === 'localhost' || 
                        window.location.hostname === '127.0.0.1' ||
                        window.location.search.includes('debug=true');
    }

    setupGlobalErrorHandling() {
        window.addEventListener('error', (event) => {
            this.handleError({
                type: 'javascript',
                message: event.message,
                filename: event.filename,
                lineno: event.lineno,
                colno: event.colno,
                error: event.error,
                timestamp: new Date()
            });
        });
    }

    setupUnhandledRejectionHandling() {
        window.addEventListener('unhandledrejection', (event) => {
            this.handleError({
                type: 'promise',
                message: event.reason?.message || 'Unhandled Promise Rejection',
                error: event.reason,
                timestamp: new Date()
            });
        });
    }

    handleError(errorInfo) {
        // 记录错误
        this.logError(errorInfo);
        
        // 显示用户友好的错误提示
        this.showUserError(errorInfo);
        
        // 在调试模式下显示详细信息
        if (this.debugMode) {
            this.showDebugError(errorInfo);
        }
    }

    logError(errorInfo) {
        // 添加到错误列表
        this.errors.push(errorInfo);
        
        // 限制错误数量
        if (this.errors.length > this.maxErrors) {
            this.errors.shift();
        }
        
        // 控制台输出
        console.error('Error captured:', errorInfo);
    }

    showUserError(errorInfo) {
        if (!window.notificationManager) {
            // 如果通知管理器不可用，使用alert
            alert('系统遇到了一个问题，请刷新页面重试。');
            return;
        }

        const userMessage = this.getUserFriendlyMessage(errorInfo);
        window.notificationManager.error(userMessage, {
            title: '系统错误',
            duration: 8000
        });
    }

    showDebugError(errorInfo) {
        if (!window.notificationManager) return;

        const debugMessage = this.getDebugMessage(errorInfo);
        window.notificationManager.warning(debugMessage, {
            title: '调试信息',
            duration: 10000,
            persistent: true
        });
    }

    getUserFriendlyMessage(errorInfo) {
        const messages = {
            'Cannot read property': '数据加载失败，请稍后重试',
            'Cannot read properties': '数据加载失败，请稍后重试',
            'is not defined': '功能模块加载失败，请刷新页面',
            'is not a function': '功能调用失败，请检查操作',
            'Network Error': '网络连接失败，请检查网络设置',
            'Failed to fetch': '数据获取失败，请稍后重试',
            'Unexpected token': '数据格式错误，请联系管理员',
            'Permission denied': '权限不足，请联系管理员'
        };

        for (const [key, message] of Object.entries(messages)) {
            if (errorInfo.message.includes(key)) {
                return message;
            }
        }

        return '系统遇到未知错误，请刷新页面重试';
    }

    getDebugMessage(errorInfo) {
        let message = `错误类型: ${errorInfo.type}\n`;
        message += `错误信息: ${errorInfo.message}\n`;
        
        if (errorInfo.filename) {
            message += `文件: ${errorInfo.filename}\n`;
        }
        
        if (errorInfo.lineno) {
            message += `行号: ${errorInfo.lineno}:${errorInfo.colno}\n`;
        }
        
        return message;
    }

    // 手动错误处理方法
    try(fn, context = null) {
        return (...args) => {
            try {
                const result = fn.apply(context, args);
                
                // 如果返回Promise，处理rejection
                if (result && typeof result.catch === 'function') {
                    return result.catch(error => {
                        this.handleError({
                            type: 'async',
                            message: error.message || 'Async operation failed',
                            error: error,
                            timestamp: new Date()
                        });
                        throw error;
                    });
                }
                
                return result;
            } catch (error) {
                this.handleError({
                    type: 'sync',
                    message: error.message || 'Synchronous operation failed',
                    error: error,
                    timestamp: new Date()
                });
                throw error;
            }
        };
    }

    // 异步函数包装器
    async tryAsync(fn, context = null) {
        try {
            return await fn.call(context);
        } catch (error) {
            this.handleError({
                type: 'async',
                message: error.message || 'Async operation failed',
                error: error,
                timestamp: new Date()
            });
            throw error;
        }
    }

    // 安全的DOM操作
    safeQuerySelector(selector) {
        try {
            return document.querySelector(selector);
        } catch (error) {
            this.handleError({
                type: 'dom',
                message: `Invalid selector: ${selector}`,
                error: error,
                timestamp: new Date()
            });
            return null;
        }
    }

    safeQuerySelectorAll(selector) {
        try {
            return document.querySelectorAll(selector);
        } catch (error) {
            this.handleError({
                type: 'dom',
                message: `Invalid selector: ${selector}`,
                error: error,
                timestamp: new Date()
            });
            return [];
        }
    }

    // 安全的JSON操作
    safeJSONParse(jsonString, defaultValue = null) {
        try {
            return JSON.parse(jsonString);
        } catch (error) {
            this.handleError({
                type: 'json',
                message: 'JSON parse failed',
                error: error,
                timestamp: new Date()
            });
            return defaultValue;
        }
    }

    safeJSONStringify(obj, defaultValue = '{}') {
        try {
            return JSON.stringify(obj);
        } catch (error) {
            this.handleError({
                type: 'json',
                message: 'JSON stringify failed',
                error: error,
                timestamp: new Date()
            });
            return defaultValue;
        }
    }

    // 安全的localStorage操作
    safeLocalStorageGet(key, defaultValue = null) {
        try {
            const value = localStorage.getItem(key);
            return value ? JSON.parse(value) : defaultValue;
        } catch (error) {
            this.handleError({
                type: 'storage',
                message: `Failed to get localStorage item: ${key}`,
                error: error,
                timestamp: new Date()
            });
            return defaultValue;
        }
    }

    safeLocalStorageSet(key, value) {
        try {
            localStorage.setItem(key, JSON.stringify(value));
            return true;
        } catch (error) {
            this.handleError({
                type: 'storage',
                message: `Failed to set localStorage item: ${key}`,
                error: error,
                timestamp: new Date()
            });
            return false;
        }
    }

    // 获取错误统计
    getErrorStats() {
        const stats = {
            total: this.errors.length,
            byType: {},
            recent: this.errors.slice(-10)
        };

        this.errors.forEach(error => {
            stats.byType[error.type] = (stats.byType[error.type] || 0) + 1;
        });

        return stats;
    }

    // 清除错误记录
    clearErrors() {
        this.errors = [];
    }

    // 导出错误日志
    exportErrors() {
        const data = {
            timestamp: new Date().toISOString(),
            userAgent: navigator.userAgent,
            url: window.location.href,
            errors: this.errors
        };

        const blob = new Blob([JSON.stringify(data, null, 2)], {
            type: 'application/json'
        });

        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `error-log-${Date.now()}.json`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
    }
}

// 创建全局实例
if (typeof window !== 'undefined') {
    window.errorHandler = new ErrorHandler();
    
    // 提供全局便捷方法
    window.safeCall = window.errorHandler.try.bind(window.errorHandler);
    window.safeAsync = window.errorHandler.tryAsync.bind(window.errorHandler);
}

// 导出模块（如果支持）
if (typeof module !== 'undefined' && module.exports) {
    module.exports = ErrorHandler;
}
