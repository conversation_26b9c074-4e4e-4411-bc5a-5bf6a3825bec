// 增强订单管理功能
class EnhancedOrderManager {
    constructor() {
        this.orders = [];
        this.filteredOrders = [];
        this.selectedOrders = new Set();
        this.currentPage = 1;
        this.pageSize = 10;
        this.sortField = 'createTime';
        this.sortDirection = 'desc';
        this.filters = {
            search: '',
            status: '',
            dateRange: '',
            amountRange: '',
            paymentMethod: '',
            shippingMethod: '',
            minAmount: '',
            maxAmount: '',
            dateFrom: '',
            dateTo: ''
        };
        
        this.initializeOrders();
    }
    
    initializeOrders() {
        // 生成示例订单数据
        this.orders = this.generateSampleOrders(50);
        this.updateStats();
        this.applyFilters();
    }
    
    generateSampleOrders(count) {
        const orders = [];
        const statuses = ['pending', 'processing', 'shipped', 'completed', 'cancelled', 'refunded'];
        const paymentMethods = ['alipay', 'wechat', 'bank', 'cash'];
        const shippingMethods = ['express', 'pickup', 'same-day'];
        const customers = ['张三', '李四', '王五', '赵六', '陈七', '刘八', '杨九', '黄十'];
        const products = ['iPhone 15', 'MacBook Pro', 'iPad Air', 'AirPods Pro', '小米手机', '华为笔记本'];
        
        for (let i = 1; i <= count; i++) {
            const createTime = new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000);
            const status = statuses[Math.floor(Math.random() * statuses.length)];
            const amount = Math.floor(Math.random() * 5000) + 100;
            const quantity = Math.floor(Math.random() * 5) + 1;
            
            orders.push({
                id: i,
                orderNumber: `ORD${String(i).padStart(6, '0')}`,
                customer: {
                    name: customers[Math.floor(Math.random() * customers.length)],
                    phone: `138${String(Math.floor(Math.random() * *********)).padStart(8, '0')}`,
                    email: `user${i}@example.com`
                },
                products: [{
                    name: products[Math.floor(Math.random() * products.length)],
                    quantity: quantity,
                    price: Math.floor(amount / quantity)
                }],
                totalAmount: amount,
                status: status,
                paymentMethod: paymentMethods[Math.floor(Math.random() * paymentMethods.length)],
                shippingMethod: shippingMethods[Math.floor(Math.random() * shippingMethods.length)],
                createTime: createTime,
                updateTime: new Date(createTime.getTime() + Math.random() * 7 * 24 * 60 * 60 * 1000),
                shippingAddress: `北京市朝阳区某某街道${Math.floor(Math.random() * 100)}号`,
                notes: Math.random() > 0.7 ? '客户要求加急处理' : '',
                trackingNumber: status === 'shipped' || status === 'completed' ? 
                    `SF${String(Math.floor(Math.random() * *********0)).padStart(10, '0')}` : null
            });
        }
        
        return orders.sort((a, b) => b.createTime - a.createTime);
    }
    
    updateStats() {
        const totalOrders = this.orders.length;
        const completedOrders = this.orders.filter(o => o.status === 'completed').length;
        const pendingOrders = this.orders.filter(o => o.status === 'pending' || o.status === 'processing').length;
        const totalValue = this.orders.reduce((sum, order) => sum + order.totalAmount, 0);
        
        document.getElementById('totalOrdersCount').textContent = totalOrders;
        document.getElementById('completedOrders').textContent = completedOrders;
        document.getElementById('pendingOrders').textContent = pendingOrders;
        document.getElementById('totalOrderValue').textContent = `¥${totalValue.toLocaleString()}`;
    }
    
    applyFilters() {
        this.filteredOrders = this.orders.filter(order => {
            // 搜索筛选
            if (this.filters.search) {
                const searchTerm = this.filters.search.toLowerCase();
                if (!order.orderNumber.toLowerCase().includes(searchTerm) &&
                    !order.customer.name.toLowerCase().includes(searchTerm) &&
                    !order.customer.phone.includes(searchTerm)) {
                    return false;
                }
            }
            
            // 状态筛选
            if (this.filters.status && order.status !== this.filters.status) {
                return false;
            }
            
            // 日期范围筛选
            if (this.filters.dateRange) {
                const now = new Date();
                let startDate;
                
                switch (this.filters.dateRange) {
                    case 'today':
                        startDate = new Date(now.getFullYear(), now.getMonth(), now.getDate());
                        break;
                    case 'week':
                        startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
                        break;
                    case 'month':
                        startDate = new Date(now.getFullYear(), now.getMonth(), 1);
                        break;
                    case 'quarter':
                        const quarter = Math.floor(now.getMonth() / 3);
                        startDate = new Date(now.getFullYear(), quarter * 3, 1);
                        break;
                }
                
                if (startDate && order.createTime < startDate) {
                    return false;
                }
            }
            
            // 金额范围筛选
            if (this.filters.amountRange) {
                const [min, max] = this.filters.amountRange.split('-').map(v => 
                    v === '+' ? Infinity : parseInt(v) || 0
                );
                if (order.totalAmount < min || (max !== Infinity && order.totalAmount > max)) {
                    return false;
                }
            }
            
            // 高级筛选
            if (this.filters.minAmount && order.totalAmount < parseInt(this.filters.minAmount)) {
                return false;
            }
            
            if (this.filters.maxAmount && order.totalAmount > parseInt(this.filters.maxAmount)) {
                return false;
            }
            
            if (this.filters.dateFrom) {
                const fromDate = new Date(this.filters.dateFrom);
                if (order.createTime < fromDate) {
                    return false;
                }
            }
            
            if (this.filters.dateTo) {
                const toDate = new Date(this.filters.dateTo);
                toDate.setHours(23, 59, 59, 999);
                if (order.createTime > toDate) {
                    return false;
                }
            }
            
            if (this.filters.paymentMethod && order.paymentMethod !== this.filters.paymentMethod) {
                return false;
            }
            
            if (this.filters.shippingMethod && order.shippingMethod !== this.filters.shippingMethod) {
                return false;
            }
            
            return true;
        });
        
        // 排序
        this.filteredOrders.sort((a, b) => {
            let aValue = a[this.sortField];
            let bValue = b[this.sortField];
            
            if (aValue instanceof Date) {
                aValue = aValue.getTime();
                bValue = bValue.getTime();
            }
            
            if (typeof aValue === 'string') {
                aValue = aValue.toLowerCase();
                bValue = bValue.toLowerCase();
            }
            
            if (this.sortDirection === 'asc') {
                return aValue > bValue ? 1 : -1;
            } else {
                return aValue < bValue ? 1 : -1;
            }
        });
        
        this.currentPage = 1;
        this.renderTable();
        this.renderPagination();
    }
    
    renderTable() {
        const tbody = document.getElementById('ordersTableBody');
        const startIndex = (this.currentPage - 1) * this.pageSize;
        const endIndex = startIndex + this.pageSize;
        const pageOrders = this.filteredOrders.slice(startIndex, endIndex);
        
        tbody.innerHTML = pageOrders.map(order => `
            <tr>
                <td>
                    <input type="checkbox" value="${order.id}" onchange="orderManager.toggleOrderSelection(${order.id})">
                </td>
                <td>
                    <div class="order-info-cell">
                        <div class="order-number">${order.orderNumber}</div>
                        <div class="order-time">${this.formatDate(order.createTime)}</div>
                        ${order.trackingNumber ? `<div class="tracking-number">快递: ${order.trackingNumber}</div>` : ''}
                    </div>
                </td>
                <td>
                    <div class="customer-info">
                        <div class="customer-name">${order.customer.name}</div>
                        <div class="customer-contact">${order.customer.phone}</div>
                        <div class="customer-email">${order.customer.email}</div>
                    </div>
                </td>
                <td>
                    <div class="product-info">
                        ${order.products.map(product => `
                            <div class="product-item">
                                <span class="product-name">${product.name}</span>
                                <span class="product-quantity">×${product.quantity}</span>
                            </div>
                        `).join('')}
                    </div>
                </td>
                <td>
                    <div class="order-amount">¥${order.totalAmount.toLocaleString()}</div>
                    <div class="payment-method">${this.getPaymentMethodText(order.paymentMethod)}</div>
                </td>
                <td>
                    <span class="status-badge status-${order.status}">${this.getStatusText(order.status)}</span>
                </td>
                <td>
                    ${this.formatDate(order.createTime)}
                </td>
                <td>
                    <div class="action-buttons">
                        <button class="btn-icon" onclick="orderManager.viewOrderDetails(${order.id})" title="查看详情">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button class="btn-icon" onclick="orderManager.editOrder(${order.id})" title="编辑">
                            <i class="fas fa-edit"></i>
                        </button>
                        ${order.status === 'pending' ? `
                            <button class="btn-icon" onclick="orderManager.processOrder(${order.id})" title="处理">
                                <i class="fas fa-play"></i>
                            </button>
                        ` : ''}
                        ${order.status === 'processing' ? `
                            <button class="btn-icon" onclick="orderManager.shipOrder(${order.id})" title="发货">
                                <i class="fas fa-shipping-fast"></i>
                            </button>
                        ` : ''}
                        <button class="btn-icon danger" onclick="orderManager.cancelOrder(${order.id})" title="取消">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                </td>
            </tr>
        `).join('');
        
        this.updateBulkActionsToolbar();
    }
    
    renderPagination() {
        const pagination = document.getElementById('ordersPagination');
        const totalPages = Math.ceil(this.filteredOrders.length / this.pageSize);
        
        if (totalPages <= 1) {
            pagination.innerHTML = '';
            return;
        }
        
        let paginationHTML = `
            <div class="pagination-info">
                显示 ${(this.currentPage - 1) * this.pageSize + 1}-${Math.min(this.currentPage * this.pageSize, this.filteredOrders.length)} 
                共 ${this.filteredOrders.length} 条记录
            </div>
            <div class="pagination-controls">
        `;
        
        // 上一页
        paginationHTML += `
            <button class="pagination-btn ${this.currentPage === 1 ? 'disabled' : ''}" 
                    onclick="orderManager.goToPage(${this.currentPage - 1})" 
                    ${this.currentPage === 1 ? 'disabled' : ''}>
                <i class="fas fa-chevron-left"></i>
            </button>
        `;
        
        // 页码
        for (let i = 1; i <= totalPages; i++) {
            if (i === 1 || i === totalPages || (i >= this.currentPage - 2 && i <= this.currentPage + 2)) {
                paginationHTML += `
                    <button class="pagination-btn ${i === this.currentPage ? 'active' : ''}" 
                            onclick="orderManager.goToPage(${i})">
                        ${i}
                    </button>
                `;
            } else if (i === this.currentPage - 3 || i === this.currentPage + 3) {
                paginationHTML += '<span class="pagination-ellipsis">...</span>';
            }
        }
        
        // 下一页
        paginationHTML += `
            <button class="pagination-btn ${this.currentPage === totalPages ? 'disabled' : ''}" 
                    onclick="orderManager.goToPage(${this.currentPage + 1})" 
                    ${this.currentPage === totalPages ? 'disabled' : ''}>
                <i class="fas fa-chevron-right"></i>
            </button>
        `;
        
        paginationHTML += '</div>';
        pagination.innerHTML = paginationHTML;
    }
    
    getStatusText(status) {
        const statusMap = {
            'pending': '待付款',
            'processing': '处理中',
            'shipped': '已发货',
            'completed': '已完成',
            'cancelled': '已取消',
            'refunded': '已退款'
        };
        return statusMap[status] || status;
    }
    
    getPaymentMethodText(method) {
        const methodMap = {
            'alipay': '支付宝',
            'wechat': '微信支付',
            'bank': '银行卡',
            'cash': '现金'
        };
        return methodMap[method] || method;
    }
    
    formatDate(date) {
        if (!date) return '';
        return new Intl.DateTimeFormat('zh-CN', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit'
        }).format(date);
    }
    
    // 订单选择相关方法
    toggleOrderSelection(orderId) {
        if (this.selectedOrders.has(orderId)) {
            this.selectedOrders.delete(orderId);
        } else {
            this.selectedOrders.add(orderId);
        }
        this.updateBulkActionsToolbar();
        this.updateSelectAllCheckbox();
    }
    
    toggleSelectAll() {
        const selectAllCheckbox = document.getElementById('selectAllOrders');
        const pageOrders = this.getPageOrders();
        
        if (selectAllCheckbox.checked) {
            pageOrders.forEach(order => this.selectedOrders.add(order.id));
        } else {
            pageOrders.forEach(order => this.selectedOrders.delete(order.id));
        }
        
        this.renderTable();
        this.updateBulkActionsToolbar();
    }
    
    updateSelectAllCheckbox() {
        const selectAllCheckbox = document.getElementById('selectAllOrders');
        const pageOrders = this.getPageOrders();
        const selectedPageOrders = pageOrders.filter(order => this.selectedOrders.has(order.id));
        
        if (selectedPageOrders.length === 0) {
            selectAllCheckbox.checked = false;
            selectAllCheckbox.indeterminate = false;
        } else if (selectedPageOrders.length === pageOrders.length) {
            selectAllCheckbox.checked = true;
            selectAllCheckbox.indeterminate = false;
        } else {
            selectAllCheckbox.checked = false;
            selectAllCheckbox.indeterminate = true;
        }
    }
    
    updateBulkActionsToolbar() {
        const toolbar = document.getElementById('orderBulkActionsToolbar');
        const selectedCount = document.getElementById('selectedOrdersCount');
        
        if (this.selectedOrders.size > 0) {
            toolbar.style.display = 'flex';
            selectedCount.textContent = this.selectedOrders.size;
        } else {
            toolbar.style.display = 'none';
        }
    }
    
    getPageOrders() {
        const startIndex = (this.currentPage - 1) * this.pageSize;
        const endIndex = startIndex + this.pageSize;
        return this.filteredOrders.slice(startIndex, endIndex);
    }
    
    // 分页方法
    goToPage(page) {
        const totalPages = Math.ceil(this.filteredOrders.length / this.pageSize);
        if (page >= 1 && page <= totalPages) {
            this.currentPage = page;
            this.renderTable();
            this.renderPagination();
        }
    }
    
    // 筛选方法
    setFilter(key, value) {
        this.filters[key] = value;
        this.applyFilters();
    }
    
    // 排序方法
    sortBy(field) {
        if (this.sortField === field) {
            this.sortDirection = this.sortDirection === 'asc' ? 'desc' : 'asc';
        } else {
            this.sortField = field;
            this.sortDirection = 'asc';
        }
        this.applyFilters();
    }
}

// 全局订单管理器实例
let orderManager = null;

// 订单操作方法
function filterOrders() {
    if (!orderManager) return;

    const search = document.getElementById('orderSearch').value;
    const status = document.getElementById('orderStatusFilter').value;
    const dateRange = document.getElementById('orderDateFilter').value;
    const amountRange = document.getElementById('orderAmountFilter').value;

    orderManager.setFilter('search', search);
    orderManager.setFilter('status', status);
    orderManager.setFilter('dateRange', dateRange);
    orderManager.setFilter('amountRange', amountRange);
}

function toggleOrderAdvancedFilters() {
    const advancedFilters = document.getElementById('orderAdvancedFilters');
    const button = event.target.closest('button');
    const icon = button.querySelector('i');

    if (advancedFilters.style.display === 'none') {
        advancedFilters.style.display = 'block';
        icon.className = 'fas fa-chevron-up';
        button.innerHTML = '<i class="fas fa-chevron-up"></i> 收起筛选';
    } else {
        advancedFilters.style.display = 'none';
        icon.className = 'fas fa-sliders-h';
        button.innerHTML = '<i class="fas fa-sliders-h"></i> 高级筛选';
    }
}

function applyOrderAdvancedFilters() {
    if (!orderManager) return;

    const minAmount = document.getElementById('minAmount').value;
    const maxAmount = document.getElementById('maxAmount').value;
    const dateFrom = document.getElementById('orderDateFrom').value;
    const dateTo = document.getElementById('orderDateTo').value;
    const paymentMethod = document.getElementById('paymentMethodFilter').value;
    const shippingMethod = document.getElementById('shippingMethodFilter').value;

    orderManager.setFilter('minAmount', minAmount);
    orderManager.setFilter('maxAmount', maxAmount);
    orderManager.setFilter('dateFrom', dateFrom);
    orderManager.setFilter('dateTo', dateTo);
    orderManager.setFilter('paymentMethod', paymentMethod);
    orderManager.setFilter('shippingMethod', shippingMethod);

    showNotification('高级筛选已应用', 'success');
}

function clearOrderAdvancedFilters() {
    if (!orderManager) return;

    document.getElementById('minAmount').value = '';
    document.getElementById('maxAmount').value = '';
    document.getElementById('orderDateFrom').value = '';
    document.getElementById('orderDateTo').value = '';
    document.getElementById('paymentMethodFilter').value = '';
    document.getElementById('shippingMethodFilter').value = '';

    orderManager.setFilter('minAmount', '');
    orderManager.setFilter('maxAmount', '');
    orderManager.setFilter('dateFrom', '');
    orderManager.setFilter('dateTo', '');
    orderManager.setFilter('paymentMethod', '');
    orderManager.setFilter('shippingMethod', '');

    showNotification('筛选条件已清除', 'info');
}

function refreshOrders() {
    if (!orderManager) return;

    orderManager.initializeOrders();
    showNotification('订单列表已刷新', 'success');
}

function toggleSelectAllOrders() {
    if (!orderManager) return;
    orderManager.toggleSelectAll();
}

// 批量操作方法
function bulkUpdateOrderStatus(newStatus) {
    if (!orderManager || orderManager.selectedOrders.size === 0) return;

    const statusText = orderManager.getStatusText(newStatus);
    if (confirm(`确定要将选中的 ${orderManager.selectedOrders.size} 个订单状态更改为"${statusText}"吗？`)) {
        orderManager.selectedOrders.forEach(orderId => {
            const order = orderManager.orders.find(o => o.id === orderId);
            if (order) {
                order.status = newStatus;
                order.updateTime = new Date();

                // 如果是发货状态，生成快递单号
                if (newStatus === 'shipped' && !order.trackingNumber) {
                    order.trackingNumber = `SF${String(Math.floor(Math.random() * *********0)).padStart(10, '0')}`;
                }
            }
        });

        orderManager.selectedOrders.clear();
        orderManager.updateStats();
        orderManager.applyFilters();
        showNotification(`订单状态已批量更新为"${statusText}"`, 'success');
    }
}

function bulkExportOrders() {
    if (!orderManager || orderManager.selectedOrders.size === 0) return;

    const selectedOrdersData = orderManager.orders.filter(o => orderManager.selectedOrders.has(o.id));
    const csvContent = generateOrderCSV(selectedOrdersData);
    downloadCSV(csvContent, `selected_orders_${new Date().toISOString().slice(0, 10)}.csv`);

    showNotification(`已导出 ${selectedOrdersData.length} 个订单`, 'success');
}

function bulkCancelOrders() {
    if (!orderManager || orderManager.selectedOrders.size === 0) return;

    if (confirm(`确定要取消选中的 ${orderManager.selectedOrders.size} 个订单吗？`)) {
        orderManager.selectedOrders.forEach(orderId => {
            const order = orderManager.orders.find(o => o.id === orderId);
            if (order && order.status !== 'completed' && order.status !== 'shipped') {
                order.status = 'cancelled';
                order.updateTime = new Date();
            }
        });

        orderManager.selectedOrders.clear();
        orderManager.updateStats();
        orderManager.applyFilters();
        showNotification('订单已批量取消', 'warning');
    }
}

// 单个订单操作方法
function viewOrderDetails(orderId) {
    const order = orderManager.orders.find(o => o.id === orderId);
    if (!order) return;

    // 创建订单详情模态框
    showOrderDetailsModal(order);
}

function editOrder(orderId) {
    const order = orderManager.orders.find(o => o.id === orderId);
    if (!order) return;

    showNotification(`编辑订单: ${order.orderNumber}`, 'info');
}

function processOrder(orderId) {
    const order = orderManager.orders.find(o => o.id === orderId);
    if (!order) return;

    if (confirm(`确定要处理订单 ${order.orderNumber} 吗？`)) {
        order.status = 'processing';
        order.updateTime = new Date();

        orderManager.updateStats();
        orderManager.applyFilters();
        showNotification(`订单 ${order.orderNumber} 已开始处理`, 'success');
    }
}

function shipOrder(orderId) {
    const order = orderManager.orders.find(o => o.id === orderId);
    if (!order) return;

    const trackingNumber = prompt('请输入快递单号:', `SF${String(Math.floor(Math.random() * *********0)).padStart(10, '0')}`);
    if (trackingNumber) {
        order.status = 'shipped';
        order.trackingNumber = trackingNumber;
        order.updateTime = new Date();

        orderManager.updateStats();
        orderManager.applyFilters();
        showNotification(`订单 ${order.orderNumber} 已发货，快递单号: ${trackingNumber}`, 'success');
    }
}

function cancelOrder(orderId) {
    const order = orderManager.orders.find(o => o.id === orderId);
    if (!order) return;

    if (order.status === 'completed' || order.status === 'shipped') {
        showNotification('已完成或已发货的订单无法取消', 'error');
        return;
    }

    if (confirm(`确定要取消订单 ${order.orderNumber} 吗？`)) {
        order.status = 'cancelled';
        order.updateTime = new Date();

        orderManager.updateStats();
        orderManager.applyFilters();
        showNotification(`订单 ${order.orderNumber} 已取消`, 'warning');
    }
}

// 导出功能
function exportOrders() {
    if (!orderManager) return;

    const csvContent = generateOrderCSV(orderManager.filteredOrders);
    downloadCSV(csvContent, `orders_export_${new Date().toISOString().slice(0, 10)}.csv`);

    showNotification(`已导出 ${orderManager.filteredOrders.length} 个订单`, 'success');
}

function generateOrderCSV(orders) {
    const headers = ['订单号', '客户姓名', '客户电话', '商品', '数量', '金额', '状态', '支付方式', '配送方式', '创建时间', '快递单号'];
    const rows = orders.map(order => [
        order.orderNumber,
        order.customer.name,
        order.customer.phone,
        order.products.map(p => p.name).join(';'),
        order.products.reduce((sum, p) => sum + p.quantity, 0),
        order.totalAmount,
        orderManager.getStatusText(order.status),
        orderManager.getPaymentMethodText(order.paymentMethod),
        order.shippingMethod,
        order.createTime.toISOString(),
        order.trackingNumber || ''
    ]);

    return [headers, ...rows].map(row =>
        row.map(field => `"${String(field).replace(/"/g, '""')}"`).join(',')
    ).join('\n');
}

function downloadCSV(content, filename) {
    const blob = new Blob(['\ufeff' + content], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);

    link.setAttribute('href', url);
    link.setAttribute('download', filename);
    link.style.visibility = 'hidden';

    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
}

function showAddOrderModal() {
    showNotification('添加订单功能开发中...', 'info');
}

function batchProcess() {
    showNotification('批量处理功能开发中...', 'info');
}

function showOrderDetailsModal(order) {
    // 简单的订单详情显示
    let details = `订单详情\n\n`;
    details += `订单号: ${order.orderNumber}\n`;
    details += `客户: ${order.customer.name}\n`;
    details += `电话: ${order.customer.phone}\n`;
    details += `邮箱: ${order.customer.email}\n`;
    details += `商品: ${order.products.map(p => `${p.name} ×${p.quantity}`).join(', ')}\n`;
    details += `金额: ¥${order.totalAmount}\n`;
    details += `状态: ${orderManager.getStatusText(order.status)}\n`;
    details += `支付方式: ${orderManager.getPaymentMethodText(order.paymentMethod)}\n`;
    details += `配送地址: ${order.shippingAddress}\n`;
    if (order.trackingNumber) {
        details += `快递单号: ${order.trackingNumber}\n`;
    }
    details += `创建时间: ${orderManager.formatDate(order.createTime)}\n`;
    details += `更新时间: ${orderManager.formatDate(order.updateTime)}\n`;
    if (order.notes) {
        details += `备注: ${order.notes}\n`;
    }

    alert(details);
}

// 初始化订单管理
function initializeOrderManagement() {
    orderManager = new EnhancedOrderManager();
    console.log('✅ 增强订单管理器已初始化');
}
