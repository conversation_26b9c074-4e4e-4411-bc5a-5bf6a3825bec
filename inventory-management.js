// 库存管理系统

// 商品数据模拟
const products = [
    {
        id: 1,
        name: 'iPhone 15 Pro',
        sku: 'IP15P-001',
        category: 'electronics',
        price: 7999.00,
        stock: 25,
        minStock: 10,
        description: '苹果最新旗舰手机',
        image: 'https://images.unsplash.com/photo-1592750475338-74b7b21085ab?w=100&h=100&fit=crop',
        createdAt: '2024-01-10',
        updatedAt: '2024-01-15'
    },
    {
        id: 2,
        name: '联想ThinkPad X1',
        sku: 'TP-X1-002',
        category: 'electronics',
        price: 12999.00,
        stock: 8,
        minStock: 5,
        description: '商务笔记本电脑',
        image: 'https://images.unsplash.com/photo-1496181133206-80ce9b88a853?w=100&h=100&fit=crop',
        createdAt: '2024-01-08',
        updatedAt: '2024-01-14'
    },
    {
        id: 3,
        name: '耐克运动鞋',
        sku: 'NK-SHOE-003',
        category: 'clothing',
        price: 899.00,
        stock: 45,
        minStock: 20,
        description: '舒适透气运动鞋',
        image: 'https://images.unsplash.com/photo-1542291026-7eec264c27ff?w=100&h=100&fit=crop',
        createdAt: '2024-01-05',
        updatedAt: '2024-01-13'
    },
    {
        id: 4,
        name: '《JavaScript高级程序设计》',
        sku: 'BOOK-JS-004',
        category: 'books',
        price: 129.00,
        stock: 3,
        minStock: 10,
        description: '前端开发必读书籍',
        image: 'https://images.unsplash.com/photo-1544716278-ca5e3f4abd8c?w=100&h=100&fit=crop',
        createdAt: '2024-01-03',
        updatedAt: '2024-01-12'
    },
    {
        id: 5,
        name: '宜家办公椅',
        sku: 'IKEA-CHAIR-005',
        category: 'home',
        price: 599.00,
        stock: 15,
        minStock: 8,
        description: '人体工学办公椅',
        image: 'https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=100&h=100&fit=crop',
        createdAt: '2024-01-01',
        updatedAt: '2024-01-11'
    }
];

// 库存操作记录
const stockRecords = [];

// 分页配置
let currentProductPage = 1;
const productsPerPage = 10;
let filteredProducts = [...products];
let editingProductId = null;

// 初始化库存管理
function initializeInventoryManagement() {
    updateInventoryStats();
    renderProductsTable();
    renderProductsPagination();
    updateWarningList();
    populateProductSelects();
}

// 更新库存统计
function updateInventoryStats() {
    const totalProducts = products.length;
    const lowStockProducts = products.filter(p => p.stock <= p.minStock).length;
    const totalValue = products.reduce((sum, p) => sum + (p.price * p.stock), 0);

    document.getElementById('totalProducts').textContent = totalProducts;
    document.getElementById('lowStockProducts').textContent = lowStockProducts;
    document.getElementById('totalInventoryValue').textContent = formatCurrency(totalValue);
}

// 格式化货币
function formatCurrency(amount) {
    return new Intl.NumberFormat('zh-CN', {
        style: 'currency',
        currency: 'CNY'
    }).format(amount);
}

// 渲染商品表格
function renderProductsTable() {
    const tbody = document.getElementById('productsTableBody');
    if (!tbody) return;

    const startIndex = (currentProductPage - 1) * productsPerPage;
    const endIndex = startIndex + productsPerPage;
    const pageProducts = filteredProducts.slice(startIndex, endIndex);

    if (pageProducts.length === 0) {
        tbody.innerHTML = `
            <tr>
                <td colspan="8" class="text-center">
                    <div class="empty-state">
                        <i class="fas fa-box-open"></i>
                        <h3>暂无商品</h3>
                        <p>还没有添加任何商品</p>
                    </div>
                </td>
            </tr>
        `;
        return;
    }

    tbody.innerHTML = pageProducts.map(product => `
        <tr>
            <td>
                <input type="checkbox" class="product-checkbox" value="${product.id}">
            </td>
            <td>
                <div class="product-info">
                    <img src="${product.image}" alt="${product.name}" class="product-image">
                    <div class="product-details">
                        <span class="product-name">${product.name}</span>
                        <span class="product-sku">SKU: ${product.sku}</span>
                    </div>
                </div>
            </td>
            <td>
                <span class="category-badge category-${product.category}">
                    ${getCategoryLabel(product.category)}
                </span>
            </td>
            <td>
                <span class="stock-quantity ${getStockStatus(product)}">${product.stock}</span>
            </td>
            <td>${formatCurrency(product.price)}</td>
            <td>${formatCurrency(product.price * product.stock)}</td>
            <td>
                <span class="status-badge ${getStockStatusClass(product)}">
                    ${getStockStatusLabel(product)}
                </span>
            </td>
            <td>
                <div class="action-buttons">
                    <button class="btn-icon" onclick="editProduct(${product.id})" title="编辑">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="btn-icon" onclick="viewProductDetails(${product.id})" title="详情">
                        <i class="fas fa-eye"></i>
                    </button>
                    <button class="btn-icon danger" onclick="deleteProduct(${product.id})" title="删除">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </td>
        </tr>
    `).join('');
}

// 获取分类标签
function getCategoryLabel(category) {
    const labels = {
        'electronics': '电子产品',
        'clothing': '服装',
        'books': '图书',
        'home': '家居用品'
    };
    return labels[category] || category;
}

// 获取库存状态
function getStockStatus(product) {
    if (product.stock === 0) return 'out-of-stock';
    if (product.stock <= product.minStock) return 'low-stock';
    return 'in-stock';
}

// 获取库存状态样式类
function getStockStatusClass(product) {
    const status = getStockStatus(product);
    switch (status) {
        case 'out-of-stock': return 'danger';
        case 'low-stock': return 'warning';
        case 'in-stock': return 'success';
        default: return 'secondary';
    }
}

// 获取库存状态标签
function getStockStatusLabel(product) {
    const status = getStockStatus(product);
    switch (status) {
        case 'out-of-stock': return '缺货';
        case 'low-stock': return '库存不足';
        case 'in-stock': return '正常';
        default: return '未知';
    }
}

// 渲染分页
function renderProductsPagination() {
    const container = document.getElementById('productsPagination');
    if (!container) return;

    const totalPages = Math.ceil(filteredProducts.length / productsPerPage);
    
    if (totalPages <= 1) {
        container.innerHTML = '';
        return;
    }

    let paginationHTML = '<div class="pagination-controls">';
    
    // 上一页
    paginationHTML += `
        <button class="pagination-btn ${currentProductPage === 1 ? 'disabled' : ''}" 
                onclick="changeProductPage(${currentProductPage - 1})" 
                ${currentProductPage === 1 ? 'disabled' : ''}>
            <i class="fas fa-chevron-left"></i>
        </button>
    `;
    
    // 页码
    for (let i = 1; i <= totalPages; i++) {
        if (i === 1 || i === totalPages || (i >= currentProductPage - 1 && i <= currentProductPage + 1)) {
            paginationHTML += `
                <button class="pagination-btn ${i === currentProductPage ? 'active' : ''}" 
                        onclick="changeProductPage(${i})">
                    ${i}
                </button>
            `;
        } else if (i === currentProductPage - 2 || i === currentProductPage + 2) {
            paginationHTML += '<span class="pagination-ellipsis">...</span>';
        }
    }
    
    // 下一页
    paginationHTML += `
        <button class="pagination-btn ${currentProductPage === totalPages ? 'disabled' : ''}" 
                onclick="changeProductPage(${currentProductPage + 1})" 
                ${currentProductPage === totalPages ? 'disabled' : ''}>
            <i class="fas fa-chevron-right"></i>
        </button>
    `;
    
    paginationHTML += '</div>';
    container.innerHTML = paginationHTML;
}

// 切换页面
function changeProductPage(page) {
    const totalPages = Math.ceil(filteredProducts.length / productsPerPage);
    if (page < 1 || page > totalPages) return;
    
    currentProductPage = page;
    renderProductsTable();
    renderProductsPagination();
}

// 筛选商品
function filterProducts() {
    const searchTerm = document.getElementById('productSearch').value.toLowerCase();
    const categoryFilter = document.getElementById('categoryFilter').value;
    const stockFilter = document.getElementById('stockFilter').value;

    filteredProducts = products.filter(product => {
        // 搜索筛选
        const matchesSearch = !searchTerm || 
            product.name.toLowerCase().includes(searchTerm) ||
            product.sku.toLowerCase().includes(searchTerm);
        
        // 分类筛选
        const matchesCategory = !categoryFilter || product.category === categoryFilter;
        
        // 库存筛选
        let matchesStock = true;
        if (stockFilter) {
            const status = getStockStatus(product);
            switch (stockFilter) {
                case 'in-stock':
                    matchesStock = status === 'in-stock';
                    break;
                case 'low-stock':
                    matchesStock = status === 'low-stock';
                    break;
                case 'out-of-stock':
                    matchesStock = status === 'out-of-stock';
                    break;
            }
        }
        
        return matchesSearch && matchesCategory && matchesStock;
    });

    currentProductPage = 1;
    renderProductsTable();
    renderProductsPagination();
}

// 更新预警列表
function updateWarningList() {
    const warningList = document.getElementById('warningList');
    const warningCount = document.getElementById('warningCount');
    
    if (!warningList || !warningCount) return;

    const lowStockProducts = products.filter(p => p.stock <= p.minStock);
    
    warningCount.textContent = lowStockProducts.length;
    
    if (lowStockProducts.length === 0) {
        warningList.innerHTML = `
            <div class="no-warnings">
                <i class="fas fa-check-circle"></i>
                <p>暂无库存预警</p>
            </div>
        `;
        return;
    }

    warningList.innerHTML = lowStockProducts.map(product => `
        <div class="warning-item">
            <div class="warning-icon">
                <i class="fas fa-exclamation-triangle"></i>
            </div>
            <div class="warning-content">
                <h4>${product.name}</h4>
                <p>当前库存: ${product.stock} | 最低库存: ${product.minStock}</p>
            </div>
            <button class="btn-sm btn-primary" onclick="quickRestock(${product.id})">
                快速补货
            </button>
        </div>
    `).join('');
}

// 填充商品选择下拉框
function populateProductSelects() {
    const selects = ['stockInProduct', 'stockOutProduct'];

    selects.forEach(selectId => {
        const select = document.getElementById(selectId);
        if (select) {
            const options = products.map(product =>
                `<option value="${product.id}">${product.name} (${product.sku})</option>`
            ).join('');

            select.innerHTML = '<option value="">请选择商品</option>' + options;
        }
    });
}

// 显示添加商品模态框
function showAddProductModal() {
    editingProductId = null;
    document.getElementById('productModalTitle').textContent = '添加商品';
    document.getElementById('productForm').reset();
    document.getElementById('productModalOverlay').classList.add('active');
}

// 关闭商品模态框
function closeProductModal() {
    document.getElementById('productModalOverlay').classList.remove('active');
    editingProductId = null;
}

// 编辑商品
function editProduct(productId) {
    const product = products.find(p => p.id === productId);
    if (!product) return;

    editingProductId = productId;
    document.getElementById('productModalTitle').textContent = '编辑商品';

    // 填充表单数据
    document.getElementById('productName').value = product.name;
    document.getElementById('productSku').value = product.sku;
    document.getElementById('productCategory').value = product.category;
    document.getElementById('productPrice').value = product.price;
    document.getElementById('productStock').value = product.stock;
    document.getElementById('productMinStock').value = product.minStock;
    document.getElementById('productDescription').value = product.description || '';

    document.getElementById('productModalOverlay').classList.add('active');
}

// 保存商品
function saveProduct() {
    const form = document.getElementById('productForm');
    const formData = new FormData(form);

    const productData = {
        name: formData.get('name').trim(),
        sku: formData.get('sku').trim(),
        category: formData.get('category'),
        price: parseFloat(formData.get('price')),
        stock: parseInt(formData.get('stock')) || 0,
        minStock: parseInt(formData.get('minStock')) || 0,
        description: formData.get('description').trim()
    };

    // 验证必填字段
    if (!productData.name || !productData.sku || !productData.category || !productData.price) {
        showNotification('请填写所有必填字段', 'error');
        return;
    }

    // 检查SKU是否重复
    const existingProduct = products.find(p => p.sku === productData.sku && p.id !== editingProductId);
    if (existingProduct) {
        showNotification('商品编码已存在', 'error');
        return;
    }

    if (editingProductId) {
        // 编辑现有商品
        const productIndex = products.findIndex(p => p.id === editingProductId);
        if (productIndex !== -1) {
            products[productIndex] = {
                ...products[productIndex],
                ...productData,
                updatedAt: new Date().toISOString().split('T')[0]
            };
            showNotification('商品信息更新成功', 'success');
        }
    } else {
        // 添加新商品
        const newProduct = {
            id: Math.max(...products.map(p => p.id), 0) + 1,
            ...productData,
            image: 'https://images.unsplash.com/photo-1560472354-b33ff0c44a43?w=100&h=100&fit=crop',
            createdAt: new Date().toISOString().split('T')[0],
            updatedAt: new Date().toISOString().split('T')[0]
        };
        products.push(newProduct);
        showNotification('商品添加成功', 'success');
    }

    closeProductModal();
    filterProducts();
    updateInventoryStats();
    updateWarningList();
    populateProductSelects();
}

// 删除商品
function deleteProduct(productId) {
    const product = products.find(p => p.id === productId);
    if (!product) return;

    if (confirm(`确定要删除商品"${product.name}"吗？此操作不可撤销！`)) {
        const index = products.findIndex(p => p.id === productId);
        if (index !== -1) {
            products.splice(index, 1);
            showNotification('商品删除成功', 'success');
            filterProducts();
            updateInventoryStats();
            updateWarningList();
            populateProductSelects();
        }
    }
}

// 查看商品详情
function viewProductDetails(productId) {
    const product = products.find(p => p.id === productId);
    if (!product) return;

    // 这里可以显示商品详情模态框
    showNotification(`查看商品详情: ${product.name}`, 'info');
}

// 全选/取消全选商品
function toggleSelectAllProducts() {
    const selectAll = document.getElementById('selectAllProducts');
    const checkboxes = document.querySelectorAll('.product-checkbox');

    checkboxes.forEach(checkbox => {
        checkbox.checked = selectAll.checked;
    });
}

// 显示入库模态框
function showStockInModal() {
    document.getElementById('stockInForm').reset();
    populateProductSelects();
    document.getElementById('stockInModalOverlay').classList.add('active');
}

// 关闭入库模态框
function closeStockInModal() {
    document.getElementById('stockInModalOverlay').classList.remove('active');
}

// 处理入库
function processStockIn() {
    const form = document.getElementById('stockInForm');
    const formData = new FormData(form);

    const stockInData = {
        productId: parseInt(formData.get('productId')),
        quantity: parseInt(formData.get('quantity')),
        price: parseFloat(formData.get('price')) || 0,
        reason: formData.get('reason'),
        note: formData.get('note').trim()
    };

    if (!stockInData.productId || !stockInData.quantity) {
        showNotification('请填写完整的入库信息', 'error');
        return;
    }

    const product = products.find(p => p.id === stockInData.productId);
    if (!product) {
        showNotification('商品不存在', 'error');
        return;
    }

    // 更新库存
    product.stock += stockInData.quantity;
    product.updatedAt = new Date().toISOString().split('T')[0];

    // 记录入库操作
    const record = {
        id: stockRecords.length + 1,
        type: 'in',
        productId: stockInData.productId,
        productName: product.name,
        quantity: stockInData.quantity,
        price: stockInData.price,
        reason: stockInData.reason,
        note: stockInData.note,
        timestamp: new Date().toISOString(),
        operator: '当前用户'
    };
    stockRecords.push(record);

    showNotification(`${product.name} 入库成功，数量：${stockInData.quantity}`, 'success');

    closeStockInModal();
    filterProducts();
    updateInventoryStats();
    updateWarningList();
}

// 显示出库模态框
function showStockOutModal() {
    document.getElementById('stockOutForm').reset();
    populateProductSelects();
    document.getElementById('stockOutModalOverlay').classList.add('active');

    // 监听商品选择变化
    const productSelect = document.getElementById('stockOutProduct');
    productSelect.addEventListener('change', function() {
        const productId = parseInt(this.value);
        const product = products.find(p => p.id === productId);
        const currentStockInput = document.getElementById('currentStock');

        if (product) {
            currentStockInput.value = product.stock;
        } else {
            currentStockInput.value = '';
        }
    });
}

// 关闭出库模态框
function closeStockOutModal() {
    document.getElementById('stockOutModalOverlay').classList.remove('active');
}

// 处理出库
function processStockOut() {
    const form = document.getElementById('stockOutForm');
    const formData = new FormData(form);

    const stockOutData = {
        productId: parseInt(formData.get('productId')),
        quantity: parseInt(formData.get('quantity')),
        reason: formData.get('reason'),
        note: formData.get('note').trim()
    };

    if (!stockOutData.productId || !stockOutData.quantity) {
        showNotification('请填写完整的出库信息', 'error');
        return;
    }

    const product = products.find(p => p.id === stockOutData.productId);
    if (!product) {
        showNotification('商品不存在', 'error');
        return;
    }

    if (product.stock < stockOutData.quantity) {
        showNotification('库存不足，无法出库', 'error');
        return;
    }

    // 更新库存
    product.stock -= stockOutData.quantity;
    product.updatedAt = new Date().toISOString().split('T')[0];

    // 记录出库操作
    const record = {
        id: stockRecords.length + 1,
        type: 'out',
        productId: stockOutData.productId,
        productName: product.name,
        quantity: stockOutData.quantity,
        reason: stockOutData.reason,
        note: stockOutData.note,
        timestamp: new Date().toISOString(),
        operator: '当前用户'
    };
    stockRecords.push(record);

    showNotification(`${product.name} 出库成功，数量：${stockOutData.quantity}`, 'success');

    closeStockOutModal();
    filterProducts();
    updateInventoryStats();
    updateWarningList();
}

// 显示调拨模态框
function showStockTransferModal() {
    showNotification('库存调拨功能开发中...', 'info');
}

// 显示盘点模态框
function showStockCheckModal() {
    showNotification('库存盘点功能开发中...', 'info');
}

// 快速补货
function quickRestock(productId) {
    const product = products.find(p => p.id === productId);
    if (!product) return;

    const quantity = prompt(`为"${product.name}"补货多少数量？`, '50');
    if (quantity && !isNaN(quantity) && parseInt(quantity) > 0) {
        product.stock += parseInt(quantity);
        product.updatedAt = new Date().toISOString().split('T')[0];

        // 记录补货操作
        const record = {
            id: stockRecords.length + 1,
            type: 'in',
            productId: productId,
            productName: product.name,
            quantity: parseInt(quantity),
            reason: 'quick_restock',
            note: '快速补货',
            timestamp: new Date().toISOString(),
            operator: '当前用户'
        };
        stockRecords.push(record);

        showNotification(`${product.name} 补货成功`, 'success');
        filterProducts();
        updateInventoryStats();
        updateWarningList();
    }
}

// 导出库存数据
function exportInventory() {
    showNotification('正在导出库存数据...', 'info');

    // 模拟导出过程
    setTimeout(() => {
        // 创建CSV数据
        const headers = ['商品名称', '商品编码', '分类', '库存数量', '单价', '库存价值', '最低库存', '状态'];
        const csvData = [
            headers.join(','),
            ...products.map(product => [
                product.name,
                product.sku,
                getCategoryLabel(product.category),
                product.stock,
                product.price,
                product.price * product.stock,
                product.minStock,
                getStockStatusLabel(product)
            ].join(','))
        ].join('\n');

        // 创建下载链接
        const blob = new Blob([csvData], { type: 'text/csv;charset=utf-8;' });
        const link = document.createElement('a');
        const url = URL.createObjectURL(blob);
        link.setAttribute('href', url);
        link.setAttribute('download', `库存数据_${new Date().toISOString().split('T')[0]}.csv`);
        link.style.visibility = 'hidden';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        showNotification('库存数据导出完成', 'success');
    }, 2000);
}

// 批量操作
function batchOperation(operation) {
    const selectedProducts = Array.from(document.querySelectorAll('.product-checkbox:checked'))
        .map(cb => parseInt(cb.value));

    if (selectedProducts.length === 0) {
        showNotification('请先选择要操作的商品', 'warning');
        return;
    }

    switch (operation) {
        case 'delete':
            if (confirm(`确定要删除选中的 ${selectedProducts.length} 个商品吗？`)) {
                selectedProducts.forEach(productId => {
                    const index = products.findIndex(p => p.id === productId);
                    if (index !== -1) {
                        products.splice(index, 1);
                    }
                });
                showNotification(`成功删除 ${selectedProducts.length} 个商品`, 'success');
                filterProducts();
                updateInventoryStats();
                updateWarningList();
                populateProductSelects();
            }
            break;
        case 'export':
            showNotification(`正在导出选中的 ${selectedProducts.length} 个商品...`, 'info');
            break;
        default:
            showNotification('未知操作', 'error');
    }
}

// 全局函数导出
window.initializeInventoryManagement = initializeInventoryManagement;
window.filterProducts = filterProducts;
window.changeProductPage = changeProductPage;
window.toggleSelectAllProducts = toggleSelectAllProducts;
window.showAddProductModal = showAddProductModal;
window.closeProductModal = closeProductModal;
window.editProduct = editProduct;
window.saveProduct = saveProduct;
window.deleteProduct = deleteProduct;
window.viewProductDetails = viewProductDetails;
window.showStockInModal = showStockInModal;
window.closeStockInModal = closeStockInModal;
window.processStockIn = processStockIn;
window.showStockOutModal = showStockOutModal;
window.closeStockOutModal = closeStockOutModal;
window.processStockOut = processStockOut;
window.showStockTransferModal = showStockTransferModal;
window.showStockCheckModal = showStockCheckModal;
window.quickRestock = quickRestock;
window.exportInventory = exportInventory;
window.batchOperation = batchOperation;
