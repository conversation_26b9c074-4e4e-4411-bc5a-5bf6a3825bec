/* 现代化主题增强样式 */

/* 全局增强 */
:root {
    /* 统一颜色系统 - 覆盖styles.css中的定义 */
    --primary-color: #667eea;
    --secondary-color: #764ba2;
    --primary-light: #7c8aed;
    --primary-dark: #5a6fd8;

    /* 现代化阴影 */
    --shadow-modern: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    --shadow-modern-lg: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
    --shadow-modern-xl: 0 35px 60px -12px rgba(0, 0, 0, 0.35);
    
    /* 现代化渐变 */
    --gradient-glass: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
    --gradient-card: linear-gradient(145deg, rgba(255, 255, 255, 0.95), rgba(255, 255, 255, 0.85));
    
    /* 毛玻璃效果 */
    --backdrop-blur: blur(20px);
    --backdrop-blur-sm: blur(10px);
}

/* 页面容器增强 */
.page-content {
    padding: 32px;
    position: relative;
    z-index: 1;
}

/* 卡片增强效果 */
.dashboard-card,
.stat-card {
    position: relative;
    overflow: hidden;
}

.dashboard-card::before,
.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.8), transparent);
    z-index: 1;
}

/* 悬浮动画增强 */
.dashboard-card,
.stat-card,
.page-header {
    animation: fadeInUp 0.6s ease-out;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 统计卡片特殊效果 */
.stat-card.primary {
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.1), rgba(255, 255, 255, 0.95));
    border-left: 4px solid var(--primary-color);
}

.stat-card.success {
    background: linear-gradient(135deg, rgba(16, 185, 129, 0.1), rgba(255, 255, 255, 0.95));
    border-left: 4px solid var(--success-color);
}

.stat-card.warning {
    background: linear-gradient(135deg, rgba(245, 158, 11, 0.1), rgba(255, 255, 255, 0.95));
    border-left: 4px solid var(--warning-color);
}

.stat-card.danger {
    background: linear-gradient(135deg, rgba(239, 68, 68, 0.1), rgba(255, 255, 255, 0.95));
    border-left: 4px solid var(--danger-color);
}

/* 图标增强效果 */
.stat-icon {
    position: relative;
    overflow: hidden;
}

.stat-icon::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transform: rotate(45deg);
    transition: all 0.6s;
    opacity: 0;
}

.stat-card:hover .stat-icon::before {
    opacity: 1;
    animation: shimmer 1.5s ease-in-out;
}

@keyframes shimmer {
    0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
    100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
}

/* 导航项增强效果 */
.nav-item {
    position: relative;
    overflow: hidden;
}

.nav-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    transition: left 0.6s;
}

.nav-item:hover::before {
    left: 100%;
}

/* 按钮增强效果 */
.btn-secondary {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: var(--text-secondary);
    backdrop-filter: var(--backdrop-blur-sm);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.btn-secondary:hover {
    background: rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.3);
    transform: translateY(-2px);
    box-shadow: var(--shadow-modern);
}

/* 表格增强 */
.table-container {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 16px;
    overflow: hidden;
    backdrop-filter: var(--backdrop-blur);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

table {
    background: transparent;
}

th {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: var(--backdrop-blur-sm);
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

tr:hover {
    background: rgba(102, 126, 234, 0.05);
    transform: scale(1.01);
    transition: all 0.2s ease;
}

/* 模态框增强 */
.modal {
    backdrop-filter: blur(10px);
    background: rgba(0, 0, 0, 0.5);
}

.modal-content {
    background: var(--gradient-card);
    backdrop-filter: var(--backdrop-blur);
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: var(--shadow-modern-xl);
    animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: translateY(-50px) scale(0.9);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

/* 搜索框增强 */
.search-box {
    position: relative;
    overflow: hidden;
}

.search-box::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--gradient-glass);
    border-radius: inherit;
    z-index: -1;
}

.search-box input {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    backdrop-filter: var(--backdrop-blur-sm);
}

.search-box input:focus {
    background: rgba(255, 255, 255, 0.15);
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

/* 通知增强 */
.notification {
    background: var(--gradient-card);
    backdrop-filter: var(--backdrop-blur);
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: var(--shadow-modern);
    animation: notificationSlide 0.3s ease-out;
}

@keyframes notificationSlide {
    from {
        opacity: 0;
        transform: translateX(100%);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* 加载状态增强 */
.loading {
    position: relative;
    overflow: hidden;
}

.loading::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
    animation: loading 1.5s infinite;
}

@keyframes loading {
    0% { left: -100%; }
    100% { left: 100%; }
}

/* 响应式增强 */
@media (max-width: 768px) {
    .page-content {
        padding: 20px;
    }
    
    .dashboard-card,
    .stat-card {
        border-radius: 16px;
    }
    
    .btn-primary,
    .btn-secondary {
        padding: 12px 20px;
    }
}

/* 深色主题适配 */
.theme-dark .dashboard-card,
.theme-dark .stat-card,
.theme-dark .page-header {
    background: rgba(45, 45, 45, 0.95);
    border-color: rgba(255, 255, 255, 0.1);
}

.theme-dark .table-container {
    background: rgba(45, 45, 45, 0.95);
}

.theme-dark th {
    background: rgba(255, 255, 255, 0.05);
}

.theme-dark tr:hover {
    background: rgba(102, 126, 234, 0.1);
}

/* 滚动条美化 */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: rgba(102, 126, 234, 0.3);
    border-radius: 4px;
    transition: background 0.3s;
}

::-webkit-scrollbar-thumb:hover {
    background: rgba(102, 126, 234, 0.5);
}

/* 选择文本样式 */
::selection {
    background: rgba(102, 126, 234, 0.3);
    color: white;
}

/* 现代化欢迎横幅 */
.welcome-banner {
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1));
    border-radius: 24px;
    padding: 40px;
    margin-bottom: 32px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    backdrop-filter: var(--backdrop-blur);
    position: relative;
    overflow: hidden;
}

.welcome-banner::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        radial-gradient(circle at 20% 20%, rgba(102, 126, 234, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 80%, rgba(118, 75, 162, 0.1) 0%, transparent 50%);
    pointer-events: none;
    z-index: 0;
}

.welcome-content {
    display: grid;
    grid-template-columns: 1fr auto;
    gap: 40px;
    align-items: center;
    position: relative;
    z-index: 1;
}

.welcome-text {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.welcome-title {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--text-primary);
    margin: 0;
    line-height: 1.2;
}

.greeting {
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.user-name-display {
    color: var(--text-primary);
}

.wave-emoji {
    display: inline-block;
    animation: wave 2s ease-in-out infinite;
    transform-origin: 70% 70%;
}

@keyframes wave {
    0%, 100% { transform: rotate(0deg); }
    10%, 30%, 50%, 70%, 90% { transform: rotate(14deg); }
    20%, 40%, 60%, 80% { transform: rotate(-8deg); }
}

.welcome-subtitle {
    font-size: 1.1rem;
    color: var(--text-secondary);
    margin: 0;
    opacity: 0.8;
}

.welcome-stats {
    display: flex;
    gap: 32px;
    margin-top: 8px;
}

.welcome-stat-item {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.welcome-stat-item .stat-value {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--primary-color);
}

.welcome-stat-item .stat-label {
    font-size: 0.875rem;
    color: var(--text-muted);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.welcome-visual {
    position: relative;
    width: 200px;
    height: 200px;
}

.floating-elements {
    position: relative;
    width: 100%;
    height: 100%;
}

.floating-element {
    position: absolute;
    font-size: 2rem;
    animation: float 3s ease-in-out infinite;
    animation-delay: var(--delay);
    opacity: 0.7;
}

.floating-element:nth-child(1) {
    top: 20%;
    left: 20%;
}

.floating-element:nth-child(2) {
    top: 20%;
    right: 20%;
}

.floating-element:nth-child(3) {
    bottom: 20%;
    left: 20%;
}

.floating-element:nth-child(4) {
    bottom: 20%;
    right: 20%;
}

@keyframes float {
    0%, 100% {
        transform: translateY(0px) rotate(0deg);
    }
    33% {
        transform: translateY(-20px) rotate(5deg);
    }
    66% {
        transform: translateY(10px) rotate(-5deg);
    }
}

/* 响应式欢迎横幅 */
@media (max-width: 768px) {
    .welcome-banner {
        padding: 24px;
        margin-bottom: 24px;
    }

    .welcome-content {
        grid-template-columns: 1fr;
        gap: 24px;
        text-align: center;
    }

    .welcome-title {
        font-size: 2rem;
    }

    .welcome-stats {
        justify-content: center;
        gap: 24px;
    }

    .welcome-visual {
        width: 150px;
        height: 150px;
        margin: 0 auto;
    }

    .floating-element {
        font-size: 1.5rem;
    }
}

/* 快捷操作按钮 */
.quick-actions {
    display: flex;
    gap: 12px;
    margin-top: 20px;
    flex-wrap: wrap;
}

.quick-action-btn {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 10px 16px;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 12px;
    color: var(--text-secondary);
    text-decoration: none;
    font-size: 13px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    backdrop-filter: var(--backdrop-blur-sm);
}

.quick-action-btn:hover {
    background: rgba(255, 255, 255, 0.2);
    border-color: var(--primary-color);
    color: var(--primary-color);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.2);
}

.quick-action-btn i {
    font-size: 14px;
}

/* 成就徽章 */
.achievement-badge {
    position: absolute;
    top: 20px;
    right: 20px;
    background: var(--gradient-primary);
    color: white;
    padding: 12px 20px;
    border-radius: 16px;
    display: flex;
    align-items: center;
    gap: 10px;
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
    transform: translateX(100px);
    opacity: 0;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    z-index: 10;
}

.achievement-badge.show {
    transform: translateX(0);
    opacity: 1;
}

.badge-icon {
    font-size: 20px;
    animation: bounce 0.6s ease-in-out;
}

.badge-text {
    font-weight: 600;
    font-size: 14px;
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateY(0);
    }
    40% {
        transform: translateY(-10px);
    }
    60% {
        transform: translateY(-5px);
    }
}

/* 系统状态样式 */
.stat-value.status-normal {
    color: var(--success-color);
}

.stat-value.status-maintenance {
    color: var(--warning-color);
}

/* 响应式快捷操作 */
@media (max-width: 768px) {
    .quick-actions {
        justify-content: center;
        gap: 8px;
    }

    .quick-action-btn {
        padding: 8px 12px;
        font-size: 12px;
    }

    .quick-action-btn span {
        display: none;
    }

    .quick-action-btn i {
        font-size: 16px;
    }

    .achievement-badge {
        top: 10px;
        right: 10px;
        padding: 8px 12px;
        font-size: 12px;
    }

    .badge-icon {
        font-size: 16px;
    }
}

/* 用户账户管理样式 */
.password-input-group {
    position: relative;
    display: flex;
    align-items: center;
}

.password-input-group input {
    flex: 1;
    padding-right: 45px;
}

.password-toggle {
    position: absolute;
    right: 12px;
    background: none;
    border: none;
    color: var(--text-muted);
    cursor: pointer;
    padding: 4px;
    border-radius: 4px;
    transition: var(--transition);
}

.password-toggle:hover {
    color: var(--text-primary);
    background: var(--gray-100);
}

.password-strength {
    margin-top: 8px;
}

.strength-bar {
    width: 100%;
    height: 4px;
    background: var(--gray-200);
    border-radius: 2px;
    overflow: hidden;
    margin-bottom: 4px;
}

.strength-fill {
    height: 100%;
    width: 0%;
    background: var(--gray-400);
    transition: all 0.3s ease;
    border-radius: 2px;
}

.strength-text {
    font-size: 12px;
    color: var(--text-muted);
    font-weight: 500;
}

.password-match {
    margin-top: 4px;
    font-size: 12px;
    font-weight: 500;
}

.password-requirements {
    margin-top: 16px;
    padding: 12px;
    background: var(--gray-50);
    border-radius: var(--border-radius);
    border: 1px solid var(--gray-200);
}

.password-requirements h4 {
    margin: 0 0 8px 0;
    font-size: 13px;
    color: var(--text-secondary);
}

.password-requirements ul {
    list-style: none;
    margin: 0;
    padding: 0;
}

.password-requirements li {
    font-size: 12px;
    padding: 2px 0;
    display: flex;
    align-items: center;
    gap: 6px;
}

.password-requirements li::before {
    content: '○';
    color: var(--gray-400);
    font-weight: bold;
}

.password-requirements li.requirement-met {
    color: var(--success-color);
}

.password-requirements li.requirement-met::before {
    content: '✓';
    color: var(--success-color);
}

.password-requirements li.requirement-unmet {
    color: var(--text-muted);
}

/* 个人资料样式 */
.profile-avatar-section {
    text-align: center;
    margin-bottom: 24px;
}

.profile-avatar-container {
    position: relative;
    display: inline-block;
}

.profile-avatar-container img {
    width: 100px;
    height: 100px;
    border-radius: 50%;
    object-fit: cover;
    border: 4px solid var(--gray-200);
    transition: var(--transition);
}

.avatar-upload-btn {
    position: absolute;
    bottom: 0;
    right: 0;
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background: var(--primary-color);
    color: white;
    border: 2px solid white;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    transition: var(--transition);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.avatar-upload-btn:hover {
    background: var(--primary-dark);
    transform: scale(1.1);
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 16px;
    margin-bottom: 16px;
}

.form-group {
    display: flex;
    flex-direction: column;
    gap: 6px;
}

.form-group label {
    font-weight: 600;
    color: var(--text-secondary);
    font-size: 13px;
}

.form-group input,
.form-group select,
.form-group textarea {
    padding: 10px 12px;
    border: 1px solid var(--gray-300);
    border-radius: var(--border-radius);
    font-size: 14px;
    transition: var(--transition);
    background: white;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

.form-group input[readonly] {
    background: var(--gray-50);
    color: var(--text-muted);
    cursor: not-allowed;
}

.form-group textarea {
    resize: vertical;
    min-height: 80px;
}

/* 模态框增强 */
.modal-content {
    max-width: 500px;
    width: 90%;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 24px;
    border-bottom: 1px solid var(--gray-200);
}

.modal-header h3 {
    margin: 0;
    color: var(--text-primary);
    font-size: 18px;
    font-weight: 600;
}

.modal-close {
    background: none;
    border: none;
    color: var(--text-muted);
    cursor: pointer;
    padding: 4px;
    border-radius: 4px;
    transition: var(--transition);
    font-size: 16px;
}

.modal-close:hover {
    color: var(--text-primary);
    background: var(--gray-100);
}

.modal-body {
    padding: 24px;
    max-height: 70vh;
    overflow-y: auto;
}

.modal-footer {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
    padding: 16px 24px;
    border-top: 1px solid var(--gray-200);
    background: var(--gray-50);
}

/* 响应式调整 */
@media (max-width: 768px) {
    .form-row {
        grid-template-columns: 1fr;
        gap: 12px;
    }

    .modal-content {
        width: 95%;
        margin: 20px auto;
    }

    .modal-body {
        padding: 20px;
    }

    .modal-header,
    .modal-footer {
        padding: 16px 20px;
    }

    .profile-avatar-container img {
        width: 80px;
        height: 80px;
    }

    .avatar-upload-btn {
        width: 28px;
        height: 28px;
        font-size: 10px;
    }
}

/* 实时通知系统样式 */
.notification-container {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 10000;
    pointer-events: none;
}

.notification-toast {
    display: flex;
    align-items: flex-start;
    gap: 12px;
    background: rgba(255, 255, 255, 0.95);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 12px;
    padding: 16px;
    margin-bottom: 12px;
    min-width: 320px;
    max-width: 400px;
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    backdrop-filter: blur(20px);
    pointer-events: auto;
    animation: slideInRight 0.3s ease-out;
    cursor: pointer;
    transition: all 0.3s ease;
}

.notification-toast:hover {
    transform: translateX(-5px);
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
}

.notification-toast.fade-out {
    animation: slideOutRight 0.3s ease-in forwards;
}

.notification-toast.high {
    border-left: 4px solid #ef4444;
    animation: slideInRight 0.3s ease-out, pulse 2s infinite;
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(100%);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slideOutRight {
    from {
        opacity: 1;
        transform: translateX(0);
    }
    to {
        opacity: 0;
        transform: translateX(100%);
    }
}

@keyframes pulse {
    0%, 100% {
        box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    }
    50% {
        box-shadow: 0 20px 25px -5px rgba(239, 68, 68, 0.2), 0 10px 10px -5px rgba(239, 68, 68, 0.1);
    }
}

.toast-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 18px;
    color: white;
    flex-shrink: 0;
}

.notification-toast.system .toast-icon {
    background: linear-gradient(135deg, #6366f1, #8b5cf6);
}

.notification-toast.user .toast-icon {
    background: linear-gradient(135deg, #10b981, #059669);
}

.notification-toast.order .toast-icon {
    background: linear-gradient(135deg, #f59e0b, #d97706);
}

.notification-toast.security .toast-icon {
    background: linear-gradient(135deg, #ef4444, #dc2626);
}

.notification-toast.info .toast-icon {
    background: linear-gradient(135deg, #3b82f6, #2563eb);
}

.notification-toast.warning .toast-icon {
    background: linear-gradient(135deg, #f59e0b, #d97706);
}

.notification-toast.error .toast-icon {
    background: linear-gradient(135deg, #ef4444, #dc2626);
}

.notification-toast.success .toast-icon {
    background: linear-gradient(135deg, #10b981, #059669);
}

.toast-content {
    flex: 1;
    min-width: 0;
}

.toast-title {
    font-weight: 600;
    color: var(--text-primary);
    font-size: 14px;
    margin-bottom: 4px;
}

.toast-message {
    color: var(--text-secondary);
    font-size: 13px;
    line-height: 1.4;
    word-wrap: break-word;
}

.toast-close {
    background: none;
    border: none;
    color: var(--text-muted);
    cursor: pointer;
    padding: 4px;
    border-radius: 4px;
    transition: var(--transition);
    flex-shrink: 0;
}

.toast-close:hover {
    color: var(--text-primary);
    background: var(--gray-100);
}

/* 通知面板样式 */
.notification-panel {
    position: fixed;
    top: 0;
    right: -400px;
    width: 400px;
    height: 100vh;
    background: rgba(255, 255, 255, 0.95);
    border-left: 1px solid rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(20px);
    z-index: 9999;
    transition: right 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    display: flex;
    flex-direction: column;
    box-shadow: -10px 0 25px rgba(0, 0, 0, 0.1);
}

.notification-panel.show {
    right: 0;
}

.notification-panel-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 24px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    background: rgba(255, 255, 255, 0.1);
}

.notification-panel-header h3 {
    margin: 0;
    color: var(--text-primary);
    font-size: 18px;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 8px;
}

.notification-panel-actions {
    display: flex;
    gap: 8px;
}

.notification-panel-body {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.notification-filters {
    display: flex;
    gap: 4px;
    padding: 16px 24px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    background: rgba(255, 255, 255, 0.05);
}

.filter-btn {
    padding: 6px 12px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    background: transparent;
    color: var(--text-secondary);
    border-radius: 6px;
    font-size: 12px;
    cursor: pointer;
    transition: var(--transition);
}

.filter-btn:hover {
    background: rgba(255, 255, 255, 0.1);
    color: var(--text-primary);
}

.filter-btn.active {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.notification-list {
    flex: 1;
    overflow-y: auto;
    padding: 0;
}

.notification-item {
    display: flex;
    align-items: flex-start;
    gap: 12px;
    padding: 16px 24px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    transition: var(--transition);
    cursor: pointer;
}

.notification-item:hover {
    background: rgba(255, 255, 255, 0.1);
}

.notification-item.unread {
    background: rgba(102, 126, 234, 0.05);
    border-left: 3px solid var(--primary-color);
}

.notification-item.high {
    border-left: 3px solid #ef4444;
}

.notification-icon {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
    background: var(--gray-100);
    flex-shrink: 0;
}

.notification-content {
    flex: 1;
    min-width: 0;
}

.notification-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 4px;
}

.notification-title {
    font-weight: 600;
    color: var(--text-primary);
    font-size: 13px;
}

.notification-time {
    color: var(--text-muted);
    font-size: 11px;
}

.notification-message {
    color: var(--text-secondary);
    font-size: 12px;
    line-height: 1.4;
    word-wrap: break-word;
}

.notification-actions {
    display: flex;
    gap: 4px;
    opacity: 0;
    transition: var(--transition);
}

.notification-item:hover .notification-actions {
    opacity: 1;
}

.notification-panel-footer {
    padding: 16px 24px;
    border-top: 1px solid rgba(255, 255, 255, 0.2);
    background: rgba(255, 255, 255, 0.05);
}

.connection-status {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 12px;
    color: var(--text-muted);
}

.connection-status i {
    font-size: 8px;
}

.no-notifications {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 60px 20px;
    color: var(--text-muted);
    text-align: center;
}

.no-notifications i {
    font-size: 48px;
    margin-bottom: 16px;
    opacity: 0.5;
}

.no-notifications p {
    margin: 0;
    font-size: 14px;
}

/* 响应式通知面板 */
@media (max-width: 768px) {
    .notification-panel {
        width: 100vw;
        right: -100vw;
    }

    .notification-toast {
        min-width: 280px;
        max-width: calc(100vw - 40px);
    }

    .notification-container {
        left: 20px;
        right: 20px;
    }
}

/* 高级图表样式 */
.charts-section {
    margin-top: 32px;
}

.charts-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 24px;
    margin-bottom: 32px;
}

.chart-card {
    background: rgba(255, 255, 255, 0.95);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 16px;
    padding: 24px;
    backdrop-filter: blur(20px);
    box-shadow: var(--shadow-modern);
    transition: all 0.3s ease;
}

.chart-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-modern-lg);
}

.chart-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 12px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

.chart-header h3 {
    margin: 0;
    color: var(--text-primary);
    font-size: 16px;
    font-weight: 600;
}

.chart-actions {
    display: flex;
    gap: 8px;
}

.chart-container {
    position: relative;
    height: 300px;
    width: 100%;
}

.chart-container canvas {
    max-width: 100%;
    max-height: 100%;
}

/* 特殊图表样式 */
.chart-card:nth-child(1) {
    grid-column: span 2;
}

.chart-card:nth-child(3) .chart-container {
    height: 250px;
}

.chart-card:nth-child(4) .chart-container {
    height: 250px;
}

/* 响应式图表 */
@media (max-width: 1200px) {
    .charts-grid {
        grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    }

    .chart-card:nth-child(1) {
        grid-column: span 1;
    }
}

@media (max-width: 768px) {
    .charts-grid {
        grid-template-columns: 1fr;
        gap: 16px;
    }

    .chart-card {
        padding: 16px;
    }

    .chart-container {
        height: 250px;
    }

    .chart-header h3 {
        font-size: 14px;
    }
}

/* 高级搜索样式 */
.global-search-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(10px);
    z-index: 10000;
    display: flex;
    align-items: flex-start;
    justify-content: center;
    padding-top: 10vh;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.global-search-overlay.show {
    opacity: 1;
    visibility: visible;
}

.global-search-container {
    background: rgba(255, 255, 255, 0.95);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 16px;
    width: 90%;
    max-width: 600px;
    backdrop-filter: blur(20px);
    box-shadow: var(--shadow-modern-xl);
    animation: searchSlideIn 0.3s ease-out;
}

@keyframes searchSlideIn {
    from {
        opacity: 0;
        transform: translateY(-50px) scale(0.9);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

.global-search-header {
    display: flex;
    align-items: center;
    gap: 16px;
    padding: 20px 24px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

.search-input-container {
    flex: 1;
    position: relative;
    display: flex;
    align-items: center;
}

.search-icon {
    position: absolute;
    left: 16px;
    color: var(--text-muted);
    font-size: 16px;
    z-index: 1;
}

#globalSearchInput {
    width: 100%;
    padding: 12px 16px 12px 48px;
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 12px;
    background: rgba(255, 255, 255, 0.1);
    color: var(--text-primary);
    font-size: 16px;
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
}

#globalSearchInput:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
    background: rgba(255, 255, 255, 0.2);
}

#globalSearchInput::placeholder {
    color: var(--text-muted);
}

.search-clear {
    position: absolute;
    right: 12px;
    background: none;
    border: none;
    color: var(--text-muted);
    cursor: pointer;
    padding: 4px;
    border-radius: 4px;
    transition: var(--transition);
}

.search-clear:hover {
    color: var(--text-primary);
    background: rgba(255, 255, 255, 0.2);
}

.search-close {
    background: none;
    border: none;
    color: var(--text-muted);
    cursor: pointer;
    padding: 8px;
    border-radius: 8px;
    transition: var(--transition);
    font-size: 16px;
}

.search-close:hover {
    color: var(--text-primary);
    background: rgba(255, 255, 255, 0.2);
}

.search-suggestions {
    max-height: 400px;
    overflow-y: auto;
    padding: 0;
    display: none;
}

.search-suggestion {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px 24px;
    cursor: pointer;
    transition: var(--transition);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.search-suggestion:hover {
    background: rgba(255, 255, 255, 0.1);
}

.search-suggestion:last-child {
    border-bottom: none;
}

.suggestion-icon {
    width: 32px;
    height: 32px;
    border-radius: 8px;
    background: var(--gray-100);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--text-secondary);
    flex-shrink: 0;
}

.suggestion-content {
    flex: 1;
    min-width: 0;
}

.suggestion-primary {
    font-weight: 500;
    color: var(--text-primary);
    font-size: 14px;
    margin-bottom: 2px;
}

.suggestion-secondary {
    color: var(--text-muted);
    font-size: 12px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.suggestion-type {
    color: var(--text-muted);
    font-size: 11px;
    background: var(--gray-100);
    padding: 2px 8px;
    border-radius: 4px;
    flex-shrink: 0;
}

.search-quick-filters {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 16px 24px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    background: rgba(255, 255, 255, 0.05);
}

.filter-label {
    color: var(--text-muted);
    font-size: 12px;
    font-weight: 500;
}

.quick-filter {
    padding: 4px 12px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    background: transparent;
    color: var(--text-secondary);
    border-radius: 6px;
    font-size: 12px;
    cursor: pointer;
    transition: var(--transition);
}

.quick-filter:hover {
    background: rgba(255, 255, 255, 0.1);
    color: var(--text-primary);
    border-color: var(--primary-color);
}

.search-history {
    padding: 16px 24px;
    display: none;
}

.search-history-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
}

.search-history-header span {
    color: var(--text-secondary);
    font-size: 13px;
    font-weight: 500;
}

.search-history-header button {
    background: none;
    border: none;
    color: var(--text-muted);
    cursor: pointer;
    font-size: 12px;
    padding: 4px 8px;
    border-radius: 4px;
    transition: var(--transition);
}

.search-history-header button:hover {
    color: var(--primary-color);
    background: rgba(102, 126, 234, 0.1);
}

.search-history-item {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 0;
    cursor: pointer;
    color: var(--text-secondary);
    font-size: 13px;
    transition: var(--transition);
}

.search-history-item:hover {
    color: var(--text-primary);
}

.search-history-item i {
    color: var(--text-muted);
    font-size: 12px;
}

.search-history-empty {
    text-align: center;
    color: var(--text-muted);
    font-size: 13px;
    padding: 20px 0;
}

.no-results {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 40px 20px;
    color: var(--text-muted);
    text-align: center;
}

.no-results i {
    font-size: 32px;
    margin-bottom: 12px;
    opacity: 0.5;
}

.no-results p {
    margin: 0;
    font-size: 14px;
}

/* 搜索高亮效果 */
.search-highlight {
    background: rgba(102, 126, 234, 0.1) !important;
    border: 2px solid var(--primary-color) !important;
    border-radius: 8px !important;
    animation: searchHighlight 2s ease-in-out;
}

@keyframes searchHighlight {
    0%, 100% {
        box-shadow: 0 0 0 0 rgba(102, 126, 234, 0.4);
    }
    50% {
        box-shadow: 0 0 0 10px rgba(102, 126, 234, 0);
    }
}

/* 系统监控面板样式 */
.system-monitor-panel {
    position: fixed;
    top: 0;
    right: -500px;
    width: 500px;
    height: 100vh;
    background: rgba(255, 255, 255, 0.95);
    border-left: 1px solid rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(20px);
    z-index: 9998;
    transition: right 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    display: flex;
    flex-direction: column;
    box-shadow: -10px 0 25px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.system-monitor-panel.show {
    right: 0;
}

.monitor-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 24px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    background: rgba(255, 255, 255, 0.1);
}

.monitor-header h3 {
    margin: 0;
    color: var(--text-primary);
    font-size: 18px;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 8px;
}

.monitor-controls {
    display: flex;
    gap: 8px;
}

.monitor-body {
    flex: 1;
    overflow-y: auto;
    padding: 0;
}

.monitor-section {
    padding: 20px 24px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.monitor-section:last-child {
    border-bottom: none;
}

.monitor-section h4 {
    margin: 0 0 16px 0;
    color: var(--text-secondary);
    font-size: 14px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.system-overview {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.overview-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.overview-icon {
    width: 40px;
    height: 40px;
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 16px;
    flex-shrink: 0;
}

.overview-icon.cpu {
    background: linear-gradient(135deg, #ef4444, #dc2626);
}

.overview-icon.memory {
    background: linear-gradient(135deg, #f59e0b, #d97706);
}

.overview-icon.disk {
    background: linear-gradient(135deg, #10b981, #059669);
}

.overview-icon.network {
    background: linear-gradient(135deg, #3b82f6, #2563eb);
}

.overview-content {
    flex: 1;
    min-width: 0;
}

.overview-label {
    font-size: 12px;
    color: var(--text-muted);
    margin-bottom: 4px;
}

.overview-value {
    font-size: 18px;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 6px;
}

.overview-bar {
    width: 100%;
    height: 4px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 2px;
    overflow: hidden;
}

.bar-fill {
    height: 100%;
    border-radius: 2px;
    transition: all 0.3s ease;
}

.bar-fill.normal {
    background: linear-gradient(90deg, #10b981, #059669);
}

.bar-fill.warning {
    background: linear-gradient(90deg, #f59e0b, #d97706);
}

.bar-fill.critical {
    background: linear-gradient(90deg, #ef4444, #dc2626);
}

.network-speed {
    display: flex;
    gap: 12px;
    font-size: 11px;
    color: var(--text-muted);
}

.monitor-chart {
    height: 200px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    padding: 16px;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.system-info {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    padding: 16px;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.info-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 12px;
}

.info-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.info-item:last-child {
    border-bottom: none;
}

.info-label {
    font-size: 13px;
    color: var(--text-muted);
}

.info-value {
    font-size: 13px;
    color: var(--text-primary);
    font-weight: 500;
}

.process-monitor {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    padding: 16px;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.process-controls {
    display: flex;
    gap: 12px;
    margin-bottom: 16px;
}

.process-search {
    flex: 1;
    padding: 8px 12px;
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 8px;
    background: rgba(255, 255, 255, 0.1);
    color: var(--text-primary);
    font-size: 13px;
}

.process-search:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.1);
}

.process-list {
    max-height: 200px;
    overflow-y: auto;
}

.process-header {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr 1fr;
    gap: 12px;
    padding: 8px 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    font-size: 12px;
    font-weight: 600;
    color: var(--text-secondary);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.process-item {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr 1fr;
    gap: 12px;
    padding: 8px 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    font-size: 12px;
    color: var(--text-primary);
}

.process-item:last-child {
    border-bottom: none;
}

.process-name {
    font-weight: 500;
}

.process-cpu {
    color: #ef4444;
    font-weight: 500;
}

.process-memory {
    color: #f59e0b;
    font-weight: 500;
}

.system-alerts {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    padding: 16px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    max-height: 200px;
    overflow-y: auto;
}

.alert-item {
    display: flex;
    align-items: flex-start;
    gap: 12px;
    padding: 12px;
    margin-bottom: 8px;
    border-radius: 8px;
    border-left: 4px solid;
}

.alert-item:last-child {
    margin-bottom: 0;
}

.alert-item.warning {
    background: rgba(245, 158, 11, 0.1);
    border-left-color: #f59e0b;
}

.alert-item.critical {
    background: rgba(239, 68, 68, 0.1);
    border-left-color: #ef4444;
}

.alert-icon {
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.alert-item.warning .alert-icon {
    color: #f59e0b;
}

.alert-item.critical .alert-icon {
    color: #ef4444;
}

.alert-content {
    flex: 1;
    min-width: 0;
}

.alert-message {
    font-size: 13px;
    color: var(--text-primary);
    font-weight: 500;
    margin-bottom: 2px;
}

.alert-time {
    font-size: 11px;
    color: var(--text-muted);
}

.no-alerts {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 40px 20px;
    color: var(--text-muted);
    text-align: center;
}

.no-alerts i {
    font-size: 32px;
    margin-bottom: 12px;
    color: #10b981;
}

.no-alerts p {
    margin: 0;
    font-size: 13px;
}

/* 响应式系统监控 */
@media (max-width: 768px) {
    .system-monitor-panel {
        width: 100vw;
        right: -100vw;
    }

    .monitor-section {
        padding: 16px 20px;
    }

    .overview-item {
        padding: 10px;
    }

    .overview-icon {
        width: 32px;
        height: 32px;
        font-size: 14px;
    }

    .overview-value {
        font-size: 16px;
    }

    .process-header,
    .process-item {
        grid-template-columns: 1.5fr 0.8fr 0.8fr 0.9fr;
        gap: 8px;
    }

    .monitor-chart {
        height: 150px;
        padding: 12px;
    }
}

/* 语言选择器样式 */
.language-selector {
    position: relative;
    margin-right: 12px;
}

.language-btn {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 12px;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 8px;
    color: var(--text-primary);
    cursor: pointer;
    transition: var(--transition);
    font-size: 13px;
    backdrop-filter: blur(10px);
}

.language-btn:hover {
    background: rgba(255, 255, 255, 0.15);
    border-color: rgba(255, 255, 255, 0.3);
}

.current-language-flag {
    font-size: 16px;
}

.current-language-name {
    font-weight: 500;
}

.language-btn i {
    font-size: 10px;
    transition: transform 0.3s ease;
}

.language-btn:hover i {
    transform: rotate(180deg);
}

.language-dropdown {
    position: absolute;
    top: 100%;
    right: 0;
    margin-top: 8px;
    background: rgba(255, 255, 255, 0.95);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 12px;
    backdrop-filter: blur(20px);
    box-shadow: var(--shadow-modern-lg);
    min-width: 200px;
    z-index: 10001;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all 0.3s ease;
}

.language-dropdown.show {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.language-option {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px 16px;
    cursor: pointer;
    transition: var(--transition);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.language-option:last-child {
    border-bottom: none;
}

.language-option:hover {
    background: rgba(255, 255, 255, 0.1);
}

.language-option.active {
    background: rgba(102, 126, 234, 0.1);
    color: var(--primary-color);
}

.language-flag {
    font-size: 16px;
}

.language-name {
    flex: 1;
    font-size: 13px;
    font-weight: 500;
}

.language-option .fas.fa-check {
    color: var(--primary-color);
    font-size: 12px;
}

/* RTL支持 */
.rtl {
    direction: rtl;
}

.rtl .language-dropdown {
    right: auto;
    left: 0;
}

.rtl .sidebar {
    right: 0;
    left: auto;
}

.rtl .main-content {
    margin-right: 280px;
    margin-left: 0;
}

.rtl .header-actions {
    flex-direction: row-reverse;
}

.rtl .nav-item {
    text-align: right;
}

.rtl .nav-icon {
    margin-left: 12px;
    margin-right: 0;
}

.rtl .notification-toast {
    right: auto;
    left: 20px;
}

.rtl .notification-panel {
    right: auto;
    left: -400px;
}

.rtl .notification-panel.show {
    left: 0;
}

.rtl .system-monitor-panel {
    right: auto;
    left: -500px;
}

.rtl .system-monitor-panel.show {
    left: 0;
}

/* 工作流管理样式 */
.workflow-panel {
    position: fixed;
    top: 0;
    right: -600px;
    width: 600px;
    height: 100vh;
    background: rgba(255, 255, 255, 0.95);
    border-left: 1px solid rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(20px);
    z-index: 9997;
    transition: right 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    display: flex;
    flex-direction: column;
    box-shadow: -10px 0 25px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.workflow-panel.show {
    right: 0;
}

.workflow-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 24px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    background: rgba(255, 255, 255, 0.1);
}

.workflow-header h3 {
    margin: 0;
    color: var(--text-primary);
    font-size: 18px;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 8px;
}

.workflow-controls {
    display: flex;
    gap: 8px;
}

.workflow-body {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.workflow-tabs {
    display: flex;
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    background: rgba(255, 255, 255, 0.05);
}

.workflow-tabs .tab-btn {
    flex: 1;
    padding: 12px 16px;
    border: none;
    background: transparent;
    color: var(--text-secondary);
    cursor: pointer;
    transition: var(--transition);
    font-size: 13px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 6px;
    border-bottom: 2px solid transparent;
}

.workflow-tabs .tab-btn:hover {
    background: rgba(255, 255, 255, 0.1);
    color: var(--text-primary);
}

.workflow-tabs .tab-btn.active {
    color: var(--primary-color);
    border-bottom-color: var(--primary-color);
    background: rgba(102, 126, 234, 0.1);
}

.workflow-content {
    flex: 1;
    overflow-y: auto;
    padding: 0;
}

.workflow-content .tab-content {
    display: none;
    padding: 20px 24px;
}

.workflow-content .tab-content.active {
    display: block;
}

.workflow-filters {
    display: flex;
    gap: 12px;
    margin-bottom: 20px;
    flex-wrap: wrap;
}

.workflow-filters select,
.workflow-filters input {
    padding: 8px 12px;
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 8px;
    background: rgba(255, 255, 255, 0.1);
    color: var(--text-primary);
    font-size: 13px;
    backdrop-filter: blur(10px);
}

.workflow-filters input {
    flex: 1;
    min-width: 200px;
}

.workflow-list {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.workflow-item {
    display: flex;
    align-items: center;
    gap: 16px;
    padding: 16px;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 12px;
    transition: var(--transition);
}

.workflow-item:hover {
    background: rgba(255, 255, 255, 0.15);
    transform: translateY(-2px);
    box-shadow: var(--shadow-modern);
}

.workflow-info {
    flex: 1;
    min-width: 0;
}

.workflow-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
}

.workflow-header h4 {
    margin: 0;
    color: var(--text-primary);
    font-size: 14px;
    font-weight: 600;
}

.workflow-status {
    padding: 2px 8px;
    border-radius: 4px;
    font-size: 11px;
    font-weight: 500;
    text-transform: uppercase;
}

.workflow-status.running {
    background: rgba(16, 185, 129, 0.2);
    color: #10b981;
}

.workflow-status.waiting {
    background: rgba(245, 158, 11, 0.2);
    color: #f59e0b;
}

.workflow-status.completed {
    background: rgba(59, 130, 246, 0.2);
    color: #3b82f6;
}

.workflow-status.paused {
    background: rgba(107, 114, 128, 0.2);
    color: #6b7280;
}

.workflow-meta {
    display: flex;
    gap: 12px;
    margin-bottom: 8px;
    flex-wrap: wrap;
}

.workflow-meta span {
    font-size: 12px;
    color: var(--text-muted);
}

.priority {
    display: flex;
    align-items: center;
    gap: 4px;
}

.priority.high {
    color: #ef4444;
}

.priority.medium {
    color: #f59e0b;
}

.priority.low {
    color: #10b981;
}

.workflow-progress {
    margin-bottom: 8px;
}

.workflow-progress .progress-bar {
    width: 100%;
    height: 4px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 2px;
    overflow: hidden;
    margin-bottom: 4px;
}

.workflow-progress .progress-fill {
    height: 100%;
    background: linear-gradient(90deg, var(--primary-color), var(--primary-light));
    border-radius: 2px;
    transition: width 0.3s ease;
}

.workflow-progress .progress-text {
    font-size: 11px;
    color: var(--text-muted);
    font-weight: 500;
}

.workflow-time {
    display: flex;
    gap: 16px;
    flex-wrap: wrap;
}

.workflow-time span {
    font-size: 11px;
    color: var(--text-muted);
}

.workflow-actions {
    display: flex;
    gap: 4px;
}

.empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 60px 20px;
    color: var(--text-muted);
    text-align: center;
}

.empty-state i {
    font-size: 48px;
    margin-bottom: 16px;
    opacity: 0.5;
}

.empty-state p {
    margin: 0;
    font-size: 14px;
}

/* 响应式工作流面板 */
@media (max-width: 768px) {
    .workflow-panel {
        width: 100vw;
        right: -100vw;
    }

    .workflow-filters {
        flex-direction: column;
    }

    .workflow-filters input {
        min-width: auto;
    }

    .workflow-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 12px;
    }

    .workflow-actions {
        align-self: flex-end;
    }
}

/* 设置下拉菜单样式 */
.settings-dropdown {
    position: relative;
}

.dropdown-menu {
    position: absolute;
    top: 100%;
    right: 0;
    margin-top: 8px;
    background: rgba(255, 255, 255, 0.95);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 12px;
    backdrop-filter: blur(20px);
    box-shadow: var(--shadow-modern-lg);
    min-width: 200px;
    z-index: 10001;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all 0.3s ease;
    overflow: hidden;
}

.dropdown-menu.show {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.dropdown-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px 16px;
    cursor: pointer;
    transition: var(--transition);
    color: var(--text-primary);
    font-size: 13px;
}

.dropdown-item:hover {
    background: rgba(255, 255, 255, 0.1);
}

.dropdown-item i {
    width: 16px;
    color: var(--text-secondary);
    font-size: 14px;
}

.dropdown-divider {
    height: 1px;
    background: rgba(255, 255, 255, 0.2);
    margin: 8px 0;
}

/* 主题定制器样式 */
.theme-customizer {
    position: fixed;
    top: 0;
    right: -400px;
    width: 400px;
    height: 100vh;
    background: rgba(255, 255, 255, 0.95);
    border-left: 1px solid rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(20px);
    z-index: 9996;
    transition: right 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    display: flex;
    flex-direction: column;
    box-shadow: -10px 0 25px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.theme-customizer.show {
    right: 0;
}

.customizer-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 24px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    background: rgba(255, 255, 255, 0.1);
}

.customizer-header h3 {
    margin: 0;
    color: var(--text-primary);
    font-size: 18px;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 8px;
}

.customizer-actions {
    display: flex;
    gap: 8px;
}

.customizer-body {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.customizer-tabs {
    display: flex;
    flex-wrap: wrap;
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    background: rgba(255, 255, 255, 0.05);
}

.customizer-tabs .tab-btn {
    flex: 1;
    min-width: 0;
    padding: 10px 8px;
    border: none;
    background: transparent;
    color: var(--text-secondary);
    cursor: pointer;
    transition: var(--transition);
    font-size: 11px;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 4px;
    border-bottom: 2px solid transparent;
}

.customizer-tabs .tab-btn:hover {
    background: rgba(255, 255, 255, 0.1);
    color: var(--text-primary);
}

.customizer-tabs .tab-btn.active {
    color: var(--primary-color);
    border-bottom-color: var(--primary-color);
    background: rgba(102, 126, 234, 0.1);
}

.customizer-tabs .tab-btn i {
    font-size: 14px;
}

.customizer-content {
    flex: 1;
    overflow-y: auto;
    padding: 0;
}

.customizer-content .tab-content {
    display: none;
    padding: 20px 24px;
}

.customizer-content .tab-content.active {
    display: block;
}

.preset-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 12px;
    margin-bottom: 24px;
}

.preset-card {
    position: relative;
    border: 2px solid rgba(255, 255, 255, 0.2);
    border-radius: 12px;
    overflow: hidden;
    cursor: pointer;
    transition: var(--transition);
}

.preset-card:hover {
    border-color: rgba(255, 255, 255, 0.4);
    transform: translateY(-2px);
}

.preset-card.active {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2);
}

.preset-preview {
    height: 60px;
    width: 100%;
}

.preset-info {
    padding: 12px;
    background: rgba(255, 255, 255, 0.1);
}

.preset-info h5 {
    margin: 0 0 4px 0;
    font-size: 13px;
    font-weight: 600;
    color: var(--text-primary);
}

.preset-info p {
    margin: 0;
    font-size: 11px;
    color: var(--text-muted);
    line-height: 1.4;
}

.preset-check {
    position: absolute;
    top: 8px;
    right: 8px;
    color: var(--primary-color);
    background: white;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 10px;
}

.color-section,
.typography-section,
.layout-section,
.effects-section {
    margin-bottom: 24px;
}

.color-section h4,
.typography-section h4,
.layout-section h4,
.effects-section h4 {
    margin: 0 0 16px 0;
    color: var(--text-secondary);
    font-size: 14px;
    font-weight: 600;
}

.color-group,
.font-group,
.size-group,
.layout-group,
.effects-group {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.color-item,
.font-item,
.size-item,
.layout-item,
.effect-item {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.color-item label,
.font-item label,
.size-item label,
.layout-item label,
.effect-item label {
    font-size: 12px;
    color: var(--text-secondary);
    font-weight: 500;
}

.color-input-group {
    display: flex;
    gap: 8px;
}

.color-input-group input[type="color"] {
    width: 40px;
    height: 32px;
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 6px;
    cursor: pointer;
}

.color-input-group input[type="text"] {
    flex: 1;
    padding: 6px 10px;
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 6px;
    background: rgba(255, 255, 255, 0.1);
    color: var(--text-primary);
    font-size: 12px;
    font-family: monospace;
}

.size-input {
    display: flex;
    align-items: center;
    gap: 12px;
}

.size-input input[type="range"] {
    flex: 1;
}

.size-input span {
    min-width: 50px;
    font-size: 12px;
    color: var(--text-primary);
    font-weight: 500;
    text-align: right;
}

.font-item select,
.layout-item select {
    padding: 8px 12px;
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 6px;
    background: rgba(255, 255, 255, 0.1);
    color: var(--text-primary);
    font-size: 12px;
}

.customizer-footer {
    border-top: 1px solid rgba(255, 255, 255, 0.2);
    padding: 20px 24px;
    background: rgba(255, 255, 255, 0.05);
}

.customizer-footer h4 {
    margin: 0 0 12px 0;
    color: var(--text-secondary);
    font-size: 14px;
    font-weight: 600;
}

.preview-container {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    padding: 16px;
}

.preview-card {
    background: var(--card-background);
    border-radius: var(--border-radius);
    padding: 16px;
    box-shadow: var(--shadow-modern);
}

.preview-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
}

.preview-header h5 {
    margin: 0;
    color: var(--text-primary);
    font-size: 14px;
}

.preview-btn {
    padding: 6px 12px;
    background: var(--primary-color);
    color: white;
    border: none;
    border-radius: 6px;
    font-size: 12px;
    cursor: pointer;
}

.preview-card p {
    margin: 0 0 12px 0;
    color: var(--text-secondary);
    font-size: 12px;
    line-height: 1.4;
}

.preview-stats {
    display: flex;
    gap: 16px;
}

.preview-stat {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 4px;
}

.stat-number {
    font-size: 16px;
    font-weight: 700;
    color: var(--primary-color);
}

.stat-label {
    font-size: 10px;
    color: var(--text-muted);
}

/* 响应式主题定制器 */
@media (max-width: 768px) {
    .theme-customizer {
        width: 100vw;
        right: -100vw;
    }

    .preset-grid {
        grid-template-columns: 1fr;
    }

    .customizer-tabs .tab-btn {
        font-size: 10px;
        padding: 8px 6px;
    }

    .customizer-tabs .tab-btn i {
        font-size: 12px;
    }
}

/* 关于对话框样式 */
.about-modal .modal-content {
    max-width: 500px;
    width: 90%;
}

.about-content {
    text-align: center;
    padding: 20px 0;
}

.about-logo {
    margin-bottom: 20px;
}

.about-logo i {
    font-size: 48px;
    color: var(--primary-color);
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.about-content h4 {
    margin: 0 0 8px 0;
    color: var(--text-primary);
    font-size: 24px;
    font-weight: 700;
}

.version {
    margin: 0 0 16px 0;
    color: var(--text-muted);
    font-size: 14px;
    font-weight: 500;
}

.description {
    margin: 0 0 24px 0;
    color: var(--text-secondary);
    font-size: 14px;
    line-height: 1.6;
    text-align: left;
}

.features {
    margin-bottom: 24px;
    text-align: left;
}

.features h5 {
    margin: 0 0 12px 0;
    color: var(--text-primary);
    font-size: 16px;
    font-weight: 600;
}

.features ul {
    margin: 0;
    padding: 0;
    list-style: none;
}

.features li {
    margin-bottom: 8px;
    color: var(--text-secondary);
    font-size: 13px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.tech-stack {
    text-align: left;
}

.tech-stack h5 {
    margin: 0 0 12px 0;
    color: var(--text-primary);
    font-size: 16px;
    font-weight: 600;
}

.tech-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
}

.tech-tag {
    padding: 4px 8px;
    background: rgba(102, 126, 234, 0.1);
    color: var(--primary-color);
    border-radius: 4px;
    font-size: 11px;
    font-weight: 500;
}

/* 旧的智能助手样式已移除，使用新的ai-assistant-floating-btn */

.assistant-avatar {
    color: white;
    font-size: 24px;
}

.assistant-pulse {
    position: absolute;
    width: 100%;
    height: 100%;
    border-radius: 50%;
    background: rgba(102, 126, 234, 0.3);
    animation: pulse 2s infinite;
}

/* assistantPulse动画已移除 */

@keyframes pulse {
    0% { transform: scale(1); opacity: 1; }
    100% { transform: scale(1.4); opacity: 0; }
}

.assistant-panel {
    position: fixed;
    bottom: 100px;
    right: 30px;
    width: 400px;
    height: 600px;
    background: rgba(255, 255, 255, 0.95);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 20px;
    backdrop-filter: blur(20px);
    box-shadow: var(--shadow-modern-xl);
    z-index: 9998;
    display: flex;
    flex-direction: column;
    opacity: 0;
    visibility: hidden;
    transform: translateY(20px) scale(0.95);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    overflow: hidden;
}

.assistant-panel.show {
    opacity: 1;
    visibility: visible;
    transform: translateY(0) scale(1);
}

.assistant-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 24px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    background: rgba(255, 255, 255, 0.1);
}

.assistant-info {
    display: flex;
    align-items: center;
    gap: 12px;
}

.assistant-avatar-large {
    width: 40px;
    height: 40px;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 18px;
}

.assistant-details h3 {
    margin: 0;
    color: var(--text-primary);
    font-size: 16px;
    font-weight: 600;
}

.assistant-status {
    font-size: 12px;
    color: #10b981;
    font-weight: 500;
}

.assistant-controls {
    display: flex;
    gap: 8px;
}

.control-btn {
    width: 32px;
    height: 32px;
    border: none;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    color: var(--text-secondary);
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    justify-content: center;
}

.control-btn:hover {
    background: rgba(255, 255, 255, 0.2);
    color: var(--text-primary);
}

.assistant-body {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.conversation-area {
    flex: 1;
    overflow-y: auto;
    padding: 16px;
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.message {
    display: flex;
    gap: 12px;
    max-width: 100%;
}

.user-message {
    flex-direction: row-reverse;
}

.message-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
    flex-shrink: 0;
}

.assistant-message .message-avatar {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: white;
}

.user-message .message-avatar {
    background: rgba(255, 255, 255, 0.2);
    color: var(--text-primary);
    border: 1px solid rgba(255, 255, 255, 0.3);
}

.message-content {
    flex: 1;
    min-width: 0;
}

.message-text {
    background: rgba(255, 255, 255, 0.1);
    padding: 12px 16px;
    border-radius: 16px;
    color: var(--text-primary);
    font-size: 14px;
    line-height: 1.5;
    white-space: pre-line;
    word-wrap: break-word;
}

.user-message .message-text {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: white;
    margin-left: auto;
    max-width: 80%;
}

.assistant-message .message-text {
    max-width: 90%;
}

.message-time {
    font-size: 11px;
    color: var(--text-muted);
    margin-top: 4px;
    text-align: right;
}

.user-message .message-time {
    text-align: left;
}

.message-actions {
    margin-top: 8px;
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
}

.action-btn {
    padding: 6px 12px;
    background: rgba(102, 126, 234, 0.1);
    color: var(--primary-color);
    border: 1px solid rgba(102, 126, 234, 0.2);
    border-radius: 8px;
    font-size: 12px;
    cursor: pointer;
    transition: var(--transition);
}

.action-btn:hover {
    background: rgba(102, 126, 234, 0.2);
    border-color: rgba(102, 126, 234, 0.3);
}

.typing {
    opacity: 0.7;
}

.typing-dots {
    display: flex;
    gap: 4px;
    padding: 12px 16px;
}

.typing-dots span {
    width: 6px;
    height: 6px;
    background: var(--text-muted);
    border-radius: 50%;
    animation: typingDots 1.4s infinite ease-in-out;
}

.typing-dots span:nth-child(1) { animation-delay: -0.32s; }
.typing-dots span:nth-child(2) { animation-delay: -0.16s; }

@keyframes typingDots {
    0%, 80%, 100% { transform: scale(0.8); opacity: 0.5; }
    40% { transform: scale(1); opacity: 1; }
}

.suggestions-area {
    border-top: 1px solid rgba(255, 255, 255, 0.2);
    padding: 16px;
    background: rgba(255, 255, 255, 0.05);
}

.suggestions-title {
    font-size: 12px;
    color: var(--text-secondary);
    font-weight: 600;
    margin-bottom: 12px;
}

.suggestions-list {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.suggestion-item {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 12px;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 8px;
    color: var(--text-primary);
    font-size: 12px;
    cursor: pointer;
    transition: var(--transition);
    text-align: left;
}

.suggestion-item:hover {
    background: rgba(255, 255, 255, 0.15);
    border-color: rgba(255, 255, 255, 0.3);
}

.suggestion-item i {
    color: var(--primary-color);
    font-size: 10px;
}

.input-area {
    border-top: 1px solid rgba(255, 255, 255, 0.2);
    padding: 16px;
    background: rgba(255, 255, 255, 0.05);
}

.input-container {
    display: flex;
    gap: 8px;
    margin-bottom: 12px;
}

.input-container input {
    flex: 1;
    padding: 12px 16px;
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 12px;
    background: rgba(255, 255, 255, 0.1);
    color: var(--text-primary);
    font-size: 14px;
    outline: none;
    transition: var(--transition);
}

.input-container input:focus {
    border-color: var(--primary-color);
    background: rgba(255, 255, 255, 0.15);
}

.input-container input::placeholder {
    color: var(--text-muted);
}

.send-btn, .voice-btn {
    width: 40px;
    height: 40px;
    border: none;
    border-radius: 10px;
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
}

.send-btn {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: white;
}

.send-btn:hover {
    transform: scale(1.05);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.voice-btn {
    background: rgba(255, 255, 255, 0.1);
    color: var(--text-secondary);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.voice-btn:hover {
    background: rgba(255, 255, 255, 0.15);
    color: var(--text-primary);
}

.voice-btn.listening {
    background: #ef4444;
    color: white;
    animation: pulse 1s infinite;
}

.quick-actions {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
}

.quick-btn {
    display: flex;
    align-items: center;
    gap: 6px;
    padding: 6px 10px;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 8px;
    color: var(--text-secondary);
    font-size: 11px;
    cursor: pointer;
    transition: var(--transition);
}

.quick-btn:hover {
    background: rgba(255, 255, 255, 0.15);
    color: var(--text-primary);
    border-color: rgba(255, 255, 255, 0.3);
}

.quick-btn i {
    font-size: 10px;
}

/* 响应式智能助手 */
@media (max-width: 768px) {
    .assistant-panel {
        bottom: 20px;
        right: 20px;
        left: 20px;
        width: auto;
        height: 70vh;
        max-height: 500px;
    }

    .assistant-button {
        bottom: 20px;
        right: 20px;
        width: 50px;
        height: 50px;
    }

    .assistant-avatar {
        font-size: 20px;
    }

    .quick-actions {
        justify-content: center;
    }

    .quick-btn {
        flex: 1;
        justify-content: center;
        min-width: 0;
    }
}

/* 权限管理样式 */
.permission-panel {
    position: fixed;
    top: 0;
    right: -700px;
    width: 700px;
    height: 100vh;
    background: rgba(255, 255, 255, 0.95);
    border-left: 1px solid rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(20px);
    z-index: 9995;
    transition: right 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    display: flex;
    flex-direction: column;
    box-shadow: -10px 0 25px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.permission-panel.show {
    right: 0;
}

.permission-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 24px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    background: rgba(255, 255, 255, 0.1);
}

.permission-header h3 {
    margin: 0;
    color: var(--text-primary);
    font-size: 18px;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 8px;
}

.permission-controls {
    display: flex;
    gap: 8px;
}

.permission-body {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.permission-tabs {
    display: flex;
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    background: rgba(255, 255, 255, 0.05);
}

.permission-tabs .tab-btn {
    flex: 1;
    padding: 12px 16px;
    border: none;
    background: transparent;
    color: var(--text-secondary);
    cursor: pointer;
    transition: var(--transition);
    font-size: 12px;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 4px;
    border-bottom: 2px solid transparent;
}

.permission-tabs .tab-btn:hover {
    background: rgba(255, 255, 255, 0.1);
    color: var(--text-primary);
}

.permission-tabs .tab-btn.active {
    color: var(--primary-color);
    border-bottom-color: var(--primary-color);
    background: rgba(102, 126, 234, 0.1);
}

.permission-tabs .tab-btn i {
    font-size: 14px;
}

.permission-content {
    flex: 1;
    overflow-y: auto;
    padding: 20px 24px;
}

.permission-content .tab-content {
    display: none;
}

.permission-content .tab-content.active {
    display: block;
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.section-header h4 {
    margin: 0;
    color: var(--text-primary);
    font-size: 16px;
    font-weight: 600;
}

.roles-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 16px;
}

.role-card {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 12px;
    padding: 16px;
    transition: var(--transition);
}

.role-card:hover {
    background: rgba(255, 255, 255, 0.15);
    transform: translateY(-2px);
    box-shadow: var(--shadow-modern);
}

.role-card.inactive {
    opacity: 0.6;
}

.role-header {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 12px;
}

.role-icon {
    width: 40px;
    height: 40px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 16px;
}

.role-info {
    flex: 1;
}

.role-info h5 {
    margin: 0 0 4px 0;
    color: var(--text-primary);
    font-size: 14px;
    font-weight: 600;
}

.role-level {
    font-size: 11px;
    color: var(--text-muted);
}

.role-status {
    padding: 2px 8px;
    border-radius: 4px;
    font-size: 10px;
    font-weight: 500;
    text-transform: uppercase;
}

.role-status.active {
    background: rgba(16, 185, 129, 0.2);
    color: #10b981;
}

.role-status.inactive {
    background: rgba(107, 114, 128, 0.2);
    color: #6b7280;
}

.role-description {
    color: var(--text-secondary);
    font-size: 12px;
    line-height: 1.4;
    margin-bottom: 12px;
}

.role-permissions {
    margin-bottom: 12px;
}

.permission-count {
    font-size: 11px;
    color: var(--text-muted);
    font-weight: 500;
}

.permission-preview {
    display: flex;
    gap: 4px;
    flex-wrap: wrap;
    margin-top: 4px;
}

.permission-tag {
    padding: 2px 6px;
    background: rgba(102, 126, 234, 0.1);
    color: var(--primary-color);
    border-radius: 3px;
    font-size: 9px;
    font-weight: 500;
}

.permission-more {
    color: var(--text-muted);
    font-size: 9px;
}

.role-actions {
    display: flex;
    gap: 4px;
    justify-content: flex-end;
}

/* API管理样式 */
.api-panel {
    position: fixed;
    top: 0;
    right: -800px;
    width: 800px;
    height: 100vh;
    background: rgba(255, 255, 255, 0.95);
    border-left: 1px solid rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(20px);
    z-index: 9994;
    transition: right 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    display: flex;
    flex-direction: column;
    box-shadow: -10px 0 25px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.api-panel.show {
    right: 0;
}

.api-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 24px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    background: rgba(255, 255, 255, 0.1);
}

.api-header h3 {
    margin: 0;
    color: var(--text-primary);
    font-size: 18px;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 8px;
}

.api-controls {
    display: flex;
    gap: 8px;
}

.api-body {
    flex: 1;
    display: flex;
    overflow: hidden;
}

.api-sidebar {
    width: 250px;
    border-right: 1px solid rgba(255, 255, 255, 0.2);
    background: rgba(255, 255, 255, 0.05);
    overflow-y: auto;
}

.api-main {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.request-builder {
    flex: 1;
    padding: 20px;
    overflow-y: auto;
}

.request-line {
    display: flex;
    gap: 8px;
    margin-bottom: 20px;
}

.method-select {
    width: 100px;
    padding: 8px 12px;
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 6px;
    background: rgba(255, 255, 255, 0.1);
    color: var(--text-primary);
    font-size: 13px;
    font-weight: 600;
}

.url-input {
    flex: 1;
    padding: 8px 12px;
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 6px;
    background: rgba(255, 255, 255, 0.1);
    color: var(--text-primary);
    font-size: 13px;
}

.send-btn {
    padding: 8px 16px;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: white;
    border: none;
    border-radius: 6px;
    font-size: 13px;
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition);
}

.send-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.request-tabs {
    display: flex;
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    margin-bottom: 20px;
}

.request-tabs .tab-btn {
    padding: 8px 16px;
    border: none;
    background: transparent;
    color: var(--text-secondary);
    cursor: pointer;
    transition: var(--transition);
    font-size: 12px;
    border-bottom: 2px solid transparent;
}

.request-tabs .tab-btn:hover {
    color: var(--text-primary);
}

.request-tabs .tab-btn.active {
    color: var(--primary-color);
    border-bottom-color: var(--primary-color);
}

/* 备份恢复样式 */
.backup-panel {
    position: fixed;
    top: 0;
    right: -600px;
    width: 600px;
    height: 100vh;
    background: rgba(255, 255, 255, 0.95);
    border-left: 1px solid rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(20px);
    z-index: 9993;
    transition: right 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    display: flex;
    flex-direction: column;
    box-shadow: -10px 0 25px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.backup-panel.show {
    right: 0;
}

.backup-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 24px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    background: rgba(255, 255, 255, 0.1);
}

.backup-header h3 {
    margin: 0;
    color: var(--text-primary);
    font-size: 18px;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 8px;
}

.backup-controls {
    display: flex;
    gap: 8px;
}

.backup-body {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.backup-tabs {
    display: flex;
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    background: rgba(255, 255, 255, 0.05);
}

.backup-tabs .tab-btn {
    flex: 1;
    padding: 10px 8px;
    border: none;
    background: transparent;
    color: var(--text-secondary);
    cursor: pointer;
    transition: var(--transition);
    font-size: 11px;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 4px;
    border-bottom: 2px solid transparent;
}

.backup-tabs .tab-btn:hover {
    background: rgba(255, 255, 255, 0.1);
    color: var(--text-primary);
}

.backup-tabs .tab-btn.active {
    color: var(--primary-color);
    border-bottom-color: var(--primary-color);
    background: rgba(102, 126, 234, 0.1);
}

.backup-tabs .tab-btn i {
    font-size: 14px;
}

.backup-content {
    flex: 1;
    overflow-y: auto;
    padding: 20px 24px;
}

.backup-content .tab-content {
    display: none;
}

.backup-content .tab-content.active {
    display: block;
}

.backup-section {
    margin-bottom: 24px;
}

.backup-section h4 {
    margin: 0 0 16px 0;
    color: var(--text-primary);
    font-size: 16px;
    font-weight: 600;
}

.backup-options {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.backup-options .checkbox-label {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 8px;
    cursor: pointer;
    transition: var(--transition);
}

.backup-options .checkbox-label:hover {
    background: rgba(255, 255, 255, 0.15);
}

.option-info {
    flex: 1;
}

.option-name {
    display: block;
    color: var(--text-primary);
    font-size: 14px;
    font-weight: 500;
    margin-bottom: 2px;
}

.option-desc {
    display: block;
    color: var(--text-muted);
    font-size: 12px;
}

.backup-config {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.config-group {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.config-group label {
    color: var(--text-secondary);
    font-size: 13px;
    font-weight: 500;
}

.config-group input,
.config-group textarea,
.config-group select {
    padding: 8px 12px;
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 6px;
    background: rgba(255, 255, 255, 0.1);
    color: var(--text-primary);
    font-size: 13px;
}

.config-group textarea {
    resize: vertical;
    min-height: 60px;
}

.config-options {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.backup-actions {
    display: flex;
    gap: 12px;
    justify-content: flex-end;
    margin-top: 24px;
}

.backup-progress {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 8px;
    padding: 16px;
    margin-top: 20px;
}

.progress-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
}

.progress-header h5 {
    margin: 0;
    color: var(--text-primary);
    font-size: 14px;
}

.progress-percentage {
    color: var(--primary-color);
    font-size: 14px;
    font-weight: 600;
}

.progress-bar {
    width: 100%;
    height: 6px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 3px;
    overflow: hidden;
    margin-bottom: 8px;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
    border-radius: 3px;
    transition: width 0.3s ease;
    width: 0%;
}

.progress-details {
    color: var(--text-secondary);
    font-size: 12px;
}

.restore-options {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 16px;
}

.restore-option {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 12px;
    padding: 20px;
    cursor: pointer;
    transition: var(--transition);
    text-align: center;
}

.restore-option:hover {
    background: rgba(255, 255, 255, 0.15);
    transform: translateY(-2px);
    box-shadow: var(--shadow-modern);
}

.restore-option .option-icon {
    width: 48px;
    height: 48px;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 12px;
    color: white;
    font-size: 20px;
}

.restore-option h5 {
    margin: 0 0 8px 0;
    color: var(--text-primary);
    font-size: 14px;
    font-weight: 600;
}

.restore-option p {
    margin: 0;
    color: var(--text-secondary);
    font-size: 12px;
    line-height: 1.4;
}

.backup-list {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.backup-item {
    display: flex;
    align-items: center;
    gap: 16px;
    padding: 16px;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 12px;
    transition: var(--transition);
}

.backup-item:hover {
    background: rgba(255, 255, 255, 0.15);
}

.backup-info {
    flex: 1;
    min-width: 0;
}

.backup-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 4px;
}

.backup-header h5 {
    margin: 0;
    color: var(--text-primary);
    font-size: 14px;
    font-weight: 600;
}

.backup-date {
    color: var(--text-muted);
    font-size: 11px;
}

.backup-description {
    color: var(--text-secondary);
    font-size: 12px;
    margin-bottom: 8px;
}

.backup-meta {
    display: flex;
    gap: 12px;
    flex-wrap: wrap;
}

.backup-meta span {
    padding: 2px 6px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 3px;
    font-size: 10px;
    color: var(--text-muted);
}

.backup-actions {
    display: flex;
    gap: 4px;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .permission-panel,
    .api-panel,
    .backup-panel {
        width: 100vw;
        right: -100vw;
    }

    .permission-panel.show,
    .api-panel.show,
    .backup-panel.show {
        right: 0;
    }

    .api-body {
        flex-direction: column;
    }

    .api-sidebar {
        width: 100%;
        height: 200px;
        border-right: none;
        border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    }

    .roles-grid {
        grid-template-columns: 1fr;
    }

    .restore-options {
        grid-template-columns: 1fr;
    }
}

/* 性能分析样式 */
.performance-panel {
    position: fixed;
    top: 0;
    right: -800px;
    width: 800px;
    height: 100vh;
    background: rgba(255, 255, 255, 0.95);
    border-left: 1px solid rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(20px);
    z-index: 9992;
    transition: right 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    display: flex;
    flex-direction: column;
    box-shadow: -10px 0 25px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.performance-panel.show {
    right: 0;
}

.performance-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 24px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    background: rgba(255, 255, 255, 0.1);
}

.performance-header h3 {
    margin: 0;
    color: var(--text-primary);
    font-size: 18px;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 8px;
}

.performance-controls {
    display: flex;
    gap: 8px;
}

.performance-body {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.performance-tabs {
    display: flex;
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    background: rgba(255, 255, 255, 0.05);
}

.performance-tabs .tab-btn {
    flex: 1;
    padding: 10px 8px;
    border: none;
    background: transparent;
    color: var(--text-secondary);
    cursor: pointer;
    transition: var(--transition);
    font-size: 11px;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 4px;
    border-bottom: 2px solid transparent;
}

.performance-tabs .tab-btn:hover {
    background: rgba(255, 255, 255, 0.1);
    color: var(--text-primary);
}

.performance-tabs .tab-btn.active {
    color: var(--primary-color);
    border-bottom-color: var(--primary-color);
    background: rgba(102, 126, 234, 0.1);
}

.performance-tabs .tab-btn i {
    font-size: 14px;
}

.performance-content {
    flex: 1;
    overflow-y: auto;
    padding: 20px 24px;
}

.performance-content .tab-content {
    display: none;
}

.performance-content .tab-content.active {
    display: block;
}

.overview-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
    gap: 16px;
    margin-bottom: 24px;
}

.perf-card {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 12px;
    padding: 16px;
    display: flex;
    align-items: center;
    gap: 12px;
    transition: var(--transition);
}

.perf-card:hover {
    background: rgba(255, 255, 255, 0.15);
    transform: translateY(-2px);
}

.perf-card .card-icon {
    width: 40px;
    height: 40px;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 16px;
}

.perf-card .card-content {
    flex: 1;
}

.perf-card h4 {
    margin: 0 0 4px 0;
    color: var(--text-secondary);
    font-size: 12px;
    font-weight: 500;
}

.metric-value {
    font-size: 18px;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 4px;
}

.metric-trend {
    display: flex;
    align-items: center;
    gap: 4px;
    font-size: 11px;
    color: #f59e0b;
}

.metric-trend.good {
    color: #10b981;
}

.metric-trend i {
    font-size: 10px;
}

.performance-charts {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
}

.chart-container {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 12px;
    padding: 16px;
}

.chart-container h4 {
    margin: 0 0 16px 0;
    color: var(--text-primary);
    font-size: 14px;
    font-weight: 600;
}

.metrics-grid {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.metric-item {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 8px;
    padding: 16px;
}

.metric-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
}

.metric-name {
    color: var(--text-primary);
    font-size: 14px;
    font-weight: 500;
}

.metric-score {
    font-size: 14px;
    font-weight: 600;
    padding: 2px 8px;
    border-radius: 4px;
}

.metric-score.good {
    background: rgba(16, 185, 129, 0.2);
    color: #10b981;
}

.metric-score.warning {
    background: rgba(245, 158, 11, 0.2);
    color: #f59e0b;
}

.metric-score.error {
    background: rgba(239, 68, 68, 0.2);
    color: #ef4444;
}

.metric-bar {
    width: 100%;
    height: 4px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 2px;
    overflow: hidden;
    margin-bottom: 8px;
}

.metric-fill {
    height: 100%;
    border-radius: 2px;
    transition: width 0.3s ease;
}

.metric-fill.good {
    background: #10b981;
}

.metric-fill.warning {
    background: #f59e0b;
}

.metric-fill.error {
    background: #ef4444;
}

.metric-description {
    color: var(--text-muted);
    font-size: 12px;
}

.js-metrics {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 12px;
}

.js-metric {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 6px;
}

.js-metric-name {
    color: var(--text-secondary);
    font-size: 13px;
}

.js-metric-value {
    color: var(--text-primary);
    font-size: 13px;
    font-weight: 600;
}

.memory-overview {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 16px;
    margin-bottom: 24px;
}

.memory-card {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 12px;
    padding: 16px;
}

.memory-card h5 {
    margin: 0 0 12px 0;
    color: var(--text-primary);
    font-size: 14px;
    font-weight: 600;
}

.memory-usage {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.usage-bar {
    width: 100%;
    height: 8px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 4px;
    overflow: hidden;
}

.usage-fill {
    height: 100%;
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
    border-radius: 4px;
    transition: width 0.3s ease;
}

.usage-text {
    color: var(--text-secondary);
    font-size: 12px;
}

.memory-metric {
    text-align: center;
}

.metric-number {
    display: block;
    font-size: 24px;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: 4px;
}

.metric-label {
    color: var(--text-muted);
    font-size: 12px;
}

.network-overview {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 16px;
    margin-bottom: 24px;
}

.network-card {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 12px;
    padding: 16px;
    text-align: center;
}

.network-card h5 {
    margin: 0 0 12px 0;
    color: var(--text-primary);
    font-size: 14px;
    font-weight: 600;
}

.network-metric {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 4px;
}

.alerts-list {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.alert-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px;
    border-radius: 8px;
    border-left: 4px solid;
}

.alert-item.info {
    background: rgba(59, 130, 246, 0.1);
    border-left-color: #3b82f6;
}

.alert-item.warning {
    background: rgba(245, 158, 11, 0.1);
    border-left-color: #f59e0b;
}

.alert-item.error {
    background: rgba(239, 68, 68, 0.1);
    border-left-color: #ef4444;
}

.alert-item.resolved {
    opacity: 0.6;
}

.alert-icon {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
}

.alert-item.info .alert-icon {
    background: rgba(59, 130, 246, 0.2);
    color: #3b82f6;
}

.alert-item.warning .alert-icon {
    background: rgba(245, 158, 11, 0.2);
    color: #f59e0b;
}

.alert-item.error .alert-icon {
    background: rgba(239, 68, 68, 0.2);
    color: #ef4444;
}

.alert-content {
    flex: 1;
}

.alert-message {
    color: var(--text-primary);
    font-size: 14px;
    margin-bottom: 4px;
}

.alert-time {
    color: var(--text-muted);
    font-size: 12px;
}

.alert-actions {
    display: flex;
    gap: 4px;
}

.resource-performance-table {
    width: 100%;
    border-collapse: collapse;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    overflow: hidden;
}

.resource-performance-table th,
.resource-performance-table td {
    padding: 8px 12px;
    text-align: left;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.resource-performance-table th {
    background: rgba(255, 255, 255, 0.1);
    color: var(--text-secondary);
    font-size: 12px;
    font-weight: 600;
}

.resource-performance-table td {
    color: var(--text-primary);
    font-size: 12px;
}

.resource-name {
    max-width: 200px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.resource-type {
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 10px;
    font-weight: 500;
    text-transform: uppercase;
}

.resource-type.script {
    background: rgba(245, 158, 11, 0.2);
    color: #f59e0b;
}

.resource-type.stylesheet {
    background: rgba(59, 130, 246, 0.2);
    color: #3b82f6;
}

.resource-type.image {
    background: rgba(16, 185, 129, 0.2);
    color: #10b981;
}

.resource-type.font {
    background: rgba(139, 92, 246, 0.2);
    color: #8b5cf6;
}

.resource-type.other {
    background: rgba(107, 114, 128, 0.2);
    color: #6b7280;
}

.resource-status {
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 10px;
    font-weight: 500;
}

.resource-status.good {
    background: rgba(16, 185, 129, 0.2);
    color: #10b981;
}

.resource-status.warning {
    background: rgba(245, 158, 11, 0.2);
    color: #f59e0b;
}

.resource-status.error {
    background: rgba(239, 68, 68, 0.2);
    color: #ef4444;
}

/* 插件系统样式 */
.plugin-panel {
    position: fixed;
    top: 0;
    right: -700px;
    width: 700px;
    height: 100vh;
    background: rgba(255, 255, 255, 0.95);
    border-left: 1px solid rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(20px);
    z-index: 9991;
    transition: right 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    display: flex;
    flex-direction: column;
    box-shadow: -10px 0 25px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.plugin-panel.show {
    right: 0;
}

.plugin-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 24px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    background: rgba(255, 255, 255, 0.1);
}

.plugin-header h3 {
    margin: 0;
    color: var(--text-primary);
    font-size: 18px;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 8px;
}

.plugin-controls {
    display: flex;
    gap: 8px;
}

.plugin-body {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.plugin-tabs {
    display: flex;
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    background: rgba(255, 255, 255, 0.05);
}

.plugin-tabs .tab-btn {
    flex: 1;
    padding: 12px 16px;
    border: none;
    background: transparent;
    color: var(--text-secondary);
    cursor: pointer;
    transition: var(--transition);
    font-size: 12px;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 4px;
    border-bottom: 2px solid transparent;
}

.plugin-tabs .tab-btn:hover {
    background: rgba(255, 255, 255, 0.1);
    color: var(--text-primary);
}

.plugin-tabs .tab-btn.active {
    color: var(--primary-color);
    border-bottom-color: var(--primary-color);
    background: rgba(102, 126, 234, 0.1);
}

.plugin-tabs .tab-btn i {
    font-size: 14px;
}

.plugin-content {
    flex: 1;
    overflow-y: auto;
    padding: 20px 24px;
}

.plugin-content .tab-content {
    display: none;
}

.plugin-content .tab-content.active {
    display: block;
}

.plugin-card {
    display: flex;
    align-items: center;
    gap: 16px;
    padding: 16px;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 12px;
    margin-bottom: 12px;
    transition: var(--transition);
}

.plugin-card:hover {
    background: rgba(255, 255, 255, 0.15);
    transform: translateY(-1px);
}

.plugin-card.disabled {
    opacity: 0.6;
}

.plugin-icon {
    width: 48px;
    height: 48px;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 20px;
    flex-shrink: 0;
}

.plugin-info {
    flex: 1;
    min-width: 0;
}

.plugin-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 4px;
}

.plugin-header h5 {
    margin: 0;
    color: var(--text-primary);
    font-size: 14px;
    font-weight: 600;
}

.plugin-version {
    color: var(--text-muted);
    font-size: 11px;
    background: rgba(255, 255, 255, 0.1);
    padding: 2px 6px;
    border-radius: 3px;
}

.plugin-description {
    color: var(--text-secondary);
    font-size: 12px;
    margin-bottom: 8px;
    line-height: 1.4;
}

.plugin-meta {
    display: flex;
    gap: 12px;
    flex-wrap: wrap;
}

.plugin-meta span {
    color: var(--text-muted);
    font-size: 11px;
}

.plugin-rating {
    display: flex;
    align-items: center;
    gap: 4px;
}

.plugin-rating i {
    color: #f59e0b;
    font-size: 10px;
}

.rating-text {
    color: var(--text-muted);
    font-size: 11px;
    font-weight: 500;
}

.plugin-features {
    display: flex;
    gap: 4px;
    flex-wrap: wrap;
    margin-top: 8px;
}

.feature-tag {
    padding: 2px 6px;
    background: rgba(102, 126, 234, 0.1);
    color: var(--primary-color);
    border-radius: 3px;
    font-size: 10px;
    font-weight: 500;
}

.plugin-actions {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
}

.plugin-price {
    color: var(--primary-color);
    font-size: 14px;
    font-weight: 600;
}

.plugin-toggle {
    display: flex;
    align-items: center;
}

.switch {
    position: relative;
    display: inline-block;
    width: 40px;
    height: 20px;
}

.switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(255, 255, 255, 0.3);
    transition: 0.3s;
    border-radius: 20px;
}

.slider:before {
    position: absolute;
    content: "";
    height: 16px;
    width: 16px;
    left: 2px;
    bottom: 2px;
    background-color: white;
    transition: 0.3s;
    border-radius: 50%;
}

input:checked + .slider {
    background-color: var(--primary-color);
}

input:checked + .slider:before {
    transform: translateX(20px);
}

.develop-tools {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 16px;
    margin-bottom: 24px;
}

.tool-card {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 12px;
    padding: 20px;
    text-align: center;
    transition: var(--transition);
}

.tool-card:hover {
    background: rgba(255, 255, 255, 0.15);
    transform: translateY(-2px);
}

.tool-icon {
    width: 48px;
    height: 48px;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 12px;
    color: white;
    font-size: 20px;
}

.tool-content h5 {
    margin: 0 0 8px 0;
    color: var(--text-primary);
    font-size: 14px;
    font-weight: 600;
}

.tool-content p {
    margin: 0 0 16px 0;
    color: var(--text-secondary);
    font-size: 12px;
    line-height: 1.4;
}

.plugin-editor {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 12px;
    overflow: hidden;
}

.plugin-editor h4 {
    margin: 0;
    padding: 16px 20px;
    background: rgba(255, 255, 255, 0.1);
    color: var(--text-primary);
    font-size: 16px;
    font-weight: 600;
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

.editor-tabs {
    display: flex;
    background: rgba(255, 255, 255, 0.05);
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

.editor-tab {
    padding: 8px 16px;
    border: none;
    background: transparent;
    color: var(--text-secondary);
    cursor: pointer;
    transition: var(--transition);
    font-size: 12px;
    border-bottom: 2px solid transparent;
}

.editor-tab:hover {
    color: var(--text-primary);
}

.editor-tab.active {
    color: var(--primary-color);
    border-bottom-color: var(--primary-color);
}

#pluginEditor {
    width: 100%;
    height: 300px;
    border: none;
    background: rgba(0, 0, 0, 0.1);
    color: var(--text-primary);
    font-family: 'Courier New', monospace;
    font-size: 12px;
    padding: 16px;
    resize: none;
    outline: none;
}

.editor-actions {
    display: flex;
    gap: 8px;
    padding: 16px 20px;
    background: rgba(255, 255, 255, 0.05);
    border-top: 1px solid rgba(255, 255, 255, 0.2);
}

.settings-form {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.setting-group {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.setting-group label {
    color: var(--text-secondary);
    font-size: 13px;
    font-weight: 500;
}

.setting-group select {
    padding: 8px 12px;
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 6px;
    background: rgba(255, 255, 255, 0.1);
    color: var(--text-primary);
    font-size: 13px;
}

.setting-desc {
    margin: 0;
    color: var(--text-muted);
    font-size: 11px;
    line-height: 1.4;
}

.settings-actions {
    display: flex;
    gap: 12px;
    justify-content: flex-end;
    margin-top: 24px;
}

/* 响应式插件面板 */
@media (max-width: 768px) {
    .performance-panel,
    .plugin-panel {
        width: 100vw;
        right: -100vw;
    }

    .performance-panel.show,
    .plugin-panel.show {
        right: 0;
    }

    .overview-cards {
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    }

    .performance-charts {
        grid-template-columns: 1fr;
    }

    .develop-tools {
        grid-template-columns: 1fr;
    }

    .plugin-card {
        flex-direction: column;
        text-align: center;
    }

    .plugin-actions {
        flex-direction: row;
        justify-content: center;
    }
}

/* 高级数据分析样式 */
.analytics-panel {
    position: fixed;
    top: 0;
    right: -1200px;
    width: 1200px;
    height: 100vh;
    background: rgba(255, 255, 255, 0.95);
    border-left: 1px solid rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(20px);
    z-index: 9990;
    transition: right 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    display: flex;
    flex-direction: column;
    box-shadow: -10px 0 25px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.analytics-panel.show {
    right: 0;
}

.analytics-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 24px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    background: rgba(255, 255, 255, 0.1);
}

.analytics-header h3 {
    margin: 0;
    color: var(--text-primary);
    font-size: 18px;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 8px;
}

.analytics-controls {
    display: flex;
    gap: 8px;
    align-items: center;
}

.analytics-controls select {
    padding: 6px 12px;
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 6px;
    background: rgba(255, 255, 255, 0.1);
    color: var(--text-primary);
    font-size: 12px;
}

.analytics-body {
    flex: 1;
    display: flex;
    overflow: hidden;
}

.analytics-sidebar {
    width: 280px;
    border-right: 1px solid rgba(255, 255, 255, 0.2);
    background: rgba(255, 255, 255, 0.05);
    overflow-y: auto;
    padding: 20px;
}

.sidebar-section {
    margin-bottom: 24px;
}

.sidebar-section h4 {
    margin: 0 0 12px 0;
    color: var(--text-primary);
    font-size: 14px;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 8px;
}

.widget-library {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 8px;
}

.widget-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 4px;
    padding: 12px 8px;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 8px;
    cursor: grab;
    transition: var(--transition);
    text-align: center;
}

.widget-item:hover {
    background: rgba(255, 255, 255, 0.15);
    transform: translateY(-2px);
}

.widget-item:active {
    cursor: grabbing;
}

.widget-item i {
    font-size: 16px;
    color: var(--primary-color);
}

.widget-item span {
    font-size: 11px;
    color: var(--text-secondary);
    font-weight: 500;
}

.data-source-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 8px;
    margin-bottom: 8px;
    transition: var(--transition);
}

.data-source-item:hover {
    background: rgba(255, 255, 255, 0.15);
}

.source-icon {
    width: 32px;
    height: 32px;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    border-radius: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 14px;
}

.source-info {
    flex: 1;
}

.source-name {
    color: var(--text-primary);
    font-size: 13px;
    font-weight: 600;
    margin-bottom: 2px;
}

.source-desc {
    color: var(--text-secondary);
    font-size: 11px;
    margin-bottom: 2px;
}

.source-meta {
    color: var(--text-muted);
    font-size: 10px;
}

.kpi-list {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.btn-small {
    padding: 6px 12px;
    font-size: 11px;
    width: 100%;
    margin-top: 8px;
}

.analytics-main {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.dashboard-toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 24px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    background: rgba(255, 255, 255, 0.05);
}

.toolbar-left {
    display: flex;
    align-items: center;
    gap: 8px;
}

.dashboard-title {
    color: var(--text-primary);
    font-size: 16px;
    font-weight: 600;
}

.toolbar-right {
    display: flex;
    gap: 8px;
}

.dashboard-canvas {
    flex: 1;
    padding: 20px;
    overflow-y: auto;
    position: relative;
}

.empty-dashboard {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    text-align: center;
    color: var(--text-secondary);
}

.empty-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 20px;
    color: white;
    font-size: 32px;
}

.empty-dashboard h3 {
    margin: 0 0 8px 0;
    color: var(--text-primary);
    font-size: 20px;
}

.empty-dashboard p {
    margin: 0 0 24px 0;
    color: var(--text-secondary);
    max-width: 400px;
}

.quick-actions {
    display: flex;
    gap: 12px;
}

.dashboard-grid {
    display: grid;
    grid-template-columns: repeat(12, 1fr);
    grid-auto-rows: 150px;
    gap: 16px;
    min-height: 100%;
}

.dashboard-widget {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 12px;
    overflow: hidden;
    transition: var(--transition);
    position: relative;
}

.dashboard-widget:hover {
    background: rgba(255, 255, 255, 0.15);
    transform: translateY(-2px);
    box-shadow: var(--shadow-modern);
}

.widget-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    background: rgba(255, 255, 255, 0.05);
}

.widget-header h4 {
    margin: 0;
    color: var(--text-primary);
    font-size: 14px;
    font-weight: 600;
}

.widget-actions {
    display: flex;
    gap: 4px;
    opacity: 0;
    transition: var(--transition);
}

.dashboard-widget:hover .widget-actions {
    opacity: 1;
}

.widget-content {
    padding: 16px;
    height: calc(100% - 60px);
    overflow: hidden;
}

.widget-error,
.widget-placeholder {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
    color: var(--text-muted);
    font-size: 12px;
    text-align: center;
}

.metric-card-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    text-align: center;
}

.metric-value {
    font-size: 32px;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: 8px;
}

.metric-label {
    color: var(--text-secondary);
    font-size: 12px;
    margin-bottom: 8px;
}

.metric-trend {
    display: flex;
    align-items: center;
    gap: 4px;
    color: #10b981;
    font-size: 11px;
    font-weight: 500;
}

.data-table-wrapper {
    height: 100%;
    overflow: auto;
}

.data-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 11px;
}

.data-table th,
.data-table td {
    padding: 6px 8px;
    text-align: left;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.data-table th {
    background: rgba(255, 255, 255, 0.1);
    color: var(--text-secondary);
    font-weight: 600;
    position: sticky;
    top: 0;
}

.data-table td {
    color: var(--text-primary);
}

/* 组件配置面板 */
.widget-config-panel {
    position: fixed;
    top: 0;
    right: -400px;
    width: 400px;
    height: 100vh;
    background: rgba(255, 255, 255, 0.95);
    border-left: 1px solid rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(20px);
    z-index: 9999;
    transition: right 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    display: flex;
    flex-direction: column;
    box-shadow: -10px 0 25px rgba(0, 0, 0, 0.1);
}

.widget-config-panel.show {
    right: 0;
}

.config-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 24px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    background: rgba(255, 255, 255, 0.1);
}

.config-header h4 {
    margin: 0;
    color: var(--text-primary);
    font-size: 16px;
    font-weight: 600;
}

.config-body {
    flex: 1;
    padding: 20px 24px;
    overflow-y: auto;
}

.config-section {
    margin-bottom: 24px;
}

.config-section h5 {
    margin: 0 0 16px 0;
    color: var(--text-primary);
    font-size: 14px;
    font-weight: 600;
}

.form-group {
    margin-bottom: 16px;
}

.form-group label {
    display: block;
    margin-bottom: 6px;
    color: var(--text-secondary);
    font-size: 13px;
    font-weight: 500;
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 6px;
    background: rgba(255, 255, 255, 0.1);
    color: var(--text-primary);
    font-size: 13px;
    transition: var(--transition);
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2);
}

.form-group textarea {
    resize: vertical;
    min-height: 60px;
}

.config-footer {
    display: flex;
    gap: 12px;
    justify-content: flex-end;
    padding: 20px 24px;
    border-top: 1px solid rgba(255, 255, 255, 0.2);
    background: rgba(255, 255, 255, 0.05);
}

/* 报表生成器和数据挖掘模态框 */
.report-builder-modal,
.data-mining-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: none;
    align-items: center;
    justify-content: center;
    z-index: 10000;
}

.modal-content.large {
    width: 90%;
    max-width: 1200px;
    height: 80%;
    max-height: 800px;
}

.report-builder-content,
.mining-content {
    display: flex;
    height: 100%;
}

.report-sidebar,
.mining-sidebar {
    width: 300px;
    border-right: 1px solid rgba(255, 255, 255, 0.2);
    background: rgba(255, 255, 255, 0.05);
    overflow-y: auto;
    padding: 20px;
}

.report-main,
.mining-main {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.report-section,
.mining-section {
    margin-bottom: 24px;
}

.report-section h4,
.mining-section h4 {
    margin: 0 0 12px 0;
    color: var(--text-primary);
    font-size: 14px;
    font-weight: 600;
}

.report-types,
.analysis-types {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 8px;
}

.report-type-item,
.analysis-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
    padding: 16px 12px;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 8px;
    cursor: pointer;
    transition: var(--transition);
    text-align: center;
}

.report-type-item:hover,
.analysis-item:hover {
    background: rgba(255, 255, 255, 0.15);
}

.report-type-item.selected,
.analysis-item.selected {
    background: rgba(102, 126, 234, 0.2);
    border-color: var(--primary-color);
}

.report-type-item i,
.analysis-item i {
    font-size: 20px;
    color: var(--primary-color);
}

.report-type-item span,
.analysis-item span {
    font-size: 12px;
    color: var(--text-primary);
    font-weight: 500;
}

.field-list {
    max-height: 200px;
    overflow-y: auto;
}

.field-item {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 12px;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 6px;
    margin-bottom: 4px;
    cursor: grab;
    transition: var(--transition);
}

.field-item:hover {
    background: rgba(255, 255, 255, 0.15);
}

.field-name {
    flex: 1;
    color: var(--text-primary);
    font-size: 12px;
    font-weight: 500;
}

.field-source {
    color: var(--text-muted);
    font-size: 10px;
}

.algorithm-list {
    display: flex;
    flex-direction: column;
    gap: 8px;
    max-height: 200px;
    overflow-y: auto;
}

.algorithm-item {
    padding: 12px;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 8px;
    cursor: pointer;
    transition: var(--transition);
}

.algorithm-item:hover {
    background: rgba(255, 255, 255, 0.15);
}

.algorithm-item.selected {
    background: rgba(102, 126, 234, 0.2);
    border-color: var(--primary-color);
}

.algorithm-name {
    color: var(--text-primary);
    font-size: 13px;
    font-weight: 600;
    margin-bottom: 4px;
}

.algorithm-desc {
    color: var(--text-secondary);
    font-size: 11px;
    margin-bottom: 4px;
}

.algorithm-complexity {
    color: var(--text-muted);
    font-size: 10px;
}

.report-config,
.mining-config {
    padding: 20px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    background: rgba(255, 255, 255, 0.05);
}

.config-tabs {
    display: flex;
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    margin-bottom: 20px;
}

.config-tabs .tab-btn {
    padding: 8px 16px;
    border: none;
    background: transparent;
    color: var(--text-secondary);
    cursor: pointer;
    transition: var(--transition);
    font-size: 12px;
    border-bottom: 2px solid transparent;
}

.config-tabs .tab-btn:hover {
    color: var(--text-primary);
}

.config-tabs .tab-btn.active {
    color: var(--primary-color);
    border-bottom-color: var(--primary-color);
}

.config-content .tab-content {
    display: none;
}

.config-content .tab-content.active {
    display: block;
}

.field-selector {
    max-height: 200px;
    overflow-y: auto;
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 6px;
    padding: 8px;
}

.field-checkboxes {
    display: flex;
    flex-direction: column;
    gap: 8px;
    max-height: 150px;
    overflow-y: auto;
}

.parameter-config {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.parameter-item {
    display: flex;
    flex-direction: column;
    gap: 6px;
}

.parameter-item label {
    color: var(--text-secondary);
    font-size: 13px;
    font-weight: 500;
}

.parameter-item small {
    color: var(--text-muted);
    font-size: 11px;
}

.report-preview,
.mining-results {
    flex: 1;
    padding: 20px;
    overflow-y: auto;
}

.report-preview h4,
.mining-results h4 {
    margin: 0 0 16px 0;
    color: var(--text-primary);
    font-size: 16px;
    font-weight: 600;
}

.preview-content {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 8px;
    padding: 16px;
    min-height: 200px;
}

.mining-loading {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 200px;
    gap: 16px;
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-top: 3px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.mining-error {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 12px;
    padding: 20px;
    color: #ef4444;
    text-align: center;
}

.mining-error i {
    font-size: 32px;
}

.mining-success {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.results-summary {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 12px;
    padding: 16px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 8px;
}

.summary-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 12px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 6px;
}

.summary-label {
    color: var(--text-secondary);
    font-size: 12px;
}

.summary-value {
    color: var(--text-primary);
    font-size: 12px;
    font-weight: 600;
}

.results-details {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    padding: 16px;
}

.results-details h6 {
    margin: 0 0 12px 0;
    color: var(--text-primary);
    font-size: 14px;
    font-weight: 600;
}

.results-actions {
    display: flex;
    gap: 12px;
    justify-content: flex-end;
}

/* 响应式设计 */
@media (max-width: 1400px) {
    .analytics-panel {
        width: 100vw;
        right: -100vw;
    }

    .analytics-panel.show {
        right: 0;
    }

    .analytics-sidebar {
        width: 250px;
    }
}

@media (max-width: 768px) {
    .analytics-body {
        flex-direction: column;
    }

    .analytics-sidebar {
        width: 100%;
        height: 200px;
        border-right: none;
        border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    }

    .dashboard-grid {
        grid-template-columns: 1fr;
    }

    .widget-library {
        grid-template-columns: repeat(3, 1fr);
    }

    .report-builder-content,
    .mining-content {
        flex-direction: column;
    }

    .report-sidebar,
    .mining-sidebar {
        width: 100%;
        height: 200px;
        border-right: none;
        border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    }
}

/* 实时通信系统样式 */
.communication-panel {
    position: fixed;
    top: 0;
    right: -1000px;
    width: 1000px;
    height: 100vh;
    background: rgba(255, 255, 255, 0.95);
    border-left: 1px solid rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(20px);
    z-index: 9989;
    transition: right 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    display: flex;
    flex-direction: column;
    box-shadow: -10px 0 25px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.communication-panel.show {
    right: 0;
}

.comm-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 24px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    background: rgba(255, 255, 255, 0.1);
}

.comm-header h3 {
    margin: 0;
    color: var(--text-primary);
    font-size: 18px;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 8px;
}

.comm-controls {
    display: flex;
    gap: 8px;
}

.comm-body {
    flex: 1;
    display: flex;
    overflow: hidden;
}

.comm-sidebar {
    width: 320px;
    border-right: 1px solid rgba(255, 255, 255, 0.2);
    background: rgba(255, 255, 255, 0.05);
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.sidebar-tabs {
    display: flex;
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    background: rgba(255, 255, 255, 0.05);
}

.sidebar-tabs .tab-btn {
    flex: 1;
    padding: 12px 8px;
    border: none;
    background: transparent;
    color: var(--text-secondary);
    cursor: pointer;
    transition: var(--transition);
    font-size: 11px;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 4px;
    border-bottom: 2px solid transparent;
}

.sidebar-tabs .tab-btn:hover {
    background: rgba(255, 255, 255, 0.1);
    color: var(--text-primary);
}

.sidebar-tabs .tab-btn.active {
    color: var(--primary-color);
    border-bottom-color: var(--primary-color);
    background: rgba(102, 126, 234, 0.1);
}

.sidebar-tabs .tab-btn i {
    font-size: 14px;
}

.sidebar-content {
    flex: 1;
    overflow: hidden;
}

.sidebar-content .tab-content {
    display: none;
    height: 100%;
    overflow-y: auto;
    padding: 16px;
}

.sidebar-content .tab-content.active {
    display: flex;
    flex-direction: column;
}

.search-box {
    position: relative;
    margin-bottom: 16px;
}

.search-box input {
    width: 100%;
    padding: 8px 12px 8px 36px;
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 20px;
    background: rgba(255, 255, 255, 0.1);
    color: var(--text-primary);
    font-size: 13px;
    outline: none;
    transition: var(--transition);
}

.search-box input:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2);
}

.search-box i {
    position: absolute;
    left: 12px;
    top: 50%;
    transform: translateY(-50%);
    color: var(--text-muted);
    font-size: 12px;
}

.chat-list,
.user-list,
.room-list {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.chat-item,
.user-item,
.room-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 12px;
    cursor: pointer;
    transition: var(--transition);
    position: relative;
}

.chat-item:hover,
.user-item:hover,
.room-item:hover {
    background: rgba(255, 255, 255, 0.15);
    transform: translateY(-1px);
}

.chat-item.unread,
.room-item.unread {
    border-left: 3px solid var(--primary-color);
}

.chat-avatar,
.user-avatar {
    position: relative;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 18px;
    flex-shrink: 0;
}

.avatar-emoji {
    font-size: 20px;
}

.online-indicator {
    position: absolute;
    bottom: 2px;
    right: 2px;
    width: 12px;
    height: 12px;
    background: #10b981;
    border: 2px solid white;
    border-radius: 50%;
}

.chat-info,
.user-info {
    flex: 1;
    min-width: 0;
}

.chat-header,
.user-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 4px;
}

.chat-name,
.user-name {
    color: var(--text-primary);
    font-size: 14px;
    font-weight: 600;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.chat-time {
    color: var(--text-muted);
    font-size: 11px;
    flex-shrink: 0;
}

.chat-preview {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.last-message {
    color: var(--text-secondary);
    font-size: 12px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    flex: 1;
}

.user-status {
    color: var(--text-muted);
    font-size: 11px;
}

.user-actions {
    display: flex;
    gap: 4px;
    opacity: 0;
    transition: var(--transition);
}

.user-item:hover .user-actions {
    opacity: 1;
}

.unread-badge {
    background: var(--primary-color);
    color: white;
    font-size: 10px;
    font-weight: 600;
    padding: 2px 6px;
    border-radius: 10px;
    min-width: 18px;
    text-align: center;
    flex-shrink: 0;
}

.online-users,
.all-users {
    margin-bottom: 20px;
}

.online-users h4,
.all-users h4 {
    margin: 0 0 12px 0;
    color: var(--text-primary);
    font-size: 14px;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 8px;
}

.room-icon {
    width: 40px;
    height: 40px;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 16px;
    flex-shrink: 0;
}

.room-info {
    flex: 1;
    min-width: 0;
}

.room-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 4px;
}

.room-name {
    color: var(--text-primary);
    font-size: 14px;
    font-weight: 600;
}

.room-time {
    color: var(--text-muted);
    font-size: 11px;
}

.room-details {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.room-desc {
    color: var(--text-secondary);
    font-size: 12px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    flex: 1;
}

.room-members {
    color: var(--text-muted);
    font-size: 11px;
    flex-shrink: 0;
    margin-left: 8px;
}

.file-upload {
    margin-bottom: 16px;
}

.file-upload .btn-primary {
    width: 100%;
}

.shared-files {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.file-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 8px;
    transition: var(--transition);
}

.file-item:hover {
    background: rgba(255, 255, 255, 0.15);
}

.file-icon {
    width: 32px;
    height: 32px;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    border-radius: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 14px;
    flex-shrink: 0;
}

.file-info {
    flex: 1;
    min-width: 0;
}

.file-name {
    color: var(--text-primary);
    font-size: 13px;
    font-weight: 500;
    margin-bottom: 2px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.file-meta {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
}

.file-meta span {
    color: var(--text-muted);
    font-size: 11px;
}

.file-actions {
    display: flex;
    gap: 4px;
    opacity: 0;
    transition: var(--transition);
}

.file-item:hover .file-actions {
    opacity: 1;
}

.comm-main {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.chat-container {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 40px;
}

.no-chat-selected {
    text-align: center;
    color: var(--text-secondary);
}

.no-chat-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 20px;
    color: white;
    font-size: 32px;
}

.no-chat-selected h3 {
    margin: 0 0 8px 0;
    color: var(--text-primary);
    font-size: 20px;
}

.no-chat-selected p {
    margin: 0;
    color: var(--text-secondary);
}

/* 通知中心样式 */
.notification-center {
    position: fixed;
    top: 0;
    right: -400px;
    width: 400px;
    height: 100vh;
    background: rgba(255, 255, 255, 0.95);
    border-left: 1px solid rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(20px);
    z-index: 10001;
    transition: right 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    display: flex;
    flex-direction: column;
    box-shadow: -10px 0 25px rgba(0, 0, 0, 0.1);
}

.notification-center.show {
    right: 0;
}

.notification-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 24px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    background: rgba(255, 255, 255, 0.1);
}

.notification-header h4 {
    margin: 0;
    color: var(--text-primary);
    font-size: 16px;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 8px;
}

.notification-actions {
    display: flex;
    gap: 4px;
}

.notification-body {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.notification-filters {
    display: flex;
    padding: 16px 24px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    background: rgba(255, 255, 255, 0.05);
    gap: 8px;
}

.filter-btn {
    padding: 6px 12px;
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 16px;
    background: transparent;
    color: var(--text-secondary);
    font-size: 12px;
    cursor: pointer;
    transition: var(--transition);
}

.filter-btn:hover {
    background: rgba(255, 255, 255, 0.1);
    color: var(--text-primary);
}

.filter-btn.active {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.notification-list {
    flex: 1;
    overflow-y: auto;
    padding: 16px 24px;
}

/* 视频通话界面样式 */
.video-call-interface {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: #000;
    z-index: 10002;
    display: none;
    flex-direction: column;
}

.video-call-interface.show {
    display: flex;
}

.video-call-container {
    flex: 1;
    display: flex;
    flex-direction: column;
    position: relative;
}

.video-header {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    z-index: 10;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    background: linear-gradient(to bottom, rgba(0,0,0,0.8), transparent);
}

.call-info {
    color: white;
}

.caller-name {
    display: block;
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 4px;
}

.call-duration {
    font-size: 14px;
    opacity: 0.8;
}

.call-controls {
    display: flex;
    gap: 12px;
}

.control-btn {
    width: 48px;
    height: 48px;
    border-radius: 50%;
    border: none;
    background: rgba(255, 255, 255, 0.2);
    color: white;
    font-size: 18px;
    cursor: pointer;
    transition: var(--transition);
    backdrop-filter: blur(10px);
}

.control-btn:hover {
    background: rgba(255, 255, 255, 0.3);
}

.control-btn.end-call {
    background: #ef4444;
}

.control-btn.end-call:hover {
    background: #dc2626;
}

.video-content {
    flex: 1;
    position: relative;
    overflow: hidden;
}

.remote-video {
    width: 100%;
    height: 100%;
    position: relative;
}

.remote-video video {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.video-placeholder {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    text-align: center;
    color: white;
}

.video-placeholder i {
    font-size: 64px;
    margin-bottom: 16px;
    opacity: 0.5;
}

.local-video {
    position: absolute;
    bottom: 20px;
    right: 20px;
    width: 200px;
    height: 150px;
    border-radius: 12px;
    overflow: hidden;
    border: 2px solid rgba(255, 255, 255, 0.3);
}

.local-video video {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.video-chat {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 240px;
    height: 200px;
    background: linear-gradient(to top, rgba(0,0,0,0.8), transparent);
    display: flex;
    flex-direction: column;
    padding: 20px;
}

.chat-messages {
    flex: 1;
    overflow-y: auto;
    margin-bottom: 12px;
}

.chat-input {
    display: flex;
    gap: 8px;
}

.chat-input input {
    flex: 1;
    padding: 8px 12px;
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 20px;
    background: rgba(255, 255, 255, 0.1);
    color: white;
    outline: none;
}

.chat-input button {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    border: none;
    background: var(--primary-color);
    color: white;
    cursor: pointer;
}

/* 广播界面样式 */
.broadcast-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: none;
    align-items: center;
    justify-content: center;
    z-index: 10003;
}

.broadcast-form {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.recipient-options,
.send-options {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.radio-label {
    display: flex;
    align-items: center;
    gap: 8px;
    cursor: pointer;
}

.radio-mark {
    width: 16px;
    height: 16px;
    border: 2px solid var(--primary-color);
    border-radius: 50%;
    position: relative;
}

.radio-label input:checked + .radio-mark::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 8px;
    height: 8px;
    background: var(--primary-color);
    border-radius: 50%;
}

.user-selector {
    max-height: 150px;
    overflow-y: auto;
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 6px;
    padding: 8px;
}

/* 响应式设计 */
@media (max-width: 1200px) {
    .communication-panel {
        width: 100vw;
        right: -100vw;
    }

    .communication-panel.show {
        right: 0;
    }
}

@media (max-width: 768px) {
    .comm-body {
        flex-direction: column;
    }

    .comm-sidebar {
        width: 100%;
        height: 250px;
        border-right: none;
        border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    }

    .sidebar-tabs .tab-btn {
        font-size: 10px;
        padding: 8px 4px;
    }

    .local-video {
        width: 120px;
        height: 90px;
        bottom: 10px;
        right: 10px;
    }

    .video-chat {
        right: 140px;
        height: 150px;
    }

    .notification-center {
        width: 100vw;
        right: -100vw;
    }

    .notification-center.show {
        right: 0;
    }
}

/* 高级安全中心样式 */
.security-panel {
    position: fixed;
    top: 0;
    right: -1400px;
    width: 1400px;
    height: 100vh;
    background: rgba(255, 255, 255, 0.95);
    border-left: 1px solid rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(20px);
    z-index: 9988;
    transition: right 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    display: flex;
    flex-direction: column;
    box-shadow: -10px 0 25px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.security-panel.show {
    right: 0;
}

.security-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 24px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    background: rgba(255, 255, 255, 0.1);
}

.security-header h3 {
    margin: 0;
    color: var(--text-primary);
    font-size: 18px;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 8px;
}

.security-controls {
    display: flex;
    gap: 8px;
    align-items: center;
}

.security-status {
    margin-right: 12px;
}

.status-indicator {
    display: flex;
    align-items: center;
    gap: 6px;
    padding: 6px 12px;
    border-radius: 16px;
    font-size: 12px;
    font-weight: 500;
}

.status-indicator.secure {
    background: rgba(16, 185, 129, 0.2);
    color: #10b981;
}

.status-indicator.warning {
    background: rgba(245, 158, 11, 0.2);
    color: #f59e0b;
}

.status-indicator.danger {
    background: rgba(239, 68, 68, 0.2);
    color: #ef4444;
}

.security-body {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.security-tabs {
    display: flex;
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    background: rgba(255, 255, 255, 0.05);
}

.security-tabs .tab-btn {
    flex: 1;
    padding: 12px 16px;
    border: none;
    background: transparent;
    color: var(--text-secondary);
    cursor: pointer;
    transition: var(--transition);
    font-size: 11px;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 4px;
    border-bottom: 2px solid transparent;
}

.security-tabs .tab-btn:hover {
    background: rgba(255, 255, 255, 0.1);
    color: var(--text-primary);
}

.security-tabs .tab-btn.active {
    color: var(--primary-color);
    border-bottom-color: var(--primary-color);
    background: rgba(102, 126, 234, 0.1);
}

.security-tabs .tab-btn i {
    font-size: 14px;
}

.security-content {
    flex: 1;
    overflow-y: auto;
    padding: 20px 24px;
}

.security-content .tab-content {
    display: none;
}

.security-content .tab-content.active {
    display: block;
}

/* 安全仪表板样式 */
.security-overview {
    display: flex;
    flex-direction: column;
    gap: 24px;
}

.security-metrics {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 16px;
}

.metric-card {
    display: flex;
    align-items: center;
    gap: 16px;
    padding: 20px;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 12px;
    transition: var(--transition);
}

.metric-card:hover {
    background: rgba(255, 255, 255, 0.15);
    transform: translateY(-2px);
}

.metric-icon {
    width: 48px;
    height: 48px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 20px;
    color: white;
    flex-shrink: 0;
}

.metric-card.threat-level .metric-icon {
    background: linear-gradient(135deg, #f59e0b, #d97706);
}

.metric-card.security-score .metric-icon {
    background: linear-gradient(135deg, #10b981, #059669);
}

.metric-card.active-threats .metric-icon {
    background: linear-gradient(135deg, #ef4444, #dc2626);
}

.metric-card.compliance-rate .metric-icon {
    background: linear-gradient(135deg, #8b5cf6, #7c3aed);
}

.metric-content {
    flex: 1;
}

.metric-content h4 {
    margin: 0 0 8px 0;
    color: var(--text-secondary);
    font-size: 13px;
    font-weight: 500;
}

.metric-value {
    font-size: 24px;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 4px;
}

.metric-trend {
    display: flex;
    align-items: center;
    gap: 4px;
    font-size: 11px;
    font-weight: 500;
}

.metric-trend.good {
    color: #10b981;
}

.metric-trend.warning {
    color: #f59e0b;
}

.metric-trend.danger {
    color: #ef4444;
}

.security-charts {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 20px;
}

.chart-container {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 12px;
    padding: 20px;
}

.chart-container h4 {
    margin: 0 0 16px 0;
    color: var(--text-primary);
    font-size: 16px;
    font-weight: 600;
}

.recent-events {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 12px;
    padding: 20px;
}

.recent-events h4 {
    margin: 0 0 16px 0;
    color: var(--text-primary);
    font-size: 16px;
    font-weight: 600;
}

.events-list {
    display: flex;
    flex-direction: column;
    gap: 12px;
    max-height: 400px;
    overflow-y: auto;
}

.event-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 8px;
    transition: var(--transition);
}

.event-item:hover {
    background: rgba(255, 255, 255, 0.15);
}

.event-item.critical {
    border-left: 4px solid #ef4444;
}

.event-item.high {
    border-left: 4px solid #f59e0b;
}

.event-item.medium {
    border-left: 4px solid #3b82f6;
}

.event-item.low {
    border-left: 4px solid #10b981;
}

.event-icon {
    width: 32px;
    height: 32px;
    border-radius: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
    color: white;
    flex-shrink: 0;
}

.event-item.critical .event-icon {
    background: #ef4444;
}

.event-item.high .event-icon {
    background: #f59e0b;
}

.event-item.medium .event-icon {
    background: #3b82f6;
}

.event-item.low .event-icon {
    background: #10b981;
}

.event-content {
    flex: 1;
    min-width: 0;
}

.event-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 4px;
}

.event-type {
    color: var(--text-primary);
    font-size: 13px;
    font-weight: 600;
}

.event-time {
    color: var(--text-muted);
    font-size: 11px;
}

.event-description {
    color: var(--text-secondary);
    font-size: 12px;
    margin-bottom: 4px;
}

.event-meta {
    display: flex;
    gap: 12px;
    flex-wrap: wrap;
}

.event-meta span {
    color: var(--text-muted);
    font-size: 11px;
}

.event-severity {
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 10px;
    font-weight: 500;
    text-transform: uppercase;
}

.event-severity.critical {
    background: rgba(239, 68, 68, 0.2);
    color: #ef4444;
}

.event-severity.high {
    background: rgba(245, 158, 11, 0.2);
    color: #f59e0b;
}

.event-severity.medium {
    background: rgba(59, 130, 246, 0.2);
    color: #3b82f6;
}

.event-severity.low {
    background: rgba(16, 185, 129, 0.2);
    color: #10b981;
}

.event-actions {
    display: flex;
    gap: 4px;
    opacity: 0;
    transition: var(--transition);
}

.event-item:hover .event-actions {
    opacity: 1;
}

/* 威胁检测样式 */
.threats-section {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
}

.section-header h4 {
    margin: 0;
    color: var(--text-primary);
    font-size: 18px;
    font-weight: 600;
}

.threat-controls,
.compliance-controls,
.policy-controls,
.audit-controls,
.encryption-controls {
    display: flex;
    gap: 8px;
}

.threat-filters {
    display: flex;
    gap: 12px;
    margin-bottom: 20px;
}

.threat-filters select {
    padding: 8px 12px;
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 6px;
    background: rgba(255, 255, 255, 0.1);
    color: var(--text-primary);
    font-size: 13px;
}

.threats-list {
    display: flex;
    flex-direction: column;
    gap: 12px;
    max-height: 600px;
    overflow-y: auto;
}

.threat-item {
    display: flex;
    align-items: center;
    gap: 16px;
    padding: 16px;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 12px;
    transition: var(--transition);
}

.threat-item:hover {
    background: rgba(255, 255, 255, 0.15);
}

.threat-item.critical {
    border-left: 4px solid #ef4444;
}

.threat-item.high {
    border-left: 4px solid #f59e0b;
}

.threat-item.medium {
    border-left: 4px solid #3b82f6;
}

.threat-item.low {
    border-left: 4px solid #10b981;
}

.threat-item.mitigated {
    opacity: 0.7;
}

.threat-item.resolved {
    opacity: 0.5;
}

.threat-icon {
    width: 40px;
    height: 40px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
    color: white;
    flex-shrink: 0;
}

.threat-item.critical .threat-icon {
    background: #ef4444;
}

.threat-item.high .threat-icon {
    background: #f59e0b;
}

.threat-item.medium .threat-icon {
    background: #3b82f6;
}

.threat-item.low .threat-icon {
    background: #10b981;
}

.threat-content {
    flex: 1;
    min-width: 0;
}

.threat-header {
    display: flex;
    gap: 12px;
    align-items: center;
    margin-bottom: 8px;
    flex-wrap: wrap;
}

.threat-type {
    color: var(--text-primary);
    font-size: 14px;
    font-weight: 600;
}

.threat-severity,
.threat-status {
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 500;
}

.threat-severity.critical {
    background: rgba(239, 68, 68, 0.2);
    color: #ef4444;
}

.threat-severity.high {
    background: rgba(245, 158, 11, 0.2);
    color: #f59e0b;
}

.threat-severity.medium {
    background: rgba(59, 130, 246, 0.2);
    color: #3b82f6;
}

.threat-severity.low {
    background: rgba(16, 185, 129, 0.2);
    color: #10b981;
}

.threat-status.active {
    background: rgba(239, 68, 68, 0.2);
    color: #ef4444;
}

.threat-status.mitigated {
    background: rgba(245, 158, 11, 0.2);
    color: #f59e0b;
}

.threat-status.resolved {
    background: rgba(16, 185, 129, 0.2);
    color: #10b981;
}

.threat-description {
    color: var(--text-secondary);
    font-size: 13px;
    margin-bottom: 8px;
}

.threat-meta {
    display: flex;
    gap: 16px;
    margin-bottom: 8px;
    flex-wrap: wrap;
}

.threat-meta span {
    color: var(--text-muted);
    font-size: 12px;
}

.threat-indicators {
    display: flex;
    gap: 6px;
    flex-wrap: wrap;
}

.indicator-tag {
    padding: 2px 6px;
    background: rgba(102, 126, 234, 0.2);
    color: var(--primary-color);
    border-radius: 3px;
    font-size: 10px;
    font-weight: 500;
}

.threat-actions {
    display: flex;
    gap: 4px;
    opacity: 0;
    transition: var(--transition);
}

.threat-item:hover .threat-actions {
    opacity: 1;
}

.empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 40px;
    text-align: center;
    color: var(--text-secondary);
}

.empty-state i {
    font-size: 48px;
    margin-bottom: 16px;
    opacity: 0.5;
}

.empty-state p {
    margin: 0 0 16px 0;
    font-size: 16px;
}

/* 合规管理样式 */
.compliance-section {
    display: flex;
    flex-direction: column;
    gap: 24px;
}

.compliance-frameworks {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 12px;
    padding: 20px;
}

.compliance-frameworks h5 {
    margin: 0 0 16px 0;
    color: var(--text-primary);
    font-size: 16px;
    font-weight: 600;
}

.frameworks-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 16px;
}

.framework-card {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 16px;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 8px;
    transition: var(--transition);
}

.framework-card:hover {
    background: rgba(255, 255, 255, 0.15);
}

.framework-icon {
    width: 40px;
    height: 40px;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 16px;
    flex-shrink: 0;
}

.framework-info {
    flex: 1;
}

.framework-info h6 {
    margin: 0 0 4px 0;
    color: var(--text-primary);
    font-size: 14px;
    font-weight: 600;
}

.compliance-status {
    display: flex;
    align-items: center;
    gap: 4px;
    margin-bottom: 4px;
}

.compliance-status.compliant {
    color: #10b981;
}

.compliance-status.warning {
    color: #f59e0b;
}

.compliance-status.non-compliant {
    color: #ef4444;
}

.compliance-status span {
    font-size: 12px;
    font-weight: 500;
}

.compliance-score {
    color: var(--text-secondary);
    font-size: 12px;
    font-weight: 600;
}

.compliance-rules {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 12px;
    padding: 20px;
}

.compliance-rules h5 {
    margin: 0 0 16px 0;
    color: var(--text-primary);
    font-size: 16px;
    font-weight: 600;
}

.rules-list {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.compliance-rule {
    padding: 16px;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 8px;
    transition: var(--transition);
}

.compliance-rule:hover {
    background: rgba(255, 255, 255, 0.15);
}

.compliance-rule.compliant {
    border-left: 4px solid #10b981;
}

.compliance-rule.warning {
    border-left: 4px solid #f59e0b;
}

.compliance-rule.non-compliant {
    border-left: 4px solid #ef4444;
}

.rule-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
}

.rule-info h6 {
    margin: 0 0 4px 0;
    color: var(--text-primary);
    font-size: 14px;
    font-weight: 600;
}

.rule-framework {
    color: var(--text-muted);
    font-size: 11px;
    background: rgba(255, 255, 255, 0.1);
    padding: 2px 6px;
    border-radius: 3px;
}

.rule-status {
    display: flex;
    align-items: center;
    gap: 8px;
}

.rule-score {
    color: var(--text-primary);
    font-size: 14px;
    font-weight: 600;
}

.rule-description {
    color: var(--text-secondary);
    font-size: 13px;
    margin-bottom: 8px;
}

.rule-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.last-check {
    color: var(--text-muted);
    font-size: 11px;
}

.rule-actions {
    display: flex;
    gap: 4px;
}

/* 安全策略样式 */
.policies-section {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.policy-categories {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 12px;
    overflow: hidden;
}

.category-tabs {
    display: flex;
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    background: rgba(255, 255, 255, 0.05);
}

.category-tab {
    flex: 1;
    padding: 12px 16px;
    border: none;
    background: transparent;
    color: var(--text-secondary);
    cursor: pointer;
    transition: var(--transition);
    font-size: 12px;
    border-bottom: 2px solid transparent;
}

.category-tab:hover {
    background: rgba(255, 255, 255, 0.1);
    color: var(--text-primary);
}

.category-tab.active {
    color: var(--primary-color);
    border-bottom-color: var(--primary-color);
    background: rgba(102, 126, 234, 0.1);
}

.category-content {
    padding: 20px;
}

.policies-list {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.policy-item {
    padding: 16px;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 8px;
    transition: var(--transition);
}

.policy-item:hover {
    background: rgba(255, 255, 255, 0.15);
}

.policy-item.active {
    border-left: 4px solid #10b981;
}

.policy-item.inactive {
    opacity: 0.6;
    border-left: 4px solid #6b7280;
}

.policy-item.draft {
    border-left: 4px solid #f59e0b;
}

.policy-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
}

.policy-info h6 {
    margin: 0 0 4px 0;
    color: var(--text-primary);
    font-size: 14px;
    font-weight: 600;
}

.policy-status {
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 500;
}

.policy-status.active {
    background: rgba(16, 185, 129, 0.2);
    color: #10b981;
}

.policy-status.inactive {
    background: rgba(107, 114, 128, 0.2);
    color: #6b7280;
}

.policy-status.draft {
    background: rgba(245, 158, 11, 0.2);
    color: #f59e0b;
}

.policy-actions {
    display: flex;
    gap: 4px;
    opacity: 0;
    transition: var(--transition);
}

.policy-item:hover .policy-actions {
    opacity: 1;
}

.policy-description {
    color: var(--text-secondary);
    font-size: 13px;
    margin-bottom: 12px;
}

.policy-rules {
    margin-bottom: 8px;
}

.policy-rules h6 {
    margin: 0 0 8px 0;
    color: var(--text-primary);
    font-size: 13px;
    font-weight: 600;
}

.policy-rules ul {
    margin: 0;
    padding-left: 16px;
    color: var(--text-secondary);
    font-size: 12px;
}

.policy-rules li {
    margin-bottom: 4px;
}

.policy-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.last-updated {
    color: var(--text-muted);
    font-size: 11px;
}

/* 审计日志样式 */
.audit-section {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.audit-controls {
    display: flex;
    gap: 12px;
    align-items: center;
    flex-wrap: wrap;
}

.date-input {
    padding: 8px 12px;
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 6px;
    background: rgba(255, 255, 255, 0.1);
    color: var(--text-primary);
    font-size: 13px;
}

.audit-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 16px;
    margin-bottom: 20px;
}

.stat-card {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 16px;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 8px;
    transition: var(--transition);
}

.stat-card:hover {
    background: rgba(255, 255, 255, 0.15);
}

.stat-icon {
    width: 32px;
    height: 32px;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    border-radius: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 14px;
    flex-shrink: 0;
}

.stat-content {
    flex: 1;
}

.stat-value {
    font-size: 18px;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 2px;
}

.stat-label {
    color: var(--text-secondary);
    font-size: 11px;
}

.audit-logs {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 12px;
    overflow: hidden;
}

.audit-table {
    width: 100%;
}

.table-header {
    display: grid;
    grid-template-columns: 150px 100px 100px 200px 80px 120px 80px;
    background: rgba(255, 255, 255, 0.1);
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

.header-cell {
    padding: 12px 8px;
    color: var(--text-secondary);
    font-size: 12px;
    font-weight: 600;
    border-right: 1px solid rgba(255, 255, 255, 0.1);
}

.table-body {
    max-height: 400px;
    overflow-y: auto;
}

.table-row {
    display: grid;
    grid-template-columns: 150px 100px 100px 200px 80px 120px 80px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    transition: var(--transition);
}

.table-row:hover {
    background: rgba(255, 255, 255, 0.1);
}

.table-cell {
    padding: 8px;
    color: var(--text-primary);
    font-size: 12px;
    border-right: 1px solid rgba(255, 255, 255, 0.1);
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.event-type-badge,
.result-badge {
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 10px;
    font-weight: 500;
    text-transform: uppercase;
}

.event-type-badge.login {
    background: rgba(59, 130, 246, 0.2);
    color: #3b82f6;
}

.event-type-badge.access {
    background: rgba(16, 185, 129, 0.2);
    color: #10b981;
}

.event-type-badge.change {
    background: rgba(245, 158, 11, 0.2);
    color: #f59e0b;
}

.event-type-badge.security {
    background: rgba(239, 68, 68, 0.2);
    color: #ef4444;
}

.result-badge.success {
    background: rgba(16, 185, 129, 0.2);
    color: #10b981;
}

.result-badge.failure {
    background: rgba(239, 68, 68, 0.2);
    color: #ef4444;
}

.result-badge.warning {
    background: rgba(245, 158, 11, 0.2);
    color: #f59e0b;
}

/* 数据加密样式 */
.encryption-section {
    display: flex;
    flex-direction: column;
    gap: 24px;
}

.encryption-overview {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.encryption-status {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 12px;
    padding: 20px;
}

.encryption-status h5 {
    margin: 0 0 16px 0;
    color: var(--text-primary);
    font-size: 16px;
    font-weight: 600;
}

.status-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 16px;
}

.status-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 8px;
}

.status-icon {
    width: 32px;
    height: 32px;
    border-radius: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
    color: white;
    flex-shrink: 0;
}

.status-icon.encrypted {
    background: #10b981;
}

.status-icon.warning {
    background: #f59e0b;
}

.status-info {
    flex: 1;
}

.status-label {
    color: var(--text-secondary);
    font-size: 12px;
    margin-bottom: 2px;
}

.status-value {
    color: var(--text-primary);
    font-size: 13px;
    font-weight: 600;
}

.key-management {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 12px;
    padding: 20px;
}

.key-management h5 {
    margin: 0 0 16px 0;
    color: var(--text-primary);
    font-size: 16px;
    font-weight: 600;
}

.keys-list {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.encryption-key {
    padding: 16px;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 8px;
    transition: var(--transition);
}

.encryption-key:hover {
    background: rgba(255, 255, 255, 0.15);
}

.encryption-key.active {
    border-left: 4px solid #10b981;
}

.encryption-key.expiring {
    border-left: 4px solid #f59e0b;
}

.encryption-key.inactive {
    opacity: 0.6;
    border-left: 4px solid #6b7280;
}

.key-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
}

.key-info h6 {
    margin: 0 0 4px 0;
    color: var(--text-primary);
    font-size: 14px;
    font-weight: 600;
}

.key-algorithm {
    color: var(--text-muted);
    font-size: 11px;
    background: rgba(255, 255, 255, 0.1);
    padding: 2px 6px;
    border-radius: 3px;
}

.key-status {
    display: flex;
    align-items: center;
}

.status-badge {
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 500;
}

.status-badge.active {
    background: rgba(16, 185, 129, 0.2);
    color: #10b981;
}

.status-badge.expiring {
    background: rgba(245, 158, 11, 0.2);
    color: #f59e0b;
}

.status-badge.inactive {
    background: rgba(107, 114, 128, 0.2);
    color: #6b7280;
}

.key-details {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.key-meta {
    display: flex;
    gap: 16px;
    flex-wrap: wrap;
}

.key-meta span {
    color: var(--text-muted);
    font-size: 11px;
}

.key-actions {
    display: flex;
    gap: 4px;
    opacity: 0;
    transition: var(--transition);
}

.encryption-key:hover .key-actions {
    opacity: 1;
}

/* 应急响应界面样式 */
.incident-response-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: none;
    align-items: center;
    justify-content: center;
    z-index: 10004;
}

.incident-response-content {
    display: flex;
    height: 100%;
}

.response-sidebar {
    width: 300px;
    border-right: 1px solid rgba(255, 255, 255, 0.2);
    background: rgba(255, 255, 255, 0.05);
    overflow-y: auto;
    padding: 20px;
}

.response-main {
    flex: 1;
    padding: 20px;
    overflow-y: auto;
}

.incident-severity {
    margin-bottom: 24px;
}

.incident-severity h4 {
    margin: 0 0 12px 0;
    color: var(--text-primary);
    font-size: 14px;
    font-weight: 600;
}

.severity-levels {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.severity-level {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 12px;
    border-radius: 6px;
    cursor: pointer;
    transition: var(--transition);
}

.severity-level:hover {
    background: rgba(255, 255, 255, 0.1);
}

.severity-indicator {
    width: 12px;
    height: 12px;
    border-radius: 50%;
}

.severity-level.critical .severity-indicator {
    background: #ef4444;
}

.severity-level.high .severity-indicator {
    background: #f59e0b;
}

.severity-level.medium .severity-indicator {
    background: #3b82f6;
}

.severity-level.low .severity-indicator {
    background: #10b981;
}

.response-templates {
    margin-bottom: 24px;
}

.response-templates h4 {
    margin: 0 0 12px 0;
    color: var(--text-primary);
    font-size: 14px;
    font-weight: 600;
}

.template-list {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.template-item {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 12px;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 6px;
    cursor: pointer;
    transition: var(--transition);
}

.template-item:hover {
    background: rgba(255, 255, 255, 0.15);
}

.template-item.selected {
    background: rgba(102, 126, 234, 0.2);
    border-color: var(--primary-color);
}

.incident-form {
    display: flex;
    flex-direction: column;
    gap: 20px;
    margin-bottom: 24px;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 16px;
}

.impact-checkboxes {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 8px;
}

.team-selector select {
    width: 100%;
    height: 100px;
}

.response-actions {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 8px;
    padding: 16px;
}

.response-actions h4 {
    margin: 0 0 12px 0;
    color: var(--text-primary);
    font-size: 14px;
    font-weight: 600;
}

/* 响应式设计 */
@media (max-width: 1600px) {
    .security-panel {
        width: 100vw;
        right: -100vw;
    }

    .security-panel.show {
        right: 0;
    }
}

@media (max-width: 768px) {
    .security-metrics {
        grid-template-columns: 1fr;
    }

    .security-charts {
        grid-template-columns: 1fr;
    }

    .frameworks-grid {
        grid-template-columns: 1fr;
    }

    .category-tabs {
        flex-wrap: wrap;
    }

    .audit-controls {
        flex-direction: column;
        align-items: stretch;
    }

    .table-header,
    .table-row {
        grid-template-columns: 1fr;
        gap: 8px;
    }

    .table-cell {
        border-right: none;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    }

    .incident-response-content {
        flex-direction: column;
    }

    .response-sidebar {
        width: 100%;
        height: 200px;
        border-right: none;
        border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    }

    .form-row {
        grid-template-columns: 1fr;
    }

    .impact-checkboxes {
        grid-template-columns: 1fr;
    }
}

/* 高级工作流引擎样式 */
.workflow-panel {
    position: fixed;
    top: 0;
    right: -100vw;
    width: 100vw;
    height: 100vh;
    background: rgba(255, 255, 255, 0.95);
    border-left: 1px solid rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(20px);
    z-index: 9987;
    transition: right 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    display: flex;
    flex-direction: column;
    box-shadow: -10px 0 25px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.workflow-panel.show {
    right: 0;
}

.workflow-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 24px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    background: rgba(255, 255, 255, 0.1);
}

.workflow-header h3 {
    margin: 0;
    color: var(--text-primary);
    font-size: 18px;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 8px;
}

.workflow-controls {
    display: flex;
    gap: 8px;
    align-items: center;
}

.workflow-selector {
    padding: 8px 12px;
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 6px;
    background: rgba(255, 255, 255, 0.1);
    color: var(--text-primary);
    font-size: 13px;
    min-width: 150px;
}

.workflow-body {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.workflow-tabs {
    display: flex;
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    background: rgba(255, 255, 255, 0.05);
}

.workflow-tabs .tab-btn {
    flex: 1;
    padding: 12px 16px;
    border: none;
    background: transparent;
    color: var(--text-secondary);
    cursor: pointer;
    transition: var(--transition);
    font-size: 11px;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 4px;
    border-bottom: 2px solid transparent;
}

.workflow-tabs .tab-btn:hover {
    background: rgba(255, 255, 255, 0.1);
    color: var(--text-primary);
}

.workflow-tabs .tab-btn.active {
    color: var(--primary-color);
    border-bottom-color: var(--primary-color);
    background: rgba(102, 126, 234, 0.1);
}

.workflow-tabs .tab-btn i {
    font-size: 14px;
}

.workflow-content {
    flex: 1;
    overflow-y: auto;
}

.workflow-content .tab-content {
    display: none;
    height: 100%;
}

.workflow-content .tab-content.active {
    display: block;
}

/* 流程设计器样式 */
.designer-layout {
    display: flex;
    height: 100%;
}

.designer-toolbar {
    width: 250px;
    border-right: 1px solid rgba(255, 255, 255, 0.2);
    background: rgba(255, 255, 255, 0.05);
    overflow-y: auto;
    padding: 16px;
}

.toolbar-section {
    margin-bottom: 24px;
}

.toolbar-section h4 {
    margin: 0 0 12px 0;
    color: var(--text-primary);
    font-size: 14px;
    font-weight: 600;
}

.node-palette {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.node-category h5 {
    margin: 0 0 8px 0;
    color: var(--text-secondary);
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
}

.node-item {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 12px;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 6px;
    cursor: grab;
    transition: var(--transition);
    margin-bottom: 4px;
}

.node-item:hover {
    background: rgba(255, 255, 255, 0.15);
    transform: translateX(2px);
}

.node-item:active {
    cursor: grabbing;
}

.node-item i {
    color: var(--primary-color);
    font-size: 14px;
    width: 16px;
    text-align: center;
}

.node-item span {
    color: var(--text-primary);
    font-size: 12px;
    font-weight: 500;
}

.designer-tools {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.tool-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 6px;
    background: rgba(255, 255, 255, 0.1);
    color: var(--text-primary);
    cursor: pointer;
    transition: var(--transition);
}

.tool-btn:hover {
    background: rgba(255, 255, 255, 0.2);
    color: var(--primary-color);
}

.designer-main {
    flex: 1;
    position: relative;
    overflow: hidden;
}

.canvas-container {
    width: 100%;
    height: 100%;
    position: relative;
    background:
        radial-gradient(circle at 20px 20px, rgba(255, 255, 255, 0.1) 1px, transparent 1px),
        radial-gradient(circle at 20px 20px, rgba(255, 255, 255, 0.05) 1px, transparent 1px);
    background-size: 20px 20px;
    background-position: 0 0, 10px 10px;
}

.canvas-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: auto;
}

.workflow-node {
    position: absolute;
    width: 120px;
    height: 80px;
    background: rgba(255, 255, 255, 0.9);
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 8px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: var(--transition);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.workflow-node:hover {
    background: rgba(255, 255, 255, 1);
    border-color: var(--primary-color);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
}

.workflow-node.selected {
    border-color: var(--primary-color);
    background: rgba(102, 126, 234, 0.1);
}

.workflow-node.start {
    border-radius: 50%;
    background: linear-gradient(135deg, #10b981, #059669);
    color: white;
}

.workflow-node.end {
    border-radius: 50%;
    background: linear-gradient(135deg, #ef4444, #dc2626);
    color: white;
}

.workflow-node.decision {
    transform: rotate(45deg);
    border-radius: 8px;
    background: linear-gradient(135deg, #f59e0b, #d97706);
    color: white;
}

.workflow-node.decision .node-icon,
.workflow-node.decision .node-label {
    transform: rotate(-45deg);
}

.node-icon {
    font-size: 20px;
    margin-bottom: 4px;
}

.node-label {
    font-size: 11px;
    font-weight: 600;
    text-align: center;
}

.designer-properties {
    width: 300px;
    border-left: 1px solid rgba(255, 255, 255, 0.2);
    background: rgba(255, 255, 255, 0.05);
    overflow-y: auto;
}

.properties-panel {
    padding: 16px;
}

.properties-panel h4 {
    margin: 0 0 16px 0;
    color: var(--text-primary);
    font-size: 14px;
    font-weight: 600;
}

.no-selection {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 40px 20px;
    text-align: center;
    color: var(--text-secondary);
}

.no-selection i {
    font-size: 32px;
    margin-bottom: 12px;
    opacity: 0.5;
}

.node-properties {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.node-properties h5 {
    margin: 0;
    color: var(--text-primary);
    font-size: 14px;
    font-weight: 600;
}

.property-group {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.property-group label {
    color: var(--text-secondary);
    font-size: 12px;
    font-weight: 500;
}

.property-group input,
.property-group select,
.property-group textarea {
    padding: 8px 12px;
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 4px;
    background: rgba(255, 255, 255, 0.1);
    color: var(--text-primary);
    font-size: 12px;
}

.property-group textarea {
    resize: vertical;
    min-height: 60px;
}

.property-actions {
    display: flex;
    gap: 8px;
    margin-top: 16px;
}

.property-actions .btn-secondary,
.property-actions .btn-danger {
    flex: 1;
    font-size: 11px;
    padding: 6px 12px;
}

/* 流程实例样式 */
.instances-section {
    padding: 20px 24px;
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.section-header h4 {
    margin: 0;
    color: var(--text-primary);
    font-size: 18px;
    font-weight: 600;
}

.instance-controls,
.task-controls,
.form-controls,
.analytics-controls,
.integration-controls {
    display: flex;
    gap: 8px;
    align-items: center;
}

.instances-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 16px;
}

.stat-card {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 16px;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 8px;
    transition: var(--transition);
}

.stat-card:hover {
    background: rgba(255, 255, 255, 0.15);
}

.stat-icon {
    width: 32px;
    height: 32px;
    border-radius: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 14px;
    flex-shrink: 0;
}

.stat-icon.running {
    background: linear-gradient(135deg, #3b82f6, #2563eb);
}

.stat-icon.completed {
    background: linear-gradient(135deg, #10b981, #059669);
}

.stat-icon.suspended {
    background: linear-gradient(135deg, #f59e0b, #d97706);
}

.stat-icon.terminated {
    background: linear-gradient(135deg, #ef4444, #dc2626);
}

.stat-content {
    flex: 1;
}

.stat-value {
    font-size: 18px;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 2px;
}

.stat-label {
    color: var(--text-secondary);
    font-size: 11px;
}

.instances-list {
    display: flex;
    flex-direction: column;
    gap: 12px;
    max-height: 600px;
    overflow-y: auto;
}

.instance-item {
    padding: 16px;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 8px;
    transition: var(--transition);
}

.instance-item:hover {
    background: rgba(255, 255, 255, 0.15);
}

.instance-item.running {
    border-left: 4px solid #3b82f6;
}

.instance-item.completed {
    border-left: 4px solid #10b981;
}

.instance-item.suspended {
    border-left: 4px solid #f59e0b;
}

.instance-item.terminated {
    border-left: 4px solid #ef4444;
}

.instance-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
}

.instance-info h6 {
    margin: 0 0 4px 0;
    color: var(--text-primary);
    font-size: 14px;
    font-weight: 600;
}

.instance-id {
    color: var(--text-muted);
    font-size: 11px;
}

.instance-status .status-badge {
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 500;
}

.status-badge.running {
    background: rgba(59, 130, 246, 0.2);
    color: #3b82f6;
}

.status-badge.completed {
    background: rgba(16, 185, 129, 0.2);
    color: #10b981;
}

.status-badge.suspended {
    background: rgba(245, 158, 11, 0.2);
    color: #f59e0b;
}

.status-badge.terminated {
    background: rgba(239, 68, 68, 0.2);
    color: #ef4444;
}

.instance-details {
    margin-bottom: 12px;
}

.instance-meta {
    display: flex;
    gap: 16px;
    margin-bottom: 8px;
    flex-wrap: wrap;
}

.instance-meta span {
    color: var(--text-muted);
    font-size: 11px;
}

.instance-progress {
    display: flex;
    align-items: center;
    gap: 8px;
}

.progress-bar {
    flex: 1;
    height: 6px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 3px;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
    transition: width 0.3s ease;
}

.progress-text {
    color: var(--text-secondary);
    font-size: 11px;
    font-weight: 600;
    min-width: 35px;
    text-align: right;
}

.instance-actions {
    display: flex;
    gap: 4px;
    opacity: 0;
    transition: var(--transition);
}

.instance-item:hover .instance-actions {
    opacity: 1;
}

/* 任务管理样式 */
.tasks-section {
    padding: 20px 24px;
    display: flex;
    flex-direction: column;
    gap: 20px;
    height: 100%;
}

.task-board {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 16px;
    flex: 1;
    overflow: hidden;
}

.task-column {
    display: flex;
    flex-direction: column;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 8px;
    overflow: hidden;
}

.column-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;
    background: rgba(255, 255, 255, 0.1);
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

.column-header h5 {
    margin: 0;
    color: var(--text-primary);
    font-size: 14px;
    font-weight: 600;
}

.task-count {
    background: var(--primary-color);
    color: white;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 600;
}

.task-list {
    flex: 1;
    padding: 12px;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.task-card {
    padding: 12px;
    background: rgba(255, 255, 255, 0.9);
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 6px;
    cursor: pointer;
    transition: var(--transition);
}

.task-card:hover {
    background: rgba(255, 255, 255, 1);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.task-card.high {
    border-left: 4px solid #ef4444;
}

.task-card.medium {
    border-left: 4px solid #f59e0b;
}

.task-card.low {
    border-left: 4px solid #10b981;
}

.task-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 8px;
}

.task-header h6 {
    margin: 0;
    color: var(--text-primary);
    font-size: 13px;
    font-weight: 600;
    flex: 1;
}

.priority-badge {
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 10px;
    font-weight: 500;
    text-transform: uppercase;
}

.priority-badge.high {
    background: rgba(239, 68, 68, 0.2);
    color: #ef4444;
}

.priority-badge.medium {
    background: rgba(245, 158, 11, 0.2);
    color: #f59e0b;
}

.priority-badge.low {
    background: rgba(16, 185, 129, 0.2);
    color: #10b981;
}

.task-description {
    color: var(--text-secondary);
    font-size: 12px;
    margin-bottom: 8px;
    line-height: 1.4;
}

.task-meta {
    display: flex;
    flex-direction: column;
    gap: 4px;
    margin-bottom: 8px;
}

.task-meta span {
    color: var(--text-muted);
    font-size: 11px;
}

.assignee {
    color: var(--primary-color);
}

.unassigned {
    color: #f59e0b;
    font-style: italic;
}

.task-actions {
    display: flex;
    gap: 4px;
    opacity: 0;
    transition: var(--transition);
}

.task-card:hover .task-actions {
    opacity: 1;
}

/* 表单设计器样式 */
.forms-section {
    padding: 20px 24px;
    display: flex;
    flex-direction: column;
    gap: 20px;
    height: 100%;
}

.form-designer {
    display: flex;
    flex: 1;
    overflow: hidden;
}

.form-toolbar {
    width: 250px;
    border-right: 1px solid rgba(255, 255, 255, 0.2);
    background: rgba(255, 255, 255, 0.05);
    overflow-y: auto;
    padding: 16px;
}

.field-palette {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.field-palette h5 {
    margin: 0 0 12px 0;
    color: var(--text-primary);
    font-size: 14px;
    font-weight: 600;
}

.field-category h6 {
    margin: 0 0 8px 0;
    color: var(--text-secondary);
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
}

.field-item {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 12px;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 6px;
    cursor: grab;
    transition: var(--transition);
    margin-bottom: 4px;
}

.field-item:hover {
    background: rgba(255, 255, 255, 0.15);
    transform: translateX(2px);
}

.field-item:active {
    cursor: grabbing;
}

.field-item i {
    color: var(--primary-color);
    font-size: 14px;
    width: 16px;
    text-align: center;
}

.field-item span {
    color: var(--text-primary);
    font-size: 12px;
    font-weight: 500;
}

.form-canvas {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.form-preview {
    flex: 1;
    padding: 20px;
    overflow-y: auto;
    background: rgba(255, 255, 255, 0.05);
}

.form-placeholder {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    color: var(--text-secondary);
    text-align: center;
}

.form-placeholder i {
    font-size: 48px;
    margin-bottom: 16px;
    opacity: 0.5;
}

.form-placeholder p {
    margin: 0;
    font-size: 16px;
}

.form-properties {
    width: 300px;
    border-left: 1px solid rgba(255, 255, 255, 0.2);
    background: rgba(255, 255, 255, 0.05);
    overflow-y: auto;
}

.form-properties-panel {
    padding: 16px;
}

.form-properties-panel h5 {
    margin: 0 0 16px 0;
    color: var(--text-primary);
    font-size: 14px;
    font-weight: 600;
}

.no-field-selected {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 40px 20px;
    text-align: center;
    color: var(--text-secondary);
}

.no-field-selected i {
    font-size: 32px;
    margin-bottom: 12px;
    opacity: 0.5;
}

/* 流程分析样式 */
.analytics-section {
    padding: 20px 24px;
    display: flex;
    flex-direction: column;
    gap: 24px;
}

.analytics-overview {
    display: flex;
    flex-direction: column;
    gap: 24px;
}

.analytics-metrics {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 16px;
}

.analytics-metrics .metric-card {
    display: flex;
    align-items: center;
    gap: 16px;
    padding: 20px;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 12px;
    transition: var(--transition);
}

.analytics-metrics .metric-card:hover {
    background: rgba(255, 255, 255, 0.15);
    transform: translateY(-2px);
}

.analytics-metrics .metric-icon {
    width: 48px;
    height: 48px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 20px;
    color: white;
    flex-shrink: 0;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
}

.analytics-metrics .metric-content {
    flex: 1;
}

.analytics-metrics .metric-content .metric-value {
    font-size: 24px;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 4px;
}

.analytics-metrics .metric-content .metric-label {
    color: var(--text-secondary);
    font-size: 13px;
    font-weight: 500;
    margin-bottom: 4px;
}

.analytics-metrics .metric-trend {
    display: flex;
    align-items: center;
    gap: 4px;
    font-size: 11px;
    font-weight: 500;
    color: #10b981;
}

.analytics-charts {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 20px;
}

.analytics-charts .chart-container {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 12px;
    padding: 20px;
}

.analytics-charts .chart-container h5 {
    margin: 0 0 16px 0;
    color: var(--text-primary);
    font-size: 16px;
    font-weight: 600;
}

.bottleneck-analysis {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 12px;
    padding: 20px;
}

.bottleneck-analysis h5 {
    margin: 0 0 16px 0;
    color: var(--text-primary);
    font-size: 16px;
    font-weight: 600;
}

.bottleneck-list {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.bottleneck-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 8px;
    transition: var(--transition);
}

.bottleneck-item:hover {
    background: rgba(255, 255, 255, 0.15);
}

.bottleneck-item.high {
    border-left: 4px solid #ef4444;
}

.bottleneck-item.medium {
    border-left: 4px solid #f59e0b;
}

.bottleneck-item.low {
    border-left: 4px solid #10b981;
}

.bottleneck-info h6 {
    margin: 0 0 4px 0;
    color: var(--text-primary);
    font-size: 14px;
    font-weight: 600;
}

.bottleneck-stats {
    display: flex;
    gap: 16px;
}

.bottleneck-stats span {
    color: var(--text-muted);
    font-size: 11px;
}

.bottleneck-severity .severity-badge {
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 500;
}

.severity-badge.high {
    background: rgba(239, 68, 68, 0.2);
    color: #ef4444;
}

.severity-badge.medium {
    background: rgba(245, 158, 11, 0.2);
    color: #f59e0b;
}

.severity-badge.low {
    background: rgba(16, 185, 129, 0.2);
    color: #10b981;
}

/* 系统集成样式 */
.integrations-section {
    padding: 20px 24px;
    display: flex;
    flex-direction: column;
    gap: 24px;
}

.integration-categories {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 12px;
    padding: 20px;
}

.category-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 16px;
}

.integration-category {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 20px;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 8px;
    text-align: center;
    transition: var(--transition);
}

.integration-category:hover {
    background: rgba(255, 255, 255, 0.15);
    transform: translateY(-2px);
}

.category-icon {
    width: 48px;
    height: 48px;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 20px;
    margin-bottom: 12px;
}

.integration-category h5 {
    margin: 0 0 8px 0;
    color: var(--text-primary);
    font-size: 16px;
    font-weight: 600;
}

.integration-category p {
    margin: 0 0 12px 0;
    color: var(--text-secondary);
    font-size: 13px;
    line-height: 1.4;
}

.integration-status {
    display: flex;
    align-items: center;
    gap: 4px;
    font-size: 12px;
    font-weight: 500;
}

.integration-status.connected {
    color: #10b981;
}

.integration-status.disconnected {
    color: #ef4444;
}

.integration-list {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 12px;
    padding: 20px;
}

.integration-list h5 {
    margin: 0 0 16px 0;
    color: var(--text-primary);
    font-size: 16px;
    font-weight: 600;
}

.integrations-table {
    display: flex;
    flex-direction: column;
}

.integrations-header {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr 1.5fr 1fr;
    gap: 16px;
    padding: 12px 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

.integrations-header .header-cell {
    color: var(--text-secondary);
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
}

.integrations-body {
    display: flex;
    flex-direction: column;
}

.integration-row {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr 1.5fr 1fr;
    gap: 16px;
    padding: 12px 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    transition: var(--transition);
}

.integration-row:hover {
    background: rgba(255, 255, 255, 0.1);
}

.integration-cell {
    display: flex;
    align-items: center;
    color: var(--text-primary);
    font-size: 13px;
}

.integration-name {
    font-weight: 600;
    margin-bottom: 2px;
}

.integration-id {
    color: var(--text-muted);
    font-size: 11px;
}

.type-badge {
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 500;
}

.type-badge.email {
    background: rgba(59, 130, 246, 0.2);
    color: #3b82f6;
}

.type-badge.database {
    background: rgba(16, 185, 129, 0.2);
    color: #10b981;
}

.type-badge.api {
    background: rgba(245, 158, 11, 0.2);
    color: #f59e0b;
}

.type-badge.webhook {
    background: rgba(139, 92, 246, 0.2);
    color: #8b5cf6;
}

.integration-actions {
    display: flex;
    gap: 4px;
}

/* 工作流向导样式 */
.workflow-wizard-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: none;
    align-items: center;
    justify-content: center;
    z-index: 10005;
}

.wizard-steps {
    display: flex;
    justify-content: center;
    margin-bottom: 32px;
    padding: 0 20px;
}

.step {
    display: flex;
    flex-direction: column;
    align-items: center;
    position: relative;
    flex: 1;
    max-width: 150px;
}

.step:not(:last-child)::after {
    content: '';
    position: absolute;
    top: 15px;
    left: 60%;
    right: -40%;
    height: 2px;
    background: rgba(255, 255, 255, 0.2);
    z-index: -1;
}

.step.active:not(:last-child)::after {
    background: var(--primary-color);
}

.step-number {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.2);
    color: var(--text-secondary);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
    font-weight: 600;
    margin-bottom: 8px;
    transition: var(--transition);
}

.step.active .step-number {
    background: var(--primary-color);
    color: white;
}

.step-title {
    color: var(--text-secondary);
    font-size: 12px;
    font-weight: 500;
    text-align: center;
    transition: var(--transition);
}

.step.active .step-title {
    color: var(--text-primary);
    font-weight: 600;
}

.wizard-content {
    min-height: 400px;
    padding: 20px;
}

.wizard-step {
    display: none;
}

.wizard-step.active {
    display: block;
}

.wizard-step h4 {
    margin: 0 0 24px 0;
    color: var(--text-primary);
    font-size: 18px;
    font-weight: 600;
}

.template-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 16px;
}

.template-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 20px;
    background: rgba(255, 255, 255, 0.1);
    border: 2px solid rgba(255, 255, 255, 0.2);
    border-radius: 8px;
    cursor: pointer;
    transition: var(--transition);
    text-align: center;
}

.template-item:hover {
    background: rgba(255, 255, 255, 0.15);
    border-color: var(--primary-color);
}

.template-item.selected {
    background: rgba(102, 126, 234, 0.1);
    border-color: var(--primary-color);
}

.template-icon {
    width: 48px;
    height: 48px;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 20px;
    margin-bottom: 12px;
}

.template-info h5 {
    margin: 0 0 8px 0;
    color: var(--text-primary);
    font-size: 16px;
    font-weight: 600;
}

.template-info p {
    margin: 0;
    color: var(--text-secondary);
    font-size: 13px;
    line-height: 1.4;
}

.config-sections {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 24px;
}

.config-section {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 8px;
    padding: 16px;
}

.config-section h5 {
    margin: 0 0 16px 0;
    color: var(--text-primary);
    font-size: 14px;
    font-weight: 600;
}

.completion-summary {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    padding: 40px 20px;
}

.summary-icon {
    width: 64px;
    height: 64px;
    background: linear-gradient(135deg, #10b981, #059669);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 24px;
    margin-bottom: 16px;
}

.completion-summary h5 {
    margin: 0 0 12px 0;
    color: var(--text-primary);
    font-size: 20px;
    font-weight: 600;
}

.completion-summary p {
    margin: 0 0 24px 0;
    color: var(--text-secondary);
    font-size: 14px;
    line-height: 1.5;
}

.next-actions {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 8px;
    padding: 16px;
    text-align: left;
    max-width: 400px;
}

.next-actions h6 {
    margin: 0 0 12px 0;
    color: var(--text-primary);
    font-size: 14px;
    font-weight: 600;
}

.next-actions ul {
    margin: 0;
    padding-left: 16px;
    color: var(--text-secondary);
    font-size: 13px;
    line-height: 1.5;
}

.next-actions li {
    margin-bottom: 4px;
}

/* 响应式设计 */
@media (max-width: 1200px) {
    .designer-layout {
        flex-direction: column;
    }

    .designer-toolbar {
        width: 100%;
        height: 200px;
        border-right: none;
        border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    }

    .designer-properties {
        width: 100%;
        height: 200px;
        border-left: none;
        border-top: 1px solid rgba(255, 255, 255, 0.2);
    }

    .form-designer {
        flex-direction: column;
    }

    .form-toolbar {
        width: 100%;
        height: 200px;
        border-right: none;
        border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    }

    .form-properties {
        width: 100%;
        height: 200px;
        border-left: none;
        border-top: 1px solid rgba(255, 255, 255, 0.2);
    }
}

@media (max-width: 768px) {
    .workflow-tabs .tab-btn {
        font-size: 10px;
        padding: 8px 4px;
    }

    .task-board {
        grid-template-columns: 1fr;
        gap: 12px;
    }

    .task-column {
        max-height: 300px;
    }

    .analytics-metrics {
        grid-template-columns: 1fr;
    }

    .analytics-charts {
        grid-template-columns: 1fr;
    }

    .category-grid {
        grid-template-columns: 1fr;
    }

    .integrations-header,
    .integration-row {
        grid-template-columns: 1fr;
        gap: 8px;
    }

    .integration-cell {
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        padding-bottom: 4px;
    }

    .template-grid {
        grid-template-columns: 1fr;
    }

    .config-sections {
        grid-template-columns: 1fr;
    }

    .wizard-steps {
        flex-wrap: wrap;
        gap: 16px;
    }

    .step {
        max-width: none;
        flex: none;
    }

    .step:not(:last-child)::after {
        display: none;
    }
}

/* 智能助手系统样式 */
.ai-assistant-floating-btn {
    position: fixed;
    bottom: 20px;
    right: 20px;
    z-index: 10001;
    transition: var(--transition);
}

.ai-assistant-floating-btn.hidden {
    transform: translateY(100px);
    opacity: 0;
    pointer-events: none;
}

.floating-btn-content {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 24px;
    cursor: pointer;
    box-shadow: 0 4px 20px rgba(102, 126, 234, 0.3);
    transition: var(--transition);
    position: relative;
}

.floating-btn-content:hover {
    transform: scale(1.1);
    box-shadow: 0 6px 25px rgba(102, 126, 234, 0.4);
}

.notification-badge {
    position: absolute;
    top: -5px;
    right: -5px;
    background: #ef4444;
    color: white;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 10px;
    font-weight: 600;
}

.floating-btn-tooltip {
    position: absolute;
    bottom: 70px;
    right: 0;
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 6px 12px;
    border-radius: 6px;
    font-size: 12px;
    white-space: nowrap;
    opacity: 0;
    transform: translateY(10px);
    transition: var(--transition);
    pointer-events: none;
}

.ai-assistant-floating-btn:hover .floating-btn-tooltip {
    opacity: 1;
    transform: translateY(0);
}

.ai-assistant-panel {
    position: fixed;
    top: 0;
    right: -450px;
    width: 450px;
    height: 100vh;
    background: rgba(255, 255, 255, 0.95);
    border-left: 1px solid rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(20px);
    z-index: 9989;
    transition: right 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    display: flex;
    flex-direction: column;
    box-shadow: -10px 0 25px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.ai-assistant-panel.show {
    right: 0;
}

.assistant-header {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 20px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    background: rgba(255, 255, 255, 0.1);
}

.assistant-avatar {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 4px;
}

.avatar-image {
    width: 40px;
    height: 40px;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 18px;
}

.status-indicator {
    display: flex;
    align-items: center;
    gap: 4px;
    font-size: 10px;
    color: var(--text-muted);
}

.status-dot {
    width: 6px;
    height: 6px;
    border-radius: 50%;
    background: #10b981;
}

.status-dot.online {
    background: #10b981;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}

.assistant-info {
    flex: 1;
}

.assistant-info h3 {
    margin: 0 0 4px 0;
    color: var(--text-primary);
    font-size: 16px;
    font-weight: 600;
}

.assistant-info p {
    margin: 0;
    color: var(--text-secondary);
    font-size: 12px;
    line-height: 1.4;
}

.assistant-controls {
    display: flex;
    gap: 4px;
}

.assistant-body {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.conversation-area {
    flex: 1;
    overflow-y: auto;
    padding: 16px;
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.welcome-message {
    margin-bottom: 16px;
}

.conversation-messages {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.message-bubble {
    display: flex;
    gap: 8px;
    max-width: 100%;
}

.message-bubble.user {
    flex-direction: row-reverse;
}

.message-bubble.user .message-content {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: white;
    margin-left: 40px;
}

.message-bubble.assistant .message-content {
    background: rgba(255, 255, 255, 0.9);
    border: 1px solid rgba(255, 255, 255, 0.3);
    margin-right: 40px;
}

.message-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
    flex-shrink: 0;
}

.message-bubble.user .message-avatar {
    background: rgba(255, 255, 255, 0.2);
    color: var(--text-primary);
}

.message-bubble.assistant .message-avatar {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: white;
}

.message-content {
    flex: 1;
    padding: 12px 16px;
    border-radius: 16px;
    position: relative;
}

.message-text {
    color: inherit;
    font-size: 14px;
    line-height: 1.5;
    margin-bottom: 4px;
}

.message-text ul {
    margin: 8px 0;
    padding-left: 16px;
}

.message-text li {
    margin-bottom: 4px;
}

.message-time {
    font-size: 11px;
    opacity: 0.7;
    text-align: right;
}

.typing-indicator {
    display: flex;
    gap: 8px;
}

.typing-animation {
    display: flex;
    gap: 4px;
    padding: 12px 16px;
}

.typing-animation span {
    width: 6px;
    height: 6px;
    background: var(--text-muted);
    border-radius: 50%;
    animation: typing 1.4s infinite ease-in-out;
}

.typing-animation span:nth-child(1) { animation-delay: -0.32s; }
.typing-animation span:nth-child(2) { animation-delay: -0.16s; }

@keyframes typing {
    0%, 80%, 100% { transform: scale(0.8); opacity: 0.5; }
    40% { transform: scale(1); opacity: 1; }
}

.input-area {
    border-top: 1px solid rgba(255, 255, 255, 0.2);
    background: rgba(255, 255, 255, 0.05);
}

.quick-actions {
    display: flex;
    gap: 8px;
    padding: 12px 16px;
    overflow-x: auto;
}

.quick-action-btn {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 4px;
    padding: 8px 12px;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 8px;
    cursor: pointer;
    transition: var(--transition);
    min-width: 60px;
    white-space: nowrap;
}

.quick-action-btn:hover {
    background: rgba(255, 255, 255, 0.2);
    border-color: var(--primary-color);
}

.quick-action-btn i {
    font-size: 16px;
    color: var(--primary-color);
}

.quick-action-btn span {
    font-size: 10px;
    color: var(--text-secondary);
    font-weight: 500;
}

.message-input-container {
    padding: 16px;
}

.input-wrapper {
    display: flex;
    align-items: flex-end;
    gap: 8px;
    background: rgba(255, 255, 255, 0.9);
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 20px;
    padding: 8px 12px;
    transition: var(--transition);
}

.input-wrapper:focus-within {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2);
}

#messageInput {
    flex: 1;
    border: none;
    background: transparent;
    resize: none;
    outline: none;
    font-size: 14px;
    color: var(--text-primary);
    line-height: 1.4;
    max-height: 120px;
    min-height: 20px;
}

#messageInput::placeholder {
    color: var(--text-muted);
}

.input-actions {
    display: flex;
    gap: 4px;
    align-items: center;
}

.voice-btn,
.attach-btn {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    background: transparent;
    border: none;
    color: var(--text-muted);
    cursor: pointer;
    transition: var(--transition);
}

.voice-btn:hover,
.attach-btn:hover {
    background: rgba(255, 255, 255, 0.2);
    color: var(--primary-color);
}

.send-btn {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--primary-color);
    border: none;
    color: white;
    cursor: pointer;
    transition: var(--transition);
}

.send-btn:hover {
    background: var(--secondary-color);
    transform: scale(1.05);
}

.assistant-suggestions {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: rgba(255, 255, 255, 0.95);
    border-top: 1px solid rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(20px);
    transform: translateY(100%);
    transition: var(--transition);
    display: none;
}

.assistant-suggestions.show {
    transform: translateY(0);
    display: block;
}

.suggestions-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

.suggestions-header h4 {
    margin: 0;
    color: var(--text-primary);
    font-size: 14px;
    font-weight: 600;
}

.suggestions-list {
    max-height: 200px;
    overflow-y: auto;
    padding: 8px;
}

.suggestion-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 8px;
    margin-bottom: 8px;
    transition: var(--transition);
}

.suggestion-item:hover {
    background: rgba(255, 255, 255, 0.2);
}

.suggestion-item.high {
    border-left: 4px solid #ef4444;
}

.suggestion-item.medium {
    border-left: 4px solid #f59e0b;
}

.suggestion-item.low {
    border-left: 4px solid #10b981;
}

.suggestion-icon {
    width: 32px;
    height: 32px;
    border-radius: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: white;
    font-size: 14px;
    flex-shrink: 0;
}

.suggestion-content {
    flex: 1;
    min-width: 0;
}

.suggestion-content h5 {
    margin: 0 0 4px 0;
    color: var(--text-primary);
    font-size: 13px;
    font-weight: 600;
}

.suggestion-content p {
    margin: 0;
    color: var(--text-secondary);
    font-size: 12px;
    line-height: 1.4;
}

.suggestion-actions {
    display: flex;
    gap: 4px;
}

.btn-sm {
    padding: 4px 8px;
    font-size: 11px;
    border-radius: 4px;
}

/* AI助手设置面板样式 */
.assistant-settings-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: none;
    align-items: center;
    justify-content: center;
    z-index: 10006;
}

.settings-tabs {
    display: flex;
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    margin-bottom: 20px;
}

.settings-tab {
    flex: 1;
    padding: 12px 16px;
    border: none;
    background: transparent;
    color: var(--text-secondary);
    cursor: pointer;
    transition: var(--transition);
    font-size: 13px;
    border-bottom: 2px solid transparent;
}

.settings-tab:hover {
    background: rgba(255, 255, 255, 0.1);
    color: var(--text-primary);
}

.settings-tab.active {
    color: var(--primary-color);
    border-bottom-color: var(--primary-color);
    background: rgba(102, 126, 234, 0.1);
}

.settings-content {
    min-height: 400px;
}

.settings-tab-content {
    display: none;
}

.settings-tab-content.active {
    display: block;
}

.settings-tab-content h4 {
    margin: 0 0 20px 0;
    color: var(--text-primary);
    font-size: 16px;
    font-weight: 600;
}

.setting-group {
    margin-bottom: 16px;
}

.setting-group label {
    display: block;
    margin-bottom: 6px;
    color: var(--text-secondary);
    font-size: 13px;
    font-weight: 500;
}

.setting-group input,
.setting-group select,
.setting-group textarea {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 6px;
    background: rgba(255, 255, 255, 0.1);
    color: var(--text-primary);
    font-size: 13px;
}

.setting-group input[type="range"] {
    width: 100%;
    margin: 8px 0;
}

.checkbox-label {
    display: flex;
    align-items: center;
    gap: 8px;
    cursor: pointer;
    margin-bottom: 0;
}

.checkbox-label input[type="checkbox"] {
    width: auto;
    margin: 0;
}

.checkmark {
    width: 16px;
    height: 16px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 3px;
    position: relative;
    transition: var(--transition);
}

.checkbox-label input[type="checkbox"]:checked + .checkmark {
    background: var(--primary-color);
    border-color: var(--primary-color);
}

.checkbox-label input[type="checkbox"]:checked + .checkmark::after {
    content: '✓';
    position: absolute;
    top: -2px;
    left: 2px;
    color: white;
    font-size: 12px;
    font-weight: bold;
}

.knowledge-stats {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 16px;
    margin-bottom: 20px;
}

.stat-item {
    text-align: center;
    padding: 16px;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 8px;
}

.stat-value {
    font-size: 24px;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: 4px;
}

.stat-label {
    color: var(--text-secondary);
    font-size: 12px;
}

.knowledge-actions {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
}

/* 知识库管理界面样式 */
.knowledge-base-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: none;
    align-items: center;
    justify-content: center;
    z-index: 10007;
}

.knowledge-toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    gap: 16px;
}

.search-box {
    display: flex;
    flex: 1;
    max-width: 300px;
}

.search-box input {
    flex: 1;
    padding: 8px 12px;
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 6px 0 0 6px;
    background: rgba(255, 255, 255, 0.1);
    color: var(--text-primary);
    font-size: 13px;
}

.search-btn {
    padding: 8px 12px;
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-left: none;
    border-radius: 0 6px 6px 0;
    background: rgba(255, 255, 255, 0.1);
    color: var(--text-secondary);
    cursor: pointer;
    transition: var(--transition);
}

.search-btn:hover {
    background: rgba(255, 255, 255, 0.2);
    color: var(--primary-color);
}

.knowledge-content {
    display: flex;
    gap: 20px;
    height: 500px;
}

.knowledge-categories {
    width: 200px;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 8px;
    padding: 16px;
    overflow-y: auto;
}

.knowledge-categories h4 {
    margin: 0 0 12px 0;
    color: var(--text-primary);
    font-size: 14px;
    font-weight: 600;
}

.category-list {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.category-item {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 12px;
    border-radius: 6px;
    cursor: pointer;
    transition: var(--transition);
}

.category-item:hover {
    background: rgba(255, 255, 255, 0.1);
}

.category-item.active {
    background: rgba(102, 126, 234, 0.2);
    color: var(--primary-color);
}

.category-item i {
    font-size: 14px;
    width: 16px;
    text-align: center;
}

.category-item span {
    flex: 1;
    font-size: 13px;
}

.category-item .count {
    background: rgba(255, 255, 255, 0.2);
    color: var(--text-secondary);
    padding: 2px 6px;
    border-radius: 10px;
    font-size: 10px;
    font-weight: 600;
}

.knowledge-list {
    flex: 1;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 8px;
    overflow: hidden;
}

.knowledge-items {
    height: 100%;
    overflow-y: auto;
    padding: 16px;
}

.empty-knowledge {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    color: var(--text-secondary);
    text-align: center;
}

.empty-knowledge i {
    font-size: 48px;
    margin-bottom: 16px;
    opacity: 0.5;
}

.knowledge-item {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 8px;
    padding: 16px;
    margin-bottom: 12px;
    transition: var(--transition);
}

.knowledge-item:hover {
    background: rgba(255, 255, 255, 0.15);
}

.knowledge-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 8px;
}

.knowledge-header h5 {
    margin: 0;
    color: var(--text-primary);
    font-size: 14px;
    font-weight: 600;
    flex: 1;
}

.knowledge-meta {
    display: flex;
    gap: 8px;
    align-items: center;
}

.category-tag {
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 10px;
    font-weight: 500;
    text-transform: uppercase;
}

.category-tag.system {
    background: rgba(59, 130, 246, 0.2);
    color: #3b82f6;
}

.category-tag.business {
    background: rgba(16, 185, 129, 0.2);
    color: #10b981;
}

.category-tag.technical {
    background: rgba(245, 158, 11, 0.2);
    color: #f59e0b;
}

.category-tag.faq {
    background: rgba(139, 92, 246, 0.2);
    color: #8b5cf6;
}

.category-tag.learned {
    background: rgba(239, 68, 68, 0.2);
    color: #ef4444;
}

.confidence-score {
    background: rgba(255, 255, 255, 0.2);
    color: var(--text-secondary);
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 10px;
    font-weight: 600;
}

.knowledge-content p {
    margin: 0 0 8px 0;
    color: var(--text-secondary);
    font-size: 13px;
    line-height: 1.4;
}

.knowledge-keywords {
    display: flex;
    gap: 4px;
    flex-wrap: wrap;
    margin-bottom: 8px;
}

.keyword-tag {
    background: rgba(102, 126, 234, 0.2);
    color: var(--primary-color);
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 10px;
    font-weight: 500;
}

.knowledge-actions {
    display: flex;
    gap: 4px;
    opacity: 0;
    transition: var(--transition);
}

.knowledge-item:hover .knowledge-actions {
    opacity: 1;
}

/* 消息内容样式 */
.action-options {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 8px;
    padding: 16px;
    margin: 8px 0;
}

.action-options h4 {
    margin: 0 0 12px 0;
    color: var(--text-primary);
    font-size: 14px;
    font-weight: 600;
}

.option-buttons {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 8px;
}

.option-btn {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 6px;
    padding: 12px 8px;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 6px;
    cursor: pointer;
    transition: var(--transition);
    text-align: center;
}

.option-btn:hover {
    background: rgba(255, 255, 255, 0.2);
    border-color: var(--primary-color);
}

.option-btn i {
    font-size: 16px;
    color: var(--primary-color);
}

.option-btn span {
    font-size: 11px;
    color: var(--text-secondary);
    font-weight: 500;
}

.daily-summary {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 8px;
    padding: 16px;
    margin: 8px 0;
}

.daily-summary h4 {
    margin: 0 0 16px 0;
    color: var(--text-primary);
    font-size: 16px;
    font-weight: 600;
}

.summary-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
    gap: 12px;
    margin-bottom: 16px;
}

.summary-stats .stat-item {
    text-align: center;
    padding: 12px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 6px;
}

.summary-stats .stat-value {
    font-size: 18px;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 4px;
}

.summary-stats .stat-label {
    color: var(--text-secondary);
    font-size: 11px;
    margin-bottom: 4px;
}

.stat-change {
    font-size: 10px;
    font-weight: 600;
    padding: 2px 4px;
    border-radius: 3px;
}

.stat-change.positive {
    background: rgba(16, 185, 129, 0.2);
    color: #10b981;
}

.stat-change.negative {
    background: rgba(239, 68, 68, 0.2);
    color: #ef4444;
}

.stat-change.stable {
    background: rgba(107, 114, 128, 0.2);
    color: #6b7280;
}

.summary-insights {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 6px;
    padding: 12px;
}

.summary-insights h5 {
    margin: 0 0 8px 0;
    color: var(--text-primary);
    font-size: 13px;
    font-weight: 600;
}

.summary-insights ul {
    margin: 0;
    padding-left: 16px;
    color: var(--text-secondary);
    font-size: 12px;
    line-height: 1.5;
}

.summary-insights li {
    margin-bottom: 4px;
}

/* 数据分析样式 */
.data-analysis {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 8px;
    padding: 16px;
    margin: 8px 0;
}

.data-analysis h4 {
    margin: 0 0 16px 0;
    color: var(--text-primary);
    font-size: 16px;
    font-weight: 600;
}

.analysis-charts {
    margin-bottom: 16px;
}

.chart-placeholder {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 150px;
    background: rgba(255, 255, 255, 0.1);
    border: 1px dashed rgba(255, 255, 255, 0.3);
    border-radius: 6px;
    color: var(--text-secondary);
}

.chart-placeholder i {
    font-size: 32px;
    margin-bottom: 8px;
    opacity: 0.5;
}

.analysis-insights {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 6px;
    padding: 12px;
}

.analysis-insights h5 {
    margin: 0 0 8px 0;
    color: var(--text-primary);
    font-size: 13px;
    font-weight: 600;
}

.analysis-insights ul {
    margin: 0;
    padding-left: 16px;
    color: var(--text-secondary);
    font-size: 12px;
    line-height: 1.5;
}

.analysis-metrics {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 12px;
    margin-bottom: 16px;
}

.analysis-metrics .metric {
    text-align: center;
    padding: 12px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 6px;
}

.metric-value {
    font-size: 18px;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: 4px;
}

.metric-label {
    color: var(--text-secondary);
    font-size: 11px;
}

/* 系统状态样式 */
.system-status {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 8px;
    padding: 16px;
    margin: 8px 0;
}

.system-status h4 {
    margin: 0 0 16px 0;
    color: var(--text-primary);
    font-size: 16px;
    font-weight: 600;
}

.status-items {
    display: flex;
    flex-direction: column;
    gap: 8px;
    margin-bottom: 16px;
}

.status-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 8px 12px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 6px;
}

.status-item.healthy {
    border-left: 4px solid #10b981;
}

.status-item.warning {
    border-left: 4px solid #f59e0b;
}

.status-item.error {
    border-left: 4px solid #ef4444;
}

.status-item i {
    font-size: 16px;
    color: var(--primary-color);
    width: 20px;
    text-align: center;
}

.status-label {
    flex: 1;
    color: var(--text-secondary);
    font-size: 13px;
}

.status-value {
    color: var(--text-primary);
    font-size: 13px;
    font-weight: 600;
}

.status-summary {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 6px;
    padding: 12px;
}

.status-summary p {
    margin: 0;
    color: var(--text-secondary);
    font-size: 13px;
    line-height: 1.4;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .ai-assistant-panel {
        width: 100vw;
        right: -100vw;
    }

    .ai-assistant-panel.show {
        right: 0;
    }

    .quick-actions {
        flex-wrap: wrap;
    }

    .quick-action-btn {
        min-width: 50px;
    }

    .option-buttons {
        grid-template-columns: repeat(2, 1fr);
    }

    .summary-stats {
        grid-template-columns: repeat(2, 1fr);
    }

    .analysis-metrics {
        grid-template-columns: 1fr;
    }

    .knowledge-content {
        flex-direction: column;
        height: auto;
    }

    .knowledge-categories {
        width: 100%;
        height: 150px;
    }

    .category-list {
        flex-direction: row;
        flex-wrap: wrap;
    }

    .settings-tabs {
        flex-wrap: wrap;
    }

    .knowledge-stats {
        grid-template-columns: 1fr;
    }
}
