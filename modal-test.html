<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>模态框测试页面</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            padding: 20px;
            background: var(--gray-100);
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: var(--border-radius);
            box-shadow: var(--shadow);
        }
        .test-buttons {
            display: flex;
            gap: 16px;
            flex-wrap: wrap;
            margin-bottom: 30px;
        }
        .test-info {
            background: var(--gray-50);
            padding: 20px;
            border-radius: var(--border-radius);
            border-left: 4px solid var(--primary-color);
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>注册表单模态框测试</h1>
        <p>测试优化后的注册表单在不同屏幕尺寸下的显示效果</p>
        
        <div class="test-buttons">
            <button class="btn-primary" onclick="showTestModal()">
                <i class="fas fa-user-plus"></i>
                测试注册表单
            </button>
            <button class="btn-secondary" onclick="showLargeModal()">
                <i class="fas fa-expand"></i>
                测试大表单
            </button>
        </div>
        
        <div class="test-info">
            <h3>修复内容：</h3>
            <ul>
                <li>🔧 修复滚动问题：确保模态框内容可以正常滚动</li>
                <li>🔧 修复CSS冲突：移除重复和冲突的样式规则</li>
                <li>🔧 修复JavaScript：添加缺失的showNotification函数</li>
                <li>✅ 移除高度限制：允许注册表单完整显示</li>
                <li>✅ 添加滚动支持：桌面端提供优雅的滚动体验</li>
                <li>✅ 优化移动端：小屏幕设备完全展开表单</li>
                <li>✅ 动态调整：根据屏幕尺寸和内容自动调整</li>
                <li>✅ 改进对齐方式：避免内容被截断</li>
            </ul>
            <p><strong>测试说明：</strong>请在不同设备上测试模态框的滚动功能和表单提交功能。</p>
        </div>
    </div>

    <!-- 测试模态框 -->
    <div class="modal-overlay" id="testModalOverlay">
        <div class="modal-container">
            <div class="modal-header">
                <h3>用户注册</h3>
                <button class="modal-close" onclick="closeTestModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-content">
                <form id="testForm">
                    <div class="form-row">
                        <div class="form-group">
                            <label for="firstName">姓名 *</label>
                            <input type="text" id="firstName" name="firstName" required>
                        </div>
                        <div class="form-group">
                            <label for="lastName">姓氏 *</label>
                            <input type="text" id="lastName" name="lastName" required>
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-group">
                            <label for="email">邮箱地址 *</label>
                            <input type="email" id="email" name="email" required>
                        </div>
                        <div class="form-group">
                            <label for="phone">手机号码</label>
                            <input type="tel" id="phone" name="phone">
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-group">
                            <label for="username">用户名 *</label>
                            <input type="text" id="username" name="username" required>
                        </div>
                        <div class="form-group">
                            <label for="password">密码 *</label>
                            <input type="password" id="password" name="password" required>
                            <small>密码至少8位，包含字母和数字</small>
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-group">
                            <label for="confirmPassword">确认密码 *</label>
                            <input type="password" id="confirmPassword" name="confirmPassword" required>
                        </div>
                        <div class="form-group">
                            <label for="birthDate">出生日期</label>
                            <input type="date" id="birthDate" name="birthDate">
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-group">
                            <label for="gender">性别</label>
                            <select id="gender" name="gender">
                                <option value="">请选择</option>
                                <option value="male">男</option>
                                <option value="female">女</option>
                                <option value="other">其他</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="country">国家/地区</label>
                            <select id="country" name="country">
                                <option value="">请选择</option>
                                <option value="cn">中国</option>
                                <option value="us">美国</option>
                                <option value="jp">日本</option>
                                <option value="kr">韩国</option>
                            </select>
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="address">详细地址</label>
                        <textarea id="address" name="address" rows="3" placeholder="请输入详细地址..."></textarea>
                    </div>
                    <div class="form-group">
                        <label for="bio">个人简介</label>
                        <textarea id="bio" name="bio" rows="4" placeholder="简单介绍一下自己..."></textarea>
                    </div>
                    <div class="form-group">
                        <label>
                            <input type="checkbox" id="terms" name="terms" required>
                            我已阅读并同意 <a href="#" style="color: var(--primary-color);">用户协议</a> 和 <a href="#" style="color: var(--primary-color);">隐私政策</a>
                        </label>
                    </div>
                    <div class="form-group">
                        <label>
                            <input type="checkbox" id="newsletter" name="newsletter">
                            订阅我们的新闻通讯
                        </label>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn-secondary" onclick="closeTestModal()">取消</button>
                <button type="button" class="btn-primary" onclick="submitForm()">
                    <i class="fas fa-user-plus"></i>
                    注册账户
                </button>
            </div>
        </div>
    </div>

    <!-- 大表单测试模态框 -->
    <div class="modal-overlay" id="largeModalOverlay">
        <div class="modal-container large">
            <div class="modal-header">
                <h3>详细信息表单</h3>
                <button class="modal-close" onclick="closeLargeModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-content">
                <form id="largeForm">
                    <!-- 基本信息 -->
                    <h4 style="margin-bottom: 16px; color: var(--gray-700); border-bottom: 2px solid var(--gray-200); padding-bottom: 8px;">基本信息</h4>
                    <div class="form-row">
                        <div class="form-group">
                            <label for="fullName">完整姓名 *</label>
                            <input type="text" id="fullName" name="fullName" required>
                        </div>
                        <div class="form-group">
                            <label for="nickname">昵称</label>
                            <input type="text" id="nickname" name="nickname">
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-group">
                            <label for="idNumber">身份证号</label>
                            <input type="text" id="idNumber" name="idNumber">
                        </div>
                        <div class="form-group">
                            <label for="nationality">国籍</label>
                            <input type="text" id="nationality" name="nationality">
                        </div>
                    </div>
                    
                    <!-- 联系信息 -->
                    <h4 style="margin: 24px 0 16px 0; color: var(--gray-700); border-bottom: 2px solid var(--gray-200); padding-bottom: 8px;">联系信息</h4>
                    <div class="form-row">
                        <div class="form-group">
                            <label for="primaryEmail">主要邮箱 *</label>
                            <input type="email" id="primaryEmail" name="primaryEmail" required>
                        </div>
                        <div class="form-group">
                            <label for="secondaryEmail">备用邮箱</label>
                            <input type="email" id="secondaryEmail" name="secondaryEmail">
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-group">
                            <label for="primaryPhone">主要电话 *</label>
                            <input type="tel" id="primaryPhone" name="primaryPhone" required>
                        </div>
                        <div class="form-group">
                            <label for="secondaryPhone">备用电话</label>
                            <input type="tel" id="secondaryPhone" name="secondaryPhone">
                        </div>
                    </div>
                    
                    <!-- 地址信息 -->
                    <h4 style="margin: 24px 0 16px 0; color: var(--gray-700); border-bottom: 2px solid var(--gray-200); padding-bottom: 8px;">地址信息</h4>
                    <div class="form-row">
                        <div class="form-group">
                            <label for="province">省份</label>
                            <select id="province" name="province">
                                <option value="">请选择省份</option>
                                <option value="beijing">北京市</option>
                                <option value="shanghai">上海市</option>
                                <option value="guangdong">广东省</option>
                                <option value="zhejiang">浙江省</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="city">城市</label>
                            <select id="city" name="city">
                                <option value="">请选择城市</option>
                            </select>
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="detailedAddress">详细地址</label>
                        <textarea id="detailedAddress" name="detailedAddress" rows="3"></textarea>
                    </div>
                    
                    <!-- 职业信息 -->
                    <h4 style="margin: 24px 0 16px 0; color: var(--gray-700); border-bottom: 2px solid var(--gray-200); padding-bottom: 8px;">职业信息</h4>
                    <div class="form-row">
                        <div class="form-group">
                            <label for="company">公司名称</label>
                            <input type="text" id="company" name="company">
                        </div>
                        <div class="form-group">
                            <label for="position">职位</label>
                            <input type="text" id="position" name="position">
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-group">
                            <label for="industry">行业</label>
                            <select id="industry" name="industry">
                                <option value="">请选择行业</option>
                                <option value="tech">科技</option>
                                <option value="finance">金融</option>
                                <option value="education">教育</option>
                                <option value="healthcare">医疗</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="experience">工作经验</label>
                            <select id="experience" name="experience">
                                <option value="">请选择</option>
                                <option value="0-1">0-1年</option>
                                <option value="1-3">1-3年</option>
                                <option value="3-5">3-5年</option>
                                <option value="5+">5年以上</option>
                            </select>
                        </div>
                    </div>
                    
                    <!-- 其他信息 -->
                    <h4 style="margin: 24px 0 16px 0; color: var(--gray-700); border-bottom: 2px solid var(--gray-200); padding-bottom: 8px;">其他信息</h4>
                    <div class="form-group">
                        <label for="interests">兴趣爱好</label>
                        <textarea id="interests" name="interests" rows="3" placeholder="请描述您的兴趣爱好..."></textarea>
                    </div>
                    <div class="form-group">
                        <label for="notes">备注</label>
                        <textarea id="notes" name="notes" rows="4" placeholder="其他需要说明的信息..."></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn-secondary" onclick="closeLargeModal()">取消</button>
                <button type="button" class="btn-primary" onclick="submitLargeForm()">
                    <i class="fas fa-save"></i>
                    保存信息
                </button>
            </div>
        </div>
    </div>

    <script>
        function showTestModal() {
            document.getElementById('testModalOverlay').classList.add('active');
        }
        
        function closeTestModal() {
            document.getElementById('testModalOverlay').classList.remove('active');
        }
        
        function showLargeModal() {
            document.getElementById('largeModalOverlay').classList.add('active');
        }
        
        function closeLargeModal() {
            document.getElementById('largeModalOverlay').classList.remove('active');
        }
        
        // 简单的通知函数
        function showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            notification.className = `notification notification-${type}`;
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: ${type === 'success' ? '#10b981' : type === 'error' ? '#ef4444' : '#3b82f6'};
                color: white;
                padding: 12px 20px;
                border-radius: 8px;
                box-shadow: 0 4px 12px rgba(0,0,0,0.15);
                z-index: 10000;
                transform: translateX(100%);
                transition: transform 0.3s ease;
            `;
            notification.textContent = message;

            document.body.appendChild(notification);

            // 显示动画
            setTimeout(() => {
                notification.style.transform = 'translateX(0)';
            }, 10);

            // 自动移除
            setTimeout(() => {
                notification.style.transform = 'translateX(100%)';
                setTimeout(() => {
                    if (notification.parentNode) {
                        notification.parentNode.removeChild(notification);
                    }
                }, 300);
            }, 3000);
        }

        function submitForm() {
            // 验证表单
            const form = document.getElementById('testForm');
            const formData = new FormData(form);

            if (!formData.get('firstName') || !formData.get('email') || !formData.get('username') || !formData.get('password')) {
                showNotification('请填写所有必填字段', 'error');
                return;
            }

            showNotification('注册表单提交成功！', 'success');
            closeTestModal();
        }

        function submitLargeForm() {
            showNotification('详细信息保存成功！', 'success');
            closeLargeModal();
        }
        
        // 点击遮罩层关闭模态框
        document.getElementById('testModalOverlay').addEventListener('click', function(e) {
            if (e.target === this) {
                closeTestModal();
            }
        });
        
        document.getElementById('largeModalOverlay').addEventListener('click', function(e) {
            if (e.target === this) {
                closeLargeModal();
            }
        });
        
        // ESC键关闭模态框
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                closeTestModal();
                closeLargeModal();
            }
        });
    </script>
</body>
</html>
