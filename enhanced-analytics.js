// 增强数据分析功能
class EnhancedAnalytics {
    constructor() {
        this.charts = {};
        this.realTimeEnabled = true;
        this.updateInterval = null;
        this.currentTimeRange = '30d';
        this.comparisonPeriod = 'lastMonth';
        this.predictionMetric = 'revenue';
        
        this.initializeData();
        this.startRealTimeUpdates();
    }
    
    initializeData() {
        // 生成模拟数据
        this.generateSampleData();
        this.updateAllMetrics();
        this.initializeCharts();
    }
    
    generateSampleData() {
        const now = new Date();
        const days = this.getTimeRangeDays(this.currentTimeRange);
        
        this.data = {
            revenue: [],
            orders: [],
            users: [],
            conversion: [],
            geographic: {
                '华东地区': 45.2,
                '华南地区': 28.7,
                '华北地区': 15.8,
                '其他地区': 10.3
            },
            realTime: {
                onlineUsers: Math.floor(Math.random() * 500) + 100,
                todayOrders: Math.floor(Math.random() * 200) + 50,
                realTimeRevenue: Math.floor(Math.random() * 50000) + 10000,
                conversionRate: (Math.random() * 5 + 2).toFixed(1)
            }
        };
        
        // 生成时间序列数据
        for (let i = days; i >= 0; i--) {
            const date = new Date(now.getTime() - i * 24 * 60 * 60 * 1000);
            const baseRevenue = 10000 + Math.random() * 5000;
            const baseOrders = 50 + Math.random() * 30;
            const baseUsers = 20 + Math.random() * 15;
            
            this.data.revenue.push({
                date: date,
                value: Math.floor(baseRevenue * (1 + Math.sin(i / 7) * 0.2))
            });
            
            this.data.orders.push({
                date: date,
                value: Math.floor(baseOrders * (1 + Math.sin(i / 7) * 0.3))
            });
            
            this.data.users.push({
                date: date,
                value: Math.floor(baseUsers * (1 + Math.sin(i / 5) * 0.4))
            });
            
            this.data.conversion.push({
                date: date,
                value: (2 + Math.random() * 3).toFixed(1)
            });
        }
    }
    
    getTimeRangeDays(range) {
        switch (range) {
            case '7d': return 7;
            case '30d': return 30;
            case '90d': return 90;
            case '1y': return 365;
            default: return 30;
        }
    }
    
    updateAllMetrics() {
        this.updateKeyMetrics();
        this.updateRealTimeMetrics();
        this.updateTrends();
    }
    
    updateKeyMetrics() {
        const totalRevenue = this.data.revenue.reduce((sum, item) => sum + item.value, 0);
        const totalOrders = this.data.orders.reduce((sum, item) => sum + item.value, 0);
        const totalUsers = this.data.users.reduce((sum, item) => sum + item.value, 0);
        const avgConversion = this.data.conversion.reduce((sum, item) => sum + parseFloat(item.value), 0) / this.data.conversion.length;
        
        document.getElementById('totalRevenue').textContent = `¥${totalRevenue.toLocaleString()}`;
        document.getElementById('totalOrders').textContent = totalOrders.toLocaleString();
        document.getElementById('totalCustomers').textContent = totalUsers.toLocaleString();
        document.getElementById('avgConversion').textContent = `${avgConversion.toFixed(1)}%`;
        
        // 计算趋势
        this.calculateTrends();
    }
    
    calculateTrends() {
        const recentRevenue = this.data.revenue.slice(-7).reduce((sum, item) => sum + item.value, 0);
        const previousRevenue = this.data.revenue.slice(-14, -7).reduce((sum, item) => sum + item.value, 0);
        const revenueTrend = ((recentRevenue - previousRevenue) / previousRevenue * 100).toFixed(1);
        
        const recentOrders = this.data.orders.slice(-7).reduce((sum, item) => sum + item.value, 0);
        const previousOrders = this.data.orders.slice(-14, -7).reduce((sum, item) => sum + item.value, 0);
        const ordersTrend = ((recentOrders - previousOrders) / previousOrders * 100).toFixed(1);
        
        this.updateTrendDisplay('revenueTrend', revenueTrend);
        this.updateTrendDisplay('ordersTrend', ordersTrend);
    }
    
    updateTrendDisplay(elementId, trend) {
        const element = document.getElementById(elementId);
        const isPositive = parseFloat(trend) >= 0;
        
        element.className = `stat-trend ${isPositive ? 'positive' : 'negative'}`;
        element.innerHTML = `
            <i class="fas fa-arrow-${isPositive ? 'up' : 'down'}"></i>
            <span>${isPositive ? '+' : ''}${trend}%</span>
        `;
    }
    
    updateRealTimeMetrics() {
        const realTime = this.data.realTime;
        
        document.getElementById('onlineUsers').textContent = realTime.onlineUsers;
        document.getElementById('todayOrders').textContent = realTime.todayOrders;
        document.getElementById('realTimeRevenue').textContent = `¥${realTime.realTimeRevenue.toLocaleString()}`;
        document.getElementById('conversionRate').textContent = `${realTime.conversionRate}%`;
        
        // 模拟变化
        this.simulateRealTimeChanges();
    }
    
    simulateRealTimeChanges() {
        const changes = {
            onlineUsers: Math.floor(Math.random() * 21) - 10,
            todayOrders: Math.floor(Math.random() * 11) - 5,
            realTimeRevenue: Math.floor(Math.random() * 2001) - 1000,
            conversionRate: (Math.random() * 0.4 - 0.2).toFixed(1)
        };
        
        this.updateChangeDisplay('onlineUsersChange', changes.onlineUsers);
        this.updateChangeDisplay('todayOrdersChange', changes.todayOrders);
        this.updateChangeDisplay('revenueChange', changes.realTimeRevenue, '¥');
        this.updateChangeDisplay('conversionChange', changes.conversionRate, '', '%');
    }
    
    updateChangeDisplay(elementId, change, prefix = '', suffix = '') {
        const element = document.getElementById(elementId);
        const isPositive = parseFloat(change) >= 0;
        
        element.className = `metric-change ${isPositive ? 'positive' : 'negative'}`;
        element.textContent = `${isPositive ? '+' : ''}${prefix}${change}${suffix}`;
    }
    
    initializeCharts() {
        this.initializeRevenueChart();
        this.initializeOrderStatusChart();
        this.initializeCustomerSourceChart();
        this.initializeComparisonChart();
        this.initializePredictionChart();
        this.initializeGeoChart();
    }
    
    initializeRevenueChart() {
        const ctx = document.getElementById('revenueChart');
        if (!ctx) return;
        
        this.charts.revenue = new Chart(ctx, {
            type: 'line',
            data: {
                labels: this.data.revenue.map(item => item.date.toLocaleDateString('zh-CN')),
                datasets: [{
                    label: '收入',
                    data: this.data.revenue.map(item => item.value),
                    borderColor: '#6366f1',
                    backgroundColor: 'rgba(99, 102, 241, 0.1)',
                    fill: true,
                    tension: 0.4
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: { display: false }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            callback: function(value) {
                                return '¥' + value.toLocaleString();
                            }
                        }
                    }
                }
            }
        });
    }
    
    initializeOrderStatusChart() {
        const ctx = document.getElementById('orderStatusChart');
        if (!ctx) return;
        
        this.charts.orderStatus = new Chart(ctx, {
            type: 'doughnut',
            data: {
                labels: ['已完成', '处理中', '已取消', '待付款'],
                datasets: [{
                    data: [65, 20, 10, 5],
                    backgroundColor: ['#10b981', '#f59e0b', '#ef4444', '#6b7280']
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: { position: 'bottom' }
                }
            }
        });
    }
    
    initializeCustomerSourceChart() {
        const ctx = document.getElementById('customerSourceChart');
        if (!ctx) return;
        
        this.charts.customerSource = new Chart(ctx, {
            type: 'bar',
            data: {
                labels: ['搜索引擎', '社交媒体', '直接访问', '邮件营销', '推荐链接'],
                datasets: [{
                    label: '客户数量',
                    data: [320, 180, 150, 90, 60],
                    backgroundColor: ['#6366f1', '#10b981', '#f59e0b', '#ef4444', '#8b5cf6']
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: { display: false }
                }
            }
        });
    }
    
    initializeComparisonChart() {
        const ctx = document.getElementById('comparisonChart');
        if (!ctx) return;
        
        const currentData = this.data.revenue.slice(-7).map(item => item.value);
        const previousData = this.data.revenue.slice(-14, -7).map(item => item.value);
        
        this.charts.comparison = new Chart(ctx, {
            type: 'line',
            data: {
                labels: ['周一', '周二', '周三', '周四', '周五', '周六', '周日'],
                datasets: [{
                    label: '本周',
                    data: currentData,
                    borderColor: '#6366f1',
                    backgroundColor: 'rgba(99, 102, 241, 0.1)'
                }, {
                    label: '上周',
                    data: previousData,
                    borderColor: '#10b981',
                    backgroundColor: 'rgba(16, 185, 129, 0.1)'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: { position: 'top' }
                }
            }
        });
    }
    
    initializePredictionChart() {
        const ctx = document.getElementById('predictionChart');
        if (!ctx) return;
        
        const historicalData = this.data.revenue.slice(-14).map(item => item.value);
        const predictionData = this.generatePrediction(historicalData, 7);
        
        this.charts.prediction = new Chart(ctx, {
            type: 'line',
            data: {
                labels: [...Array(21).keys()].map(i => `第${i+1}天`),
                datasets: [{
                    label: '历史数据',
                    data: [...historicalData, ...Array(7).fill(null)],
                    borderColor: '#6366f1',
                    backgroundColor: 'rgba(99, 102, 241, 0.1)'
                }, {
                    label: '预测数据',
                    data: [...Array(14).fill(null), ...predictionData],
                    borderColor: '#f59e0b',
                    backgroundColor: 'rgba(245, 158, 11, 0.1)',
                    borderDash: [5, 5]
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: { position: 'top' }
                }
            }
        });
        
        this.updatePredictionSummary(predictionData);
    }
    
    generatePrediction(data, days) {
        // 简单的线性预测算法
        const trend = (data[data.length - 1] - data[0]) / data.length;
        const lastValue = data[data.length - 1];
        
        return Array.from({length: days}, (_, i) => 
            Math.max(0, Math.floor(lastValue + trend * (i + 1) + (Math.random() - 0.5) * 1000))
        );
    }
    
    updatePredictionSummary(predictionData) {
        const nextWeek = predictionData.slice(0, 7).reduce((sum, val) => sum + val, 0);
        const confidence = Math.floor(Math.random() * 20 + 75); // 75-95%
        
        document.getElementById('nextWeekPrediction').textContent = `¥${nextWeek.toLocaleString()}`;
        document.getElementById('nextMonthPrediction').textContent = `¥${(nextWeek * 4.3).toLocaleString()}`;
        document.getElementById('predictionConfidence').textContent = `${confidence}%`;
    }
    
    initializeGeoChart() {
        const ctx = document.getElementById('geoChart');
        if (!ctx) return;
        
        this.charts.geo = new Chart(ctx, {
            type: 'pie',
            data: {
                labels: Object.keys(this.data.geographic),
                datasets: [{
                    data: Object.values(this.data.geographic),
                    backgroundColor: ['#6366f1', '#10b981', '#f59e0b', '#ef4444']
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: { position: 'right' }
                }
            }
        });
    }
    
    startRealTimeUpdates() {
        if (this.updateInterval) {
            clearInterval(this.updateInterval);
        }
        
        this.updateInterval = setInterval(() => {
            if (this.realTimeEnabled) {
                this.updateRealTimeMetrics();
                this.updateOnlineStatus();
            }
        }, 5000); // 每5秒更新一次
    }
    
    updateOnlineStatus() {
        // 模拟在线用户数变化
        this.data.realTime.onlineUsers += Math.floor(Math.random() * 21) - 10;
        this.data.realTime.onlineUsers = Math.max(50, this.data.realTime.onlineUsers);
    }
    
    updateTrends() {
        // 更新趋势数据
        this.calculateTrends();
    }
}

// 全局分析器实例
let analyticsManager = null;

// 数据分析操作方法
function updateAnalytics() {
    if (!analyticsManager) return;

    const timeRange = document.getElementById('timeRangeSelect').value;
    analyticsManager.currentTimeRange = timeRange;
    analyticsManager.generateSampleData();
    analyticsManager.updateAllMetrics();

    // 重新初始化图表
    Object.values(analyticsManager.charts).forEach(chart => {
        if (chart) chart.destroy();
    });
    analyticsManager.initializeCharts();

    showNotification(`数据已更新为${timeRange}`, 'success');
}

function exportReport() {
    if (!analyticsManager) return;

    const reportData = {
        timeRange: analyticsManager.currentTimeRange,
        generatedAt: new Date().toISOString(),
        metrics: {
            totalRevenue: analyticsManager.data.revenue.reduce((sum, item) => sum + item.value, 0),
            totalOrders: analyticsManager.data.orders.reduce((sum, item) => sum + item.value, 0),
            totalUsers: analyticsManager.data.users.reduce((sum, item) => sum + item.value, 0)
        },
        revenueData: analyticsManager.data.revenue,
        ordersData: analyticsManager.data.orders,
        usersData: analyticsManager.data.users,
        geographic: analyticsManager.data.geographic
    };

    const jsonContent = JSON.stringify(reportData, null, 2);
    const blob = new Blob([jsonContent], { type: 'application/json' });
    const url = URL.createObjectURL(blob);

    const a = document.createElement('a');
    a.href = url;
    a.download = `analytics_report_${new Date().toISOString().slice(0, 10)}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);

    showNotification('报告已导出', 'success');
}

function refreshData() {
    if (!analyticsManager) return;

    analyticsManager.generateSampleData();
    analyticsManager.updateAllMetrics();

    // 重新初始化图表
    Object.values(analyticsManager.charts).forEach(chart => {
        if (chart) chart.destroy();
    });
    analyticsManager.initializeCharts();

    showNotification('数据已刷新', 'success');
}

function toggleRealTimeUpdates() {
    if (!analyticsManager) return;

    analyticsManager.realTimeEnabled = !analyticsManager.realTimeEnabled;

    const button = document.querySelector('button[onclick="toggleRealTimeUpdates()"]');
    const icon = document.getElementById('realTimeToggleIcon');
    const status = document.getElementById('realTimeStatus');

    if (analyticsManager.realTimeEnabled) {
        button.innerHTML = '<i class="fas fa-pause" id="realTimeToggleIcon"></i> 暂停';
        status.innerHTML = '<span class="status-dot active"></span> 实时更新';
        analyticsManager.startRealTimeUpdates();
        showNotification('实时更新已启用', 'success');
    } else {
        button.innerHTML = '<i class="fas fa-play" id="realTimeToggleIcon"></i> 启动';
        status.innerHTML = '<span class="status-dot inactive"></span> 已暂停';
        showNotification('实时更新已暂停', 'warning');
    }
}

function updateComparison() {
    if (!analyticsManager) return;

    const period = document.getElementById('comparisonPeriod').value;
    analyticsManager.comparisonPeriod = period;

    // 重新生成对比数据
    if (analyticsManager.charts.comparison) {
        analyticsManager.charts.comparison.destroy();
        analyticsManager.initializeComparisonChart();
    }

    showNotification(`对比周期已更新`, 'info');
}

function updatePrediction() {
    if (!analyticsManager) return;

    const metric = document.getElementById('predictionMetric').value;
    analyticsManager.predictionMetric = metric;

    // 重新生成预测数据
    if (analyticsManager.charts.prediction) {
        analyticsManager.charts.prediction.destroy();
        analyticsManager.initializePredictionChart();
    }

    showNotification(`预测指标已更新为${metric}`, 'info');
}

function toggleMapView() {
    showNotification('地图视图功能开发中...', 'info');
}

function exportGeoData() {
    if (!analyticsManager) return;

    const geoData = analyticsManager.data.geographic;
    const csvContent = Object.entries(geoData)
        .map(([region, percentage]) => `"${region}","${percentage}%"`)
        .join('\n');

    const blob = new Blob(['\ufeff地区,占比\n' + csvContent], { type: 'text/csv;charset=utf-8;' });
    const url = URL.createObjectURL(blob);

    const a = document.createElement('a');
    a.href = url;
    a.download = `geo_analysis_${new Date().toISOString().slice(0, 10)}.csv`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);

    showNotification('地理数据已导出', 'success');
}

function toggleTableView() {
    const table = document.getElementById('analyticsTable');
    const button = event.target.closest('button');

    if (table.style.display === 'none') {
        table.style.display = 'table';
        button.innerHTML = '<i class="fas fa-chart-bar"></i> 图表视图';
        populateAnalyticsTable();
    } else {
        table.style.display = 'none';
        button.innerHTML = '<i class="fas fa-table"></i> 表格视图';
    }
}

function populateAnalyticsTable() {
    if (!analyticsManager) return;

    const tbody = document.querySelector('#analyticsTable tbody');
    if (!tbody) return;

    const data = analyticsManager.data.revenue.slice(-10); // 显示最近10天

    tbody.innerHTML = data.map(item => `
        <tr>
            <td>${item.date.toLocaleDateString('zh-CN')}</td>
            <td>¥${item.value.toLocaleString()}</td>
            <td>${analyticsManager.data.orders.find(o =>
                o.date.toDateString() === item.date.toDateString()
            )?.value || 0}</td>
            <td>${analyticsManager.data.users.find(u =>
                u.date.toDateString() === item.date.toDateString()
            )?.value || 0}</td>
            <td>${analyticsManager.data.conversion.find(c =>
                c.date.toDateString() === item.date.toDateString()
            )?.value || 0}%</td>
        </tr>
    `).join('');
}

// 初始化数据分析
function initializeAnalytics() {
    analyticsManager = new EnhancedAnalytics();
    console.log('✅ 增强数据分析器已初始化');
}
