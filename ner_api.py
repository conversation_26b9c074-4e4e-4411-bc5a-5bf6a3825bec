#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
NER数据生成API服务
提供RESTful API接口用于生成和管理NER训练数据
"""

from flask import Flask, request, jsonify, send_file
from flask_cors import CORS
import json
import os
import tempfile
from datetime import datetime
from ner_data_generator import NERDataGenerator

app = Flask(__name__)
CORS(app)  # 允许跨域请求

# 全局变量存储当前数据集
current_dataset = []
current_stats = {}

@app.route('/api/ner/generate', methods=['POST'])
def generate_ner_data():
    """生成NER训练数据"""
    try:
        data = request.get_json()
        
        # 获取参数
        sample_count = data.get('sample_count', 1000)
        entity_types = data.get('entity_types', {
            'person': True,
            'location': True,
            'organization': True,
            'time': True,
            'number': True
        })
        
        # 验证参数
        if not isinstance(sample_count, int) or sample_count < 100 or sample_count > 50000:
            return jsonify({'error': '样本数量必须在100-50000之间'}), 400
        
        # 创建生成器
        generator = NERDataGenerator()
        
        # 根据配置修改生成器（这里可以扩展以支持实体类型选择）
        
        # 生成数据集
        global current_dataset, current_stats
        current_dataset = generator.generate_dataset(sample_count)
        current_stats = generator.generate_statistics(current_dataset)
        
        # 返回统计信息
        return jsonify({
            'success': True,
            'message': f'成功生成 {sample_count} 条NER训练数据',
            'stats': current_stats,
            'sample_preview': current_dataset[:5] if current_dataset else []
        })
        
    except Exception as e:
        return jsonify({'error': f'生成数据时发生错误: {str(e)}'}), 500

@app.route('/api/ner/stats', methods=['GET'])
def get_ner_stats():
    """获取当前数据集统计信息"""
    global current_stats
    if not current_stats:
        return jsonify({'error': '暂无数据，请先生成数据集'}), 404
    
    return jsonify({
        'success': True,
        'stats': current_stats
    })

@app.route('/api/ner/preview', methods=['GET'])
def get_ner_preview():
    """获取数据预览"""
    global current_dataset
    if not current_dataset:
        return jsonify({'error': '暂无数据，请先生成数据集'}), 404
    
    # 获取分页参数
    page = request.args.get('page', 1, type=int)
    per_page = request.args.get('per_page', 10, type=int)
    
    start = (page - 1) * per_page
    end = start + per_page
    
    preview_data = current_dataset[start:end]
    
    return jsonify({
        'success': True,
        'data': preview_data,
        'total': len(current_dataset),
        'page': page,
        'per_page': per_page,
        'total_pages': (len(current_dataset) + per_page - 1) // per_page
    })

@app.route('/api/ner/download', methods=['GET'])
def download_ner_data():
    """下载NER数据集"""
    global current_dataset
    if not current_dataset:
        return jsonify({'error': '暂无数据，请先生成数据集'}), 404
    
    try:
        # 获取输出格式
        output_format = request.args.get('format', 'json')
        
        # 创建临时文件
        temp_dir = tempfile.gettempdir()
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        
        if output_format == 'json':
            filename = f'ner_data_{timestamp}.json'
            filepath = os.path.join(temp_dir, filename)
            
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(current_dataset, f, ensure_ascii=False, indent=2)
                
        elif output_format == 'conll':
            filename = f'ner_data_{timestamp}.conll'
            filepath = os.path.join(temp_dir, filename)
            
            with open(filepath, 'w', encoding='utf-8') as f:
                for sample in current_dataset:
                    tokens = sample['tokens']
                    labels = sample['labels']
                    
                    for token, label in zip(tokens, labels):
                        f.write(f'{token}\t{label}\n')
                    f.write('\n')  # 句子间空行
                    
        elif output_format == 'bio':
            filename = f'ner_data_{timestamp}.txt'
            filepath = os.path.join(temp_dir, filename)
            
            with open(filepath, 'w', encoding='utf-8') as f:
                for sample in current_dataset:
                    f.write(f"Text: {sample['text']}\n")
                    f.write(f"Labels: {' '.join(sample['labels'])}\n")
                    f.write('-' * 50 + '\n')
        else:
            return jsonify({'error': '不支持的输出格式'}), 400
        
        return send_file(filepath, as_attachment=True, download_name=filename)
        
    except Exception as e:
        return jsonify({'error': f'下载文件时发生错误: {str(e)}'}), 500

@app.route('/api/ner/clear', methods=['DELETE'])
def clear_ner_data():
    """清空当前数据集"""
    global current_dataset, current_stats
    current_dataset = []
    current_stats = {}
    
    return jsonify({
        'success': True,
        'message': '数据集已清空'
    })

@app.route('/api/ner/sample', methods=['GET'])
def get_sample_data():
    """获取单个样本数据（用于详细查看）"""
    global current_dataset
    if not current_dataset:
        return jsonify({'error': '暂无数据，请先生成数据集'}), 404
    
    sample_id = request.args.get('id', 0, type=int)
    
    if sample_id < 0 or sample_id >= len(current_dataset):
        return jsonify({'error': '样本ID无效'}), 400
    
    sample = current_dataset[sample_id]
    
    # 添加实体标注信息
    entities = []
    tokens = sample['tokens']
    labels = sample['labels']
    
    current_entity = None
    for i, (token, label) in enumerate(zip(tokens, labels)):
        if label.startswith('B-'):
            if current_entity:
                entities.append(current_entity)
            current_entity = {
                'text': token,
                'label': label[2:],
                'start': i,
                'end': i
            }
        elif label.startswith('I-') and current_entity:
            current_entity['text'] += token
            current_entity['end'] = i
        else:
            if current_entity:
                entities.append(current_entity)
                current_entity = None
    
    if current_entity:
        entities.append(current_entity)
    
    return jsonify({
        'success': True,
        'sample': sample,
        'entities': entities
    })

@app.route('/api/health', methods=['GET'])
def health_check():
    """健康检查接口"""
    return jsonify({
        'status': 'healthy',
        'service': 'NER Data Generator API',
        'version': '1.0.0',
        'timestamp': datetime.now().isoformat()
    })

if __name__ == '__main__':
    print("启动NER数据生成API服务...")
    print("API文档:")
    print("  POST /api/ner/generate - 生成NER数据")
    print("  GET  /api/ner/stats - 获取统计信息")
    print("  GET  /api/ner/preview - 获取数据预览")
    print("  GET  /api/ner/download - 下载数据集")
    print("  DELETE /api/ner/clear - 清空数据")
    print("  GET  /api/ner/sample - 获取单个样本")
    print("  GET  /api/health - 健康检查")
    print()
    
    app.run(host='0.0.0.0', port=5000, debug=True)
