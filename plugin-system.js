// 插件扩展系统
class PluginSystem {
    constructor() {
        this.plugins = new Map();
        this.hooks = new Map();
        this.pluginStore = [];
        this.installedPlugins = [];
        this.pluginAPI = {};
        this.sandboxes = new Map();
        
        this.initializePluginSystem();
        this.setupPluginAPI();
        this.loadInstalledPlugins();
    }

    initializePluginSystem() {
        this.createPluginInterface();
        this.bindPluginEvents();
        this.setupHooks();
        this.loadPluginStore();
    }

    createPluginInterface() {
        const pluginPanel = document.createElement('div');
        pluginPanel.id = 'pluginPanel';
        pluginPanel.className = 'plugin-panel';
        pluginPanel.innerHTML = `
            <div class="plugin-header">
                <h3>
                    <i class="fas fa-puzzle-piece"></i>
                    插件管理
                </h3>
                <div class="plugin-controls">
                    <button class="btn-secondary" onclick="pluginSystem.installFromFile()">
                        <i class="fas fa-upload"></i>
                        安装插件
                    </button>
                    <button class="btn-primary" onclick="pluginSystem.createPlugin()">
                        <i class="fas fa-plus"></i>
                        创建插件
                    </button>
                    <button class="btn-icon" onclick="pluginSystem.closePluginPanel()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            </div>
            
            <div class="plugin-body">
                <div class="plugin-tabs">
                    <button class="tab-btn active" data-tab="installed">
                        <i class="fas fa-check-circle"></i>
                        已安装
                    </button>
                    <button class="tab-btn" data-tab="store">
                        <i class="fas fa-store"></i>
                        插件商店
                    </button>
                    <button class="tab-btn" data-tab="develop">
                        <i class="fas fa-code"></i>
                        开发工具
                    </button>
                    <button class="tab-btn" data-tab="settings">
                        <i class="fas fa-cog"></i>
                        插件设置
                    </button>
                </div>
                
                <div class="plugin-content">
                    <!-- 已安装插件 -->
                    <div class="tab-content active" id="installedTab">
                        <div class="installed-section">
                            <div class="section-header">
                                <h4>已安装的插件</h4>
                                <div class="plugin-filters">
                                    <select id="pluginStatusFilter">
                                        <option value="all">所有状态</option>
                                        <option value="enabled">已启用</option>
                                        <option value="disabled">已禁用</option>
                                    </select>
                                    <select id="pluginCategoryFilter">
                                        <option value="all">所有分类</option>
                                        <option value="ui">界面增强</option>
                                        <option value="data">数据处理</option>
                                        <option value="workflow">工作流</option>
                                        <option value="integration">集成工具</option>
                                    </select>
                                </div>
                            </div>
                            
                            <div class="installed-plugins" id="installedPlugins">
                                <!-- 已安装插件列表 -->
                            </div>
                        </div>
                    </div>
                    
                    <!-- 插件商店 -->
                    <div class="tab-content" id="storeTab">
                        <div class="store-section">
                            <div class="store-header">
                                <div class="store-search">
                                    <input type="text" id="storeSearchInput" placeholder="搜索插件...">
                                    <button class="search-btn">
                                        <i class="fas fa-search"></i>
                                    </button>
                                </div>
                                <div class="store-filters">
                                    <select id="storeCategoryFilter">
                                        <option value="all">所有分类</option>
                                        <option value="ui">界面增强</option>
                                        <option value="data">数据处理</option>
                                        <option value="workflow">工作流</option>
                                        <option value="integration">集成工具</option>
                                    </select>
                                    <select id="storeSortFilter">
                                        <option value="popular">最受欢迎</option>
                                        <option value="newest">最新发布</option>
                                        <option value="rating">评分最高</option>
                                        <option value="downloads">下载最多</option>
                                    </select>
                                </div>
                            </div>
                            
                            <div class="store-plugins" id="storePlugins">
                                <!-- 插件商店列表 -->
                            </div>
                        </div>
                    </div>
                    
                    <!-- 开发工具 -->
                    <div class="tab-content" id="developTab">
                        <div class="develop-section">
                            <h4>插件开发工具</h4>
                            
                            <div class="develop-tools">
                                <div class="tool-card">
                                    <div class="tool-icon">
                                        <i class="fas fa-file-code"></i>
                                    </div>
                                    <div class="tool-content">
                                        <h5>插件模板生成器</h5>
                                        <p>快速生成插件基础结构</p>
                                        <button class="btn-primary" onclick="pluginSystem.generateTemplate()">
                                            生成模板
                                        </button>
                                    </div>
                                </div>
                                
                                <div class="tool-card">
                                    <div class="tool-icon">
                                        <i class="fas fa-bug"></i>
                                    </div>
                                    <div class="tool-content">
                                        <h5>插件调试器</h5>
                                        <p>调试和测试插件功能</p>
                                        <button class="btn-primary" onclick="pluginSystem.openDebugger()">
                                            打开调试器
                                        </button>
                                    </div>
                                </div>
                                
                                <div class="tool-card">
                                    <div class="tool-icon">
                                        <i class="fas fa-book"></i>
                                    </div>
                                    <div class="tool-content">
                                        <h5>API文档</h5>
                                        <p>查看插件开发API文档</p>
                                        <button class="btn-primary" onclick="pluginSystem.openAPIDoc()">
                                            查看文档
                                        </button>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="plugin-editor">
                                <h4>插件编辑器</h4>
                                <div class="editor-container">
                                    <div class="editor-tabs">
                                        <button class="editor-tab active" data-file="manifest.json">manifest.json</button>
                                        <button class="editor-tab" data-file="main.js">main.js</button>
                                        <button class="editor-tab" data-file="style.css">style.css</button>
                                    </div>
                                    <textarea id="pluginEditor" placeholder="在此编写插件代码..."></textarea>
                                    <div class="editor-actions">
                                        <button class="btn-secondary" onclick="pluginSystem.validatePlugin()">
                                            <i class="fas fa-check"></i>
                                            验证
                                        </button>
                                        <button class="btn-primary" onclick="pluginSystem.testPlugin()">
                                            <i class="fas fa-play"></i>
                                            测试
                                        </button>
                                        <button class="btn-primary" onclick="pluginSystem.savePlugin()">
                                            <i class="fas fa-save"></i>
                                            保存
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 插件设置 -->
                    <div class="tab-content" id="settingsTab">
                        <div class="settings-section">
                            <h4>插件系统设置</h4>
                            
                            <div class="settings-form">
                                <div class="setting-group">
                                    <label class="checkbox-label">
                                        <input type="checkbox" id="enablePluginSandbox" checked>
                                        <span class="checkmark"></span>
                                        启用插件沙箱 (推荐)
                                    </label>
                                    <p class="setting-desc">在隔离环境中运行插件，提高安全性</p>
                                </div>
                                
                                <div class="setting-group">
                                    <label class="checkbox-label">
                                        <input type="checkbox" id="autoUpdatePlugins" checked>
                                        <span class="checkmark"></span>
                                        自动更新插件
                                    </label>
                                    <p class="setting-desc">自动检查并安装插件更新</p>
                                </div>
                                
                                <div class="setting-group">
                                    <label class="checkbox-label">
                                        <input type="checkbox" id="allowDevPlugins">
                                        <span class="checkmark"></span>
                                        允许开发者插件
                                    </label>
                                    <p class="setting-desc">允许安装未签名的开发者插件</p>
                                </div>
                                
                                <div class="setting-group">
                                    <label>插件存储位置</label>
                                    <select id="pluginStorageLocation">
                                        <option value="local">本地存储</option>
                                        <option value="cloud">云端同步</option>
                                    </select>
                                </div>
                                
                                <div class="setting-group">
                                    <label>最大插件数量</label>
                                    <select id="maxPluginCount">
                                        <option value="10">10个</option>
                                        <option value="20" selected>20个</option>
                                        <option value="50">50个</option>
                                        <option value="unlimited">无限制</option>
                                    </select>
                                </div>
                            </div>
                            
                            <div class="settings-actions">
                                <button class="btn-secondary" onclick="pluginSystem.resetPluginSettings()">
                                    <i class="fas fa-undo"></i>
                                    重置设置
                                </button>
                                <button class="btn-primary" onclick="pluginSystem.savePluginSettings()">
                                    <i class="fas fa-save"></i>
                                    保存设置
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
        
        document.body.appendChild(pluginPanel);
    }

    setupPluginAPI() {
        // 为插件提供的API接口
        this.pluginAPI = {
            // 系统API
            system: {
                getVersion: () => '2.0.0',
                getUser: () => window.permissionManager?.currentUser || null,
                showNotification: (message, type) => {
                    if (window.showNotification) {
                        showNotification(message, type);
                    }
                }
            },
            
            // UI API
            ui: {
                addMenuItem: (item) => this.addMenuItem(item),
                addToolbarButton: (button) => this.addToolbarButton(button),
                createModal: (config) => this.createModal(config),
                addCSS: (css) => this.addCSS(css)
            },
            
            // 数据API
            data: {
                get: (key) => localStorage.getItem(`plugin_${key}`),
                set: (key, value) => localStorage.setItem(`plugin_${key}`, value),
                remove: (key) => localStorage.removeItem(`plugin_${key}`)
            },
            
            // 事件API
            events: {
                on: (event, callback) => this.addEventListener(event, callback),
                off: (event, callback) => this.removeEventListener(event, callback),
                emit: (event, data) => this.emitEvent(event, data)
            },
            
            // 网络API
            network: {
                request: (config) => this.makeRequest(config),
                upload: (file, config) => this.uploadFile(file, config)
            }
        };
    }

    setupHooks() {
        // 设置系统钩子
        this.hooks.set('page_load', []);
        this.hooks.set('user_login', []);
        this.hooks.set('data_save', []);
        this.hooks.set('menu_render', []);
        this.hooks.set('notification_show', []);
    }

    loadPluginStore() {
        // 模拟插件商店数据
        this.pluginStore = [
            {
                id: 'theme-switcher',
                name: '主题切换器',
                description: '快速切换系统主题的便捷工具',
                version: '1.2.0',
                author: '系统开发团队',
                category: 'ui',
                rating: 4.8,
                downloads: 1250,
                price: 0,
                screenshots: ['screenshot1.jpg', 'screenshot2.jpg'],
                features: ['快速主题切换', '自定义主题', '定时切换'],
                size: '45KB',
                lastUpdated: '2024-01-15'
            },
            {
                id: 'data-exporter',
                name: '高级数据导出器',
                description: '支持多种格式的数据导出工具',
                version: '2.1.0',
                author: '数据工具开发者',
                category: 'data',
                rating: 4.6,
                downloads: 890,
                price: 29.99,
                screenshots: ['export1.jpg', 'export2.jpg'],
                features: ['多格式导出', '批量处理', '定时导出'],
                size: '128KB',
                lastUpdated: '2024-01-10'
            },
            {
                id: 'workflow-templates',
                name: '工作流模板库',
                description: '丰富的工作流模板集合',
                version: '1.0.5',
                author: '工作流专家',
                category: 'workflow',
                rating: 4.9,
                downloads: 2100,
                price: 0,
                screenshots: ['workflow1.jpg', 'workflow2.jpg'],
                features: ['50+模板', '一键导入', '自定义修改'],
                size: '256KB',
                lastUpdated: '2024-01-20'
            }
        ];
        
        this.renderStorePlugins();
    }

    loadInstalledPlugins() {
        // 从localStorage加载已安装的插件
        try {
            const stored = localStorage.getItem('installedPlugins');
            if (stored) {
                this.installedPlugins = JSON.parse(stored);
                this.installedPlugins.forEach(plugin => {
                    if (plugin.enabled) {
                        this.loadPlugin(plugin);
                    }
                });
            }
        } catch (error) {
            console.error('加载插件失败:', error);
        }
        
        this.renderInstalledPlugins();
    }

    bindPluginEvents() {
        // 标签切换
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('tab-btn') && e.target.closest('.plugin-panel')) {
                this.switchPluginTab(e.target);
            }
        });
        
        // 插件筛选
        document.getElementById('pluginStatusFilter')?.addEventListener('change', () => {
            this.filterInstalledPlugins();
        });
        
        document.getElementById('pluginCategoryFilter')?.addEventListener('change', () => {
            this.filterInstalledPlugins();
        });
        
        // 商店筛选
        document.getElementById('storeCategoryFilter')?.addEventListener('change', () => {
            this.filterStorePlugins();
        });
        
        document.getElementById('storeSortFilter')?.addEventListener('change', () => {
            this.sortStorePlugins();
        });
        
        document.getElementById('storeSearchInput')?.addEventListener('input', () => {
            this.searchStorePlugins();
        });
    }

    switchPluginTab(tabBtn) {
        const container = tabBtn.closest('.plugin-panel');
        const tabName = tabBtn.dataset.tab;
        
        // 更新标签状态
        container.querySelectorAll('.tab-btn').forEach(btn => btn.classList.remove('active'));
        container.querySelectorAll('.tab-content').forEach(content => content.classList.remove('active'));
        
        tabBtn.classList.add('active');
        container.querySelector(`#${tabName}Tab`).classList.add('active');
        
        // 加载对应内容
        this.loadTabContent(tabName);
    }

    loadTabContent(tabName) {
        switch (tabName) {
            case 'installed':
                this.renderInstalledPlugins();
                break;
            case 'store':
                this.renderStorePlugins();
                break;
            case 'develop':
                this.loadDevelopTools();
                break;
            case 'settings':
                this.loadPluginSettings();
                break;
        }
    }

    renderInstalledPlugins() {
        const container = document.getElementById('installedPlugins');
        if (!container) return;
        
        if (this.installedPlugins.length === 0) {
            container.innerHTML = `
                <div class="empty-state">
                    <i class="fas fa-puzzle-piece"></i>
                    <p>暂未安装任何插件</p>
                    <button class="btn-primary" onclick="pluginSystem.switchPluginTab(document.querySelector('[data-tab=store]'))">
                        浏览插件商店
                    </button>
                </div>
            `;
            return;
        }
        
        container.innerHTML = this.installedPlugins.map(plugin => `
            <div class="plugin-card installed ${plugin.enabled ? 'enabled' : 'disabled'}">
                <div class="plugin-icon">
                    <i class="fas fa-${this.getPluginIcon(plugin.category)}"></i>
                </div>
                <div class="plugin-info">
                    <div class="plugin-header">
                        <h5>${plugin.name}</h5>
                        <span class="plugin-version">v${plugin.version}</span>
                    </div>
                    <div class="plugin-description">${plugin.description}</div>
                    <div class="plugin-meta">
                        <span class="plugin-author">作者: ${plugin.author}</span>
                        <span class="plugin-category">${this.getCategoryName(plugin.category)}</span>
                    </div>
                </div>
                <div class="plugin-actions">
                    <div class="plugin-toggle">
                        <label class="switch">
                            <input type="checkbox" ${plugin.enabled ? 'checked' : ''} 
                                   onchange="pluginSystem.togglePlugin('${plugin.id}', this.checked)">
                            <span class="slider"></span>
                        </label>
                    </div>
                    <button class="btn-icon" onclick="pluginSystem.configurePlugin('${plugin.id}')" title="配置">
                        <i class="fas fa-cog"></i>
                    </button>
                    <button class="btn-icon" onclick="pluginSystem.uninstallPlugin('${plugin.id}')" title="卸载">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </div>
        `).join('');
    }

    renderStorePlugins() {
        const container = document.getElementById('storePlugins');
        if (!container) return;
        
        container.innerHTML = this.pluginStore.map(plugin => `
            <div class="plugin-card store">
                <div class="plugin-icon">
                    <i class="fas fa-${this.getPluginIcon(plugin.category)}"></i>
                </div>
                <div class="plugin-info">
                    <div class="plugin-header">
                        <h5>${plugin.name}</h5>
                        <div class="plugin-rating">
                            ${this.renderStars(plugin.rating)}
                            <span class="rating-text">${plugin.rating}</span>
                        </div>
                    </div>
                    <div class="plugin-description">${plugin.description}</div>
                    <div class="plugin-meta">
                        <span class="plugin-author">作者: ${plugin.author}</span>
                        <span class="plugin-downloads">${plugin.downloads} 下载</span>
                        <span class="plugin-size">${plugin.size}</span>
                    </div>
                    <div class="plugin-features">
                        ${plugin.features.slice(0, 3).map(feature => 
                            `<span class="feature-tag">${feature}</span>`
                        ).join('')}
                    </div>
                </div>
                <div class="plugin-actions">
                    <div class="plugin-price">
                        ${plugin.price === 0 ? '免费' : `¥${plugin.price}`}
                    </div>
                    <button class="btn-primary" onclick="pluginSystem.installPlugin('${plugin.id}')">
                        ${this.isPluginInstalled(plugin.id) ? '已安装' : '安装'}
                    </button>
                </div>
            </div>
        `).join('');
    }

    getPluginIcon(category) {
        const icons = {
            'ui': 'paint-brush',
            'data': 'database',
            'workflow': 'project-diagram',
            'integration': 'plug'
        };
        return icons[category] || 'puzzle-piece';
    }

    getCategoryName(category) {
        const names = {
            'ui': '界面增强',
            'data': '数据处理',
            'workflow': '工作流',
            'integration': '集成工具'
        };
        return names[category] || category;
    }

    renderStars(rating) {
        const fullStars = Math.floor(rating);
        const hasHalfStar = rating % 1 >= 0.5;
        let stars = '';
        
        for (let i = 0; i < fullStars; i++) {
            stars += '<i class="fas fa-star"></i>';
        }
        
        if (hasHalfStar) {
            stars += '<i class="fas fa-star-half-alt"></i>';
        }
        
        const emptyStars = 5 - fullStars - (hasHalfStar ? 1 : 0);
        for (let i = 0; i < emptyStars; i++) {
            stars += '<i class="far fa-star"></i>';
        }
        
        return stars;
    }

    isPluginInstalled(pluginId) {
        return this.installedPlugins.some(plugin => plugin.id === pluginId);
    }

    async installPlugin(pluginId) {
        const storePlugin = this.pluginStore.find(p => p.id === pluginId);
        if (!storePlugin) return;
        
        if (this.isPluginInstalled(pluginId)) {
            if (window.showNotification) {
                showNotification('插件已安装', 'info');
            }
            return;
        }
        
        try {
            // 模拟安装过程
            const plugin = {
                ...storePlugin,
                enabled: true,
                installedAt: new Date(),
                config: {}
            };
            
            this.installedPlugins.push(plugin);
            this.saveInstalledPlugins();
            
            // 加载插件
            await this.loadPlugin(plugin);
            
            this.renderInstalledPlugins();
            this.renderStorePlugins();
            
            if (window.showNotification) {
                showNotification(`插件 "${plugin.name}" 安装成功`, 'success');
            }
            
        } catch (error) {
            if (window.showNotification) {
                showNotification('插件安装失败: ' + error.message, 'error');
            }
        }
    }

    async loadPlugin(plugin) {
        try {
            // 创建插件沙箱
            const sandbox = this.createPluginSandbox(plugin);
            this.sandboxes.set(plugin.id, sandbox);
            
            // 模拟插件代码执行
            await this.executePluginCode(plugin, sandbox);
            
            this.plugins.set(plugin.id, plugin);
            
        } catch (error) {
            console.error(`加载插件 ${plugin.id} 失败:`, error);
            throw error;
        }
    }

    createPluginSandbox(plugin) {
        // 创建插件运行沙箱
        const sandbox = {
            plugin: {
                id: plugin.id,
                name: plugin.name,
                version: plugin.version
            },
            api: this.pluginAPI,
            console: {
                log: (...args) => console.log(`[${plugin.name}]`, ...args),
                error: (...args) => console.error(`[${plugin.name}]`, ...args),
                warn: (...args) => console.warn(`[${plugin.name}]`, ...args)
            }
        };
        
        return sandbox;
    }

    async executePluginCode(plugin, sandbox) {
        // 模拟插件代码执行
        // 实际实现中会加载和执行插件的JavaScript代码
        
        // 示例插件功能
        switch (plugin.id) {
            case 'theme-switcher':
                this.loadThemeSwitcherPlugin(sandbox);
                break;
            case 'data-exporter':
                this.loadDataExporterPlugin(sandbox);
                break;
            case 'workflow-templates':
                this.loadWorkflowTemplatesPlugin(sandbox);
                break;
        }
    }

    loadThemeSwitcherPlugin(sandbox) {
        // 主题切换器插件示例
        sandbox.api.ui.addToolbarButton({
            id: 'theme-switcher',
            icon: 'fas fa-palette',
            title: '快速切换主题',
            onClick: () => {
                if (window.themeCustomizer) {
                    themeCustomizer.showCustomizer();
                }
            }
        });
    }

    loadDataExporterPlugin(sandbox) {
        // 数据导出器插件示例
        sandbox.api.ui.addMenuItem({
            id: 'advanced-export',
            text: '高级导出',
            icon: 'fas fa-file-export',
            onClick: () => {
                sandbox.api.system.showNotification('高级导出功能已启动', 'info');
            }
        });
    }

    loadWorkflowTemplatesPlugin(sandbox) {
        // 工作流模板插件示例
        sandbox.api.events.on('workflow_create', (data) => {
            sandbox.console.log('工作流创建事件:', data);
        });
    }

    togglePlugin(pluginId, enabled) {
        const plugin = this.installedPlugins.find(p => p.id === pluginId);
        if (!plugin) return;
        
        plugin.enabled = enabled;
        
        if (enabled) {
            this.loadPlugin(plugin);
        } else {
            this.unloadPlugin(pluginId);
        }
        
        this.saveInstalledPlugins();
        this.renderInstalledPlugins();
        
        if (window.showNotification) {
            showNotification(
                `插件 "${plugin.name}" 已${enabled ? '启用' : '禁用'}`, 
                'info'
            );
        }
    }

    unloadPlugin(pluginId) {
        // 卸载插件
        const sandbox = this.sandboxes.get(pluginId);
        if (sandbox) {
            // 清理插件添加的UI元素
            this.cleanupPluginUI(pluginId);
            this.sandboxes.delete(pluginId);
        }
        
        this.plugins.delete(pluginId);
    }

    cleanupPluginUI(pluginId) {
        // 清理插件添加的UI元素
        document.querySelectorAll(`[data-plugin="${pluginId}"]`).forEach(element => {
            element.remove();
        });
    }

    uninstallPlugin(pluginId) {
        if (confirm('确定要卸载这个插件吗？')) {
            this.unloadPlugin(pluginId);
            this.installedPlugins = this.installedPlugins.filter(p => p.id !== pluginId);
            this.saveInstalledPlugins();
            this.renderInstalledPlugins();
            
            if (window.showNotification) {
                showNotification('插件已卸载', 'success');
            }
        }
    }

    // API实现方法
    addMenuItem(item) {
        // 添加菜单项到系统菜单
        console.log('添加菜单项:', item);
    }

    addToolbarButton(button) {
        // 添加工具栏按钮
        const toolbar = document.querySelector('.header-actions');
        if (toolbar) {
            const btn = document.createElement('button');
            btn.className = 'action-btn';
            btn.title = button.title;
            btn.innerHTML = `<i class="${button.icon}"></i>`;
            btn.onclick = button.onClick;
            btn.dataset.plugin = button.id;
            toolbar.appendChild(btn);
        }
    }

    createModal(config) {
        // 创建模态框
        const modal = document.createElement('div');
        modal.className = 'modal plugin-modal';
        modal.innerHTML = `
            <div class="modal-content">
                <div class="modal-header">
                    <h3>${config.title}</h3>
                    <button class="modal-close" onclick="this.closest('.modal').remove()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="modal-body">
                    ${config.content}
                </div>
                <div class="modal-footer">
                    ${config.buttons || ''}
                </div>
            </div>
        `;
        
        document.body.appendChild(modal);
        modal.style.display = 'flex';
        
        return modal;
    }

    addCSS(css) {
        // 添加CSS样式
        const style = document.createElement('style');
        style.textContent = css;
        document.head.appendChild(style);
    }

    addEventListener(event, callback) {
        if (!this.hooks.has(event)) {
            this.hooks.set(event, []);
        }
        this.hooks.get(event).push(callback);
    }

    removeEventListener(event, callback) {
        if (this.hooks.has(event)) {
            const callbacks = this.hooks.get(event);
            const index = callbacks.indexOf(callback);
            if (index > -1) {
                callbacks.splice(index, 1);
            }
        }
    }

    emitEvent(event, data) {
        if (this.hooks.has(event)) {
            this.hooks.get(event).forEach(callback => {
                try {
                    callback(data);
                } catch (error) {
                    console.error('插件事件处理错误:', error);
                }
            });
        }
    }

    async makeRequest(config) {
        // 网络请求
        return fetch(config.url, {
            method: config.method || 'GET',
            headers: config.headers || {},
            body: config.data ? JSON.stringify(config.data) : undefined
        });
    }

    async uploadFile(file, config) {
        // 文件上传
        const formData = new FormData();
        formData.append('file', file);
        
        return fetch(config.url, {
            method: 'POST',
            headers: config.headers || {},
            body: formData
        });
    }

    // 面板控制方法
    showPluginPanel() {
        const panel = document.getElementById('pluginPanel');
        if (panel) {
            panel.classList.add('show');
        }
    }

    closePluginPanel() {
        const panel = document.getElementById('pluginPanel');
        if (panel) {
            panel.classList.remove('show');
        }
    }

    // 存储方法
    saveInstalledPlugins() {
        try {
            localStorage.setItem('installedPlugins', JSON.stringify(this.installedPlugins));
        } catch (error) {
            console.error('保存插件列表失败:', error);
        }
    }

    // 其他方法的占位符
    installFromFile() {
        const input = document.createElement('input');
        input.type = 'file';
        input.accept = '.zip,.js';
        input.onchange = (e) => {
            const file = e.target.files[0];
            if (file) {
                console.log('安装插件文件:', file.name);
                // 实现文件安装逻辑
            }
        };
        input.click();
    }

    createPlugin() {
        this.switchPluginTab(document.querySelector('[data-tab="develop"]'));
        this.showPluginPanel();
    }

    configurePlugin(pluginId) {
        console.log('配置插件:', pluginId);
    }

    filterInstalledPlugins() {
        console.log('筛选已安装插件');
    }

    filterStorePlugins() {
        console.log('筛选商店插件');
    }

    sortStorePlugins() {
        console.log('排序商店插件');
    }

    searchStorePlugins() {
        console.log('搜索商店插件');
    }

    loadDevelopTools() {
        console.log('加载开发工具');
    }

    loadPluginSettings() {
        console.log('加载插件设置');
    }

    generateTemplate() {
        console.log('生成插件模板');
    }

    openDebugger() {
        console.log('打开插件调试器');
    }

    openAPIDoc() {
        console.log('打开API文档');
    }

    validatePlugin() {
        console.log('验证插件');
    }

    testPlugin() {
        console.log('测试插件');
    }

    savePlugin() {
        console.log('保存插件');
    }

    resetPluginSettings() {
        console.log('重置插件设置');
    }

    savePluginSettings() {
        console.log('保存插件设置');
    }
}

// 全局插件系统实例
let pluginSystem = null;

// 初始化插件系统
function initializePluginSystem() {
    pluginSystem = new PluginSystem();
    console.log('✅ 插件扩展系统已初始化');
}

// 显示插件面板
function showPluginPanel() {
    if (pluginSystem) {
        pluginSystem.showPluginPanel();
    }
}

// 页面加载时初始化
document.addEventListener('DOMContentLoaded', function() {
    setTimeout(initializePluginSystem, 1500);
});
