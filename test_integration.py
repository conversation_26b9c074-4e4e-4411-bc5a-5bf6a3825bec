#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
整合系统测试脚本
测试NER API和前端集成功能
"""

import requests
import json
import time
import os
from ner_data_generator import NERDataGenerator

def test_ner_generator():
    """测试NER数据生成器"""
    print("🧪 测试NER数据生成器...")
    
    try:
        generator = NERDataGenerator()
        
        # 生成少量测试数据
        dataset = generator.generate_dataset(10)
        
        if len(dataset) == 10:
            print("✅ NER数据生成器测试通过")
            
            # 显示一个样本
            sample = dataset[0]
            print(f"   样本文本: {sample['text']}")
            print(f"   实体数量: {len([l for l in sample['labels'] if l.startswith('B-')])}")
            return True
        else:
            print("❌ NER数据生成器测试失败")
            return False
            
    except Exception as e:
        print(f"❌ NER数据生成器测试出错: {e}")
        return False

def test_api_endpoints():
    """测试API端点"""
    print("🌐 测试API端点...")
    
    base_url = "http://localhost:5000/api"
    
    # 测试健康检查
    try:
        response = requests.get(f"{base_url}/health", timeout=5)
        if response.status_code == 200:
            print("✅ API健康检查通过")
        else:
            print("❌ API健康检查失败")
            return False
    except requests.exceptions.RequestException:
        print("❌ API服务未启动或无法连接")
        return False
    
    # 测试数据生成
    try:
        payload = {
            "sample_count": 100,
            "entity_types": {
                "person": True,
                "location": True,
                "organization": True,
                "time": True,
                "number": True
            }
        }
        
        response = requests.post(f"{base_url}/ner/generate", 
                               json=payload, timeout=30)
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                print("✅ API数据生成测试通过")
                print(f"   生成样本数: {result['stats']['total_samples']}")
                return True
            else:
                print(f"❌ API数据生成失败: {result.get('error')}")
                return False
        else:
            print(f"❌ API数据生成请求失败: {response.status_code}")
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"❌ API数据生成测试出错: {e}")
        return False

def test_file_structure():
    """测试文件结构"""
    print("📁 测试文件结构...")
    
    required_files = [
        'ner_api.py',
        'ner_data_generator.py', 
        'ner-management.js',
        'index.html',
        'login.html',
        'app.js',
        'styles.css',
        'start_system.py',
        'requirements.txt'
    ]
    
    missing_files = []
    for file in required_files:
        if not os.path.exists(file):
            missing_files.append(file)
    
    if missing_files:
        print("❌ 缺少以下文件:")
        for file in missing_files:
            print(f"   - {file}")
        return False
    else:
        print("✅ 文件结构检查通过")
        return True

def test_dependencies():
    """测试Python依赖"""
    print("📦 测试Python依赖...")
    
    try:
        import flask
        import flask_cors
        print("✅ Python依赖检查通过")
        return True
    except ImportError as e:
        print(f"❌ 缺少Python依赖: {e}")
        print("请运行: pip install -r requirements.txt")
        return False

def test_frontend_files():
    """测试前端文件内容"""
    print("🎨 测试前端文件...")
    
    # 检查HTML文件是否包含NER页面
    try:
        with open('index.html', 'r', encoding='utf-8') as f:
            html_content = f.read()
            
        if 'ner-page' in html_content and 'NER数据管理' in html_content:
            print("✅ HTML文件包含NER页面")
        else:
            print("❌ HTML文件缺少NER页面内容")
            return False
            
        # 检查JavaScript文件
        if os.path.exists('ner-management.js'):
            with open('ner-management.js', 'r', encoding='utf-8') as f:
                js_content = f.read()
                
            if 'generateNERData' in js_content and 'NER_API_BASE' in js_content:
                print("✅ JavaScript文件包含NER功能")
            else:
                print("❌ JavaScript文件缺少NER功能")
                return False
        
        # 检查CSS文件
        with open('styles.css', 'r', encoding='utf-8') as f:
            css_content = f.read()
            
        if 'ner-page' in css_content and 'entity-' in css_content:
            print("✅ CSS文件包含NER样式")
        else:
            print("❌ CSS文件缺少NER样式")
            return False
            
        return True
        
    except Exception as e:
        print(f"❌ 前端文件测试出错: {e}")
        return False

def run_all_tests():
    """运行所有测试"""
    print("🚀 开始整合系统测试")
    print("=" * 50)
    
    tests = [
        ("文件结构", test_file_structure),
        ("Python依赖", test_dependencies),
        ("NER数据生成器", test_ner_generator),
        ("前端文件", test_frontend_files),
        ("API端点", test_api_endpoints)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n📋 {test_name}测试:")
        if test_func():
            passed += 1
        print()
    
    print("=" * 50)
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！系统整合成功！")
        print()
        print("💡 下一步:")
        print("   1. 运行 python start_system.py 启动系统")
        print("   2. 访问 http://localhost:8000/login.html")
        print("   3. 使用测试账号登录并体验NER功能")
    else:
        print("⚠️  部分测试失败，请检查上述问题")
        
        if passed >= 3:  # 基础功能正常
            print()
            print("💡 基础功能正常，可以尝试启动系统:")
            print("   python start_system.py")

if __name__ == "__main__":
    run_all_tests()
