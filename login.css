/* 现代登录页面样式 */ 

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    --primary-color: #6366f1;
    --primary-light: #818cf8;
    --primary-dark: #4f46e5;
    --success-color: #10b981;
    --warning-color: #f59e0b;
    --danger-color: #ef4444;
    --gray-50: #f8fafc;
    --gray-100: #f1f5f9;
    --gray-200: #e2e8f0;
    --gray-300: #cbd5e1;
    --gray-400: #94a3b8;
    --gray-500: #64748b;
    --gray-600: #475569;
    --gray-700: #334155;
    --gray-800: #1e293b;
    --gray-900: #0f172a;
    --border-radius: 12px;
    --border-radius-lg: 16px;
    --shadow-lg: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--gray-50);
    position: relative;
    /* 移除 overflow: hidden 以允许页面滚动 */
}

/* 背景动画 */
.login-background {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -1;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #6366f1 100%);
}

.background-shapes {
    position: absolute;
    width: 100%;
    height: 100%;
    overflow: hidden;
}

.shape {
    position: absolute;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.1);
    animation: float 20s infinite linear;
}

.shape-1 {
    width: 300px;
    height: 300px;
    top: -150px;
    left: -150px;
    animation-delay: 0s;
}

.shape-2 {
    width: 200px;
    height: 200px;
    top: 50%;
    right: -100px;
    animation-delay: -10s;
}

.shape-3 {
    width: 150px;
    height: 150px;
    bottom: -75px;
    left: 50%;
    animation-delay: -5s;
}

@keyframes float {
    0%, 100% {
        transform: translateY(0) rotate(0deg);
    }
    50% {
        transform: translateY(-20px) rotate(180deg);
    }
}

.login-container {
    width: 100%;
    max-width: 1200px;
    padding: 20px;
    display: grid;
    grid-template-columns: 1fr 400px;
    gap: 60px;
    align-items: center;
}

.login-card {
    background: white;
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-lg);
    overflow: hidden;
    animation: slideUp 0.8s ease-out;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(40px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.login-header {
    text-align: center;
    padding: 40px 32px 32px;
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 100%);
    color: white;
    position: relative;
}

.login-header::before {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
}

.logo {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 16px;
    margin-bottom: 16px;
}

.logo-icon {
    width: 48px;
    height: 48px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: var(--border-radius);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.3);
}

.logo-text h1 {
    font-size: 28px;
    font-weight: 700;
    margin-bottom: 4px;
    letter-spacing: -0.5px;
}

.logo-text span {
    font-size: 14px;
    opacity: 0.9;
    font-weight: 400;
}

.login-header p {
    opacity: 0.95;
    font-size: 16px;
    font-weight: 400;
    line-height: 1.5;
}

.login-form {
    padding: 32px;
}

.form-tabs {
    display: flex;
    margin-bottom: 32px;
    background: var(--gray-50);
    border-radius: var(--border-radius);
    padding: 4px;
    position: relative;
}

.tab-btn {
    flex: 1;
    padding: 12px 16px;
    border: none;
    background: none;
    font-size: 14px;
    font-weight: 600;
    color: var(--gray-600);
    cursor: pointer;
    border-radius: calc(var(--border-radius) - 4px);
    transition: var(--transition);
    position: relative;
    z-index: 2;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

.tab-btn.active {
    color: var(--primary-color);
    background: white;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.tab-btn i {
    font-size: 16px;
}

.tab-content {
    display: none;
    animation: fadeIn 0.3s ease-out;
}

.tab-content.active {
    display: block;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 16px;
    margin-bottom: 20px;
}

.form-group {
    margin-bottom: 24px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: var(--gray-700);
    font-size: 14px;
}

.input-wrapper {
    position: relative;
    display: flex;
    align-items: center;
}

.input-wrapper i {
    position: absolute;
    left: 16px;
    color: var(--gray-400);
    z-index: 1;
    font-size: 16px;
}

.input-wrapper input {
    width: 100%;
    padding: 16px 48px 16px 48px;
    border: 2px solid var(--gray-200);
    border-radius: var(--border-radius);
    font-size: 16px;
    font-weight: 400;
    background: white;
    transition: var(--transition);
    color: var(--gray-900);
}

.input-wrapper input::placeholder {
    color: var(--gray-400);
    font-weight: 400;
}

.input-wrapper input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 4px rgba(99, 102, 241, 0.1);
}

.input-wrapper.error input {
    border-color: var(--danger-color);
    box-shadow: 0 0 0 4px rgba(239, 68, 68, 0.1);
}

.input-wrapper.success input {
    border-color: var(--success-color);
    box-shadow: 0 0 0 4px rgba(16, 185, 129, 0.1);
}

.toggle-password {
    position: absolute;
    right: 16px;
    background: none;
    border: none;
    color: var(--gray-400);
    cursor: pointer;
    padding: 8px;
    border-radius: 6px;
    transition: var(--transition);
}

.toggle-password:hover {
    color: var(--gray-600);
    background: var(--gray-100);
}

/* 密码强度指示器 */
.password-strength {
    margin-top: 8px;
    display: flex;
    align-items: center;
    gap: 12px;
}

.strength-bar {
    flex: 1;
    height: 4px;
    background: var(--gray-200);
    border-radius: 2px;
    overflow: hidden;
}

.strength-fill {
    height: 100%;
    width: 0%;
    background: var(--gray-300);
    transition: var(--transition);
    border-radius: 2px;
}

.strength-fill.weak {
    width: 25%;
    background: var(--danger-color);
}

.strength-fill.fair {
    width: 50%;
    background: var(--warning-color);
}

.strength-fill.good {
    width: 75%;
    background: var(--success-color);
}

.strength-fill.strong {
    width: 100%;
    background: var(--success-color);
}

.strength-text {
    font-size: 12px;
    color: var(--gray-500);
    font-weight: 500;
}

.form-options {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 28px;
}

.checkbox-wrapper {
    display: flex;
    align-items: center;
    cursor: pointer;
    font-size: 14px;
    color: var(--gray-700);
    gap: 12px;
}

.checkbox-wrapper input {
    display: none;
}

.checkmark {
    width: 20px;
    height: 20px;
    border: 2px solid var(--gray-300);
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: var(--transition);
    background: white;
}

.checkmark i {
    font-size: 12px;
    color: white;
    opacity: 0;
    transition: var(--transition);
}

.checkbox-wrapper input:checked + .checkmark {
    background: var(--primary-color);
    border-color: var(--primary-color);
}

.checkbox-wrapper input:checked + .checkmark i {
    opacity: 1;
}

.checkbox-label {
    font-weight: 500;
}

.terms-link {
    color: var(--primary-color);
    text-decoration: none;
    font-weight: 600;
}

.terms-link:hover {
    text-decoration: underline;
}

.forgot-password {
    color: var(--primary-color);
    text-decoration: none;
    font-size: 14px;
    font-weight: 600;
    transition: var(--transition);
}

.forgot-password:hover {
    color: var(--primary-dark);
    text-decoration: underline;
}

.btn-primary {
    width: 100%;
    padding: 16px 24px;
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 100%);
    color: white;
    border: none;
    border-radius: var(--border-radius);
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    margin-bottom: 24px;
    position: relative;
    overflow: hidden;
}

.btn-primary::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

.btn-primary:hover::before {
    left: 100%;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 12px 24px rgba(99, 102, 241, 0.4);
}

.btn-primary:active {
    transform: translateY(0);
}

/* 分割线 */
.divider {
    text-align: center;
    margin: 24px 0;
    position: relative;
}

.divider::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    height: 1px;
    background: var(--gray-200);
}

.divider span {
    background: white;
    padding: 0 16px;
    color: var(--gray-500);
    font-size: 14px;
    font-weight: 500;
    position: relative;
    z-index: 1;
}

/* 社交登录按钮 */
.social-login {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.social-btn {
    width: 100%;
    padding: 14px 20px;
    border: 2px solid var(--gray-200);
    background: white;
    border-radius: var(--border-radius);
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 12px;
    color: var(--gray-700);
}

.social-btn:hover {
    border-color: var(--gray-300);
    background: var(--gray-50);
    transform: translateY(-1px);
}

.social-btn.google:hover {
    border-color: #db4437;
    color: #db4437;
}

.social-btn.github:hover {
    border-color: #333;
    color: #333;
}

.social-btn i {
    font-size: 18px;
}

/* 功能特色部分 */
.features-section {
    background: white;
    border-radius: var(--border-radius-lg);
    padding: 40px;
    box-shadow: var(--shadow-lg);
    animation: slideUp 0.8s ease-out 0.2s both;
}

.features-section h3 {
    font-size: 24px;
    font-weight: 700;
    color: var(--gray-900);
    margin-bottom: 32px;
    text-align: center;
}

.features-list {
    display: grid;
    gap: 24px;
}

.feature-item {
    display: flex;
    align-items: flex-start;
    gap: 16px;
    padding: 20px;
    background: var(--gray-50);
    border-radius: var(--border-radius);
    transition: var(--transition);
}

.feature-item:hover {
    background: white;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    transform: translateY(-2px);
}

.feature-item i {
    width: 48px;
    height: 48px;
    background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
    color: white;
    border-radius: var(--border-radius);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 20px;
    flex-shrink: 0;
}

.feature-content h4 {
    font-size: 16px;
    font-weight: 600;
    color: var(--gray-900);
    margin-bottom: 4px;
}

.feature-content p {
    font-size: 14px;
    color: var(--gray-600);
    line-height: 1.5;
}

/* 错误消息 */
.error-message {
    color: var(--danger-color);
    font-size: 12px;
    margin-top: 6px;
    min-height: 18px;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 4px;
}

.error-message:before {
    content: '⚠';
    font-size: 10px;
}

/* 响应式设计 */
@media (max-width: 1024px) {
    .login-container {
        grid-template-columns: 1fr;
        max-width: 500px;
        gap: 40px;
    }

    .features-section {
        order: -1;
    }
}

@media (max-width: 768px) {
    body {
        /* 移动端允许滚动 */
        align-items: flex-start;
        padding: 20px 0;
    }

    .login-container {
        padding: 16px;
        gap: 32px;
        /* 确保容器可以超出视口高度 */
        min-height: auto;
    }

    .login-card {
        margin: 0;
        /* 移动端确保卡片可以完整显示 */
        max-height: none;
    }

    .login-header {
        padding: 32px 24px 24px;
    }

    .login-form {
        padding: 24px;
        /* 确保表单内容可以完整显示 */
        max-height: none;
    }

    .logo-text h1 {
        font-size: 24px;
    }

    .form-row {
        grid-template-columns: 1fr;
        gap: 0;
    }

    .features-section {
        padding: 32px 24px;
    }

    .features-section h3 {
        font-size: 20px;
        margin-bottom: 24px;
    }

    .feature-item {
        padding: 16px;
    }

    .feature-item i {
        width: 40px;
        height: 40px;
        font-size: 18px;
    }
}

@media (max-width: 480px) {
    body {
        /* 超小屏幕优化 */
        padding: 10px 0;
        align-items: flex-start;
    }

    .login-container {
        padding: 12px;
        /* 确保在小屏幕上有足够的空间 */
        width: 100%;
        max-width: none;
    }

    .login-card {
        /* 小屏幕上移除固定高度限制 */
        max-height: none;
        min-height: auto;
    }

    .login-header {
        padding: 24px 20px 20px;
    }

    .login-form {
        padding: 20px;
        /* 确保表单可以完整显示 */
        max-height: none;
        overflow: visible;
    }

    .logo {
        flex-direction: column;
        gap: 12px;
    }

    .logo-text {
        text-align: center;
    }

    .logo-text h1 {
        font-size: 20px;
    }

    .input-wrapper input {
        padding: 14px 40px 14px 40px;
        font-size: 16px; /* 防止iOS缩放 */
    }

    .btn-primary {
        padding: 14px 20px;
    }

    /* 确保注册表单在小屏幕上可以完整显示 */
    .tab-content {
        max-height: none;
        overflow: visible;
    }

    /* 优化表单间距 */
    .form-group {
        margin-bottom: 20px;
    }

    .features-section {
        padding: 24px 20px;
    }

    .social-login {
        gap: 8px;
    }

    .social-btn {
        padding: 12px 16px;
        font-size: 13px;
    }
}

/* 加载动画 */
.loading {
    position: relative;
    pointer-events: none;
}

.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-top-color: white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

/* 滚动条样式 */
::-webkit-scrollbar {
    width: 6px;
}

::-webkit-scrollbar-track {
    background: var(--gray-100);
    border-radius: 3px;
}

::-webkit-scrollbar-thumb {
    background: var(--gray-300);
    border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--gray-400);
}

/* 角色快速切换样式 */
.role-switcher {
    margin-top: 32px;
    padding: 24px;
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 16px;
    backdrop-filter: blur(10px);
}

.role-switcher-header {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 16px;
    color: rgba(255, 255, 255, 0.8);
    font-size: 14px;
    font-weight: 500;
}

.role-switcher-header i {
    color: var(--primary-color);
}

.role-buttons {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
    gap: 12px;
}

.role-btn {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 6px;
    padding: 16px 12px;
    background: rgba(255, 255, 255, 0.08);
    border: 1px solid rgba(255, 255, 255, 0.15);
    border-radius: 12px;
    color: white;
    cursor: pointer;
    transition: all 0.3s ease;
    text-align: center;
}

.role-btn:hover {
    background: rgba(255, 255, 255, 0.15);
    border-color: rgba(255, 255, 255, 0.3);
    transform: translateY(-2px);
}

.role-btn i {
    font-size: 20px;
    margin-bottom: 4px;
}

.role-btn span {
    font-size: 13px;
    font-weight: 500;
    margin-bottom: 2px;
}

.role-btn small {
    font-size: 11px;
    opacity: 0.7;
    font-family: 'Courier New', monospace;
}

.role-btn.admin i {
    color: #fbbf24;
}

.role-btn.manager i {
    color: #3b82f6;
}

.role-btn.user i {
    color: #10b981;
}

.role-btn.test i {
    color: #8b5cf6;
}

.role-btn.demo i {
    color: #f59e0b;
}

.role-btn.admin:hover {
    border-color: #fbbf24;
    box-shadow: 0 4px 20px rgba(251, 191, 36, 0.3);
}

.role-btn.manager:hover {
    border-color: #3b82f6;
    box-shadow: 0 4px 20px rgba(59, 130, 246, 0.3);
}

.role-btn.user:hover {
    border-color: #10b981;
    box-shadow: 0 4px 20px rgba(16, 185, 129, 0.3);
}

.role-btn.test:hover {
    border-color: #8b5cf6;
    box-shadow: 0 4px 20px rgba(139, 92, 246, 0.3);
}

.role-btn.demo:hover {
    border-color: #f59e0b;
    box-shadow: 0 4px 20px rgba(245, 158, 11, 0.3);
}

/* 响应式角色切换 */
@media (max-width: 768px) {
    .role-switcher {
        margin-top: 24px;
        padding: 20px;
    }

    .role-buttons {
        grid-template-columns: repeat(2, 1fr);
        gap: 10px;
    }

    .role-btn {
        padding: 12px 8px;
    }

    .role-btn i {
        font-size: 18px;
    }

    .role-btn span {
        font-size: 12px;
    }

    .role-btn small {
        font-size: 10px;
    }
}

@media (max-width: 480px) {
    .role-buttons {
        grid-template-columns: 1fr;
        gap: 8px;
    }

    .role-btn {
        flex-direction: row;
        justify-content: flex-start;
        text-align: left;
        padding: 12px 16px;
    }

    .role-btn i {
        font-size: 16px;
        margin-bottom: 0;
        margin-right: 8px;
    }

    .role-btn span {
        flex: 1;
    }
}