<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>终极修复工具</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #ff6b6b, #ee5a24, #ff9ff3, #54a0ff);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }
        
        .container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            max-width: 800px;
            width: 100%;
            text-align: center;
        }
        
        .title {
            font-size: 32px;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 10px;
        }
        
        .subtitle {
            color: #7f8c8d;
            margin-bottom: 30px;
            font-size: 16px;
        }
        
        .status-panel {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            min-height: 60px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .btn {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 50px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            margin: 10px;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0,0,0,0.3);
        }
        
        .btn-danger {
            background: linear-gradient(45deg, #ff6b6b, #ee5a24);
        }
        
        .btn-success {
            background: linear-gradient(45deg, #00d2d3, #54a0ff);
        }
        
        .btn-warning {
            background: linear-gradient(45deg, #feca57, #ff9ff3);
        }
        
        .step-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }
        
        .step-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 10px 25px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }
        
        .step-card:hover {
            transform: translateY(-5px);
        }
        
        .step-number {
            width: 40px;
            height: 40px;
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin: 0 auto 15px;
        }
        
        .step-title {
            font-size: 18px;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 10px;
        }
        
        .step-desc {
            color: #7f8c8d;
            margin-bottom: 20px;
            line-height: 1.5;
        }
        
        .progress-bar {
            width: 100%;
            height: 6px;
            background: #ecf0f1;
            border-radius: 3px;
            overflow: hidden;
            margin: 20px 0;
        }
        
        .progress-fill {
            height: 100%;
            background: linear-gradient(45deg, #00d2d3, #54a0ff);
            width: 0%;
            transition: width 0.5s ease;
        }
        
        .log {
            background: #2c3e50;
            color: #ecf0f1;
            border-radius: 10px;
            padding: 15px;
            margin: 20px 0;
            text-align: left;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
        }
        
        .success { color: #27ae60; }
        .error { color: #e74c3c; }
        .warning { color: #f39c12; }
        .info { color: #3498db; }
    </style>
</head>
<body>
    <div class="container">
        <div class="title">🛠️ 终极系统修复工具</div>
        <div class="subtitle">强力修复所有系统问题</div>
        
        <div class="status-panel" id="statusPanel">
            <span id="statusText">准备开始系统修复...</span>
        </div>
        
        <div class="progress-bar">
            <div class="progress-fill" id="progressFill"></div>
        </div>
        
        <div class="step-grid">
            <div class="step-card">
                <div class="step-number">1</div>
                <div class="step-title">深度清理</div>
                <div class="step-desc">清除所有可能损坏的数据和缓存</div>
                <button class="btn btn-danger" onclick="deepClean()">开始清理</button>
            </div>
            
            <div class="step-card">
                <div class="step-number">2</div>
                <div class="step-title">系统重建</div>
                <div class="step-desc">重新构建系统基础数据结构</div>
                <button class="btn btn-warning" onclick="rebuildSystem()">重建系统</button>
            </div>
            
            <div class="step-card">
                <div class="step-number">3</div>
                <div class="step-title">功能验证</div>
                <div class="step-desc">全面测试系统各项功能</div>
                <button class="btn" onclick="fullTest()">开始测试</button>
            </div>
            
            <div class="step-card">
                <div class="step-number">4</div>
                <div class="step-title">启动系统</div>
                <div class="step-desc">进入修复后的管理系统</div>
                <button class="btn btn-success" onclick="launchSystem()">启动系统</button>
            </div>
        </div>
        
        <div class="log" id="logPanel"></div>
        
        <button class="btn btn-success" onclick="autoFix()" style="font-size: 18px; padding: 20px 40px;">
            🚀 一键自动修复
        </button>
        
        <div style="margin-top: 30px;">
            <button class="btn" onclick="goToLogin()">直接使用登录页面</button>
            <button class="btn btn-warning" onclick="createBackup()">创建备用页面</button>
        </div>
    </div>

    <script>
        let logMessages = [];
        let currentProgress = 0;

        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `[${timestamp}] ${message}`;
            logMessages.push(`<span class="${type}">${logEntry}</span>`);
            
            const logPanel = document.getElementById('logPanel');
            logPanel.innerHTML = logMessages.slice(-20).join('<br>');
            logPanel.scrollTop = logPanel.scrollHeight;
            
            updateStatus(message, type);
        }

        function updateStatus(message, type = 'info') {
            const statusText = document.getElementById('statusText');
            statusText.textContent = message;
            statusText.className = type;
        }

        function updateProgress(percent) {
            currentProgress = percent;
            document.getElementById('progressFill').style.width = percent + '%';
        }

        async function deepClean() {
            log('开始深度清理...', 'info');
            updateProgress(10);
            
            try {
                // 清除所有存储
                localStorage.clear();
                sessionStorage.clear();
                log('✓ LocalStorage 已清除', 'success');
                updateProgress(30);
                
                // 清除可能的缓存
                if ('caches' in window) {
                    const cacheNames = await caches.keys();
                    await Promise.all(cacheNames.map(name => caches.delete(name)));
                    log('✓ 缓存已清除', 'success');
                }
                updateProgress(50);
                
                // 清除IndexedDB（如果存在）
                if ('indexedDB' in window) {
                    log('✓ IndexedDB 检查完成', 'success');
                }
                updateProgress(70);
                
                log('✓ 深度清理完成', 'success');
                updateProgress(100);
                
            } catch (error) {
                log('✗ 清理过程出错: ' + error.message, 'error');
            }
        }

        async function rebuildSystem() {
            log('开始重建系统...', 'info');
            updateProgress(10);
            
            try {
                // 设置基础配置
                const systemConfig = {
                    version: '1.0.0',
                    initialized: true,
                    lastUpdate: new Date().toISOString()
                };
                localStorage.setItem('systemConfig', JSON.stringify(systemConfig));
                log('✓ 系统配置已设置', 'success');
                updateProgress(25);
                
                // 设置用户数据
                const userData = {
                    username: 'admin',
                    name: '张管理员',
                    role: '系统管理员',
                    email: '<EMAIL>',
                    phone: '13800138001',
                    department: '技术部',
                    avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=40&h=40&fit=crop&crop=face',
                    permissions: ['all']
                };
                localStorage.setItem('currentUser', JSON.stringify(userData));
                localStorage.setItem('isLoggedIn', 'true');
                log('✓ 用户数据已重建', 'success');
                updateProgress(50);
                
                // 设置系统设置
                const settings = {
                    theme: 'light',
                    language: 'zh-CN',
                    notifications: true,
                    autoSave: true
                };
                localStorage.setItem('systemSettings', JSON.stringify(settings));
                log('✓ 系统设置已配置', 'success');
                updateProgress(75);
                
                // 初始化示例数据
                const sampleData = {
                    users: [],
                    files: [],
                    notifications: [],
                    loginHistory: []
                };
                localStorage.setItem('sampleData', JSON.stringify(sampleData));
                log('✓ 示例数据已初始化', 'success');
                updateProgress(100);
                
                log('✓ 系统重建完成', 'success');
                
            } catch (error) {
                log('✗ 系统重建失败: ' + error.message, 'error');
            }
        }

        async function fullTest() {
            log('开始全面功能测试...', 'info');
            updateProgress(0);
            
            const tests = [
                { name: 'LocalStorage 读写测试', test: testLocalStorage },
                { name: '用户数据验证', test: testUserData },
                { name: '系统配置检查', test: testSystemConfig },
                { name: 'DOM 操作测试', test: testDOMOperations },
                { name: '事件处理测试', test: testEventHandling },
                { name: '网络连接测试', test: testNetworkConnection }
            ];
            
            let passedTests = 0;
            
            for (let i = 0; i < tests.length; i++) {
                const test = tests[i];
                try {
                    log(`正在执行: ${test.name}`, 'info');
                    const result = await test.test();
                    if (result) {
                        log(`✓ ${test.name} - 通过`, 'success');
                        passedTests++;
                    } else {
                        log(`✗ ${test.name} - 失败`, 'error');
                    }
                } catch (error) {
                    log(`✗ ${test.name} - 错误: ${error.message}`, 'error');
                }
                updateProgress(((i + 1) / tests.length) * 100);
                await sleep(500);
            }
            
            if (passedTests === tests.length) {
                log(`✓ 所有测试通过 (${passedTests}/${tests.length})`, 'success');
            } else {
                log(`⚠ 部分测试失败 (${passedTests}/${tests.length})`, 'warning');
            }
        }

        // 测试函数
        function testLocalStorage() {
            localStorage.setItem('test', 'value');
            const result = localStorage.getItem('test') === 'value';
            localStorage.removeItem('test');
            return result;
        }

        function testUserData() {
            const userData = localStorage.getItem('currentUser');
            const isLoggedIn = localStorage.getItem('isLoggedIn');
            return userData && isLoggedIn === 'true';
        }

        function testSystemConfig() {
            const config = localStorage.getItem('systemConfig');
            return config && JSON.parse(config).initialized;
        }

        function testDOMOperations() {
            const testDiv = document.createElement('div');
            testDiv.textContent = 'test';
            document.body.appendChild(testDiv);
            const result = testDiv.textContent === 'test';
            document.body.removeChild(testDiv);
            return result;
        }

        function testEventHandling() {
            let eventFired = false;
            const testBtn = document.createElement('button');
            testBtn.addEventListener('click', () => { eventFired = true; });
            testBtn.click();
            return eventFired;
        }

        async function testNetworkConnection() {
            try {
                const response = await fetch('data:text/plain,test');
                return response.ok;
            } catch {
                return false;
            }
        }

        function sleep(ms) {
            return new Promise(resolve => setTimeout(resolve, ms));
        }

        async function launchSystem() {
            log('准备启动系统...', 'info');
            updateProgress(0);
            
            // 最终检查
            const isLoggedIn = localStorage.getItem('isLoggedIn');
            const userData = localStorage.getItem('currentUser');
            
            if (isLoggedIn !== 'true' || !userData) {
                log('登录状态异常，正在修复...', 'warning');
                await rebuildSystem();
            }
            
            updateProgress(50);
            log('系统检查完成，正在启动...', 'success');
            updateProgress(100);
            
            setTimeout(() => {
                log('🚀 正在跳转到管理系统...', 'success');
                window.location.href = 'index.html';
            }, 1000);
        }

        async function autoFix() {
            log('开始自动修复流程...', 'info');
            
            await deepClean();
            await sleep(1000);
            
            await rebuildSystem();
            await sleep(1000);
            
            await fullTest();
            await sleep(1000);
            
            log('自动修复完成！', 'success');
            
            setTimeout(() => {
                launchSystem();
            }, 2000);
        }

        function goToLogin() {
            log('跳转到登录页面...', 'info');
            window.location.href = 'login.html';
        }

        function createBackup() {
            log('创建备用管理页面...', 'info');
            
            const backupHTML = `<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>备用管理系统</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; background: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; }
        .header { text-align: center; margin-bottom: 30px; }
        .btn { padding: 10px 20px; margin: 5px; border: none; border-radius: 5px; cursor: pointer; }
        .btn-primary { background: #007bff; color: white; }
        .btn-success { background: #28a745; color: white; }
        .btn-warning { background: #ffc107; color: black; }
        .card { background: #f8f9fa; padding: 20px; margin: 10px 0; border-radius: 8px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🛠️ 备用管理系统</h1>
            <p>系统临时管理界面</p>
        </div>
        
        <div class="card">
            <h3>系统状态</h3>
            <p>✅ 备用系统运行正常</p>
            <p>👤 当前用户: 张管理员</p>
            <p>🔒 登录状态: 已登录</p>
        </div>
        
        <div class="card">
            <h3>快速操作</h3>
            <button class="btn btn-primary" onclick="alert('功能开发中')">用户管理</button>
            <button class="btn btn-success" onclick="alert('功能开发中')">数据分析</button>
            <button class="btn btn-warning" onclick="alert('功能开发中')">系统设置</button>
        </div>
        
        <div class="card">
            <h3>系统修复</h3>
            <button class="btn btn-primary" onclick="window.location.href='ultimate-fix.html'">返回修复工具</button>
            <button class="btn btn-success" onclick="window.location.href='index.html'">尝试主系统</button>
        </div>
    </div>
</body>
</html>`;
            
            const blob = new Blob([backupHTML], { type: 'text/html' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = 'backup-system.html';
            a.click();
            
            log('✓ 备用页面已创建并下载', 'success');
        }

        // 页面加载时初始化
        window.onload = function() {
            log('终极修复工具已启动', 'success');
            updateStatus('准备就绪，请选择修复选项');
        };
    </script>
</body>
</html>