<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>UI界面优化展示 - 企业管理系统</title>
    <link rel="stylesheet" href="../assets/css/styles.css">
    <link rel="stylesheet" href="../assets/css/modern-theme.css">
    <link rel="stylesheet" href="../assets/css/page-layouts.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        .showcase-container {
            max-width: 1400px;
            margin: 0 auto;
        }
        .showcase-title {
            color: white;
            text-align: center;
            margin-bottom: 40px;
            font-size: 36px;
            font-weight: 700;
        }
        .section-divider {
            margin: 48px 0;
            text-align: center;
        }
        .section-title {
            color: white;
            font-size: 24px;
            font-weight: 600;
            margin-bottom: 24px;
            padding: 16px 24px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 12px;
            backdrop-filter: blur(10px);
        }
        .demo-section {
            margin-bottom: 48px;
        }
        .comparison-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 24px;
            margin-bottom: 32px;
        }
        .comparison-item {
            background: rgba(255, 255, 255, 0.98);
            border-radius: 16px;
            padding: 24px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
        }
        .comparison-label {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 16px;
            padding: 8px 16px;
            border-radius: 8px;
            text-align: center;
        }
        .before-label {
            background: #fee2e2;
            color: #dc2626;
        }
        .after-label {
            background: #dcfce7;
            color: #16a34a;
        }
        .old-style {
            padding: 16px;
            background: #f3f4f6;
            border-radius: 8px;
            border: 1px solid #d1d5db;
        }
        .old-style input {
            padding: 8px 12px;
            border: 1px solid #d1d5db;
            border-radius: 4px;
            margin: 4px;
        }
        .old-style button {
            padding: 8px 16px;
            background: #6b7280;
            color: white;
            border: none;
            border-radius: 4px;
            margin: 4px;
        }
        .feature-highlight {
            background: rgba(255, 255, 255, 0.98);
            border-radius: 16px;
            padding: 32px;
            margin: 24px 0;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
        }
        .highlight-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 24px;
            margin-top: 24px;
        }
        .highlight-item {
            text-align: center;
            padding: 20px;
            background: #f8fafc;
            border-radius: 12px;
            border: 2px solid #e5e7eb;
            transition: all 0.3s ease;
        }
        .highlight-item:hover {
            border-color: #667eea;
            transform: translateY(-4px);
        }
        .highlight-icon {
            width: 60px;
            height: 60px;
            background: var(--gradient-primary);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 16px;
            color: white;
            font-size: 24px;
        }
        .back-btn {
            position: fixed;
            top: 20px;
            left: 20px;
            background: rgba(255, 255, 255, 0.9);
            border: none;
            border-radius: 12px;
            padding: 12px 20px;
            cursor: pointer;
            font-weight: 600;
            color: #374151;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            transition: all 0.3s ease;
        }
        .back-btn:hover {
            background: white;
            transform: translateY(-2px);
        }
    </style>
</head>
<body>
    <button class="back-btn" onclick="goBack()">
        <i class="fas fa-arrow-left"></i> 返回主系统
    </button>

    <div class="showcase-container">
        <h1 class="showcase-title">🎨 UI界面优化展示</h1>

        <!-- 整体改进概述 -->
        <div class="feature-highlight">
            <h2 style="text-align: center; margin-bottom: 16px; color: #1f2937;">界面优化成果</h2>
            <p style="text-align: center; color: #6b7280; margin-bottom: 32px;">
                通过重新设计页面布局、优化组件结构和改进交互体验，让系统界面更加整齐有序、现代化
            </p>
            <div class="highlight-grid">
                <div class="highlight-item">
                    <div class="highlight-icon">
                        <i class="fas fa-layer-group"></i>
                    </div>
                    <h3 style="margin: 0 0 8px 0; color: #1f2937;">统一布局</h3>
                    <p style="margin: 0; color: #6b7280; font-size: 14px;">标准化的页面结构和组件布局</p>
                </div>
                <div class="highlight-item">
                    <div class="highlight-icon">
                        <i class="fas fa-search"></i>
                    </div>
                    <h3 style="margin: 0 0 8px 0; color: #1f2937;">优化搜索</h3>
                    <p style="margin: 0; color: #6b7280; font-size: 14px;">改进的搜索框和筛选器设计</p>
                </div>
                <div class="highlight-item">
                    <div class="highlight-icon">
                        <i class="fas fa-table"></i>
                    </div>
                    <h3 style="margin: 0 0 8px 0; color: #1f2937;">现代表格</h3>
                    <p style="margin: 0; color: #6b7280; font-size: 14px;">清晰的数据表格和分页设计</p>
                </div>
                <div class="highlight-item">
                    <div class="highlight-icon">
                        <i class="fas fa-bolt"></i>
                    </div>
                    <h3 style="margin: 0 0 8px 0; color: #1f2937;">快速操作</h3>
                    <p style="margin: 0; color: #6b7280; font-size: 14px;">便捷的快速操作区域</p>
                </div>
            </div>
        </div>

        <!-- 页面头部对比 -->
        <div class="demo-section">
            <div class="section-title">📋 页面头部优化</div>
            <div class="comparison-grid">
                <div class="comparison-item">
                    <div class="comparison-label before-label">优化前</div>
                    <div class="old-style">
                        <h2 style="margin: 0 0 8px 0;">用户管理</h2>
                        <button>添加用户</button>
                        <button>导入</button>
                        <button>导出</button>
                    </div>
                </div>
                <div class="comparison-item">
                    <div class="comparison-label after-label">优化后</div>
                    <div class="page-header">
                        <div class="page-title-section">
                            <h1 style="margin: 0 0 8px 0; font-size: 24px;">用户管理</h1>
                            <p style="margin: 0; color: rgba(255,255,255,0.8);">管理系统用户和权限设置</p>
                        </div>
                        <div class="page-actions">
                            <button class="btn-primary">
                                <i class="fas fa-plus"></i> 添加用户
                            </button>
                            <button class="btn-secondary">
                                <i class="fas fa-download"></i> 导出数据
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 快速操作区域展示 -->
        <div class="demo-section">
            <div class="section-title">⚡ 快速操作区域</div>
            <div class="quick-actions-section">
                <h3 class="quick-actions-title">快速操作</h3>
                <div class="quick-actions-grid">
                    <button class="quick-action-btn">
                        <div class="quick-action-icon">
                            <i class="fas fa-user-plus"></i>
                        </div>
                        <div>
                            <div style="font-weight: 600;">添加用户</div>
                            <div style="font-size: 12px; color: #6b7280;">创建新用户账户</div>
                        </div>
                    </button>
                    <button class="quick-action-btn">
                        <div class="quick-action-icon">
                            <i class="fas fa-upload"></i>
                        </div>
                        <div>
                            <div style="font-weight: 600;">批量导入</div>
                            <div style="font-size: 12px; color: #6b7280;">从文件导入用户</div>
                        </div>
                    </button>
                    <button class="quick-action-btn">
                        <div class="quick-action-icon">
                            <i class="fas fa-download"></i>
                        </div>
                        <div>
                            <div style="font-weight: 600;">导出数据</div>
                            <div style="font-size: 12px; color: #6b7280;">导出用户列表</div>
                        </div>
                    </button>
                    <button class="quick-action-btn">
                        <div class="quick-action-icon">
                            <i class="fas fa-chart-bar"></i>
                        </div>
                        <div>
                            <div style="font-weight: 600;">用户分析</div>
                            <div style="font-size: 12px; color: #6b7280;">查看用户统计</div>
                        </div>
                    </button>
                </div>
            </div>
        </div>

        <!-- 搜索工具栏展示 -->
        <div class="demo-section">
            <div class="section-title">🔍 搜索工具栏优化</div>
            <div class="page-toolbar">
                <div class="toolbar-row">
                    <div class="toolbar-section">
                        <div class="search-box">
                            <i class="fas fa-search search-icon"></i>
                            <input type="text" placeholder="搜索用户名、邮箱或姓名...">
                        </div>
                    </div>
                    
                    <div class="toolbar-divider"></div>
                    
                    <div class="toolbar-section">
                        <div class="filter-group">
                            <label>角色：</label>
                            <select class="filter-select">
                                <option>所有角色</option>
                                <option>系统管理员</option>
                                <option>部门经理</option>
                            </select>
                        </div>
                        
                        <div class="filter-group">
                            <label>状态：</label>
                            <select class="filter-select">
                                <option>所有状态</option>
                                <option>活跃</option>
                                <option>非活跃</option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="toolbar-divider"></div>
                    
                    <div class="toolbar-section">
                        <button class="btn-secondary btn-sm">
                            <i class="fas fa-sync"></i> 刷新
                        </button>
                        <button class="btn-secondary btn-sm">
                            <i class="fas fa-sliders-h"></i> 高级筛选
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 数据表格展示 -->
        <div class="demo-section">
            <div class="section-title">📊 数据表格优化</div>
            <div class="data-table-container">
                <div class="table-header">
                    <h3 class="table-title">用户列表</h3>
                    <div class="table-actions">
                        <button class="btn-secondary btn-sm">
                            <i class="fas fa-check-square"></i> 全选
                        </button>
                        <button class="btn-secondary btn-sm">
                            <i class="fas fa-square"></i> 取消选择
                        </button>
                    </div>
                </div>
                
                <div style="overflow-x: auto;">
                    <table class="modern-table">
                        <thead>
                            <tr>
                                <th style="width: 50px;"><input type="checkbox"></th>
                                <th>用户信息</th>
                                <th>角色</th>
                                <th>状态</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td><input type="checkbox"></td>
                                <td>
                                    <div style="display: flex; align-items: center; gap: 12px;">
                                        <div style="width: 40px; height: 40px; background: #667eea; border-radius: 50%; display: flex; align-items: center; justify-content: center; color: white; font-weight: 600;">张</div>
                                        <div>
                                            <div style="font-weight: 600;">张三</div>
                                            <div style="font-size: 12px; color: #6b7280;"><EMAIL></div>
                                        </div>
                                    </div>
                                </td>
                                <td><span style="background: #dbeafe; color: #1e40af; padding: 4px 8px; border-radius: 4px; font-size: 12px;">管理员</span></td>
                                <td><span style="background: #dcfce7; color: #16a34a; padding: 4px 8px; border-radius: 4px; font-size: 12px;">活跃</span></td>
                                <td>
                                    <button class="btn-secondary btn-sm">编辑</button>
                                    <button class="btn-secondary btn-sm">删除</button>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                
                <div class="pagination-container">
                    <div class="pagination-info">显示第 1-10 条，共 100 条记录</div>
                    <div class="pagination-controls">
                        <button class="pagination-btn"><i class="fas fa-chevron-left"></i></button>
                        <button class="pagination-btn active">1</button>
                        <button class="pagination-btn">2</button>
                        <button class="pagination-btn">3</button>
                        <button class="pagination-btn"><i class="fas fa-chevron-right"></i></button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 返回按钮 -->
        <div style="text-align: center; margin-top: 48px;">
            <button class="btn-primary" style="padding: 16px 32px; font-size: 18px;" onclick="goBack()">
                <i class="fas fa-arrow-left"></i> 返回主系统体验优化效果
            </button>
        </div>
    </div>

    <script>
        function goBack() {
            window.location.href = 'index.html';
        }
    </script>
</body>
</html>
