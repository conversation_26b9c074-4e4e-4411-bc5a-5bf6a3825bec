// 高级工作流引擎
class WorkflowEngine {
    constructor() {
        this.workflows = [];
        this.workflowInstances = [];
        this.tasks = [];
        this.forms = [];
        this.integrations = [];
        this.templates = [];
        this.analytics = {};
        this.currentWorkflow = null;
        this.designerCanvas = null;
        this.selectedNode = null;
        this.draggedNode = null;
        
        this.initializeWorkflowEngine();
        this.setupDesigner();
        this.loadWorkflowTemplates();
        this.startMonitoring();
    }

    initializeWorkflowEngine() {
        this.createWorkflowInterface();
        this.bindWorkflowEvents();
        this.setupTaskEngine();
        this.loadWorkflowData();
    }

    createWorkflowInterface() {
        const workflowPanel = document.createElement('div');
        workflowPanel.id = 'workflowPanel';
        workflowPanel.className = 'workflow-panel';
        workflowPanel.innerHTML = `
            <div class="workflow-header">
                <h3>
                    <i class="fas fa-project-diagram"></i>
                    工作流引擎
                </h3>
                <div class="workflow-controls">
                    <select id="workflowSelector" class="workflow-selector">
                        <option value="">选择工作流</option>
                    </select>
                    <button class="btn-secondary" onclick="workflowEngine.importWorkflow()">
                        <i class="fas fa-upload"></i>
                        导入
                    </button>
                    <button class="btn-secondary" onclick="workflowEngine.exportWorkflow()">
                        <i class="fas fa-download"></i>
                        导出
                    </button>
                    <button class="btn-primary" onclick="workflowEngine.createWorkflow()">
                        <i class="fas fa-plus"></i>
                        新建工作流
                    </button>
                    <button class="btn-icon" onclick="workflowEngine.closeWorkflowPanel()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            </div>
            
            <div class="workflow-body">
                <div class="workflow-tabs">
                    <button class="tab-btn active" data-tab="designer">
                        <i class="fas fa-drafting-compass"></i>
                        流程设计
                    </button>
                    <button class="tab-btn" data-tab="instances">
                        <i class="fas fa-play"></i>
                        流程实例
                    </button>
                    <button class="tab-btn" data-tab="tasks">
                        <i class="fas fa-tasks"></i>
                        任务管理
                    </button>
                    <button class="tab-btn" data-tab="forms">
                        <i class="fas fa-wpforms"></i>
                        表单设计
                    </button>
                    <button class="tab-btn" data-tab="analytics">
                        <i class="fas fa-chart-line"></i>
                        流程分析
                    </button>
                    <button class="tab-btn" data-tab="integrations">
                        <i class="fas fa-plug"></i>
                        系统集成
                    </button>
                </div>
                
                <div class="workflow-content">
                    <!-- 流程设计器 -->
                    <div class="tab-content active" id="designerTab">
                        <div class="designer-layout">
                            <div class="designer-toolbar">
                                <div class="toolbar-section">
                                    <h4>节点库</h4>
                                    <div class="node-palette">
                                        <div class="node-category">
                                            <h5>基础节点</h5>
                                            <div class="node-item" data-type="start" draggable="true">
                                                <i class="fas fa-play-circle"></i>
                                                <span>开始</span>
                                            </div>
                                            <div class="node-item" data-type="end" draggable="true">
                                                <i class="fas fa-stop-circle"></i>
                                                <span>结束</span>
                                            </div>
                                            <div class="node-item" data-type="task" draggable="true">
                                                <i class="fas fa-square"></i>
                                                <span>任务</span>
                                            </div>
                                            <div class="node-item" data-type="decision" draggable="true">
                                                <i class="fas fa-diamond"></i>
                                                <span>决策</span>
                                            </div>
                                        </div>
                                        
                                        <div class="node-category">
                                            <h5>高级节点</h5>
                                            <div class="node-item" data-type="parallel" draggable="true">
                                                <i class="fas fa-code-branch"></i>
                                                <span>并行</span>
                                            </div>
                                            <div class="node-item" data-type="subprocess" draggable="true">
                                                <i class="fas fa-sitemap"></i>
                                                <span>子流程</span>
                                            </div>
                                            <div class="node-item" data-type="timer" draggable="true">
                                                <i class="fas fa-clock"></i>
                                                <span>定时器</span>
                                            </div>
                                            <div class="node-item" data-type="script" draggable="true">
                                                <i class="fas fa-code"></i>
                                                <span>脚本</span>
                                            </div>
                                        </div>
                                        
                                        <div class="node-category">
                                            <h5>集成节点</h5>
                                            <div class="node-item" data-type="email" draggable="true">
                                                <i class="fas fa-envelope"></i>
                                                <span>邮件</span>
                                            </div>
                                            <div class="node-item" data-type="api" draggable="true">
                                                <i class="fas fa-plug"></i>
                                                <span>API调用</span>
                                            </div>
                                            <div class="node-item" data-type="database" draggable="true">
                                                <i class="fas fa-database"></i>
                                                <span>数据库</span>
                                            </div>
                                            <div class="node-item" data-type="webhook" draggable="true">
                                                <i class="fas fa-link"></i>
                                                <span>Webhook</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="toolbar-section">
                                    <h4>工具栏</h4>
                                    <div class="designer-tools">
                                        <button class="tool-btn" onclick="workflowEngine.saveWorkflow()" title="保存">
                                            <i class="fas fa-save"></i>
                                        </button>
                                        <button class="tool-btn" onclick="workflowEngine.validateWorkflow()" title="验证">
                                            <i class="fas fa-check-circle"></i>
                                        </button>
                                        <button class="tool-btn" onclick="workflowEngine.deployWorkflow()" title="部署">
                                            <i class="fas fa-rocket"></i>
                                        </button>
                                        <button class="tool-btn" onclick="workflowEngine.previewWorkflow()" title="预览">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <button class="tool-btn" onclick="workflowEngine.clearCanvas()" title="清空">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="designer-main">
                                <div class="canvas-container">
                                    <canvas id="workflowCanvas" width="1200" height="800"></canvas>
                                    <div class="canvas-overlay" id="canvasOverlay">
                                        <!-- 动态生成的节点和连接线 -->
                                    </div>
                                </div>
                            </div>
                            
                            <div class="designer-properties">
                                <div class="properties-panel" id="propertiesPanel">
                                    <h4>属性面板</h4>
                                    <div class="properties-content" id="propertiesContent">
                                        <div class="no-selection">
                                            <i class="fas fa-mouse-pointer"></i>
                                            <p>选择一个节点查看属性</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 流程实例 -->
                    <div class="tab-content" id="instancesTab">
                        <div class="instances-section">
                            <div class="section-header">
                                <h4>流程实例管理</h4>
                                <div class="instance-controls">
                                    <select id="instanceStatusFilter">
                                        <option value="all">所有状态</option>
                                        <option value="running">运行中</option>
                                        <option value="completed">已完成</option>
                                        <option value="suspended">已暂停</option>
                                        <option value="terminated">已终止</option>
                                    </select>
                                    <button class="btn-secondary" onclick="workflowEngine.refreshInstances()">
                                        <i class="fas fa-sync"></i>
                                        刷新
                                    </button>
                                    <button class="btn-primary" onclick="workflowEngine.startWorkflowInstance()">
                                        <i class="fas fa-play"></i>
                                        启动实例
                                    </button>
                                </div>
                            </div>
                            
                            <div class="instances-stats">
                                <div class="stat-card">
                                    <div class="stat-icon running">
                                        <i class="fas fa-play"></i>
                                    </div>
                                    <div class="stat-content">
                                        <div class="stat-value" id="runningInstances">0</div>
                                        <div class="stat-label">运行中</div>
                                    </div>
                                </div>
                                
                                <div class="stat-card">
                                    <div class="stat-icon completed">
                                        <i class="fas fa-check"></i>
                                    </div>
                                    <div class="stat-content">
                                        <div class="stat-value" id="completedInstances">0</div>
                                        <div class="stat-label">已完成</div>
                                    </div>
                                </div>
                                
                                <div class="stat-card">
                                    <div class="stat-icon suspended">
                                        <i class="fas fa-pause"></i>
                                    </div>
                                    <div class="stat-content">
                                        <div class="stat-value" id="suspendedInstances">0</div>
                                        <div class="stat-label">已暂停</div>
                                    </div>
                                </div>
                                
                                <div class="stat-card">
                                    <div class="stat-icon terminated">
                                        <i class="fas fa-stop"></i>
                                    </div>
                                    <div class="stat-content">
                                        <div class="stat-value" id="terminatedInstances">0</div>
                                        <div class="stat-label">已终止</div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="instances-list" id="instancesList">
                                <!-- 流程实例列表 -->
                            </div>
                        </div>
                    </div>
                    
                    <!-- 任务管理 -->
                    <div class="tab-content" id="tasksTab">
                        <div class="tasks-section">
                            <div class="section-header">
                                <h4>任务管理</h4>
                                <div class="task-controls">
                                    <select id="taskStatusFilter">
                                        <option value="all">所有状态</option>
                                        <option value="pending">待处理</option>
                                        <option value="assigned">已分配</option>
                                        <option value="in_progress">进行中</option>
                                        <option value="completed">已完成</option>
                                    </select>
                                    <select id="taskAssigneeFilter">
                                        <option value="all">所有人员</option>
                                        <option value="me">我的任务</option>
                                        <option value="unassigned">未分配</option>
                                    </select>
                                    <button class="btn-secondary" onclick="workflowEngine.refreshTasks()">
                                        <i class="fas fa-sync"></i>
                                        刷新
                                    </button>
                                </div>
                            </div>
                            
                            <div class="task-board">
                                <div class="task-column" data-status="pending">
                                    <div class="column-header">
                                        <h5>待处理</h5>
                                        <span class="task-count" id="pendingCount">0</span>
                                    </div>
                                    <div class="task-list" id="pendingTasks">
                                        <!-- 待处理任务 -->
                                    </div>
                                </div>
                                
                                <div class="task-column" data-status="assigned">
                                    <div class="column-header">
                                        <h5>已分配</h5>
                                        <span class="task-count" id="assignedCount">0</span>
                                    </div>
                                    <div class="task-list" id="assignedTasks">
                                        <!-- 已分配任务 -->
                                    </div>
                                </div>
                                
                                <div class="task-column" data-status="in_progress">
                                    <div class="column-header">
                                        <h5>进行中</h5>
                                        <span class="task-count" id="inProgressCount">0</span>
                                    </div>
                                    <div class="task-list" id="inProgressTasks">
                                        <!-- 进行中任务 -->
                                    </div>
                                </div>
                                
                                <div class="task-column" data-status="completed">
                                    <div class="column-header">
                                        <h5>已完成</h5>
                                        <span class="task-count" id="completedCount">0</span>
                                    </div>
                                    <div class="task-list" id="completedTasks">
                                        <!-- 已完成任务 -->
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 表单设计 -->
                    <div class="tab-content" id="formsTab">
                        <div class="forms-section">
                            <div class="section-header">
                                <h4>表单设计器</h4>
                                <div class="form-controls">
                                    <select id="formSelector">
                                        <option value="">选择表单</option>
                                    </select>
                                    <button class="btn-secondary" onclick="workflowEngine.importForm()">
                                        <i class="fas fa-upload"></i>
                                        导入
                                    </button>
                                    <button class="btn-primary" onclick="workflowEngine.createForm()">
                                        <i class="fas fa-plus"></i>
                                        新建表单
                                    </button>
                                </div>
                            </div>
                            
                            <div class="form-designer">
                                <div class="form-toolbar">
                                    <div class="field-palette">
                                        <h5>字段组件</h5>
                                        <div class="field-category">
                                            <h6>基础字段</h6>
                                            <div class="field-item" data-type="text" draggable="true">
                                                <i class="fas fa-font"></i>
                                                <span>文本框</span>
                                            </div>
                                            <div class="field-item" data-type="textarea" draggable="true">
                                                <i class="fas fa-align-left"></i>
                                                <span>多行文本</span>
                                            </div>
                                            <div class="field-item" data-type="select" draggable="true">
                                                <i class="fas fa-list"></i>
                                                <span>下拉选择</span>
                                            </div>
                                            <div class="field-item" data-type="radio" draggable="true">
                                                <i class="fas fa-dot-circle"></i>
                                                <span>单选按钮</span>
                                            </div>
                                            <div class="field-item" data-type="checkbox" draggable="true">
                                                <i class="fas fa-check-square"></i>
                                                <span>复选框</span>
                                            </div>
                                        </div>
                                        
                                        <div class="field-category">
                                            <h6>高级字段</h6>
                                            <div class="field-item" data-type="date" draggable="true">
                                                <i class="fas fa-calendar"></i>
                                                <span>日期选择</span>
                                            </div>
                                            <div class="field-item" data-type="file" draggable="true">
                                                <i class="fas fa-file-upload"></i>
                                                <span>文件上传</span>
                                            </div>
                                            <div class="field-item" data-type="number" draggable="true">
                                                <i class="fas fa-hashtag"></i>
                                                <span>数字输入</span>
                                            </div>
                                            <div class="field-item" data-type="email" draggable="true">
                                                <i class="fas fa-envelope"></i>
                                                <span>邮箱输入</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="form-canvas">
                                    <div class="form-preview" id="formPreview">
                                        <div class="form-placeholder">
                                            <i class="fas fa-wpforms"></i>
                                            <p>拖拽字段到这里开始设计表单</p>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="form-properties">
                                    <div class="form-properties-panel" id="formPropertiesPanel">
                                        <h5>字段属性</h5>
                                        <div class="form-properties-content" id="formPropertiesContent">
                                            <div class="no-field-selected">
                                                <i class="fas fa-mouse-pointer"></i>
                                                <p>选择一个字段查看属性</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 流程分析 -->
                    <div class="tab-content" id="analyticsTab">
                        <div class="analytics-section">
                            <div class="section-header">
                                <h4>流程分析</h4>
                                <div class="analytics-controls">
                                    <select id="analyticsTimeRange">
                                        <option value="7d">最近7天</option>
                                        <option value="30d">最近30天</option>
                                        <option value="90d">最近90天</option>
                                        <option value="1y">最近1年</option>
                                    </select>
                                    <button class="btn-secondary" onclick="workflowEngine.refreshAnalytics()">
                                        <i class="fas fa-sync"></i>
                                        刷新
                                    </button>
                                    <button class="btn-primary" onclick="workflowEngine.generateReport()">
                                        <i class="fas fa-file-alt"></i>
                                        生成报告
                                    </button>
                                </div>
                            </div>
                            
                            <div class="analytics-overview">
                                <div class="analytics-metrics">
                                    <div class="metric-card">
                                        <div class="metric-icon">
                                            <i class="fas fa-play"></i>
                                        </div>
                                        <div class="metric-content">
                                            <div class="metric-value" id="totalInstances">0</div>
                                            <div class="metric-label">总实例数</div>
                                            <div class="metric-trend">
                                                <i class="fas fa-arrow-up"></i>
                                                <span>+12%</span>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="metric-card">
                                        <div class="metric-icon">
                                            <i class="fas fa-clock"></i>
                                        </div>
                                        <div class="metric-content">
                                            <div class="metric-value" id="avgDuration">0</div>
                                            <div class="metric-label">平均耗时</div>
                                            <div class="metric-trend">
                                                <i class="fas fa-arrow-down"></i>
                                                <span>-8%</span>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="metric-card">
                                        <div class="metric-icon">
                                            <i class="fas fa-percentage"></i>
                                        </div>
                                        <div class="metric-content">
                                            <div class="metric-value" id="completionRate">0%</div>
                                            <div class="metric-label">完成率</div>
                                            <div class="metric-trend">
                                                <i class="fas fa-arrow-up"></i>
                                                <span>+5%</span>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="metric-card">
                                        <div class="metric-icon">
                                            <i class="fas fa-exclamation-triangle"></i>
                                        </div>
                                        <div class="metric-content">
                                            <div class="metric-value" id="errorRate">0%</div>
                                            <div class="metric-label">错误率</div>
                                            <div class="metric-trend">
                                                <i class="fas fa-arrow-down"></i>
                                                <span>-2%</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="analytics-charts">
                                    <div class="chart-container">
                                        <h5>流程执行趋势</h5>
                                        <canvas id="executionTrendChart" width="400" height="200"></canvas>
                                    </div>
                                    
                                    <div class="chart-container">
                                        <h5>任务分布</h5>
                                        <canvas id="taskDistributionChart" width="400" height="200"></canvas>
                                    </div>
                                </div>
                                
                                <div class="bottleneck-analysis">
                                    <h5>瓶颈分析</h5>
                                    <div class="bottleneck-list" id="bottleneckList">
                                        <!-- 瓶颈分析结果 -->
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 系统集成 -->
                    <div class="tab-content" id="integrationsTab">
                        <div class="integrations-section">
                            <div class="section-header">
                                <h4>系统集成</h4>
                                <div class="integration-controls">
                                    <button class="btn-secondary" onclick="workflowEngine.testIntegration()">
                                        <i class="fas fa-vial"></i>
                                        测试连接
                                    </button>
                                    <button class="btn-primary" onclick="workflowEngine.addIntegration()">
                                        <i class="fas fa-plus"></i>
                                        添加集成
                                    </button>
                                </div>
                            </div>
                            
                            <div class="integration-categories">
                                <div class="category-grid">
                                    <div class="integration-category">
                                        <div class="category-icon">
                                            <i class="fas fa-envelope"></i>
                                        </div>
                                        <h5>邮件系统</h5>
                                        <p>SMTP、Exchange、Gmail等</p>
                                        <div class="integration-status connected">
                                            <i class="fas fa-check-circle"></i>
                                            <span>已连接</span>
                                        </div>
                                    </div>
                                    
                                    <div class="integration-category">
                                        <div class="category-icon">
                                            <i class="fas fa-database"></i>
                                        </div>
                                        <h5>数据库</h5>
                                        <p>MySQL、PostgreSQL、MongoDB等</p>
                                        <div class="integration-status connected">
                                            <i class="fas fa-check-circle"></i>
                                            <span>已连接</span>
                                        </div>
                                    </div>
                                    
                                    <div class="integration-category">
                                        <div class="category-icon">
                                            <i class="fas fa-cloud"></i>
                                        </div>
                                        <h5>云服务</h5>
                                        <p>AWS、Azure、阿里云等</p>
                                        <div class="integration-status disconnected">
                                            <i class="fas fa-times-circle"></i>
                                            <span>未连接</span>
                                        </div>
                                    </div>
                                    
                                    <div class="integration-category">
                                        <div class="category-icon">
                                            <i class="fas fa-comments"></i>
                                        </div>
                                        <h5>通讯工具</h5>
                                        <p>钉钉、企业微信、Slack等</p>
                                        <div class="integration-status connected">
                                            <i class="fas fa-check-circle"></i>
                                            <span>已连接</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="integration-list">
                                <h5>已配置的集成</h5>
                                <div class="integrations-table" id="integrationsTable">
                                    <!-- 集成列表 -->
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
        
        document.body.appendChild(workflowPanel);
        
        // 创建工作流创建向导
        this.createWorkflowWizard();
        
        // 创建任务详情面板
        this.createTaskDetailPanel();
        
        // 创建表单预览模态框
        this.createFormPreviewModal();
    }

    createWorkflowWizard() {
        const wizard = document.createElement('div');
        wizard.id = 'workflowWizard';
        wizard.className = 'workflow-wizard-modal';
        wizard.innerHTML = `
            <div class="modal-content large">
                <div class="modal-header">
                    <h3>创建工作流</h3>
                    <button class="modal-close" onclick="workflowEngine.closeWorkflowWizard()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="modal-body">
                    <div class="wizard-steps">
                        <div class="step active" data-step="1">
                            <div class="step-number">1</div>
                            <div class="step-title">基本信息</div>
                        </div>
                        <div class="step" data-step="2">
                            <div class="step-number">2</div>
                            <div class="step-title">选择模板</div>
                        </div>
                        <div class="step" data-step="3">
                            <div class="step-number">3</div>
                            <div class="step-title">配置参数</div>
                        </div>
                        <div class="step" data-step="4">
                            <div class="step-number">4</div>
                            <div class="step-title">完成创建</div>
                        </div>
                    </div>
                    
                    <div class="wizard-content">
                        <!-- 步骤1: 基本信息 -->
                        <div class="wizard-step active" id="wizardStep1">
                            <h4>工作流基本信息</h4>
                            <div class="form-group">
                                <label>工作流名称</label>
                                <input type="text" id="workflowName" placeholder="输入工作流名称">
                            </div>
                            <div class="form-group">
                                <label>工作流描述</label>
                                <textarea id="workflowDescription" placeholder="描述工作流的用途和功能" rows="3"></textarea>
                            </div>
                            <div class="form-group">
                                <label>工作流分类</label>
                                <select id="workflowCategory">
                                    <option value="approval">审批流程</option>
                                    <option value="business">业务流程</option>
                                    <option value="automation">自动化流程</option>
                                    <option value="integration">集成流程</option>
                                    <option value="custom">自定义流程</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label>优先级</label>
                                <select id="workflowPriority">
                                    <option value="low">低</option>
                                    <option value="medium" selected>中</option>
                                    <option value="high">高</option>
                                    <option value="urgent">紧急</option>
                                </select>
                            </div>
                        </div>
                        
                        <!-- 步骤2: 选择模板 -->
                        <div class="wizard-step" id="wizardStep2">
                            <h4>选择工作流模板</h4>
                            <div class="template-grid">
                                <div class="template-item" data-template="blank">
                                    <div class="template-icon">
                                        <i class="fas fa-file-alt"></i>
                                    </div>
                                    <div class="template-info">
                                        <h5>空白模板</h5>
                                        <p>从头开始创建工作流</p>
                                    </div>
                                </div>
                                
                                <div class="template-item" data-template="approval">
                                    <div class="template-icon">
                                        <i class="fas fa-check-circle"></i>
                                    </div>
                                    <div class="template-info">
                                        <h5>审批流程</h5>
                                        <p>标准的审批工作流模板</p>
                                    </div>
                                </div>
                                
                                <div class="template-item" data-template="sequential">
                                    <div class="template-icon">
                                        <i class="fas fa-arrow-right"></i>
                                    </div>
                                    <div class="template-info">
                                        <h5>顺序流程</h5>
                                        <p>按顺序执行的工作流</p>
                                    </div>
                                </div>
                                
                                <div class="template-item" data-template="parallel">
                                    <div class="template-icon">
                                        <i class="fas fa-code-branch"></i>
                                    </div>
                                    <div class="template-info">
                                        <h5>并行流程</h5>
                                        <p>支持并行执行的工作流</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 步骤3: 配置参数 -->
                        <div class="wizard-step" id="wizardStep3">
                            <h4>配置工作流参数</h4>
                            <div class="config-sections">
                                <div class="config-section">
                                    <h5>执行设置</h5>
                                    <div class="form-group">
                                        <label class="checkbox-label">
                                            <input type="checkbox" id="autoStart">
                                            <span class="checkmark"></span>
                                            自动启动
                                        </label>
                                    </div>
                                    <div class="form-group">
                                        <label class="checkbox-label">
                                            <input type="checkbox" id="enableNotifications">
                                            <span class="checkmark"></span>
                                            启用通知
                                        </label>
                                    </div>
                                    <div class="form-group">
                                        <label class="checkbox-label">
                                            <input type="checkbox" id="enableLogging">
                                            <span class="checkmark"></span>
                                            启用日志记录
                                        </label>
                                    </div>
                                </div>
                                
                                <div class="config-section">
                                    <h5>超时设置</h5>
                                    <div class="form-group">
                                        <label>实例超时时间（小时）</label>
                                        <input type="number" id="instanceTimeout" value="24" min="1" max="168">
                                    </div>
                                    <div class="form-group">
                                        <label>任务超时时间（小时）</label>
                                        <input type="number" id="taskTimeout" value="8" min="1" max="72">
                                    </div>
                                </div>
                                
                                <div class="config-section">
                                    <h5>权限设置</h5>
                                    <div class="form-group">
                                        <label>可启动用户</label>
                                        <select id="startUsers" multiple>
                                            <option value="all">所有用户</option>
                                            <option value="admin">管理员</option>
                                            <option value="manager">经理</option>
                                            <option value="user">普通用户</option>
                                        </select>
                                    </div>
                                    <div class="form-group">
                                        <label>可查看用户</label>
                                        <select id="viewUsers" multiple>
                                            <option value="all">所有用户</option>
                                            <option value="admin">管理员</option>
                                            <option value="manager">经理</option>
                                            <option value="user">普通用户</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 步骤4: 完成创建 -->
                        <div class="wizard-step" id="wizardStep4">
                            <h4>创建完成</h4>
                            <div class="completion-summary">
                                <div class="summary-icon">
                                    <i class="fas fa-check-circle"></i>
                                </div>
                                <h5>工作流创建成功！</h5>
                                <p>您的工作流已经创建完成，现在可以开始设计流程了。</p>
                                
                                <div class="next-actions">
                                    <h6>接下来您可以：</h6>
                                    <ul>
                                        <li>在流程设计器中添加节点和连接</li>
                                        <li>配置每个节点的属性和行为</li>
                                        <li>设计相关的表单</li>
                                        <li>测试和部署工作流</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button class="btn-secondary" id="wizardPrevBtn" onclick="workflowEngine.previousWizardStep()" style="display: none;">
                        <i class="fas fa-arrow-left"></i>
                        上一步
                    </button>
                    <button class="btn-secondary" onclick="workflowEngine.closeWorkflowWizard()">取消</button>
                    <button class="btn-primary" id="wizardNextBtn" onclick="workflowEngine.nextWizardStep()">
                        下一步
                        <i class="fas fa-arrow-right"></i>
                    </button>
                </div>
            </div>
        `;
        
        document.body.appendChild(wizard);
    }

    setupDesigner() {
        // 初始化画布
        this.designerCanvas = document.getElementById('workflowCanvas');
        if (this.designerCanvas) {
            this.setupCanvasEvents();
        }
    }

    setupCanvasEvents() {
        const canvas = this.designerCanvas;
        const overlay = document.getElementById('canvasOverlay');
        
        // 画布拖放事件
        overlay.addEventListener('dragover', (e) => {
            e.preventDefault();
        });
        
        overlay.addEventListener('drop', (e) => {
            e.preventDefault();
            this.handleNodeDrop(e);
        });
        
        // 节点拖拽事件
        document.addEventListener('dragstart', (e) => {
            if (e.target.classList.contains('node-item')) {
                this.draggedNode = e.target.dataset.type;
            }
        });
    }

    handleNodeDrop(e) {
        if (!this.draggedNode) return;
        
        const rect = e.target.getBoundingClientRect();
        const x = e.clientX - rect.left;
        const y = e.clientY - rect.top;
        
        this.addNodeToCanvas(this.draggedNode, x, y);
        this.draggedNode = null;
    }

    addNodeToCanvas(nodeType, x, y) {
        const nodeId = 'node_' + Date.now();
        const node = {
            id: nodeId,
            type: nodeType,
            x: x,
            y: y,
            properties: this.getDefaultNodeProperties(nodeType)
        };
        
        // 添加到当前工作流
        if (!this.currentWorkflow) {
            this.currentWorkflow = {
                id: 'workflow_' + Date.now(),
                name: '新工作流',
                nodes: [],
                connections: []
            };
        }
        
        this.currentWorkflow.nodes.push(node);
        this.renderNode(node);
    }

    renderNode(node) {
        const overlay = document.getElementById('canvasOverlay');
        const nodeElement = document.createElement('div');
        nodeElement.className = `workflow-node ${node.type}`;
        nodeElement.dataset.nodeId = node.id;
        nodeElement.style.left = node.x + 'px';
        nodeElement.style.top = node.y + 'px';
        
        nodeElement.innerHTML = `
            <div class="node-icon">
                <i class="fas fa-${this.getNodeIcon(node.type)}"></i>
            </div>
            <div class="node-label">${this.getNodeLabel(node.type)}</div>
        `;
        
        // 添加点击事件
        nodeElement.addEventListener('click', () => {
            this.selectNode(node);
        });
        
        overlay.appendChild(nodeElement);
    }

    getNodeIcon(nodeType) {
        const icons = {
            'start': 'play-circle',
            'end': 'stop-circle',
            'task': 'square',
            'decision': 'diamond',
            'parallel': 'code-branch',
            'subprocess': 'sitemap',
            'timer': 'clock',
            'script': 'code',
            'email': 'envelope',
            'api': 'plug',
            'database': 'database',
            'webhook': 'link'
        };
        return icons[nodeType] || 'square';
    }

    getNodeLabel(nodeType) {
        const labels = {
            'start': '开始',
            'end': '结束',
            'task': '任务',
            'decision': '决策',
            'parallel': '并行',
            'subprocess': '子流程',
            'timer': '定时器',
            'script': '脚本',
            'email': '邮件',
            'api': 'API调用',
            'database': '数据库',
            'webhook': 'Webhook'
        };
        return labels[nodeType] || nodeType;
    }

    getDefaultNodeProperties(nodeType) {
        const defaults = {
            'start': { name: '开始节点', description: '' },
            'end': { name: '结束节点', description: '' },
            'task': { name: '任务节点', assignee: '', form: '', description: '' },
            'decision': { name: '决策节点', condition: '', description: '' },
            'parallel': { name: '并行节点', branches: 2, description: '' },
            'subprocess': { name: '子流程节点', workflow: '', description: '' },
            'timer': { name: '定时器节点', duration: '1h', description: '' },
            'script': { name: '脚本节点', script: '', language: 'javascript', description: '' },
            'email': { name: '邮件节点', to: '', subject: '', template: '', description: '' },
            'api': { name: 'API节点', url: '', method: 'GET', headers: {}, description: '' },
            'database': { name: '数据库节点', query: '', connection: '', description: '' },
            'webhook': { name: 'Webhook节点', url: '', method: 'POST', description: '' }
        };
        return defaults[nodeType] || { name: nodeType, description: '' };
    }

    selectNode(node) {
        // 清除之前的选择
        document.querySelectorAll('.workflow-node.selected').forEach(el => {
            el.classList.remove('selected');
        });
        
        // 选择当前节点
        const nodeElement = document.querySelector(`[data-node-id="${node.id}"]`);
        if (nodeElement) {
            nodeElement.classList.add('selected');
        }
        
        this.selectedNode = node;
        this.showNodeProperties(node);
    }

    showNodeProperties(node) {
        const propertiesContent = document.getElementById('propertiesContent');
        if (!propertiesContent) return;
        
        propertiesContent.innerHTML = `
            <div class="node-properties">
                <h5>${this.getNodeLabel(node.type)}属性</h5>
                <div class="property-group">
                    <label>节点名称</label>
                    <input type="text" value="${node.properties.name}" onchange="workflowEngine.updateNodeProperty('${node.id}', 'name', this.value)">
                </div>
                <div class="property-group">
                    <label>描述</label>
                    <textarea onchange="workflowEngine.updateNodeProperty('${node.id}', 'description', this.value)">${node.properties.description || ''}</textarea>
                </div>
                ${this.getSpecificProperties(node)}
                <div class="property-actions">
                    <button class="btn-secondary" onclick="workflowEngine.duplicateNode('${node.id}')">
                        <i class="fas fa-copy"></i>
                        复制
                    </button>
                    <button class="btn-danger" onclick="workflowEngine.deleteNode('${node.id}')">
                        <i class="fas fa-trash"></i>
                        删除
                    </button>
                </div>
            </div>
        `;
    }

    getSpecificProperties(node) {
        switch (node.type) {
            case 'task':
                return `
                    <div class="property-group">
                        <label>分配给</label>
                        <select onchange="workflowEngine.updateNodeProperty('${node.id}', 'assignee', this.value)">
                            <option value="">选择用户</option>
                            <option value="admin" ${node.properties.assignee === 'admin' ? 'selected' : ''}>管理员</option>
                            <option value="manager" ${node.properties.assignee === 'manager' ? 'selected' : ''}>经理</option>
                            <option value="user" ${node.properties.assignee === 'user' ? 'selected' : ''}>用户</option>
                        </select>
                    </div>
                    <div class="property-group">
                        <label>关联表单</label>
                        <select onchange="workflowEngine.updateNodeProperty('${node.id}', 'form', this.value)">
                            <option value="">选择表单</option>
                        </select>
                    </div>
                `;
            case 'decision':
                return `
                    <div class="property-group">
                        <label>判断条件</label>
                        <textarea onchange="workflowEngine.updateNodeProperty('${node.id}', 'condition', this.value)">${node.properties.condition || ''}</textarea>
                    </div>
                `;
            case 'timer':
                return `
                    <div class="property-group">
                        <label>等待时间</label>
                        <input type="text" value="${node.properties.duration}" onchange="workflowEngine.updateNodeProperty('${node.id}', 'duration', this.value)" placeholder="例如: 1h, 30m, 2d">
                    </div>
                `;
            case 'email':
                return `
                    <div class="property-group">
                        <label>收件人</label>
                        <input type="text" value="${node.properties.to}" onchange="workflowEngine.updateNodeProperty('${node.id}', 'to', this.value)">
                    </div>
                    <div class="property-group">
                        <label>邮件主题</label>
                        <input type="text" value="${node.properties.subject}" onchange="workflowEngine.updateNodeProperty('${node.id}', 'subject', this.value)">
                    </div>
                `;
            default:
                return '';
        }
    }

    updateNodeProperty(nodeId, property, value) {
        if (!this.currentWorkflow) return;
        
        const node = this.currentWorkflow.nodes.find(n => n.id === nodeId);
        if (node) {
            node.properties[property] = value;
            
            // 更新节点显示
            if (property === 'name') {
                const nodeElement = document.querySelector(`[data-node-id="${nodeId}"] .node-label`);
                if (nodeElement) {
                    nodeElement.textContent = value;
                }
            }
        }
    }

    bindWorkflowEvents() {
        // 标签切换
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('tab-btn') && e.target.closest('.workflow-panel')) {
                this.switchWorkflowTab(e.target);
            }
            
            if (e.target.classList.contains('template-item')) {
                this.selectTemplate(e.target);
            }
        });
        
        // 工作流选择器变化
        document.getElementById('workflowSelector')?.addEventListener('change', (e) => {
            this.loadWorkflow(e.target.value);
        });
    }

    switchWorkflowTab(tabBtn) {
        const container = tabBtn.closest('.workflow-panel');
        const tabName = tabBtn.dataset.tab;
        
        // 更新标签状态
        container.querySelectorAll('.tab-btn').forEach(btn => btn.classList.remove('active'));
        container.querySelectorAll('.tab-content').forEach(content => content.classList.remove('active'));
        
        tabBtn.classList.add('active');
        container.querySelector(`#${tabName}Tab`).classList.add('active');
        
        // 加载对应内容
        this.loadWorkflowTabContent(tabName);
    }

    loadWorkflowTabContent(tabName) {
        switch (tabName) {
            case 'designer':
                this.refreshDesigner();
                break;
            case 'instances':
                this.refreshInstances();
                break;
            case 'tasks':
                this.refreshTasks();
                break;
            case 'forms':
                this.refreshForms();
                break;
            case 'analytics':
                this.refreshAnalytics();
                break;
            case 'integrations':
                this.refreshIntegrations();
                break;
        }
    }

    loadWorkflowTemplates() {
        // 加载工作流模板
        this.templates = [
            {
                id: 'approval',
                name: '审批流程',
                description: '标准的审批工作流模板',
                category: 'approval',
                nodes: [
                    { type: 'start', x: 100, y: 100 },
                    { type: 'task', x: 250, y: 100 },
                    { type: 'decision', x: 400, y: 100 },
                    { type: 'end', x: 550, y: 100 }
                ]
            },
            {
                id: 'sequential',
                name: '顺序流程',
                description: '按顺序执行的工作流',
                category: 'business',
                nodes: [
                    { type: 'start', x: 100, y: 100 },
                    { type: 'task', x: 250, y: 100 },
                    { type: 'task', x: 400, y: 100 },
                    { type: 'end', x: 550, y: 100 }
                ]
            }
        ];
    }

    setupTaskEngine() {
        // 设置任务引擎
        console.log('任务引擎已设置');
    }

    startMonitoring() {
        // 启动监控
        setInterval(() => {
            this.updateInstanceMetrics();
            this.checkTaskTimeouts();
        }, 30000); // 每30秒更新一次
    }

    loadWorkflowData() {
        // 加载工作流数据
        const savedData = localStorage.getItem('workflowEngineData');
        if (savedData) {
            const data = JSON.parse(savedData);
            this.workflows = data.workflows || [];
            this.workflowInstances = data.instances || [];
            this.tasks = data.tasks || [];
            this.forms = data.forms || [];
        }
        
        this.updateWorkflowSelector();
    }

    updateWorkflowSelector() {
        const selector = document.getElementById('workflowSelector');
        if (selector) {
            selector.innerHTML = '<option value="">选择工作流</option>' +
                this.workflows.map(wf => 
                    `<option value="${wf.id}">${wf.name}</option>`
                ).join('');
        }
    }

    // 面板控制方法
    showWorkflowPanel() {
        const panel = document.getElementById('workflowPanel');
        if (panel) {
            panel.classList.add('show');
            this.refreshDesigner();
        }
    }

    closeWorkflowPanel() {
        const panel = document.getElementById('workflowPanel');
        if (panel) {
            panel.classList.remove('show');
        }
    }

    // 其他方法的占位符
    createWorkflow() {
        const wizard = document.getElementById('workflowWizard');
        if (wizard) {
            wizard.style.display = 'flex';
        }
    }

    closeWorkflowWizard() {
        const wizard = document.getElementById('workflowWizard');
        if (wizard) {
            wizard.style.display = 'none';
        }
    }

    refreshDesigner() {
        // 刷新设计器画布
        this.clearCanvas();
        if (this.currentWorkflow && this.currentWorkflow.nodes) {
            this.currentWorkflow.nodes.forEach(node => {
                this.renderNode(node);
            });
        }
    }

    clearCanvas() {
        const overlay = document.getElementById('canvasOverlay');
        if (overlay) {
            overlay.innerHTML = '';
        }
    }

    refreshInstances() {
        // 生成模拟实例数据
        this.generateMockInstances();
        this.renderInstancesList();
        this.updateInstanceStats();
    }

    generateMockInstances() {
        if (this.workflowInstances.length === 0) {
            const statuses = ['running', 'completed', 'suspended', 'terminated'];
            const workflows = ['审批流程', '采购流程', '请假流程', '报销流程'];

            for (let i = 0; i < 20; i++) {
                this.workflowInstances.push({
                    id: 'instance_' + i,
                    workflowName: workflows[Math.floor(Math.random() * workflows.length)],
                    status: statuses[Math.floor(Math.random() * statuses.length)],
                    startTime: new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000),
                    endTime: Math.random() > 0.5 ? new Date() : null,
                    initiator: 'User' + (i % 5 + 1),
                    currentTask: Math.random() > 0.3 ? '审批任务' : null,
                    progress: Math.floor(Math.random() * 100)
                });
            }
        }
    }

    renderInstancesList() {
        const instancesList = document.getElementById('instancesList');
        if (!instancesList) return;

        const filteredInstances = this.getFilteredInstances();

        instancesList.innerHTML = filteredInstances.map(instance => `
            <div class="instance-item ${instance.status}">
                <div class="instance-header">
                    <div class="instance-info">
                        <h6>${instance.workflowName}</h6>
                        <span class="instance-id">ID: ${instance.id}</span>
                    </div>
                    <div class="instance-status">
                        <span class="status-badge ${instance.status}">${this.getStatusName(instance.status)}</span>
                    </div>
                </div>
                <div class="instance-details">
                    <div class="instance-meta">
                        <span class="initiator">发起人: ${instance.initiator}</span>
                        <span class="start-time">开始时间: ${instance.startTime.toLocaleString()}</span>
                        ${instance.currentTask ? `<span class="current-task">当前任务: ${instance.currentTask}</span>` : ''}
                    </div>
                    <div class="instance-progress">
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: ${instance.progress}%"></div>
                        </div>
                        <span class="progress-text">${instance.progress}%</span>
                    </div>
                </div>
                <div class="instance-actions">
                    <button class="btn-icon" onclick="workflowEngine.viewInstance('${instance.id}')" title="查看详情">
                        <i class="fas fa-eye"></i>
                    </button>
                    ${instance.status === 'running' ? `
                        <button class="btn-icon" onclick="workflowEngine.suspendInstance('${instance.id}')" title="暂停">
                            <i class="fas fa-pause"></i>
                        </button>
                    ` : ''}
                    ${instance.status === 'suspended' ? `
                        <button class="btn-icon" onclick="workflowEngine.resumeInstance('${instance.id}')" title="恢复">
                            <i class="fas fa-play"></i>
                        </button>
                    ` : ''}
                    <button class="btn-icon" onclick="workflowEngine.terminateInstance('${instance.id}')" title="终止">
                        <i class="fas fa-stop"></i>
                    </button>
                </div>
            </div>
        `).join('');
    }

    getFilteredInstances() {
        const statusFilter = document.getElementById('instanceStatusFilter')?.value || 'all';

        return this.workflowInstances.filter(instance => {
            if (statusFilter !== 'all' && instance.status !== statusFilter) return false;
            return true;
        });
    }

    getStatusName(status) {
        const names = {
            'running': '运行中',
            'completed': '已完成',
            'suspended': '已暂停',
            'terminated': '已终止'
        };
        return names[status] || status;
    }

    updateInstanceStats() {
        const stats = {
            running: this.workflowInstances.filter(i => i.status === 'running').length,
            completed: this.workflowInstances.filter(i => i.status === 'completed').length,
            suspended: this.workflowInstances.filter(i => i.status === 'suspended').length,
            terminated: this.workflowInstances.filter(i => i.status === 'terminated').length
        };

        document.getElementById('runningInstances').textContent = stats.running;
        document.getElementById('completedInstances').textContent = stats.completed;
        document.getElementById('suspendedInstances').textContent = stats.suspended;
        document.getElementById('terminatedInstances').textContent = stats.terminated;
    }

    refreshTasks() {
        // 生成模拟任务数据
        this.generateMockTasks();
        this.renderTaskBoard();
    }

    generateMockTasks() {
        if (this.tasks.length === 0) {
            const taskTypes = ['审批任务', '审核任务', '处理任务', '确认任务'];
            const assignees = ['Alice', 'Bob', 'Charlie', 'Diana'];
            const statuses = ['pending', 'assigned', 'in_progress', 'completed'];

            for (let i = 0; i < 30; i++) {
                this.tasks.push({
                    id: 'task_' + i,
                    name: taskTypes[Math.floor(Math.random() * taskTypes.length)],
                    description: '这是一个示例任务描述',
                    status: statuses[Math.floor(Math.random() * statuses.length)],
                    assignee: Math.random() > 0.3 ? assignees[Math.floor(Math.random() * assignees.length)] : null,
                    priority: ['low', 'medium', 'high'][Math.floor(Math.random() * 3)],
                    dueDate: new Date(Date.now() + Math.random() * 7 * 24 * 60 * 60 * 1000),
                    createdAt: new Date(Date.now() - Math.random() * 3 * 24 * 60 * 60 * 1000),
                    workflowInstance: 'instance_' + Math.floor(Math.random() * 10)
                });
            }
        }
    }

    renderTaskBoard() {
        const statuses = ['pending', 'assigned', 'in_progress', 'completed'];

        statuses.forEach(status => {
            const tasks = this.tasks.filter(task => task.status === status);
            const taskList = document.getElementById(status + 'Tasks');
            const taskCount = document.getElementById(status + 'Count');

            if (taskCount) {
                taskCount.textContent = tasks.length;
            }

            if (taskList) {
                taskList.innerHTML = tasks.map(task => `
                    <div class="task-card ${task.priority}" draggable="true" data-task-id="${task.id}">
                        <div class="task-header">
                            <h6>${task.name}</h6>
                            <span class="priority-badge ${task.priority}">${this.getPriorityName(task.priority)}</span>
                        </div>
                        <div class="task-description">${task.description}</div>
                        <div class="task-meta">
                            ${task.assignee ? `<span class="assignee">分配给: ${task.assignee}</span>` : '<span class="unassigned">未分配</span>'}
                            <span class="due-date">截止: ${task.dueDate.toLocaleDateString()}</span>
                        </div>
                        <div class="task-actions">
                            <button class="btn-icon" onclick="workflowEngine.viewTask('${task.id}')" title="查看详情">
                                <i class="fas fa-eye"></i>
                            </button>
                            ${task.status === 'pending' ? `
                                <button class="btn-icon" onclick="workflowEngine.assignTask('${task.id}')" title="分配任务">
                                    <i class="fas fa-user-plus"></i>
                                </button>
                            ` : ''}
                            ${task.status === 'assigned' ? `
                                <button class="btn-icon" onclick="workflowEngine.startTask('${task.id}')" title="开始任务">
                                    <i class="fas fa-play"></i>
                                </button>
                            ` : ''}
                            ${task.status === 'in_progress' ? `
                                <button class="btn-icon" onclick="workflowEngine.completeTask('${task.id}')" title="完成任务">
                                    <i class="fas fa-check"></i>
                                </button>
                            ` : ''}
                        </div>
                    </div>
                `).join('');
            }
        });
    }

    getPriorityName(priority) {
        const names = {
            'low': '低',
            'medium': '中',
            'high': '高'
        };
        return names[priority] || priority;
    }

    refreshForms() {
        // 生成模拟表单数据
        this.generateMockForms();
        this.updateFormSelector();
    }

    generateMockForms() {
        if (this.forms.length === 0) {
            this.forms = [
                {
                    id: 'form_approval',
                    name: '审批申请表',
                    description: '标准审批流程表单',
                    fields: [
                        { type: 'text', name: 'title', label: '申请标题', required: true },
                        { type: 'textarea', name: 'description', label: '申请描述', required: true },
                        { type: 'select', name: 'priority', label: '优先级', options: ['低', '中', '高'] },
                        { type: 'date', name: 'deadline', label: '期望完成日期' }
                    ]
                },
                {
                    id: 'form_leave',
                    name: '请假申请表',
                    description: '员工请假申请表单',
                    fields: [
                        { type: 'select', name: 'type', label: '请假类型', options: ['年假', '病假', '事假', '调休'] },
                        { type: 'date', name: 'startDate', label: '开始日期', required: true },
                        { type: 'date', name: 'endDate', label: '结束日期', required: true },
                        { type: 'textarea', name: 'reason', label: '请假原因', required: true }
                    ]
                }
            ];
        }
    }

    updateFormSelector() {
        const selector = document.getElementById('formSelector');
        if (selector) {
            selector.innerHTML = '<option value="">选择表单</option>' +
                this.forms.map(form =>
                    `<option value="${form.id}">${form.name}</option>`
                ).join('');
        }
    }

    refreshAnalytics() {
        // 更新分析数据
        this.updateAnalyticsMetrics();
        this.renderAnalyticsCharts();
        this.renderBottleneckAnalysis();
    }

    updateAnalyticsMetrics() {
        // 计算分析指标
        const totalInstances = this.workflowInstances.length;
        const completedInstances = this.workflowInstances.filter(i => i.status === 'completed');
        const avgDuration = this.calculateAverageDuration(completedInstances);
        const completionRate = totalInstances > 0 ? Math.round((completedInstances.length / totalInstances) * 100) : 0;
        const errorRate = Math.floor(Math.random() * 5); // 模拟错误率

        document.getElementById('totalInstances').textContent = totalInstances;
        document.getElementById('avgDuration').textContent = avgDuration;
        document.getElementById('completionRate').textContent = completionRate + '%';
        document.getElementById('errorRate').textContent = errorRate + '%';
    }

    calculateAverageDuration(instances) {
        if (instances.length === 0) return '0小时';

        const totalDuration = instances.reduce((sum, instance) => {
            if (instance.endTime && instance.startTime) {
                return sum + (instance.endTime - instance.startTime);
            }
            return sum;
        }, 0);

        const avgMs = totalDuration / instances.length;
        const avgHours = Math.round(avgMs / (1000 * 60 * 60));

        return avgHours + '小时';
    }

    renderAnalyticsCharts() {
        // 渲染执行趋势图
        const trendCanvas = document.getElementById('executionTrendChart');
        if (trendCanvas) {
            const ctx = trendCanvas.getContext('2d');
            ctx.fillStyle = '#667eea';
            ctx.fillRect(10, 10, trendCanvas.width - 20, trendCanvas.height - 20);

            ctx.fillStyle = 'white';
            ctx.font = '14px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('流程执行趋势图', trendCanvas.width / 2, trendCanvas.height / 2);
        }

        // 渲染任务分布图
        const distributionCanvas = document.getElementById('taskDistributionChart');
        if (distributionCanvas) {
            const ctx = distributionCanvas.getContext('2d');
            ctx.fillStyle = '#764ba2';
            ctx.fillRect(10, 10, distributionCanvas.width - 20, distributionCanvas.height - 20);

            ctx.fillStyle = 'white';
            ctx.font = '14px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('任务分布图', distributionCanvas.width / 2, distributionCanvas.height / 2);
        }
    }

    renderBottleneckAnalysis() {
        const bottleneckList = document.getElementById('bottleneckList');
        if (!bottleneckList) return;

        // 模拟瓶颈分析数据
        const bottlenecks = [
            {
                node: '审批节点',
                avgTime: '2.5小时',
                instances: 15,
                severity: 'high'
            },
            {
                node: '数据验证节点',
                avgTime: '1.8小时',
                instances: 8,
                severity: 'medium'
            },
            {
                node: '文档生成节点',
                avgTime: '45分钟',
                instances: 12,
                severity: 'low'
            }
        ];

        bottleneckList.innerHTML = bottlenecks.map(bottleneck => `
            <div class="bottleneck-item ${bottleneck.severity}">
                <div class="bottleneck-info">
                    <h6>${bottleneck.node}</h6>
                    <div class="bottleneck-stats">
                        <span class="avg-time">平均耗时: ${bottleneck.avgTime}</span>
                        <span class="instance-count">影响实例: ${bottleneck.instances}</span>
                    </div>
                </div>
                <div class="bottleneck-severity">
                    <span class="severity-badge ${bottleneck.severity}">${this.getSeverityName(bottleneck.severity)}</span>
                </div>
            </div>
        `).join('');
    }

    getSeverityName(severity) {
        const names = {
            'low': '轻微',
            'medium': '中等',
            'high': '严重'
        };
        return names[severity] || severity;
    }

    refreshIntegrations() {
        // 渲染集成列表
        this.renderIntegrationsTable();
    }

    renderIntegrationsTable() {
        const integrationsTable = document.getElementById('integrationsTable');
        if (!integrationsTable) return;

        // 模拟集成数据
        const integrations = [
            {
                id: 'int_email',
                name: 'SMTP邮件服务',
                type: 'email',
                status: 'connected',
                lastTest: new Date(Date.now() - 2 * 60 * 60 * 1000),
                config: { host: 'smtp.example.com', port: 587 }
            },
            {
                id: 'int_db',
                name: 'MySQL数据库',
                type: 'database',
                status: 'connected',
                lastTest: new Date(Date.now() - 30 * 60 * 1000),
                config: { host: 'localhost', database: 'workflow' }
            },
            {
                id: 'int_api',
                name: '第三方API',
                type: 'api',
                status: 'disconnected',
                lastTest: new Date(Date.now() - 24 * 60 * 60 * 1000),
                config: { baseUrl: 'https://api.example.com' }
            }
        ];

        integrationsTable.innerHTML = `
            <div class="integrations-header">
                <div class="header-cell">名称</div>
                <div class="header-cell">类型</div>
                <div class="header-cell">状态</div>
                <div class="header-cell">最后测试</div>
                <div class="header-cell">操作</div>
            </div>
            <div class="integrations-body">
                ${integrations.map(integration => `
                    <div class="integration-row">
                        <div class="integration-cell">
                            <div class="integration-name">${integration.name}</div>
                            <div class="integration-id">${integration.id}</div>
                        </div>
                        <div class="integration-cell">
                            <span class="type-badge ${integration.type}">${this.getIntegrationTypeName(integration.type)}</span>
                        </div>
                        <div class="integration-cell">
                            <span class="status-badge ${integration.status}">${this.getIntegrationStatusName(integration.status)}</span>
                        </div>
                        <div class="integration-cell">
                            ${integration.lastTest.toLocaleString()}
                        </div>
                        <div class="integration-cell">
                            <div class="integration-actions">
                                <button class="btn-icon" onclick="workflowEngine.testIntegration('${integration.id}')" title="测试连接">
                                    <i class="fas fa-vial"></i>
                                </button>
                                <button class="btn-icon" onclick="workflowEngine.editIntegration('${integration.id}')" title="编辑">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button class="btn-icon" onclick="workflowEngine.deleteIntegration('${integration.id}')" title="删除">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                `).join('')}
            </div>
        `;
    }

    getIntegrationTypeName(type) {
        const names = {
            'email': '邮件',
            'database': '数据库',
            'api': 'API',
            'webhook': 'Webhook'
        };
        return names[type] || type;
    }

    getIntegrationStatusName(status) {
        const names = {
            'connected': '已连接',
            'disconnected': '未连接',
            'error': '错误'
        };
        return names[status] || status;
    }

    updateInstanceMetrics() {
        // 更新实例指标
        this.updateInstanceStats();
    }

    checkTaskTimeouts() {
        // 检查任务超时
        const now = new Date();
        this.tasks.forEach(task => {
            if (task.status === 'in_progress' && task.dueDate < now) {
                console.log(`任务 ${task.id} 已超时`);
            }
        });
    }
}

// 全局工作流引擎实例
let workflowEngine = null;

// 初始化工作流引擎
function initializeWorkflowEngine() {
    workflowEngine = new WorkflowEngine();
    console.log('✅ 高级工作流引擎已初始化');
}

// 显示工作流面板
function showWorkflowPanel() {
    if (workflowEngine) {
        workflowEngine.showWorkflowPanel();
    }
}

// 页面加载时初始化
document.addEventListener('DOMContentLoaded', function() {
    setTimeout(initializeWorkflowEngine, 1900);
});
