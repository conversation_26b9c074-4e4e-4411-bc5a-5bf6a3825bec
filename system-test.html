<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>系统功能测试</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 16px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .test-header {
            background: linear-gradient(135deg, #6366f1, #818cf8);
            color: white;
            padding: 24px;
            text-align: center;
        }
        
        .test-header h1 {
            font-size: 28px;
            margin-bottom: 8px;
        }
        
        .test-content {
            padding: 24px;
        }
        
        .test-section {
            margin-bottom: 32px;
            border: 1px solid #e2e8f0;
            border-radius: 12px;
            overflow: hidden;
        }
        
        .section-header {
            background: #f8fafc;
            padding: 16px 20px;
            border-bottom: 1px solid #e2e8f0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .section-title {
            font-size: 18px;
            font-weight: 600;
            color: #1e293b;
        }
        
        .section-content {
            padding: 20px;
        }
        
        .test-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 16px;
            margin-bottom: 20px;
        }
        
        .test-card {
            border: 2px solid #e2e8f0;
            border-radius: 8px;
            padding: 16px;
            transition: all 0.3s;
            cursor: pointer;
        }
        
        .test-card:hover {
            border-color: #6366f1;
            box-shadow: 0 4px 12px rgba(99, 102, 241, 0.15);
        }
        
        .test-card.success {
            border-color: #10b981;
            background: #f0fdf4;
        }
        
        .test-card.error {
            border-color: #ef4444;
            background: #fef2f2;
        }
        
        .test-card.running {
            border-color: #f59e0b;
            background: #fffbeb;
        }
        
        .card-header {
            display: flex;
            align-items: center;
            gap: 12px;
            margin-bottom: 12px;
        }
        
        .card-icon {
            width: 40px;
            height: 40px;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 18px;
        }
        
        .card-icon.success { background: #dcfce7; color: #166534; }
        .card-icon.error { background: #fecaca; color: #dc2626; }
        .card-icon.running { background: #fef3c7; color: #d97706; }
        .card-icon.pending { background: #e0e7ff; color: #4338ca; }
        
        .card-title {
            font-weight: 600;
            color: #1e293b;
        }
        
        .card-description {
            color: #64748b;
            font-size: 14px;
            margin-bottom: 12px;
        }
        
        .btn {
            background: #6366f1;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.2s;
            margin: 4px;
        }
        
        .btn:hover {
            background: #4f46e5;
        }
        
        .btn-sm {
            padding: 6px 12px;
            font-size: 12px;
        }
        
        .btn-success { background: #10b981; }
        .btn-success:hover { background: #059669; }
        
        .btn-danger { background: #ef4444; }
        .btn-danger:hover { background: #dc2626; }
        
        .status-indicator {
            display: inline-flex;
            align-items: center;
            gap: 6px;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
        }
        
        .status-success {
            background: #dcfce7;
            color: #166534;
        }
        
        .status-error {
            background: #fecaca;
            color: #dc2626;
        }
        
        .status-running {
            background: #fef3c7;
            color: #d97706;
        }
        
        .status-pending {
            background: #e0e7ff;
            color: #4338ca;
        }
        
        .test-log {
            background: #f8fafc;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            padding: 16px;
            font-family: 'Courier New', monospace;
            font-size: 13px;
            max-height: 200px;
            overflow-y: auto;
            margin-top: 16px;
        }
        
        .log-entry {
            margin-bottom: 4px;
            padding: 2px 0;
        }
        
        .log-success { color: #059669; }
        .log-error { color: #dc2626; }
        .log-info { color: #0284c7; }
        .log-warning { color: #d97706; }
        
        @keyframes spin {
            to { transform: rotate(360deg); }
        }
        
        .spinning {
            animation: spin 1s linear infinite;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-header">
            <h1><i class="fas fa-vial"></i> 系统功能测试中心</h1>
            <p>全面测试现代企业管理系统的各项功能</p>
        </div>
        
        <div class="test-content">
            <!-- 登录功能测试 -->
            <div class="test-section">
                <div class="section-header">
                    <h2 class="section-title">🔐 登录功能测试</h2>
                    <button class="btn btn-sm" onclick="runAllLoginTests()">
                        <i class="fas fa-play"></i> 运行所有测试
                    </button>
                </div>
                <div class="section-content">
                    <div class="test-grid" id="loginTests">
                        <!-- 登录测试卡片将通过JS生成 -->
                    </div>
                </div>
            </div>
            
            <!-- 页面功能测试 -->
            <div class="test-section">
                <div class="section-header">
                    <h2 class="section-title">📄 页面功能测试</h2>
                    <button class="btn btn-sm" onclick="runPageTests()">
                        <i class="fas fa-play"></i> 测试页面
                    </button>
                </div>
                <div class="section-content">
                    <div class="test-grid" id="pageTests">
                        <!-- 页面测试卡片将通过JS生成 -->
                    </div>
                </div>
            </div>
            
            <!-- 浏览器兼容性测试 -->
            <div class="test-section">
                <div class="section-header">
                    <h2 class="section-title">🌐 浏览器兼容性测试</h2>
                    <button class="btn btn-sm" onclick="runCompatibilityTests()">
                        <i class="fas fa-play"></i> 检查兼容性
                    </button>
                </div>
                <div class="section-content">
                    <div class="test-grid" id="compatibilityTests">
                        <!-- 兼容性测试卡片将通过JS生成 -->
                    </div>
                </div>
            </div>
            
            <!-- 测试日志 -->
            <div class="test-section">
                <div class="section-header">
                    <h2 class="section-title">📋 测试日志</h2>
                    <button class="btn btn-sm btn-danger" onclick="clearLog()">
                        <i class="fas fa-trash"></i> 清除日志
                    </button>
                </div>
                <div class="section-content">
                    <div class="test-log" id="testLog">
                        <div class="log-entry log-info">[INFO] 测试系统已初始化</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="system-test.js"></script>
</body>
</html>
