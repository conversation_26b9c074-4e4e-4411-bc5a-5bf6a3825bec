// 数据导入导出系统
class DataImportExport {
    constructor() {
        this.supportedFormats = {
            export: ['csv', 'xlsx', 'json', 'pdf'],
            import: ['csv', 'xlsx', 'json']
        };
        this.exportTemplates = {
            users: {
                fields: ['username', 'name', 'email', 'role', 'department', 'status', 'createdAt'],
                headers: ['用户名', '姓名', '邮箱', '角色', '部门', '状态', '创建时间']
            },
            orders: {
                fields: ['orderId', 'customerName', 'customerEmail', 'product', 'amount', 'status', 'orderDate'],
                headers: ['订单号', '客户姓名', '客户邮箱', '产品', '金额', '状态', '订单日期']
            },
            files: {
                fields: ['filename', 'type', 'size', 'uploadedBy', 'uploadDate', 'tags'],
                headers: ['文件名', '类型', '大小', '上传者', '上传日期', '标签']
            }
        };
        
        this.initializeImportExport();
    }

    initializeImportExport() {
        this.createImportExportModal();
        this.bindEvents();
    }

    createImportExportModal() {
        const modal = document.createElement('div');
        modal.id = 'importExportModal';
        modal.className = 'modal import-export-modal';
        modal.innerHTML = `
            <div class="modal-content import-export-content">
                <div class="modal-header">
                    <h3 id="importExportTitle">数据导入导出</h3>
                    <button class="modal-close" onclick="dataImportExport.closeModal()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                
                <div class="modal-body">
                    <div class="import-export-tabs">
                        <button class="tab-btn active" data-tab="export">
                            <i class="fas fa-download"></i>
                            数据导出
                        </button>
                        <button class="tab-btn" data-tab="import">
                            <i class="fas fa-upload"></i>
                            数据导入
                        </button>
                    </div>
                    
                    <!-- 导出面板 -->
                    <div class="tab-content active" id="exportPanel">
                        <div class="export-options">
                            <div class="option-group">
                                <label>选择数据类型</label>
                                <div class="data-type-selector">
                                    <label class="checkbox-label">
                                        <input type="checkbox" name="exportType" value="users" checked>
                                        <span class="checkmark"></span>
                                        用户数据
                                    </label>
                                    <label class="checkbox-label">
                                        <input type="checkbox" name="exportType" value="orders" checked>
                                        <span class="checkmark"></span>
                                        订单数据
                                    </label>
                                    <label class="checkbox-label">
                                        <input type="checkbox" name="exportType" value="files">
                                        <span class="checkmark"></span>
                                        文件数据
                                    </label>
                                </div>
                            </div>
                            
                            <div class="option-group">
                                <label>导出格式</label>
                                <div class="format-selector">
                                    <label class="radio-label">
                                        <input type="radio" name="exportFormat" value="csv" checked>
                                        <span class="radio-mark"></span>
                                        CSV格式
                                    </label>
                                    <label class="radio-label">
                                        <input type="radio" name="exportFormat" value="xlsx">
                                        <span class="radio-mark"></span>
                                        Excel格式
                                    </label>
                                    <label class="radio-label">
                                        <input type="radio" name="exportFormat" value="json">
                                        <span class="radio-mark"></span>
                                        JSON格式
                                    </label>
                                    <label class="radio-label">
                                        <input type="radio" name="exportFormat" value="pdf">
                                        <span class="radio-mark"></span>
                                        PDF格式
                                    </label>
                                </div>
                            </div>
                            
                            <div class="option-group">
                                <label>时间范围</label>
                                <div class="date-range-selector">
                                    <input type="date" id="exportStartDate" placeholder="开始日期">
                                    <span>至</span>
                                    <input type="date" id="exportEndDate" placeholder="结束日期">
                                </div>
                            </div>
                            
                            <div class="option-group">
                                <label>高级选项</label>
                                <div class="advanced-options">
                                    <label class="checkbox-label">
                                        <input type="checkbox" id="includeHeaders" checked>
                                        <span class="checkmark"></span>
                                        包含表头
                                    </label>
                                    <label class="checkbox-label">
                                        <input type="checkbox" id="compressFile">
                                        <span class="checkmark"></span>
                                        压缩文件
                                    </label>
                                    <label class="checkbox-label">
                                        <input type="checkbox" id="encryptFile">
                                        <span class="checkmark"></span>
                                        加密文件
                                    </label>
                                </div>
                            </div>
                        </div>
                        
                        <div class="export-preview" id="exportPreview">
                            <h4>导出预览</h4>
                            <div class="preview-content">
                                <p>请选择导出选项以查看预览</p>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 导入面板 -->
                    <div class="tab-content" id="importPanel">
                        <div class="import-options">
                            <div class="option-group">
                                <label>选择导入文件</label>
                                <div class="file-upload-area" id="fileUploadArea">
                                    <div class="upload-icon">
                                        <i class="fas fa-cloud-upload-alt"></i>
                                    </div>
                                    <div class="upload-text">
                                        <p>拖拽文件到此处或点击选择文件</p>
                                        <small>支持 CSV, Excel, JSON 格式</small>
                                    </div>
                                    <input type="file" id="importFileInput" accept=".csv,.xlsx,.xls,.json" style="display: none;">
                                </div>
                            </div>
                            
                            <div class="option-group" id="importOptionsGroup" style="display: none;">
                                <label>导入选项</label>
                                <div class="import-settings">
                                    <label class="checkbox-label">
                                        <input type="checkbox" id="skipFirstRow" checked>
                                        <span class="checkmark"></span>
                                        跳过第一行（表头）
                                    </label>
                                    <label class="checkbox-label">
                                        <input type="checkbox" id="validateData" checked>
                                        <span class="checkmark"></span>
                                        验证数据格式
                                    </label>
                                    <label class="checkbox-label">
                                        <input type="checkbox" id="updateExisting">
                                        <span class="checkmark"></span>
                                        更新现有数据
                                    </label>
                                </div>
                            </div>
                            
                            <div class="import-mapping" id="importMapping" style="display: none;">
                                <h4>字段映射</h4>
                                <div class="mapping-table" id="mappingTable">
                                    <!-- 字段映射表格将在这里生成 -->
                                </div>
                            </div>
                        </div>
                        
                        <div class="import-preview" id="importPreview" style="display: none;">
                            <h4>导入预览</h4>
                            <div class="preview-stats" id="previewStats">
                                <!-- 导入统计信息 -->
                            </div>
                            <div class="preview-table" id="previewTable">
                                <!-- 预览表格 -->
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="modal-footer">
                    <button type="button" class="btn-secondary" onclick="dataImportExport.closeModal()">取消</button>
                    <button type="button" class="btn-primary" id="executeButton" onclick="dataImportExport.executeAction()">
                        <i class="fas fa-download"></i>
                        开始导出
                    </button>
                </div>
            </div>
        `;
        
        document.body.appendChild(modal);
    }

    bindEvents() {
        // 标签切换
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('tab-btn') && e.target.closest('.import-export-modal')) {
                this.switchTab(e.target.dataset.tab);
            }
        });

        // 文件上传区域
        const uploadArea = document.getElementById('fileUploadArea');
        const fileInput = document.getElementById('importFileInput');
        
        if (uploadArea && fileInput) {
            uploadArea.addEventListener('click', () => fileInput.click());
            uploadArea.addEventListener('dragover', this.handleDragOver.bind(this));
            uploadArea.addEventListener('drop', this.handleFileDrop.bind(this));
            fileInput.addEventListener('change', this.handleFileSelect.bind(this));
        }

        // 导出选项变化
        document.addEventListener('change', (e) => {
            if (e.target.name === 'exportType' || e.target.name === 'exportFormat') {
                this.updateExportPreview();
            }
        });
    }

    switchTab(tab) {
        // 切换标签
        document.querySelectorAll('.import-export-modal .tab-btn').forEach(btn => {
            btn.classList.remove('active');
        });
        document.querySelectorAll('.import-export-modal .tab-content').forEach(content => {
            content.classList.remove('active');
        });

        document.querySelector(`[data-tab="${tab}"]`).classList.add('active');
        document.getElementById(`${tab}Panel`).classList.add('active');

        // 更新按钮文本
        const executeButton = document.getElementById('executeButton');
        if (tab === 'export') {
            executeButton.innerHTML = '<i class="fas fa-download"></i> 开始导出';
        } else {
            executeButton.innerHTML = '<i class="fas fa-upload"></i> 开始导入';
        }
    }

    showModal(type = 'export') {
        const modal = document.getElementById('importExportModal');
        if (modal) {
            modal.style.display = 'flex';
            this.switchTab(type);
        }
    }

    closeModal() {
        const modal = document.getElementById('importExportModal');
        if (modal) {
            modal.style.display = 'none';
        }
    }

    handleDragOver(e) {
        e.preventDefault();
        e.stopPropagation();
        e.currentTarget.classList.add('drag-over');
    }

    handleFileDrop(e) {
        e.preventDefault();
        e.stopPropagation();
        e.currentTarget.classList.remove('drag-over');
        
        const files = e.dataTransfer.files;
        if (files.length > 0) {
            this.processImportFile(files[0]);
        }
    }

    handleFileSelect(e) {
        const file = e.target.files[0];
        if (file) {
            this.processImportFile(file);
        }
    }

    processImportFile(file) {
        // 验证文件类型
        const allowedTypes = ['.csv', '.xlsx', '.xls', '.json'];
        const fileExtension = '.' + file.name.split('.').pop().toLowerCase();
        
        if (!allowedTypes.includes(fileExtension)) {
            if (window.showNotification) {
                showNotification('不支持的文件格式', 'error');
            }
            return;
        }

        // 显示文件信息
        this.displayFileInfo(file);
        
        // 读取文件内容
        this.readFileContent(file);
    }

    displayFileInfo(file) {
        const uploadArea = document.getElementById('fileUploadArea');
        uploadArea.innerHTML = `
            <div class="file-info">
                <div class="file-icon">
                    <i class="fas fa-file-${this.getFileIcon(file.name)}"></i>
                </div>
                <div class="file-details">
                    <div class="file-name">${file.name}</div>
                    <div class="file-size">${this.formatFileSize(file.size)}</div>
                </div>
                <button class="btn-icon" onclick="dataImportExport.resetFileUpload()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        `;

        // 显示导入选项
        document.getElementById('importOptionsGroup').style.display = 'block';
    }

    getFileIcon(filename) {
        const extension = filename.split('.').pop().toLowerCase();
        switch (extension) {
            case 'csv': return 'csv';
            case 'xlsx':
            case 'xls': return 'excel';
            case 'json': return 'code';
            default: return 'alt';
        }
    }

    formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    readFileContent(file) {
        const reader = new FileReader();
        
        reader.onload = (e) => {
            const content = e.target.result;
            const extension = file.name.split('.').pop().toLowerCase();
            
            try {
                let data;
                switch (extension) {
                    case 'csv':
                        data = this.parseCSV(content);
                        break;
                    case 'json':
                        data = JSON.parse(content);
                        break;
                    case 'xlsx':
                    case 'xls':
                        // 这里需要使用 SheetJS 库来解析 Excel 文件
                        data = this.parseExcel(content);
                        break;
                    default:
                        throw new Error('不支持的文件格式');
                }
                
                this.displayImportPreview(data);
            } catch (error) {
                if (window.showNotification) {
                    showNotification('文件解析失败: ' + error.message, 'error');
                }
            }
        };
        
        if (file.name.endsWith('.xlsx') || file.name.endsWith('.xls')) {
            reader.readAsArrayBuffer(file);
        } else {
            reader.readAsText(file);
        }
    }

    parseCSV(content) {
        const lines = content.split('\n');
        const headers = lines[0].split(',').map(h => h.trim().replace(/"/g, ''));
        const data = [];
        
        for (let i = 1; i < lines.length; i++) {
            if (lines[i].trim()) {
                const values = lines[i].split(',').map(v => v.trim().replace(/"/g, ''));
                const row = {};
                headers.forEach((header, index) => {
                    row[header] = values[index] || '';
                });
                data.push(row);
            }
        }
        
        return { headers, data };
    }

    parseExcel(content) {
        // 这里应该使用 SheetJS 库，现在先返回模拟数据
        return {
            headers: ['用户名', '姓名', '邮箱'],
            data: [
                { '用户名': 'user1', '姓名': '张三', '邮箱': '<EMAIL>' },
                { '用户名': 'user2', '姓名': '李四', '邮箱': '<EMAIL>' }
            ]
        };
    }

    displayImportPreview(parsedData) {
        const previewContainer = document.getElementById('importPreview');
        const statsContainer = document.getElementById('previewStats');
        const tableContainer = document.getElementById('previewTable');
        
        // 显示统计信息
        statsContainer.innerHTML = `
            <div class="stats-grid">
                <div class="stat-item">
                    <span class="stat-label">总记录数</span>
                    <span class="stat-value">${parsedData.data.length}</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">字段数</span>
                    <span class="stat-value">${parsedData.headers.length}</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">预计导入</span>
                    <span class="stat-value">${parsedData.data.length}</span>
                </div>
            </div>
        `;
        
        // 显示预览表格
        const previewData = parsedData.data.slice(0, 5); // 只显示前5行
        tableContainer.innerHTML = `
            <div class="preview-table-container">
                <table class="preview-table">
                    <thead>
                        <tr>
                            ${parsedData.headers.map(header => `<th>${header}</th>`).join('')}
                        </tr>
                    </thead>
                    <tbody>
                        ${previewData.map(row => `
                            <tr>
                                ${parsedData.headers.map(header => `<td>${row[header] || ''}</td>`).join('')}
                            </tr>
                        `).join('')}
                    </tbody>
                </table>
                ${parsedData.data.length > 5 ? `<p class="preview-note">显示前5条记录，共${parsedData.data.length}条</p>` : ''}
            </div>
        `;
        
        previewContainer.style.display = 'block';
        this.importData = parsedData;
    }

    updateExportPreview() {
        const selectedTypes = Array.from(document.querySelectorAll('input[name="exportType"]:checked')).map(cb => cb.value);
        const format = document.querySelector('input[name="exportFormat"]:checked').value;
        
        const previewContainer = document.getElementById('exportPreview');
        const previewContent = previewContainer.querySelector('.preview-content');
        
        if (selectedTypes.length === 0) {
            previewContent.innerHTML = '<p>请选择要导出的数据类型</p>';
            return;
        }
        
        // 生成预览内容
        let previewHTML = '<div class="export-summary">';
        previewHTML += `<h5>导出格式: ${format.toUpperCase()}</h5>`;
        previewHTML += '<div class="export-items">';
        
        selectedTypes.forEach(type => {
            const template = this.exportTemplates[type];
            if (template) {
                previewHTML += `
                    <div class="export-item">
                        <h6>${this.getTypeLabel(type)}</h6>
                        <p>字段: ${template.headers.join(', ')}</p>
                        <p>预计记录数: ${this.getEstimatedRecords(type)}</p>
                    </div>
                `;
            }
        });
        
        previewHTML += '</div></div>';
        previewContent.innerHTML = previewHTML;
    }

    getTypeLabel(type) {
        const labels = {
            users: '用户数据',
            orders: '订单数据',
            files: '文件数据'
        };
        return labels[type] || type;
    }

    getEstimatedRecords(type) {
        // 模拟记录数
        const counts = {
            users: 156,
            orders: 342,
            files: 89
        };
        return counts[type] || 0;
    }

    resetFileUpload() {
        const uploadArea = document.getElementById('fileUploadArea');
        uploadArea.innerHTML = `
            <div class="upload-icon">
                <i class="fas fa-cloud-upload-alt"></i>
            </div>
            <div class="upload-text">
                <p>拖拽文件到此处或点击选择文件</p>
                <small>支持 CSV, Excel, JSON 格式</small>
            </div>
            <input type="file" id="importFileInput" accept=".csv,.xlsx,.xls,.json" style="display: none;">
        `;
        
        document.getElementById('importOptionsGroup').style.display = 'none';
        document.getElementById('importPreview').style.display = 'none';
        
        // 重新绑定事件
        const fileInput = document.getElementById('importFileInput');
        uploadArea.addEventListener('click', () => fileInput.click());
        fileInput.addEventListener('change', this.handleFileSelect.bind(this));
    }

    executeAction() {
        const activeTab = document.querySelector('.import-export-modal .tab-btn.active').dataset.tab;
        
        if (activeTab === 'export') {
            this.executeExport();
        } else {
            this.executeImport();
        }
    }

    executeExport() {
        const selectedTypes = Array.from(document.querySelectorAll('input[name="exportType"]:checked')).map(cb => cb.value);
        const format = document.querySelector('input[name="exportFormat"]:checked').value;
        const includeHeaders = document.getElementById('includeHeaders').checked;
        const startDate = document.getElementById('exportStartDate').value;
        const endDate = document.getElementById('exportEndDate').value;
        
        if (selectedTypes.length === 0) {
            if (window.showNotification) {
                showNotification('请选择要导出的数据类型', 'warning');
            }
            return;
        }
        
        // 模拟导出过程
        this.showExportProgress();
        
        setTimeout(() => {
            this.generateExportFile(selectedTypes, format, {
                includeHeaders,
                startDate,
                endDate
            });
        }, 2000);
    }

    executeImport() {
        if (!this.importData) {
            if (window.showNotification) {
                showNotification('请先选择要导入的文件', 'warning');
            }
            return;
        }
        
        const skipFirstRow = document.getElementById('skipFirstRow').checked;
        const validateData = document.getElementById('validateData').checked;
        const updateExisting = document.getElementById('updateExisting').checked;
        
        // 模拟导入过程
        this.showImportProgress();
        
        setTimeout(() => {
            this.processImportData(this.importData, {
                skipFirstRow,
                validateData,
                updateExisting
            });
        }, 2000);
    }

    showExportProgress() {
        const executeButton = document.getElementById('executeButton');
        executeButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 导出中...';
        executeButton.disabled = true;
    }

    showImportProgress() {
        const executeButton = document.getElementById('executeButton');
        executeButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 导入中...';
        executeButton.disabled = true;
    }

    generateExportFile(types, format, options) {
        // 生成模拟数据
        const exportData = this.generateMockExportData(types);
        
        let content, filename, mimeType;
        
        switch (format) {
            case 'csv':
                content = this.generateCSV(exportData, options.includeHeaders);
                filename = `export-${new Date().toISOString().split('T')[0]}.csv`;
                mimeType = 'text/csv';
                break;
            case 'json':
                content = JSON.stringify(exportData, null, 2);
                filename = `export-${new Date().toISOString().split('T')[0]}.json`;
                mimeType = 'application/json';
                break;
            case 'xlsx':
                // 这里应该使用 SheetJS 生成 Excel 文件
                content = this.generateCSV(exportData, options.includeHeaders);
                filename = `export-${new Date().toISOString().split('T')[0]}.xlsx`;
                mimeType = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';
                break;
            case 'pdf':
                // 这里应该使用 jsPDF 生成 PDF 文件
                content = this.generateCSV(exportData, options.includeHeaders);
                filename = `export-${new Date().toISOString().split('T')[0]}.pdf`;
                mimeType = 'application/pdf';
                break;
        }
        
        this.downloadFile(content, filename, mimeType);
        this.resetExportButton();
        this.closeModal();
        
        if (window.showNotification) {
            showNotification('数据导出成功', 'success');
        }
    }

    generateMockExportData(types) {
        const data = {};
        
        if (types.includes('users')) {
            data.users = [
                { username: 'admin', name: '张管理员', email: '<EMAIL>', role: '系统管理员', department: '技术部', status: '活跃', createdAt: '2024-01-01' },
                { username: 'manager', name: '李经理', email: '<EMAIL>', role: '部门经理', department: '销售部', status: '活跃', createdAt: '2024-01-02' }
            ];
        }
        
        if (types.includes('orders')) {
            data.orders = [
                { orderId: 'ORD-2024-001', customerName: '张三', customerEmail: '<EMAIL>', product: '产品A', amount: 2599, status: '已完成', orderDate: '2024-01-15' },
                { orderId: 'ORD-2024-002', customerName: '李四', customerEmail: '<EMAIL>', product: '产品B', amount: 1299, status: '处理中', orderDate: '2024-01-16' }
            ];
        }
        
        if (types.includes('files')) {
            data.files = [
                { filename: '用户手册.pdf', type: 'PDF', size: '2.5MB', uploadedBy: '张管理员', uploadDate: '2024-01-10', tags: '文档,手册' },
                { filename: '系统截图.png', type: 'PNG', size: '1.2MB', uploadedBy: '李经理', uploadDate: '2024-01-11', tags: '图片,截图' }
            ];
        }
        
        return data;
    }

    generateCSV(data, includeHeaders) {
        let csv = '';
        
        Object.keys(data).forEach(type => {
            const items = data[type];
            if (items.length === 0) return;
            
            const template = this.exportTemplates[type];
            if (!template) return;
            
            // 添加类型标题
            csv += `\n${this.getTypeLabel(type)}\n`;
            
            // 添加表头
            if (includeHeaders) {
                csv += template.headers.join(',') + '\n';
            }
            
            // 添加数据行
            items.forEach(item => {
                const row = template.fields.map(field => {
                    const value = item[field] || '';
                    return `"${value}"`;
                });
                csv += row.join(',') + '\n';
            });
            
            csv += '\n';
        });
        
        return csv;
    }

    downloadFile(content, filename, mimeType) {
        const blob = new Blob([content], { type: mimeType });
        const url = URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = filename;
        link.click();
        URL.revokeObjectURL(url);
    }

    processImportData(data, options) {
        // 模拟数据导入处理
        let processedCount = 0;
        let errorCount = 0;
        
        data.data.forEach(row => {
            if (options.validateData) {
                // 简单的数据验证
                const hasRequiredFields = Object.values(row).some(value => value && value.trim());
                if (hasRequiredFields) {
                    processedCount++;
                } else {
                    errorCount++;
                }
            } else {
                processedCount++;
            }
        });
        
        this.resetImportButton();
        this.closeModal();
        
        if (window.showNotification) {
            showNotification(`数据导入完成：成功 ${processedCount} 条，失败 ${errorCount} 条`, 'success');
        }
    }

    resetExportButton() {
        const executeButton = document.getElementById('executeButton');
        executeButton.innerHTML = '<i class="fas fa-download"></i> 开始导出';
        executeButton.disabled = false;
    }

    resetImportButton() {
        const executeButton = document.getElementById('executeButton');
        executeButton.innerHTML = '<i class="fas fa-upload"></i> 开始导入';
        executeButton.disabled = false;
    }
}

// 全局数据导入导出实例
let dataImportExport = null;

// 初始化数据导入导出系统
function initializeDataImportExport() {
    dataImportExport = new DataImportExport();
    console.log('✅ 数据导入导出系统已初始化');
}

// 显示导入导出模态框
function showImportExportModal(type = 'export') {
    if (dataImportExport) {
        dataImportExport.showModal(type);
    }
}

// 页面加载时初始化
document.addEventListener('DOMContentLoaded', function() {
    setTimeout(initializeDataImportExport, 700);
});
