// 系统设置功能
class SystemSettingsManager {
    constructor() {
        this.settings = {
            profile: {
                name: '',
                email: '',
                phone: '',
                department: '',
                bio: ''
            },
            interface: {
                theme: 'light',
                sidebarExpanded: true,
                animationsEnabled: true,
                language: 'zh-CN'
            },
            notifications: {
                desktop: true,
                email: true,
                sound: false,
                frequency: 'realtime'
            },
            security: {
                autoLogout: 'never',
                twoFactorAuth: false
            },
            data: {
                autoBackup: true,
                backupFrequency: 'daily'
            }
        };
        
        this.loadSettings();
        this.initializeSettings();
    }
    
    loadSettings() {
        // 从localStorage加载设置
        const savedSettings = localStorage.getItem('systemSettings');
        if (savedSettings) {
            try {
                const parsed = JSON.parse(savedSettings);
                this.settings = { ...this.settings, ...parsed };
            } catch (error) {
                console.error('加载设置失败:', error);
            }
        }
        
        // 从当前用户信息加载个人资料
        const currentUser = localStorage.getItem('currentUser');
        if (currentUser) {
            try {
                const user = JSON.parse(currentUser);
                this.settings.profile = {
                    name: user.name || user.username || '',
                    email: user.email || '',
                    phone: user.phone || '',
                    department: user.department || '',
                    bio: user.bio || ''
                };
            } catch (error) {
                console.error('加载用户信息失败:', error);
            }
        }
    }
    
    saveSettings() {
        try {
            localStorage.setItem('systemSettings', JSON.stringify(this.settings));
            
            // 更新当前用户信息
            const currentUser = localStorage.getItem('currentUser');
            if (currentUser) {
                const user = JSON.parse(currentUser);
                user.name = this.settings.profile.name;
                user.email = this.settings.profile.email;
                user.phone = this.settings.profile.phone;
                user.department = this.settings.profile.department;
                user.bio = this.settings.profile.bio;
                localStorage.setItem('currentUser', JSON.stringify(user));
            }
            
            return true;
        } catch (error) {
            console.error('保存设置失败:', error);
            return false;
        }
    }
    
    initializeSettings() {
        // 初始化个人资料
        this.initializeProfile();
        
        // 初始化界面设置
        this.initializeInterface();
        
        // 初始化通知设置
        this.initializeNotifications();
        
        // 初始化安全设置
        this.initializeSecurity();
        
        // 初始化数据设置
        this.initializeData();
        
        // 应用主题设置
        this.applyTheme();
        
        // 绑定事件监听器
        this.bindEventListeners();
    }
    
    initializeProfile() {
        const profile = this.settings.profile;
        
        document.getElementById('profileName').value = profile.name;
        document.getElementById('profileEmail').value = profile.email;
        document.getElementById('profilePhone').value = profile.phone;
        document.getElementById('profileDepartment').value = profile.department;
        document.getElementById('profileBio').value = profile.bio;
    }
    
    initializeInterface() {
        const interface = this.settings.interface;
        
        document.getElementById('themeMode').value = interface.theme;
        document.getElementById('sidebarExpanded').checked = interface.sidebarExpanded;
        document.getElementById('animationsEnabled').checked = interface.animationsEnabled;
        document.getElementById('languageSelect').value = interface.language;
    }
    
    initializeNotifications() {
        const notifications = this.settings.notifications;
        
        document.getElementById('desktopNotifications').checked = notifications.desktop;
        document.getElementById('emailNotifications').checked = notifications.email;
        document.getElementById('soundNotifications').checked = notifications.sound;
        document.getElementById('notificationFrequency').value = notifications.frequency;
    }
    
    initializeSecurity() {
        const security = this.settings.security;
        
        document.getElementById('autoLogout').value = security.autoLogout;
        document.getElementById('twoFactorAuth').checked = security.twoFactorAuth;
    }
    
    initializeData() {
        const data = this.settings.data;
        
        document.getElementById('autoBackup').checked = data.autoBackup;
        document.getElementById('backupFrequency').value = data.backupFrequency;
    }
    
    bindEventListeners() {
        // 个人资料变更监听
        ['profileName', 'profileEmail', 'profilePhone', 'profileDepartment', 'profileBio'].forEach(id => {
            const element = document.getElementById(id);
            if (element) {
                element.addEventListener('change', () => this.updateProfileSetting(id));
            }
        });
        
        // 界面设置变更监听
        document.getElementById('themeMode').addEventListener('change', () => this.updateTheme());
        document.getElementById('sidebarExpanded').addEventListener('change', () => this.updateSidebarState());
        document.getElementById('animationsEnabled').addEventListener('change', () => this.updateAnimations());
        document.getElementById('languageSelect').addEventListener('change', () => this.updateLanguage());
        
        // 通知设置变更监听
        ['desktopNotifications', 'emailNotifications', 'soundNotifications'].forEach(id => {
            document.getElementById(id).addEventListener('change', () => this.updateNotificationSetting(id));
        });
        document.getElementById('notificationFrequency').addEventListener('change', () => this.updateNotificationFrequency());
        
        // 安全设置变更监听
        document.getElementById('autoLogout').addEventListener('change', () => this.updateAutoLogout());
        document.getElementById('twoFactorAuth').addEventListener('change', () => this.updateTwoFactorAuth());
        
        // 数据设置变更监听
        document.getElementById('autoBackup').addEventListener('change', () => this.updateAutoBackup());
        document.getElementById('backupFrequency').addEventListener('change', () => this.updateBackupFrequency());
    }
    
    updateProfileSetting(fieldId) {
        const element = document.getElementById(fieldId);
        const field = fieldId.replace('profile', '').toLowerCase();
        
        this.settings.profile[field] = element.value;
        this.saveSettings();
        
        // 如果是姓名，更新用户菜单显示
        if (field === 'name' && window.userMenuManager) {
            userMenuManager.updateUserDisplay();
        }
    }
    
    updateTheme() {
        const theme = document.getElementById('themeMode').value;
        this.settings.interface.theme = theme;
        this.applyTheme();
        this.saveSettings();
        
        showNotification(`主题已切换为${theme === 'light' ? '浅色' : theme === 'dark' ? '深色' : '自动'}模式`, 'success');
    }
    
    applyTheme() {
        const theme = this.settings.interface.theme;
        const body = document.body;
        
        // 移除现有主题类
        body.classList.remove('theme-light', 'theme-dark', 'theme-auto');
        
        // 应用新主题
        if (theme === 'dark') {
            body.classList.add('theme-dark');
        } else if (theme === 'auto') {
            body.classList.add('theme-auto');
            // 检测系统主题偏好
            if (window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches) {
                body.classList.add('theme-dark');
            }
        } else {
            body.classList.add('theme-light');
        }
    }
    
    updateSidebarState() {
        const expanded = document.getElementById('sidebarExpanded').checked;
        this.settings.interface.sidebarExpanded = expanded;
        this.saveSettings();
        
        // 应用侧边栏状态
        const sidebar = document.querySelector('.sidebar');
        if (sidebar) {
            if (expanded) {
                sidebar.classList.remove('collapsed');
            } else {
                sidebar.classList.add('collapsed');
            }
        }
        
        showNotification(`侧边栏默认状态已设置为${expanded ? '展开' : '收起'}`, 'info');
    }
    
    updateAnimations() {
        const enabled = document.getElementById('animationsEnabled').checked;
        this.settings.interface.animationsEnabled = enabled;
        this.saveSettings();
        
        // 应用动画设置
        const body = document.body;
        if (enabled) {
            body.classList.remove('no-animations');
        } else {
            body.classList.add('no-animations');
        }
        
        showNotification(`动画效果已${enabled ? '启用' : '禁用'}`, 'info');
    }
    
    updateLanguage() {
        const language = document.getElementById('languageSelect').value;
        this.settings.interface.language = language;
        this.saveSettings();
        
        showNotification('语言设置已保存，刷新页面后生效', 'info');
    }
    
    updateNotificationSetting(settingId) {
        const element = document.getElementById(settingId);
        const setting = settingId.replace('Notifications', '').replace('desktop', 'desktop').replace('email', 'email').replace('sound', 'sound');
        
        this.settings.notifications[setting] = element.checked;
        this.saveSettings();
        
        // 请求桌面通知权限
        if (settingId === 'desktopNotifications' && element.checked) {
            this.requestNotificationPermission();
        }
        
        showNotification(`${setting}通知已${element.checked ? '启用' : '禁用'}`, 'info');
    }
    
    updateNotificationFrequency() {
        const frequency = document.getElementById('notificationFrequency').value;
        this.settings.notifications.frequency = frequency;
        this.saveSettings();
        
        showNotification(`通知频率已设置为${frequency}`, 'info');
    }
    
    updateAutoLogout() {
        const timeout = document.getElementById('autoLogout').value;
        this.settings.security.autoLogout = timeout;
        this.saveSettings();
        
        // 设置自动登出定时器
        this.setupAutoLogout();
        
        showNotification(`自动登出时间已设置为${timeout === 'never' ? '从不' : timeout + '分钟'}`, 'info');
    }
    
    updateTwoFactorAuth() {
        const enabled = document.getElementById('twoFactorAuth').checked;
        this.settings.security.twoFactorAuth = enabled;
        this.saveSettings();
        
        showNotification(`双因素认证已${enabled ? '启用' : '禁用'}`, enabled ? 'success' : 'warning');
    }
    
    updateAutoBackup() {
        const enabled = document.getElementById('autoBackup').checked;
        this.settings.data.autoBackup = enabled;
        this.saveSettings();
        
        showNotification(`自动备份已${enabled ? '启用' : '禁用'}`, 'info');
    }
    
    updateBackupFrequency() {
        const frequency = document.getElementById('backupFrequency').value;
        this.settings.data.backupFrequency = frequency;
        this.saveSettings();
        
        showNotification(`备份频率已设置为${frequency}`, 'info');
    }
    
    requestNotificationPermission() {
        if ('Notification' in window) {
            Notification.requestPermission().then(permission => {
                if (permission === 'granted') {
                    showNotification('桌面通知权限已授予', 'success');
                } else {
                    showNotification('桌面通知权限被拒绝', 'warning');
                }
            });
        }
    }
    
    setupAutoLogout() {
        const timeout = this.settings.security.autoLogout;
        
        // 清除现有定时器
        if (this.autoLogoutTimer) {
            clearTimeout(this.autoLogoutTimer);
        }
        
        if (timeout !== 'never') {
            const timeoutMs = parseInt(timeout) * 60 * 1000;
            
            this.autoLogoutTimer = setTimeout(() => {
                if (confirm('由于长时间无操作，系统将自动登出。是否继续保持登录？')) {
                    this.setupAutoLogout(); // 重新设置定时器
                } else {
                    if (window.userMenuManager) {
                        userMenuManager.performLogout();
                    }
                }
            }, timeoutMs);
        }
    }
    
    resetAllSettings() {
        if (confirm('确定要重置所有设置吗？此操作不可撤销。')) {
            // 重置为默认设置
            this.settings = {
                profile: { name: '', email: '', phone: '', department: '', bio: '' },
                interface: { theme: 'light', sidebarExpanded: true, animationsEnabled: true, language: 'zh-CN' },
                notifications: { desktop: true, email: true, sound: false, frequency: 'realtime' },
                security: { autoLogout: 'never', twoFactorAuth: false },
                data: { autoBackup: true, backupFrequency: 'daily' }
            };
            
            this.saveSettings();
            this.initializeSettings();
            
            showNotification('所有设置已重置为默认值', 'success');
        }
    }
    
    saveAllSettings() {
        if (this.saveSettings()) {
            showNotification('所有设置已保存', 'success');
        } else {
            showNotification('保存设置失败', 'error');
        }
    }
}

// 全局设置管理器实例
let settingsManager = null;

// 设置相关的全局函数
function saveAllSettings() {
    if (settingsManager) {
        settingsManager.saveAllSettings();
    }
}

function resetSettings() {
    if (settingsManager) {
        settingsManager.resetAllSettings();
    }
}

function viewLoginHistory() {
    showNotification('登录历史功能开发中...', 'info');
}

function showChangePasswordModal() {
    showNotification('修改密码功能开发中...', 'info');
}

function exportUserData() {
    if (settingsManager) {
        const userData = {
            settings: settingsManager.settings,
            exportTime: new Date().toISOString(),
            version: '1.0.0'
        };
        
        const jsonContent = JSON.stringify(userData, null, 2);
        const blob = new Blob([jsonContent], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        
        const a = document.createElement('a');
        a.href = url;
        a.download = `user_data_${new Date().toISOString().slice(0, 10)}.json`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
        
        showNotification('用户数据已导出', 'success');
    }
}

function clearCache() {
    if (confirm('确定要清除所有缓存数据吗？这将清除浏览器中的临时数据。')) {
        try {
            // 清除localStorage中的缓存数据（保留重要设置）
            const importantKeys = ['isLoggedIn', 'currentUser', 'systemSettings'];
            const allKeys = Object.keys(localStorage);
            
            allKeys.forEach(key => {
                if (!importantKeys.includes(key)) {
                    localStorage.removeItem(key);
                }
            });
            
            // 清除sessionStorage
            sessionStorage.clear();
            
            showNotification('缓存已清除', 'success');
        } catch (error) {
            console.error('清除缓存失败:', error);
            showNotification('清除缓存失败', 'error');
        }
    }
}

// 初始化系统设置
function initializeSettings() {
    settingsManager = new SystemSettingsManager();
    console.log('✅ 系统设置已初始化');
}

// 页面加载时初始化
document.addEventListener('DOMContentLoaded', function() {
    // 延迟初始化，确保页面元素已加载
    setTimeout(() => {
        if (document.getElementById('settings')) {
            initializeSettings();
        }
    }, 200);
});
