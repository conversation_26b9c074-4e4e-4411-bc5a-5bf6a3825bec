// 系统功能测试脚本

// 测试账号数据
const TEST_ACCOUNTS = [
    { username: 'admin', password: 'admin123', role: '系统管理员', name: '张管理员' },
    { username: 'manager', password: 'manager123', role: '部门经理', name: '李经理' },
    { username: 'user', password: 'user123', role: '普通用户', name: '王用户' },
    { username: 'test', password: 'test123', role: '测试用户', name: '测试员' },
    { username: 'demo', password: 'demo123', role: '演示用户', name: '演示员' }
];

// 页面测试配置
const PAGE_TESTS = [
    { name: '登录页面', url: 'login.html', description: '检查登录页面是否正常加载' },
    { name: '后台管理', url: 'index.html', description: '检查后台管理页面是否正常' },
    { name: '测试页面', url: 'test.html', description: '检查测试页面功能' }
];

// 浏览器兼容性检查
const COMPATIBILITY_TESTS = [
    { name: 'LocalStorage', test: () => typeof(Storage) !== "undefined" },
    { name: 'ES6 支持', test: () => { try { eval('const x = () => {}'); return true; } catch(e) { return false; } } },
    { name: 'CSS Grid', test: () => CSS.supports('display', 'grid') },
    { name: 'CSS Flexbox', test: () => CSS.supports('display', 'flex') },
    { name: 'Fetch API', test: () => typeof fetch !== 'undefined' },
    { name: 'Promise', test: () => typeof Promise !== 'undefined' }
];

// 日志记录
function log(message, type = 'info') {
    const logContainer = document.getElementById('testLog');
    const timestamp = new Date().toLocaleTimeString();
    const logEntry = document.createElement('div');
    logEntry.className = `log-entry log-${type}`;
    logEntry.textContent = `[${timestamp}] [${type.toUpperCase()}] ${message}`;
    logContainer.appendChild(logEntry);
    logContainer.scrollTop = logContainer.scrollHeight;
}

// 清除日志
function clearLog() {
    document.getElementById('testLog').innerHTML = '<div class="log-entry log-info">[INFO] 日志已清除</div>';
}

// 更新测试卡片状态
function updateTestCard(cardId, status, message = '') {
    const card = document.getElementById(cardId);
    if (!card) return;
    
    // 移除所有状态类
    card.classList.remove('success', 'error', 'running');
    
    // 添加新状态
    card.classList.add(status);
    
    // 更新图标
    const icon = card.querySelector('.card-icon');
    const iconElement = icon.querySelector('i');
    
    icon.className = `card-icon ${status}`;
    
    switch(status) {
        case 'success':
            iconElement.className = 'fas fa-check';
            break;
        case 'error':
            iconElement.className = 'fas fa-times';
            break;
        case 'running':
            iconElement.className = 'fas fa-spinner spinning';
            break;
        default:
            iconElement.className = 'fas fa-clock';
    }
    
    // 更新状态指示器
    const statusIndicator = card.querySelector('.status-indicator');
    if (statusIndicator) {
        statusIndicator.className = `status-indicator status-${status}`;
        statusIndicator.innerHTML = `<i class="fas fa-circle"></i> ${getStatusText(status)}`;
    }
    
    // 更新描述
    if (message) {
        const description = card.querySelector('.card-description');
        if (description) {
            description.textContent = message;
        }
    }
}

function getStatusText(status) {
    const statusTexts = {
        'pending': '等待中',
        'running': '运行中',
        'success': '成功',
        'error': '失败'
    };
    return statusTexts[status] || '未知';
}

// 生成登录测试卡片
function generateLoginTestCards() {
    const container = document.getElementById('loginTests');
    
    TEST_ACCOUNTS.forEach((account, index) => {
        const card = document.createElement('div');
        card.className = 'test-card';
        card.id = `login-test-${index}`;
        
        card.innerHTML = `
            <div class="card-header">
                <div class="card-icon pending">
                    <i class="fas fa-clock"></i>
                </div>
                <div>
                    <div class="card-title">测试账号: ${account.username}</div>
                    <div class="status-indicator status-pending">
                        <i class="fas fa-circle"></i> 等待中
                    </div>
                </div>
            </div>
            <div class="card-description">测试 ${account.role} 账号登录功能</div>
            <button class="btn btn-sm" onclick="testSingleLogin(${index})">
                <i class="fas fa-play"></i> 单独测试
            </button>
            <button class="btn btn-sm btn-success" onclick="quickLogin(${index})">
                <i class="fas fa-sign-in-alt"></i> 快速登录
            </button>
        `;
        
        container.appendChild(card);
    });
}

// 生成页面测试卡片
function generatePageTestCards() {
    const container = document.getElementById('pageTests');
    
    PAGE_TESTS.forEach((page, index) => {
        const card = document.createElement('div');
        card.className = 'test-card';
        card.id = `page-test-${index}`;
        
        card.innerHTML = `
            <div class="card-header">
                <div class="card-icon pending">
                    <i class="fas fa-clock"></i>
                </div>
                <div>
                    <div class="card-title">${page.name}</div>
                    <div class="status-indicator status-pending">
                        <i class="fas fa-circle"></i> 等待中
                    </div>
                </div>
            </div>
            <div class="card-description">${page.description}</div>
            <button class="btn btn-sm" onclick="testSinglePage(${index})">
                <i class="fas fa-play"></i> 测试页面
            </button>
            <button class="btn btn-sm btn-success" onclick="openPage('${page.url}')">
                <i class="fas fa-external-link-alt"></i> 打开页面
            </button>
        `;
        
        container.appendChild(card);
    });
}

// 生成兼容性测试卡片
function generateCompatibilityTestCards() {
    const container = document.getElementById('compatibilityTests');
    
    COMPATIBILITY_TESTS.forEach((test, index) => {
        const card = document.createElement('div');
        card.className = 'test-card';
        card.id = `compat-test-${index}`;
        
        card.innerHTML = `
            <div class="card-header">
                <div class="card-icon pending">
                    <i class="fas fa-clock"></i>
                </div>
                <div>
                    <div class="card-title">${test.name}</div>
                    <div class="status-indicator status-pending">
                        <i class="fas fa-circle"></i> 等待中
                    </div>
                </div>
            </div>
            <div class="card-description">检查浏览器对 ${test.name} 的支持</div>
            <button class="btn btn-sm" onclick="testSingleCompatibility(${index})">
                <i class="fas fa-play"></i> 检查支持
            </button>
        `;
        
        container.appendChild(card);
    });
}

// 测试单个登录账号
async function testSingleLogin(index) {
    const account = TEST_ACCOUNTS[index];
    const cardId = `login-test-${index}`;
    
    updateTestCard(cardId, 'running', '正在测试登录功能...');
    log(`开始测试账号: ${account.username}`, 'info');
    
    try {
        // 模拟登录验证
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        // 检查账号信息是否完整
        if (!account.username || !account.password || !account.name || !account.role) {
            throw new Error('账号信息不完整');
        }
        
        updateTestCard(cardId, 'success', `${account.role} 账号测试通过`);
        log(`账号 ${account.username} 测试成功`, 'success');
        
    } catch (error) {
        updateTestCard(cardId, 'error', `测试失败: ${error.message}`);
        log(`账号 ${account.username} 测试失败: ${error.message}`, 'error');
    }
}

// 快速登录
function quickLogin(index) {
    const account = TEST_ACCOUNTS[index];
    
    // 保存登录状态
    localStorage.setItem('isLoggedIn', 'true');
    localStorage.setItem('currentUser', JSON.stringify({
        username: account.username,
        name: account.name,
        role: account.role
    }));
    
    log(`使用账号 ${account.username} 快速登录成功`, 'success');
    
    if (confirm(`已使用 ${account.name} 账号登录，是否跳转到后台管理？`)) {
        window.open('index.html', '_blank');
    }
}

// 运行所有登录测试
async function runAllLoginTests() {
    log('开始运行所有登录测试', 'info');
    
    for (let i = 0; i < TEST_ACCOUNTS.length; i++) {
        await testSingleLogin(i);
        await new Promise(resolve => setTimeout(resolve, 500)); // 间隔500ms
    }
    
    log('所有登录测试完成', 'success');
}

// 测试单个页面
async function testSinglePage(index) {
    const page = PAGE_TESTS[index];
    const cardId = `page-test-${index}`;
    
    updateTestCard(cardId, 'running', '正在检查页面...');
    log(`开始测试页面: ${page.name}`, 'info');
    
    try {
        // 检查文件是否存在（简单检查）
        await new Promise(resolve => setTimeout(resolve, 800));
        
        updateTestCard(cardId, 'success', '页面检查通过');
        log(`页面 ${page.name} 测试成功`, 'success');
        
    } catch (error) {
        updateTestCard(cardId, 'error', `页面检查失败: ${error.message}`);
        log(`页面 ${page.name} 测试失败: ${error.message}`, 'error');
    }
}

// 打开页面
function openPage(url) {
    window.open(url, '_blank');
    log(`打开页面: ${url}`, 'info');
}

// 运行页面测试
async function runPageTests() {
    log('开始运行页面测试', 'info');
    
    for (let i = 0; i < PAGE_TESTS.length; i++) {
        await testSinglePage(i);
        await new Promise(resolve => setTimeout(resolve, 300));
    }
    
    log('所有页面测试完成', 'success');
}

// 测试单个兼容性
function testSingleCompatibility(index) {
    const test = COMPATIBILITY_TESTS[index];
    const cardId = `compat-test-${index}`;
    
    updateTestCard(cardId, 'running', '正在检查兼容性...');
    log(`开始检查: ${test.name}`, 'info');
    
    try {
        const result = test.test();
        
        if (result) {
            updateTestCard(cardId, 'success', '支持此功能');
            log(`${test.name} 兼容性检查通过`, 'success');
        } else {
            updateTestCard(cardId, 'error', '不支持此功能');
            log(`${test.name} 兼容性检查失败`, 'warning');
        }
        
    } catch (error) {
        updateTestCard(cardId, 'error', `检查失败: ${error.message}`);
        log(`${test.name} 兼容性检查出错: ${error.message}`, 'error');
    }
}

// 运行兼容性测试
async function runCompatibilityTests() {
    log('开始运行兼容性测试', 'info');
    
    for (let i = 0; i < COMPATIBILITY_TESTS.length; i++) {
        testSingleCompatibility(i);
        await new Promise(resolve => setTimeout(resolve, 200));
    }
    
    log('所有兼容性测试完成', 'success');
}

// 页面初始化
document.addEventListener('DOMContentLoaded', function() {
    log('测试系统初始化中...', 'info');
    
    generateLoginTestCards();
    generatePageTestCards();
    generateCompatibilityTestCards();
    
    log('测试系统初始化完成', 'success');
    
    // 自动运行兼容性测试
    setTimeout(() => {
        runCompatibilityTests();
    }, 1000);
});
