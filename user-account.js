// 用户账户管理功能
class UserAccountManager {
    constructor() {
        this.currentUser = null;
        this.loadCurrentUser();
        this.bindEvents();
    }
    
    loadCurrentUser() {
        const userData = localStorage.getItem('currentUser');
        if (userData) {
            try {
                this.currentUser = JSON.parse(userData);
            } catch (error) {
                console.error('加载用户数据失败:', error);
            }
        }
    }
    
    saveCurrentUser() {
        if (this.currentUser) {
            localStorage.setItem('currentUser', JSON.stringify(this.currentUser));
        }
    }
    
    bindEvents() {
        // 密码强度检测
        const newPasswordInput = document.getElementById('newPassword');
        if (newPasswordInput) {
            newPasswordInput.addEventListener('input', () => {
                this.checkPasswordStrength(newPasswordInput.value);
            });
        }
        
        // 确认密码匹配检测
        const confirmPasswordInput = document.getElementById('confirmPassword');
        if (confirmPasswordInput) {
            confirmPasswordInput.addEventListener('input', () => {
                this.checkPasswordMatch();
            });
        }
    }
    
    // 显示修改密码模态框
    showChangePasswordModal() {
        const modal = document.getElementById('changePasswordModal');
        if (modal) {
            modal.style.display = 'flex';
            // 清空表单
            document.getElementById('changePasswordForm').reset();
            this.resetPasswordStrength();
        }
    }
    
    // 关闭修改密码模态框
    closeChangePasswordModal() {
        const modal = document.getElementById('changePasswordModal');
        if (modal) {
            modal.style.display = 'none';
        }
    }
    
    // 显示个人资料模态框
    showUserProfile() {
        const modal = document.getElementById('userProfileModal');
        if (modal) {
            modal.style.display = 'flex';
            this.loadUserProfile();
        }
    }
    
    // 关闭个人资料模态框
    closeUserProfileModal() {
        const modal = document.getElementById('userProfileModal');
        if (modal) {
            modal.style.display = 'none';
        }
    }
    
    // 加载用户资料到表单
    loadUserProfile() {
        if (!this.currentUser) return;
        
        document.getElementById('profileUsername').value = this.currentUser.username || '';
        document.getElementById('profileName').value = this.currentUser.name || '';
        document.getElementById('profileEmail').value = this.currentUser.email || '';
        document.getElementById('profilePhone').value = this.currentUser.phone || '';
        document.getElementById('profileDepartment').value = this.currentUser.department || '';
        document.getElementById('profileBio').value = this.currentUser.bio || '';
        
        // 更新头像
        const avatarImg = document.getElementById('profileAvatarImg');
        if (avatarImg) {
            const avatarName = encodeURIComponent(this.currentUser.name || this.currentUser.username);
            avatarImg.src = `https://ui-avatars.com/api/?name=${avatarName}&background=6366f1&color=fff&size=100`;
        }
    }
    
    // 处理密码修改
    handlePasswordChange(event) {
        event.preventDefault();
        
        const currentPassword = document.getElementById('currentPassword').value;
        const newPassword = document.getElementById('newPassword').value;
        const confirmPassword = document.getElementById('confirmPassword').value;
        
        // 验证当前密码（这里简化处理，实际应该调用后端API）
        if (currentPassword !== 'admin123') {
            showNotification('当前密码不正确', 'error');
            return;
        }
        
        // 验证新密码
        if (newPassword.length < 6) {
            showNotification('新密码长度至少6个字符', 'error');
            return;
        }
        
        if (newPassword !== confirmPassword) {
            showNotification('两次输入的密码不一致', 'error');
            return;
        }
        
        // 模拟密码修改
        setTimeout(() => {
            showNotification('密码修改成功', 'success');
            this.closeChangePasswordModal();
            
            // 记录密码修改时间
            if (this.currentUser) {
                this.currentUser.passwordChangedAt = new Date().toISOString();
                this.saveCurrentUser();
            }
        }, 1000);
    }
    
    // 处理个人资料更新
    handleProfileUpdate(event) {
        event.preventDefault();
        
        const formData = new FormData(event.target);
        const updatedUser = {
            ...this.currentUser,
            name: formData.get('name'),
            email: formData.get('email'),
            phone: formData.get('phone'),
            department: formData.get('department'),
            bio: formData.get('bio'),
            updatedAt: new Date().toISOString()
        };
        
        // 模拟保存
        setTimeout(() => {
            this.currentUser = updatedUser;
            this.saveCurrentUser();
            
            // 更新界面显示
            if (window.userMenuManager) {
                userMenuManager.updateUserDisplay();
            }
            
            showNotification('个人资料更新成功', 'success');
            this.closeUserProfileModal();
        }, 1000);
    }
    
    // 密码强度检测
    checkPasswordStrength(password) {
        const strengthBar = document.getElementById('strengthFill');
        const strengthText = document.getElementById('strengthText');
        const requirements = {
            length: password.length >= 6,
            uppercase: /[A-Z]/.test(password),
            lowercase: /[a-z]/.test(password),
            number: /\d/.test(password),
            special: /[!@#$%^&*(),.?":{}|<>]/.test(password)
        };
        
        // 更新要求列表
        Object.keys(requirements).forEach(req => {
            const element = document.getElementById(`req-${req}`);
            if (element) {
                element.className = requirements[req] ? 'requirement-met' : 'requirement-unmet';
            }
        });
        
        // 计算强度
        const metRequirements = Object.values(requirements).filter(Boolean).length;
        let strength = 0;
        let strengthLabel = '';
        let strengthColor = '';
        
        if (metRequirements <= 1) {
            strength = 20;
            strengthLabel = '很弱';
            strengthColor = '#ef4444';
        } else if (metRequirements === 2) {
            strength = 40;
            strengthLabel = '弱';
            strengthColor = '#f97316';
        } else if (metRequirements === 3) {
            strength = 60;
            strengthLabel = '中等';
            strengthColor = '#eab308';
        } else if (metRequirements === 4) {
            strength = 80;
            strengthLabel = '强';
            strengthColor = '#22c55e';
        } else {
            strength = 100;
            strengthLabel = '很强';
            strengthColor = '#16a34a';
        }
        
        if (strengthBar) {
            strengthBar.style.width = strength + '%';
            strengthBar.style.backgroundColor = strengthColor;
        }
        
        if (strengthText) {
            strengthText.textContent = `密码强度: ${strengthLabel}`;
            strengthText.style.color = strengthColor;
        }
    }
    
    // 检查密码匹配
    checkPasswordMatch() {
        const newPassword = document.getElementById('newPassword').value;
        const confirmPassword = document.getElementById('confirmPassword').value;
        const matchIndicator = document.getElementById('passwordMatch');
        
        if (confirmPassword.length === 0) {
            matchIndicator.textContent = '';
            return;
        }
        
        if (newPassword === confirmPassword) {
            matchIndicator.textContent = '✓ 密码匹配';
            matchIndicator.style.color = '#16a34a';
        } else {
            matchIndicator.textContent = '✗ 密码不匹配';
            matchIndicator.style.color = '#ef4444';
        }
    }
    
    // 重置密码强度显示
    resetPasswordStrength() {
        const strengthBar = document.getElementById('strengthFill');
        const strengthText = document.getElementById('strengthText');
        const matchIndicator = document.getElementById('passwordMatch');
        
        if (strengthBar) {
            strengthBar.style.width = '0%';
        }
        
        if (strengthText) {
            strengthText.textContent = '密码强度';
            strengthText.style.color = '';
        }
        
        if (matchIndicator) {
            matchIndicator.textContent = '';
        }
        
        // 重置要求列表
        ['length', 'uppercase', 'lowercase', 'number', 'special'].forEach(req => {
            const element = document.getElementById(`req-${req}`);
            if (element) {
                element.className = 'requirement-unmet';
            }
        });
    }
    
    // 切换密码可见性
    togglePasswordVisibility(inputId) {
        const input = document.getElementById(inputId);
        const button = input.nextElementSibling;
        const icon = button.querySelector('i');
        
        if (input.type === 'password') {
            input.type = 'text';
            icon.className = 'fas fa-eye-slash';
        } else {
            input.type = 'password';
            icon.className = 'fas fa-eye';
        }
    }
    
    // 选择头像文件
    selectAvatarFile() {
        document.getElementById('avatarFileInput').click();
    }
    
    // 处理头像上传
    handleAvatarUpload(event) {
        const file = event.target.files[0];
        if (file) {
            if (file.size > 5 * 1024 * 1024) { // 5MB限制
                showNotification('头像文件大小不能超过5MB', 'error');
                return;
            }
            
            const reader = new FileReader();
            reader.onload = (e) => {
                const avatarImg = document.getElementById('profileAvatarImg');
                if (avatarImg) {
                    avatarImg.src = e.target.result;
                }
                
                // 保存头像数据（实际项目中应该上传到服务器）
                if (this.currentUser) {
                    this.currentUser.avatar = e.target.result;
                    this.saveCurrentUser();
                }
                
                showNotification('头像上传成功', 'success');
            };
            reader.readAsDataURL(file);
        }
    }
    
    // 查看登录历史
    viewLoginHistory() {
        // 模拟登录历史数据
        const loginHistory = [
            { time: '2024-01-15 09:30:25', ip: '*************', device: 'Chrome on Windows', location: '北京' },
            { time: '2024-01-14 14:22:10', ip: '*************', device: 'Chrome on Windows', location: '北京' },
            { time: '2024-01-13 08:45:33', ip: '*********', device: 'Safari on iPhone', location: '上海' },
            { time: '2024-01-12 16:18:45', ip: '*************', device: 'Chrome on Windows', location: '北京' }
        ];
        
        let historyHtml = '最近登录记录:\n\n';
        loginHistory.forEach(record => {
            historyHtml += `时间: ${record.time}\n`;
            historyHtml += `IP: ${record.ip}\n`;
            historyHtml += `设备: ${record.device}\n`;
            historyHtml += `位置: ${record.location}\n\n`;
        });
        
        alert(historyHtml);
    }
}

// 全局用户账户管理器实例
let userAccountManager = null;

// 全局函数
function showChangePasswordModal() {
    if (userAccountManager) {
        userAccountManager.showChangePasswordModal();
    }
}

function closeChangePasswordModal() {
    if (userAccountManager) {
        userAccountManager.closeChangePasswordModal();
    }
}

function showUserProfile() {
    if (userAccountManager) {
        userAccountManager.showUserProfile();
    }
}

function closeUserProfileModal() {
    if (userAccountManager) {
        userAccountManager.closeUserProfileModal();
    }
}

function handlePasswordChange(event) {
    if (userAccountManager) {
        userAccountManager.handlePasswordChange(event);
    }
}

function handleProfileUpdate(event) {
    if (userAccountManager) {
        userAccountManager.handleProfileUpdate(event);
    }
}

function togglePasswordVisibility(inputId) {
    if (userAccountManager) {
        userAccountManager.togglePasswordVisibility(inputId);
    }
}

function selectAvatarFile() {
    if (userAccountManager) {
        userAccountManager.selectAvatarFile();
    }
}

function handleAvatarUpload(event) {
    if (userAccountManager) {
        userAccountManager.handleAvatarUpload(event);
    }
}

function viewLoginHistory() {
    if (userAccountManager) {
        userAccountManager.viewLoginHistory();
    }
}

// 初始化用户账户管理
function initializeUserAccount() {
    userAccountManager = new UserAccountManager();
    console.log('✅ 用户账户管理已初始化');
}

// 页面加载时初始化
document.addEventListener('DOMContentLoaded', function() {
    setTimeout(initializeUserAccount, 100);
});
