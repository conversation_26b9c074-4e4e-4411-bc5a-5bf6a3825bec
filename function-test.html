<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>功能测试诊断</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .test-result {
            padding: 10px;
            margin: 5px 0;
            border-radius: 5px;
        }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .warning { background: #fff3cd; color: #856404; }
        .btn {
            padding: 10px 20px;
            margin: 5px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            background: #007bff;
            color: white;
        }
        .btn:hover { background: #0056b3; }
        .function-list {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 10px;
            margin: 10px 0;
        }
        .function-item {
            padding: 10px;
            background: #f8f9fa;
            border-radius: 5px;
            font-family: monospace;
            font-size: 12px;
        }
        .available { border-left: 4px solid #28a745; }
        .missing { border-left: 4px solid #dc3545; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 系统功能诊断工具</h1>
        <p>检查管理系统各项功能的可用性</p>
        
        <div class="test-section">
            <h3>基础功能测试</h3>
            <button class="btn" onclick="testBasicFunctions()">测试基础功能</button>
            <div id="basicResults"></div>
        </div>
        
        <div class="test-section">
            <h3>JavaScript文件加载测试</h3>
            <button class="btn" onclick="testScriptLoading()">测试脚本加载</button>
            <div id="scriptResults"></div>
        </div>
        
        <div class="test-section">
            <h3>功能模块初始化测试</h3>
            <button class="btn" onclick="testModuleInitialization()">测试模块初始化</button>
            <div id="moduleResults"></div>
        </div>
        
        <div class="test-section">
            <h3>导航功能测试</h3>
            <button class="btn" onclick="testNavigation()">测试导航</button>
            <div id="navigationResults"></div>
        </div>
        
        <div class="test-section">
            <h3>用户交互测试</h3>
            <button class="btn" onclick="testUserInteraction()">测试用户交互</button>
            <div id="interactionResults"></div>
        </div>
        
        <div class="test-section">
            <h3>修复建议</h3>
            <button class="btn" onclick="generateFixSuggestions()">生成修复建议</button>
            <div id="fixSuggestions"></div>
        </div>
        
        <div style="text-align: center; margin-top: 30px;">
            <button class="btn" onclick="window.location.href='index.html'" style="background: #28a745;">进入管理系统</button>
            <button class="btn" onclick="window.location.href='ultimate-fix.html'" style="background: #dc3545;">使用修复工具</button>
        </div>
    </div>

    <script>
        function showResult(containerId, message, type = 'success') {
            const container = document.getElementById(containerId);
            const div = document.createElement('div');
            div.className = `test-result ${type}`;
            div.innerHTML = message;
            container.appendChild(div);
        }

        function clearResults(containerId) {
            document.getElementById(containerId).innerHTML = '';
        }

        function testBasicFunctions() {
            clearResults('basicResults');
            
            // 测试localStorage
            try {
                localStorage.setItem('test', 'value');
                const value = localStorage.getItem('test');
                localStorage.removeItem('test');
                if (value === 'value') {
                    showResult('basicResults', '✓ LocalStorage 功能正常', 'success');
                } else {
                    showResult('basicResults', '✗ LocalStorage 功能异常', 'error');
                }
            } catch (error) {
                showResult('basicResults', '✗ LocalStorage 错误: ' + error.message, 'error');
            }
            
            // 测试DOM操作
            try {
                const testDiv = document.createElement('div');
                testDiv.textContent = 'test';
                document.body.appendChild(testDiv);
                const success = testDiv.textContent === 'test';
                document.body.removeChild(testDiv);
                
                if (success) {
                    showResult('basicResults', '✓ DOM 操作功能正常', 'success');
                } else {
                    showResult('basicResults', '✗ DOM 操作功能异常', 'error');
                }
            } catch (error) {
                showResult('basicResults', '✗ DOM 操作错误: ' + error.message, 'error');
            }
            
            // 测试事件处理
            try {
                let eventFired = false;
                const testBtn = document.createElement('button');
                testBtn.addEventListener('click', () => { eventFired = true; });
                testBtn.click();
                
                if (eventFired) {
                    showResult('basicResults', '✓ 事件处理功能正常', 'success');
                } else {
                    showResult('basicResults', '✗ 事件处理功能异常', 'error');
                }
            } catch (error) {
                showResult('basicResults', '✗ 事件处理错误: ' + error.message, 'error');
            }
        }

        function testScriptLoading() {
            clearResults('scriptResults');
            
            const scripts = [
                'app.js',
                'user-management.js',
                'analytics.js',
                'order-management.js',
                'settings.js',
                'file-management.js',
                'help-center.js'
            ];
            
            scripts.forEach(script => {
                const scriptElements = document.querySelectorAll(`script[src="${script}"]`);
                if (scriptElements.length > 0) {
                    showResult('scriptResults', `✓ ${script} 已加载`, 'success');
                } else {
                    showResult('scriptResults', `✗ ${script} 未找到`, 'error');
                }
            });
        }

        function testModuleInitialization() {
            clearResults('moduleResults');
            
            const modules = [
                { name: 'initializeUserManagement', file: 'user-management.js' },
                { name: 'initializeAnalytics', file: 'analytics.js' },
                { name: 'initializeOrderManagement', file: 'order-management.js' },
                { name: 'initializeSettings', file: 'settings.js' },
                { name: 'initializeFileManagement', file: 'file-management.js' },
                { name: 'initializeHelpCenter', file: 'help-center.js' },
                { name: 'initializeInventoryManagement', file: 'inventory-management.js' },
                { name: 'initializeReportsManagement', file: 'reports-management.js' },
                { name: 'initializePermissionManagement', file: 'permission-management.js' }
            ];
            
            modules.forEach(module => {
                if (typeof window[module.name] === 'function') {
                    showResult('moduleResults', `✓ ${module.name} 可用`, 'success');
                } else {
                    showResult('moduleResults', `✗ ${module.name} 不可用 (来自 ${module.file})`, 'error');
                }
            });
        }

        function testNavigation() {
            clearResults('navigationResults');
            
            // 测试导航元素
            const navItems = document.querySelectorAll('.nav-item');
            if (navItems.length > 0) {
                showResult('navigationResults', `✓ 找到 ${navItems.length} 个导航项`, 'success');
            } else {
                showResult('navigationResults', '✗ 未找到导航项', 'error');
            }
            
            // 测试页面元素
            const pages = document.querySelectorAll('.page');
            if (pages.length > 0) {
                showResult('navigationResults', `✓ 找到 ${pages.length} 个页面`, 'success');
            } else {
                showResult('navigationResults', '✗ 未找到页面元素', 'error');
            }
            
            // 测试导航函数
            if (typeof initializeNavigation === 'function') {
                showResult('navigationResults', '✓ initializeNavigation 函数可用', 'success');
            } else {
                showResult('navigationResults', '✗ initializeNavigation 函数不可用', 'error');
            }
        }

        function testUserInteraction() {
            clearResults('interactionResults');
            
            // 测试按钮点击
            const buttons = document.querySelectorAll('button');
            showResult('interactionResults', `✓ 找到 ${buttons.length} 个按钮`, 'success');
            
            // 测试表单元素
            const forms = document.querySelectorAll('form');
            showResult('interactionResults', `✓ 找到 ${forms.length} 个表单`, 'success');
            
            // 测试模态框
            const modals = document.querySelectorAll('.modal-overlay');
            showResult('interactionResults', `✓ 找到 ${modals.length} 个模态框`, 'success');
            
            // 测试通知函数
            if (typeof showNotification === 'function') {
                showResult('interactionResults', '✓ showNotification 函数可用', 'success');
                try {
                    showNotification('测试通知', 'info');
                    showResult('interactionResults', '✓ 通知功能测试成功', 'success');
                } catch (error) {
                    showResult('interactionResults', '✗ 通知功能测试失败: ' + error.message, 'error');
                }
            } else {
                showResult('interactionResults', '✗ showNotification 函数不可用', 'error');
            }
        }

        function generateFixSuggestions() {
            clearResults('fixSuggestions');
            
            const suggestions = [
                '1. 使用终极修复工具进行完整系统重置',
                '2. 检查浏览器控制台是否有JavaScript错误',
                '3. 清除浏览器缓存和LocalStorage数据',
                '4. 确保所有JavaScript文件正确加载',
                '5. 检查网络连接和文件访问权限',
                '6. 尝试使用不同的浏览器访问系统',
                '7. 如果问题持续，使用备用登录页面'
            ];
            
            suggestions.forEach(suggestion => {
                showResult('fixSuggestions', suggestion, 'warning');
            });
            
            showResult('fixSuggestions', '<strong>推荐操作：</strong>点击下方"使用修复工具"按钮进行自动修复', 'success');
        }

        // 页面加载时自动运行基础测试
        window.onload = function() {
            // 设置登录状态以便测试
            if (localStorage.getItem('isLoggedIn') !== 'true') {
                localStorage.setItem('isLoggedIn', 'true');
                localStorage.setItem('currentUser', JSON.stringify({
                    username: 'admin',
                    name: '张管理员',
                    role: '系统管理员'
                }));
            }
            
            setTimeout(() => {
                testBasicFunctions();
                testScriptLoading();
                testModuleInitialization();
            }, 1000);
        };
    </script>
</body>
</html>